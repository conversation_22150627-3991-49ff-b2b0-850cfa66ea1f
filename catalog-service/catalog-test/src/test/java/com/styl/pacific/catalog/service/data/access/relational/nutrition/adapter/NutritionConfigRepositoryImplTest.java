/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.relational.nutrition.adapter;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import com.styl.pacific.catalog.service.data.access.relational.nutrition.loaders.ClassPathDefaultNutritionDataLoader;
import com.styl.pacific.catalog.service.domain.nutrition.dto.NutritionConfigDto;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class NutritionConfigRepositoryImplTest {
	@Mock
	private ClassPathDefaultNutritionDataLoader classPathDefaultNutritionDataLoader;

	@InjectMocks
	private NutritionConfigRepositoryImpl nutritionConfigRepository;

	@Test
	void shouldReturnNutritionConfigs_whenFindAll() {
		// Arrange
		NutritionConfigDto nutritionConfigDtoMock = NutritionConfigDto.builder()
				.name("name")
				.unit("unit")
				.build();
		List<NutritionConfigDto> nutritionConfigListMock = List.of(nutritionConfigDtoMock);
		when(classPathDefaultNutritionDataLoader.loadContent()).thenReturn(Optional.of(nutritionConfigListMock));
		// Act
		List<NutritionConfigDto> nutritionConfigList = nutritionConfigRepository.findAll();
		// Assert
		assertEquals(nutritionConfigListMock, nutritionConfigList);
	}

	@Test
	void shouldReturnEmpty_whenFindAll() {
		// Arrange
		when(classPathDefaultNutritionDataLoader.loadContent()).thenReturn(Optional.empty());
		// Act
		List<NutritionConfigDto> nutritionConfigList = nutritionConfigRepository.findAll();
		// Assert
		assertTrue(nutritionConfigList.isEmpty());
	}

}
