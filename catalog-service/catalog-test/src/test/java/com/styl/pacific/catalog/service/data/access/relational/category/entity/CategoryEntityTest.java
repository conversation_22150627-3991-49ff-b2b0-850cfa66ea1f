/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.relational.category.entity;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.List;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 */
class CategoryEntityTest {

	private static final Long CATEGORY_ID = 1L;
	private static final String CATEGORY_NAME = "Category";
	private static final String CATEGORY_IMAGE_URL = "/category/image";
	private static final String CATEGORY_DESCRIPTION = "Category description";
	private static final Instant CATEGORY_CREATED_AT = ZonedDateTime.now()
			.toInstant();
	private static final Instant CATEGORY_UPDATED_AT = ZonedDateTime.now()
			.toInstant();
	private static final Instant CATEGORY_DELETED_AT = ZonedDateTime.now()
			.toInstant();
	private static final Long TENANT_ID = 1L;

	@Test
	void shouldCreate_whenBuilder() {
		// Arrange
		CategoryEntity child1 = getCategoryArrange();
		CategoryEntity child2 = getCategoryArrange();
		CategoryEntity child3 = getCategoryArrange();
		List<CategoryEntity> children = List.of(child1, child2, child3);
		// Act
		CategoryEntity entity = CategoryEntity.builder()

				.id(CATEGORY_ID)

				.tenantId(TENANT_ID)

				.name(CATEGORY_NAME)

				.iconPath(CATEGORY_IMAGE_URL)

				.description(CATEGORY_DESCRIPTION)

				.subCategories(children)

				.parentId(CATEGORY_ID)

				.createdAt(CATEGORY_CREATED_AT)

				.updatedAt(CATEGORY_UPDATED_AT)

				.deletedAt(CATEGORY_DELETED_AT)
				.build();
		// Assert
		assertEquals(CATEGORY_ID, entity.getId());
		assertEquals(TENANT_ID, entity.getTenantId());
		assertEquals(CATEGORY_NAME, entity.getName());
		assertEquals(CATEGORY_IMAGE_URL, entity.getIconPath());
		assertEquals(CATEGORY_DESCRIPTION, entity.getDescription());
		assertEquals(CATEGORY_CREATED_AT, entity.getCreatedAt());
		assertEquals(CATEGORY_UPDATED_AT, entity.getUpdatedAt());
		assertEquals(CATEGORY_DELETED_AT, entity.getDeletedAt());
		assertEquals(children, entity.getSubCategories());
		assertEquals(CATEGORY_ID, entity.getParentId());
	}

	@Test
	void shouldEqual_whenCompareHasCode() {
		// Arrange
		CategoryEntity entity1 = getCategoryArrange();
		CategoryEntity entity2 = getCategoryArrange();
		// Act
		// Assert
		assertEquals(entity1.hashCode(), entity2.hashCode());
	}

	@Test
	void shouldEqual_whenCompare() {
		// Arrange
		CategoryEntity entity1 = getCategoryArrange();
		CategoryEntity entity2 = getCategoryArrange();
		// Act
		// Assert
		assertEquals(entity1, entity2);
	}

	private CategoryEntity getCategoryArrange() {
		return CategoryEntity.builder()

				.id(CATEGORY_ID)

				.tenantId(TENANT_ID)

				.name(CATEGORY_NAME)

				.iconPath(CATEGORY_IMAGE_URL)

				.description(CATEGORY_DESCRIPTION)

				.createdAt(CATEGORY_CREATED_AT)

				.updatedAt(CATEGORY_UPDATED_AT)

				.deletedAt(CATEGORY_DELETED_AT)
				.build();
	}
}
