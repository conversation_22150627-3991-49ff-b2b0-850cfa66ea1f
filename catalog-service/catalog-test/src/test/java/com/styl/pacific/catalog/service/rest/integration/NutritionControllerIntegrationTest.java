/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.rest.integration;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.styl.pacific.application.rest.dto.ErrorResponse;
import com.styl.pacific.catalog.service.config.IntegrationTestConfiguration;
import com.styl.pacific.catalog.service.domain.nutrition.dto.command.CreateNutritionCommand;
import com.styl.pacific.catalog.service.domain.nutrition.dto.command.UpdateNutritionCommand;
import com.styl.pacific.catalog.service.features.tenant.TenantCreatedEventKafkaConsumer;
import com.styl.pacific.catalog.service.shared.http.nutrition.response.NutritionResponse;
import com.styl.pacific.common.constant.PacificRestConstants;
import com.styl.pacific.common.test.BaseWebClientWithDbTest;
import com.styl.pacific.domain.dto.CurrencyResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantSettingsResponse;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.reactive.server.WebTestClient;

/**
 * <AUTHOR>
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = IntegrationTestConfiguration.class)
class NutritionControllerIntegrationTest extends BaseWebClientWithDbTest {

	private static final String NUTRITION_PATH = "/api/catalog/nutrition";

	private static final Long ID = 1L;
	private static final Long TENANT_ID = 2L;
	private static final String NAME = "Calories";
	private static final String UNIT = "gm";
	private static final Instant CREATED_AT;
	private static final Instant UPDATED_AT;
	private static final String CURRENCY_CODE = "SGD";
	private static Long NEW_ID;

	static {
		DateTimeFormatter formatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME;
		CREATED_AT = OffsetDateTime.parse("2024-05-13T15:05:46.685Z", formatter)
				.toInstant();
		UPDATED_AT = OffsetDateTime.parse("2024-05-14T09:56:00.130Z", formatter)
				.toInstant();
	}

	@Autowired
	@Qualifier("tenantService")
	private WireMockServer tenantServiceMock;

	@Autowired
	private ObjectMapper objectMapper;

	@MockitoBean
	private TenantCreatedEventKafkaConsumer tenantCreatedEventKafkaConsumer;

	@Test
	@Order(2)
	void shouldReturn200WithNutritionResponse_whenGetById() {
		// Arrange
		Map<String, Object> uriVariables = new HashMap<>();
		uriVariables.put("id", ID);
		// Act
		WebTestClient.ResponseSpec responseSpec = webClient.get()
				.uri(uriBuilder -> {
					uriBuilder.path(NUTRITION_PATH);
					return uriBuilder.pathSegment("{id}")
							.build(uriVariables);
				})
				.accept(MediaType.APPLICATION_JSON)
				.headers(this::setHeaders)
				.exchange();
		// Assert
		responseSpec.expectStatus()
				.isOk()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectBody(NutritionResponse.class)
				.consumeWith(result -> {
					NutritionResponse body = result.getResponseBody();
					assert body != null;
					assertEquals(ID.toString(), body.id());
					assertEquals(TENANT_ID.toString(), body.tenantId());
					assertEquals(NAME, body.name());
					assertEquals(UNIT, body.unit());
					assertEquals(CREATED_AT.toEpochMilli(), body.createdAt());
					assertEquals(UPDATED_AT.toEpochMilli(), body.updatedAt());
				});
	}

	@Test
	@Order(3)
	void shouldReturn201WithNutritionResponse_whenPostCreate() throws Exception {
		// Arrange
		TenantResponse tenantResponse = TenantResponse.builder()
				.tenantId(TENANT_ID)
				.settings(TenantSettingsResponse.builder()
						.currency(CurrencyResponse.builder()
								.currencyCode(CURRENCY_CODE)
								.build())
						.build())
				.build();
		tenantServiceMock.stubFor(WireMock.get(WireMock.urlPathMatching("/api/tenant/tenants/" + TENANT_ID))
				.willReturn(WireMock.aResponse()
						.withStatus(HttpStatus.OK.value())
						.withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
						.withBody(objectMapper.writeValueAsString(tenantResponse))));
		CreateNutritionCommand commandMock = CreateNutritionCommand.builder()
				.name(NAME)
				.unit(UNIT)
				.build();
		// Act
		WebTestClient.ResponseSpec responseSpec = webClient.post()
				.uri(uriBuilder -> {
					uriBuilder.path(NUTRITION_PATH);
					return uriBuilder.build();
				})
				.accept(MediaType.APPLICATION_JSON)
				.headers(this::setHeaders)
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(commandMock)
				.exchange();
		// Assert
		responseSpec.expectStatus()
				.isCreated()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectBody(NutritionResponse.class)
				.consumeWith(result -> {
					NutritionResponse body = result.getResponseBody();
					assert body != null;
					NEW_ID = Long.parseLong(body.id());
					assertEquals(commandMock.name(), body.name());
					assertEquals(commandMock.unit(), body.unit());
				});
	}

	@Test
	@Order(4)
	void shouldReturn200WithNutritionResponse_whenPutUpdate() {
		// Arrange

		UpdateNutritionCommand commandMock = UpdateNutritionCommand.builder()
				.name(NAME)
				.unit(UNIT)
				.build();
		Map<String, Object> uriVariables = new HashMap<>();
		uriVariables.put("id", ID);

		// Act
		WebTestClient.ResponseSpec responseSpec = webClient.put()
				.uri(uriBuilder -> {
					uriBuilder.path(NUTRITION_PATH);
					return uriBuilder.pathSegment("{id}")
							.build(uriVariables);
				})
				.accept(MediaType.APPLICATION_JSON)
				.headers(this::setHeaders)
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(commandMock)
				.exchange();
		// Assert
		responseSpec.expectStatus()
				.isOk()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectBody(NutritionResponse.class)
				.consumeWith(result -> {
					NutritionResponse body = result.getResponseBody();
					assert body != null;
					assertEquals(commandMock.name(), body.name());
					assertEquals(commandMock.unit(), body.unit());
				});

	}

	@Test
	void shouldReturn204_whenDelete() {
		// Arrange
		Map<String, Object> uriVariables = new HashMap<>();
		uriVariables.put("id", NEW_ID);
		// Act
		WebTestClient.ResponseSpec responseSpec = webClient.delete()
				.uri(uriBuilder -> {
					uriBuilder.path(NUTRITION_PATH);
					return uriBuilder.pathSegment("{id}")
							.build(uriVariables);
				})
				.accept(MediaType.APPLICATION_JSON)
				.headers(this::setHeaders)
				.exchange();
		// Assert
		responseSpec.expectStatus()
				.isNoContent();
	}

	@Test
	void shouldReturn400_whenDelete() {
		// Arrange
		Map<String, Object> uriVariables = new HashMap<>();
		uriVariables.put("id", ID);
		String exceptionMessageExpect = "have some products related to";
		// Act
		WebTestClient.ResponseSpec responseSpec = webClient.delete()
				.uri(uriBuilder -> {
					uriBuilder.path(NUTRITION_PATH);
					return uriBuilder.pathSegment("{id}")
							.build(uriVariables);
				})
				.accept(MediaType.APPLICATION_JSON)
				.headers(this::setHeaders)
				.exchange();
		// Assert
		responseSpec.expectStatus()
				.isBadRequest()
				.expectHeader()
				.contentType(MediaType.APPLICATION_JSON)
				.expectBody(ErrorResponse.class)
				.consumeWith(result -> {
					ErrorResponse body = result.getResponseBody();
					assert body != null;
					assertThat(body.getDetails()
							.getFirst()).contains(exceptionMessageExpect);
				});
	}

	private void setHeaders(HttpHeaders headers) {
		headers.set(PacificRestConstants.PlatformHeader.HEADER_X_REQUEST_ID, Long.toString(new Random().nextLong()));
		headers.set(PacificRestConstants.PlatformHeader.HEADER_X_TENANT_ID, Long.toString(TENANT_ID));
	}
}
