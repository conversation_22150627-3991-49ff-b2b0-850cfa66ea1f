/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.messaging.tenant;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.timeout;
import static org.mockito.Mockito.verify;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.WireMockServer;
import com.styl.pacific.catalog.service.config.IntegrationTestConfiguration;
import com.styl.pacific.catalog.service.domain.nutrition.NutritionTenantService;
import com.styl.pacific.common.test.BaseWebClientWithDbTest;
import com.styl.pacific.common.test.container.KafkaContainerTest;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.kafka.producer.service.KafkaProducer;
import com.styl.pacific.kafka.tenant.avro.model.TenantCreatedEventAvroModel;
import java.time.Instant;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.support.SendResult;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@ContextConfiguration(classes = { IntegrationTestConfiguration.class })
@TestPropertySource(properties = "pacific.kafka.catalog-service.consumers.tenant-created-event.enabled=false")
class TenantCreateEventKafkaConsumerPropertiesIntegrationTest extends BaseWebClientWithDbTest implements
		KafkaContainerTest {

	private static final Long TENANT_ID = 2L;
	private static final String TENANT_NAME = "Tenant name";
	private static final String REALM_ID = "realm-test";

	@Autowired
	@Qualifier("tenantService")
	private WireMockServer tenantServiceMock;

	@Autowired
	private ObjectMapper objectMapper;

	@Autowired
	private KafkaProducer<UUID, TenantCreatedEventAvroModel> producer;

	@MockitoSpyBean
	private NutritionTenantService nutritionTenantService;

	@Value(value = "${pacific.kafka.catalog-service.consumers.tenant-created-event.group-id}")
	private String ID;

	@Value(value = "${pacific.kafka.catalog-service.consumers.tenant-created-event.topic-name}")
	private String TOPIC;

	@Test
	void shouldNotDetectConsumer_whenDisable() throws ExecutionException, InterruptedException {
		// Arrange
		TenantCreatedEventAvroModel tenantCreatedEvent = TenantCreatedEventAvroModel.newBuilder()
				.setTenantId(TENANT_ID)
				.setId(UUID.randomUUID())
				.setName(TENANT_NAME)
				.setRealmId(REALM_ID)
				.setEventTime(Instant.now()
						.toEpochMilli())
				.build();
		// Act
		CompletableFuture<SendResult<UUID, TenantCreatedEventAvroModel>> completableFuture = new CompletableFuture<>();
		completableFuture.orTimeout(5000, TimeUnit.MILLISECONDS);
		producer.send(TOPIC, tenantCreatedEvent.getId(), tenantCreatedEvent, (uuidTenantCreatedEventAvroModelSendResult,
				throwable) -> {
			if (throwable != null) {
				completableFuture.completeExceptionally(throwable);

			} else {
				completableFuture.complete(uuidTenantCreatedEventAvroModelSendResult);
			}
		});
		// Assert
		assertNotNull(completableFuture.get());
		verify(nutritionTenantService, timeout(5000).times(0)).initDefaultNutrition(new TenantId(TENANT_ID));
	}
}
