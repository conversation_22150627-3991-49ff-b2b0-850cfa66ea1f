/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.relational.product.adapter;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.querydsl.core.types.Predicate;
import com.styl.pacific.catalog.service.data.access.relational.category.entity.CategoryEntity;
import com.styl.pacific.catalog.service.data.access.relational.healthierchoice.entity.HealthierChoiceEntity;
import com.styl.pacific.catalog.service.data.access.relational.product.entity.ProductEntity;
import com.styl.pacific.catalog.service.data.access.relational.product.mapper.ProductDataAccessMapper;
import com.styl.pacific.catalog.service.data.access.relational.product.repository.ProductAllergenJpaRepository;
import com.styl.pacific.catalog.service.data.access.relational.product.repository.ProductImageJpaRepository;
import com.styl.pacific.catalog.service.data.access.relational.product.repository.ProductJpaRepository;
import com.styl.pacific.catalog.service.data.access.relational.product.repository.ProductNutritionJpaRepository;
import com.styl.pacific.catalog.service.data.access.relational.product.repository.ProductOptionItemJpaRepository;
import com.styl.pacific.catalog.service.data.access.relational.product.repository.ProductOptionJpaRepository;
import com.styl.pacific.catalog.service.domain.product.dto.ProductDto;
import com.styl.pacific.catalog.service.domain.product.dto.query.PaginationProductQuery;
import com.styl.pacific.catalog.service.domain.product.dto.query.ProductFilter;
import com.styl.pacific.catalog.service.domain.product.entity.Product;
import com.styl.pacific.catalog.service.domain.product.entity.ProductImage;
import com.styl.pacific.catalog.service.domain.product.entity.ProductNutrition;
import com.styl.pacific.catalog.service.domain.product.enums.ProductStatus;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.CategoryId;
import com.styl.pacific.domain.valueobject.HealthierChoiceId;
import com.styl.pacific.domain.valueobject.Money;
import com.styl.pacific.domain.valueobject.NutritionId;
import com.styl.pacific.domain.valueobject.ProductId;
import com.styl.pacific.domain.valueobject.ProductImageId;
import com.styl.pacific.domain.valueobject.StoreId;
import com.styl.pacific.domain.valueobject.TenantId;
import java.math.BigInteger;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class ProductRepositoryImplTest {
	private static final Long PRODUCT_ID = 11L;
	private static final Long CATEGORY_ID = 1L;
	private static final Long HEALTHIER_CHOICE_ID = 1L;
	private static final Long STORE_ID = 1L;
	private static final Long TENANT_ID = 1L;
	private static final String NAME = "Product 11";
	private static final String BARCODE = "1100000";
	private static final String SKU = "SKU11";
	private static final String BRIEF_DESCRIPTION = "briefDescription";
	private static final String DESCRIPTION = "description";
	private static final String INGREDIENTS = "ingredients";
	private static final ProductStatus STATUS = ProductStatus.ACTIVE;
	private static final BigInteger UNIT_PRICE = BigInteger.valueOf(100);

	private static final Long NUTRITION_ID = 1L;
	private static final Double NUTRITION_VALUE = 100.0;

	private static final Long IMAGE_ID = 1L;
	private static final String IMAGE_URL = "http://url.com";

	private static final Long INVENTORY_QUANTITY = 1L;

	@Mock
	private ProductJpaRepository productJpaRepository;

	@Mock
	private ProductNutritionJpaRepository productNutritionJpaRepository;

	@Mock
	private ProductOptionItemJpaRepository productOptionItemJpaRepository;

	@Mock
	private ProductOptionJpaRepository productOptionJpaRepository;

	@Mock
	private ProductImageJpaRepository productImageJpaRepository;

	@Mock
	private ProductAllergenJpaRepository productAllergenJpaRepository;

	@Spy
	private ProductDataAccessMapper productDataAccessMapper = ProductDataAccessMapper.INSTANCE;

	@InjectMocks
	private ProductRepositoryImpl productRepository;

	@Test
	void shouldReturnProduct_whenFindById() {
		// Arrange
		ProductEntity productEntityMock = getProductEntity();
		when(productJpaRepository.findOneWithFetchJoin(any())).thenReturn(Optional.of(productEntityMock));
		// Act
		Optional<Product> productExist = productRepository.findById(new TenantId(TENANT_ID), new ProductId(PRODUCT_ID));
		// Assert
		verify(productJpaRepository, times(1)).findOneWithFetchJoin(any());
		assertThat(productExist).isPresent();
		Product product = productExist.get();

		assertEquals(productEntityMock.getId(), product.getId()
				.getValue());
		assertEquals(productEntityMock.getCategory()
				.getId(), product.getCategoryId()
						.getValue());
		assertEquals(productEntityMock.getTenantId(), product.getTenantId()
				.getValue());
		assertEquals(productEntityMock.getBarcode(), product.getBarcode());
		assertEquals(productEntityMock.getBriefInformation(), product.getBriefInformation());
		assertEquals(productEntityMock.getDescription(), product.getDescription());
		assertEquals(productEntityMock.getName(), product.getName());
		assertEquals(productEntityMock.getSku(), product.getSku());
		assertEquals(productEntityMock.getStoreId(), product.getStoreId()
				.getValue());
		assertEquals(productEntityMock.getIngredients(), product.getIngredients());
		assertEquals(productEntityMock.getStatus(), product.getStatus());
		assertEquals(productEntityMock.getUnitPrice(), product.getUnitPrice()
				.getAmount());
	}

	@Test
	void shouldReturnProduct_whenSave() {
		// Arrange
		Product productMock = getProduct();
		when(productJpaRepository.save(any(ProductEntity.class))).then(invocation -> invocation.getArgument(0));
		// Act
		ProductDto productSave = productRepository.saveDto(productMock);
		// Assert
		verify(productJpaRepository, times(1)).save(any(ProductEntity.class));
		assertEquals(productMock.getId(), productSave.id());
		assertEquals(productMock.getCategoryId(), productSave.category()
				.id());
		assertEquals(productMock.getTenantId(), productSave.tenantId());
		assertEquals(productMock.getBarcode(), productSave.barcode());
		assertEquals(productMock.getBriefInformation(), productSave.briefInformation());
		assertEquals(productMock.getDescription(), productSave.description());
		assertEquals(productMock.getName(), productSave.name());
		assertEquals(productMock.getSku(), productSave.sku());
		assertEquals(productMock.getStoreId(), productSave.storeId());
		assertEquals(productMock.getIngredients(), productSave.ingredients());
		assertEquals(productMock.getStatus(), productSave.status());
		assertEquals(productMock.getUnitPrice()
				.getAmount(), productSave.unitPrice());
	}

	@Test
	void shouldRemove_whenDeleteById() {
		// Arrange
		ProductEntity entityMock = getProductEntity();
		when(productJpaRepository.findOne(any(Predicate.class))).thenReturn(Optional.of(entityMock));
		// Act
		productRepository.deleteById(new TenantId(TENANT_ID), new ProductId(PRODUCT_ID));
		// Assert
		verify(productJpaRepository, times(1)).findOne(any(Predicate.class));
		verify(productJpaRepository, times(1)).delete(any(ProductEntity.class));
	}

	@Test
	void shouldReturnProduct_whenUpdate() {
		// Arrange
		Product productMock = getProduct();
		when(productJpaRepository.saveAndFlush(any(ProductEntity.class))).then(invocation -> invocation.getArgument(0));
		// Act
		ProductDto productSave = productRepository.updateDto(productMock);
		// Assert
		verify(productJpaRepository, times(1)).saveAndFlush(any(ProductEntity.class));
		assertEquals(productMock.getId(), productSave.id());
		assertEquals(productMock.getCategoryId(), productSave.category()
				.id());
		assertEquals(productMock.getTenantId(), productSave.tenantId());
		assertEquals(productMock.getBarcode(), productSave.barcode());
		assertEquals(productMock.getBriefInformation(), productSave.briefInformation());
		assertEquals(productMock.getDescription(), productSave.description());
		assertEquals(productMock.getName(), productSave.name());
		assertEquals(productMock.getSku(), productSave.sku());
		assertEquals(productMock.getStoreId(), productSave.storeId());
		assertEquals(productMock.getIngredients(), productSave.ingredients());
		assertEquals(productMock.getStatus(), productSave.status());
		assertEquals(productMock.getUnitPrice()
				.getAmount(), productSave.unitPrice());
		assertEquals(productMock.getImages()
				.size(), productSave.images()
						.size());
		assertEquals(productMock.getImages()
				.getFirst()
				.getId()
				.getValue(), productSave.images()
						.getFirst()
						.getId()
						.getValue());
		assertEquals(productMock.getNutrition()
				.size(), productSave.nutrition()
						.size());
		assertEquals(productMock.getNutrition()
				.getFirst()
				.getNutritionId(), productSave.nutrition()
						.getFirst()
						.id());
		assertEquals(productMock.getNutrition()
				.getFirst()
				.getValue(), productSave.nutrition()
						.getFirst()
						.value());
	}

	@Test
	void shouldReturnPagingProduct_whenFindPagingQuery() {
		// Arrange
		ProductEntity productMock = getProductEntity();
		List<ProductEntity> productsMock = List.of(productMock);

		int pageMock = 0;
		int sizeMock = 200;

		Pageable page = PageRequest.of(pageMock, sizeMock);
		Page<ProductEntity> pageEntity = new PageImpl<>(productsMock, page, productsMock.size());
		PaginationProductQuery queryMock = new PaginationProductQuery(ProductFilter.builder()
				.barcode(BARCODE)
				.categoryIds(List.of(CATEGORY_ID))
				.fromUnitPrice(UNIT_PRICE)
				.toUnitPrice(UNIT_PRICE)
				.name(NAME)
				.build(), sizeMock, pageMock, "ASC", Set.of(ProductEntity.FIELD_ID));

		when(productJpaRepository.findBy(any(Predicate.class), any())).thenReturn(pageEntity);
		when(productNutritionJpaRepository.findBy(any(Predicate.class), any())).thenReturn(Collections.emptyList());
		when(productImageJpaRepository.findBy(any(Predicate.class), any())).thenReturn(Collections.emptyList());
		when(productOptionJpaRepository.findBy(any(Predicate.class), any())).thenReturn(Collections.emptyList());
		when(productAllergenJpaRepository.findBy(any(Predicate.class), any())).thenReturn(Collections.emptyList());
		// Act
		Paging<ProductDto> pageProduct = productRepository.findAllPaging(new TenantId(TENANT_ID), queryMock);
		// Assert
		verify(productJpaRepository, times(1)).findBy(any(Predicate.class), any());
		verify(productNutritionJpaRepository, times(1)).findBy(any(Predicate.class), any());
		verify(productImageJpaRepository, times(1)).findBy(any(Predicate.class), any());
		verify(productOptionJpaRepository, times(1)).findBy(any(Predicate.class), any());
		verify(productAllergenJpaRepository, times(1)).findBy(any(Predicate.class), any());
		assertEquals(productsMock.size(), pageProduct.getContent()
				.size());
	}

	@Test
	void shouldReturnCheckedExist_whenCheckExist() {
		// Arrange
		when(productJpaRepository.exists(any(Predicate.class))).thenReturn(true);
		// Act
		boolean existResult = productRepository.existById(new TenantId(TENANT_ID), new ProductId(PRODUCT_ID));
		// Assert
		assertTrue(existResult);
	}

	Product getProduct() {
		ProductNutrition productNutrition = ProductNutrition.builder()
				.productId(new ProductId(PRODUCT_ID))
				.nutritionId(new NutritionId(NUTRITION_ID))
				.value(NUTRITION_VALUE)
				.build();
		ProductImage productImage = ProductImage.builder()
				.id(new ProductImageId(IMAGE_ID))
				.imagePath(IMAGE_URL)
				.build();
		return Product.builder()
				.id(new ProductId(PRODUCT_ID))
				.tenantId(new TenantId(TENANT_ID))
				.healthierChoiceId(new HealthierChoiceId(HEALTHIER_CHOICE_ID))
				.categoryId(new CategoryId(CATEGORY_ID))
				.storeId(new StoreId(STORE_ID))
				.name(NAME)
				.sku(SKU)
				.barcode(BARCODE)
				.briefInformation(BRIEF_DESCRIPTION)
				.description(DESCRIPTION)
				.ingredients(INGREDIENTS)
				.status(STATUS)
				.unitPrice(new Money(UNIT_PRICE))
				.images(List.of(productImage))
				.nutrition(List.of(productNutrition))
				.build();
	}

	ProductEntity getProductEntity() {
		CategoryEntity categoryEntity = CategoryEntity.builder()
				.id(CATEGORY_ID)
				.build();
		HealthierChoiceEntity healthierChoice = HealthierChoiceEntity.builder()
				.id(CATEGORY_ID)
				.build();
		return ProductEntity.builder()
				.id(PRODUCT_ID)
				.tenantId(TENANT_ID)
				.categoryId(CATEGORY_ID)
				.options(Collections.emptyList())
				.images(Collections.emptyList())
				.nutrition(Collections.emptyList())
				.category(categoryEntity)
				.healthierChoice(healthierChoice)
				.healthierChoiceId(HEALTHIER_CHOICE_ID)
				.storeId(STORE_ID)
				.name(NAME)
				.sku(SKU)
				.barcode(BARCODE)
				.briefInformation(BRIEF_DESCRIPTION)
				.description(DESCRIPTION)
				.ingredients(INGREDIENTS)
				.status(STATUS)
				.unitPrice(UNIT_PRICE)
				.build();
	}
}
