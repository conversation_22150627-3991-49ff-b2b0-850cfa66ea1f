/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.relational.category.adapter;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.styl.pacific.catalog.service.config.IntegrationTestConfiguration;
import com.styl.pacific.catalog.service.domain.category.dto.CategoryStubDto;
import com.styl.pacific.catalog.service.domain.category.dto.query.TreeCategoryQuery;
import com.styl.pacific.catalog.service.domain.category.entity.Category;
import com.styl.pacific.catalog.service.domain.category.output.repository.CategoryRepository;
import com.styl.pacific.common.test.BaseDataJpaTest;
import com.styl.pacific.domain.valueobject.CategoryId;
import com.styl.pacific.domain.valueobject.TenantId;
import io.micrometer.core.instrument.MeterRegistry;

import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

/**
 * <AUTHOR>
 */

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@ContextConfiguration(classes = {IntegrationTestConfiguration.class})
public class CategoryRepositoryIntegrationTest extends BaseDataJpaTest {

    private static final Long CATEGORY_ID = 100L;
    private static final Long TENANT_ID = 2L;
    private static final String CATEGORY_NAME = "sas";
    private static final String CATEGORY_DESCRIPTION = "Category description";
    private static final String CATEGORY_IMAGE_URL = "category/image";

    private static final Long CATEGORY_EXIST_ID = 1L;
    private static final Long TENANT_EXIST_ID = 2L;
    private static final String CATEGORY_EXIST_NAME = "Fresh";
    private static final String CATEGORY_EXIST_IMAGE_URL = "bucket:image/loremflickr.com/640/480";

    @Autowired
    private CategoryRepository categoryRepository;

    @Autowired
    private TestEntityManager entityManager;

    @MockitoBean
    private MeterRegistry meterRegistry;

    @Order(0)
    @Test
    void shouldReturnCategory_whenFindById() {
        // Act
        Optional<Category> categoryFind = categoryRepository.findById(new TenantId(TENANT_ID), new CategoryId(
                CATEGORY_EXIST_ID));
        // Assert
        assertTrue(categoryFind.isPresent());
        Category find = categoryFind.get();
        assertEquals(CATEGORY_EXIST_ID, find.getId()
                .getValue());
        assertEquals(TENANT_EXIST_ID, find.getTenantId()
                .getValue());
        assertEquals(CATEGORY_EXIST_NAME, find.getName());
        assertEquals(CATEGORY_EXIST_IMAGE_URL, find.getIconPath());
        assertNotNull(find.getCreatedAt());
        assertNotNull(find.getUpdatedAt());
    }

    @Order(1)
    @Test
    void shouldStoredCategory_whenSave() {
        // Arrange
        Category category = Category.builder()
                .id(new CategoryId(CATEGORY_ID))
                .tenantId(new TenantId(TENANT_ID))
                .name(CATEGORY_NAME)
                .description(CATEGORY_DESCRIPTION)
                .parentId(new CategoryId(CATEGORY_EXIST_ID))
                .iconPath(CATEGORY_IMAGE_URL)
                .build();
        // Act
        CategoryStubDto result = categoryRepository.save(category);
        // Assert
        assertNotNull(result);
        assertEquals(category.getId(), result.id());
        assertEquals(category.getTenantId(), result.tenantId());
        assertEquals(category.getName(), result.name());
        assertEquals(category.getIconPath(), result.iconPath());
        assertEquals(category.getParentId(), result.parentId());
        assertEquals(category.getDescription(), result.description());
        entityManager.flush();

        Optional<Category> findResult = categoryRepository.findById(new TenantId(TENANT_ID), new CategoryId(
                CATEGORY_ID));
        assertTrue(findResult.isPresent());
        Category find = findResult.get();
        assertEquals(category.getId()
                .getValue(), find.getId()
                .getValue());
        assertEquals(category.getTenantId()
                .getValue(), find.getTenantId()
                .getValue());
        assertEquals(category.getName(), find.getName());
        assertEquals(category.getIconPath(), find.getIconPath());
        assertEquals(category.getDescription(), find.getDescription());
        assertEquals(category.getParentId()
                .getValue(), find.getParentId()
                .getValue());
        assertNotNull(find.getCreatedAt());
        assertNotNull(find.getUpdatedAt());
    }

    @Order(3)
    @Test
    void shouldReturnCategory_whenUpdate() {
        // Arrange
        Long anotherExistId = 2L;
        Optional<Category> categoryOldFind = categoryRepository.findById(new TenantId(TENANT_ID), new CategoryId(
                CATEGORY_EXIST_ID));
        assertTrue(categoryOldFind.isPresent());
        Category categoryOld = categoryOldFind.get();
        categoryOld.setParentId(new CategoryId(anotherExistId));
        // Act
        CategoryStubDto result = categoryRepository.update(categoryOld);
        // Assert
        assertNotNull(result);
        assertEquals(categoryOld.getId(), result.id());
        assertEquals(categoryOld.getTenantId(), result.tenantId());
        assertEquals(categoryOld.getName(), result.name());
        assertEquals(categoryOld.getIconPath(), result.iconPath());
        assertEquals(categoryOld.getCreatedAt(), result.createdAt());
        assertNotEquals(categoryOld.getUpdatedAt(), result.updatedAt());
    }

    @Order(4)
    @Test
    void shouldSuccess_whenDelete() {
        // Act
        categoryRepository.deleteById(new TenantId(TENANT_ID), new CategoryId(CATEGORY_EXIST_ID));
        // Assert
        Optional<Category> category = categoryRepository.findById(new TenantId(TENANT_ID), new CategoryId(
                CATEGORY_EXIST_ID));
        assertTrue(category.isEmpty());
    }

    @Order(5)
    @Test
    void returnCategoryWithDeletedSubCategory() {
        // Act
        List<Category> result = categoryRepository.findTreeAll(new TenantId(TENANT_ID), TreeCategoryQuery.builder().build());
        // Assert
        assertFalse(result.isEmpty());
        assertEquals(4, result.size());
        assertEquals(0, result.stream().filter(x -> x.getId().getValue() == 21L).findFirst().map(c -> c.getSubCategories().size()).orElse(null));
    }

}
