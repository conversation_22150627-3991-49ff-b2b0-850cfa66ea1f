/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.clients.tenant;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

import com.styl.pacific.catalog.service.domain.tenant.dto.TenantDto;
import com.styl.pacific.catalog.service.domain.tenant.dto.TenantSettingDto;
import com.styl.pacific.domain.dto.CountryResponse;
import com.styl.pacific.domain.dto.CurrencyResponse;
import com.styl.pacific.domain.dto.TimezoneResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantSettingsResponse;
import java.util.Optional;
import java.util.TimeZone;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class TenantRepositoryImplTest {
	private static final Long TENANT_ID = 2L;
	private static final String NAME = "1118 Tenant";
	private static final String BUSINESS_REG_NO = "123456789";
	private static final String BUSINESS_TYPE = "CORPORATE";
	private static final String STATUS = "NEW";
	private static final String EMAIL = "<EMAIL>";
	private static final String PHONE_NUMBER = "+1234567890";
	private static final String CONTACT_REMARKS = null;
	private static final String EMAIL2 = "<EMAIL>";
	private static final String PHONE_NUMBER2 = "+1234567891";
	private static final String CONTACT_REMARKS2 = null;
	private static final String EMAIL3 = "<EMAIL>";
	private static final String PHONE_NUMBER3 = "+1234567892";
	private static final String CONTACT_REMARKS3 = null;
	private static final String WEBSITE = "https://www.example.com";
	private static final String REALM_ID = "98e1503d-f9f9-4b99-92a9-9c3a7ede7a51";
	private static final String ADDRESS_LINE1 = "123 Main St";
	private static final String ADDRESS_LINE2 = "Suite 100";
	private static final String CITY = "Example City";
	private static final String COUNTRY = "VN";
	private static final String POSTAL_CODE = "12345";
	private static final Long CREATED_AT = 1723450856993L;
	private static final Long UPDATED_AT = 1723450856993L;
	private static final Long ACTIVATED_AT = null;
	private static final String DEFAULT_DOMAIN = "test.com";
	private static final String ZONE_ID = "Asia/Ho_Chi_Minh";
	private static final String CURRENCY_CODE = "VND";
	private static final String DATE_FORMAT = "yyyy-MM-dd";
	private static final String TIME_FORMAT = "HH:mm";

	@Mock
	private TenantClient tenantClient;

	@InjectMocks
	private TenantRepositoryImpl tenantRepository;

	@Test
	void shouldReturnTenantResponse_whenGetById() {
		// Arrange
		TenantResponse responseMock = TenantResponse.builder()
				.tenantId(TENANT_ID)
				.name(NAME)
				.businessRegNo(BUSINESS_REG_NO)
				.businessType(BUSINESS_TYPE)
				.status(STATUS)
				.email(EMAIL)
				.phoneNumber(PHONE_NUMBER)
				.addressLine1(ADDRESS_LINE1)
				.contactRemarks(CONTACT_REMARKS)
				.email2(EMAIL2)
				.phoneNumber2(PHONE_NUMBER2)
				.addressLine2(ADDRESS_LINE2)
				.contactRemarks2(CONTACT_REMARKS2)
				.email3(EMAIL3)
				.phoneNumber3(PHONE_NUMBER3)
				.contactRemarks3(CONTACT_REMARKS3)
				.website(WEBSITE)
				.realmId(REALM_ID)
				.city(CITY)
				.country(CountryResponse.of(COUNTRY))
				.postalCode(POSTAL_CODE)
				.createdAt(CREATED_AT)
				.updatedAt(UPDATED_AT)
				.activatedAt(ACTIVATED_AT)
				.build();
		when(tenantClient.getTenant(anyLong())).thenReturn(ResponseEntity.ok(responseMock));
		// Act
		Optional<TenantDto> response = tenantRepository.findById(TENANT_ID);
		// Assert
		assertTrue(response.isPresent());
		TenantDto tenantDto = response.get();
		assertEquals(TENANT_ID, tenantDto.getTenantId());
		assertEquals(NAME, tenantDto.getName());
		assertEquals(BUSINESS_REG_NO, tenantDto.getBusinessRegNo());
		assertEquals(BUSINESS_TYPE, tenantDto.getBusinessType());
		assertEquals(STATUS, tenantDto.getStatus());
		assertEquals(EMAIL, tenantDto.getEmail());
		assertEquals(PHONE_NUMBER, tenantDto.getPhoneNumber());
		assertEquals(ADDRESS_LINE1, tenantDto.getAddressLine1());
		assertEquals(CONTACT_REMARKS, tenantDto.getContactRemarks());
		assertEquals(EMAIL2, tenantDto.getEmail2());
		assertEquals(PHONE_NUMBER2, tenantDto.getPhoneNumber2());
		assertEquals(ADDRESS_LINE2, tenantDto.getAddressLine2());
		assertEquals(CONTACT_REMARKS2, tenantDto.getContactRemarks2());
		assertEquals(EMAIL3, tenantDto.getEmail3());
		assertEquals(PHONE_NUMBER3, tenantDto.getPhoneNumber3());
		assertEquals(CONTACT_REMARKS3, tenantDto.getContactRemarks3());
		assertEquals(WEBSITE, tenantDto.getWebsite());
		assertEquals(REALM_ID, tenantDto.getRealmId());
		assertEquals(CITY, tenantDto.getCity());
		assertEquals(COUNTRY, tenantDto.getCountry()
				.getCountryCode());
		assertEquals(POSTAL_CODE, tenantDto.getPostalCode());
		assertEquals(CREATED_AT, tenantDto.getCreatedAt());
		assertEquals(UPDATED_AT, tenantDto.getUpdatedAt());
		assertEquals(ACTIVATED_AT, tenantDto.getActivatedAt());
	}

	@Test
	void shouldReturnTenantSettingResponse_whenGetById() {
		// Arrange
		TenantSettingsResponse responseMock = TenantSettingsResponse.builder()
				.currency(CurrencyResponse.builder()
						.currencyCode(CURRENCY_CODE)
						.build())
				.timeZone(new TimezoneResponse(TimeZone.getTimeZone(ZONE_ID)))
				.dateFormat(DATE_FORMAT)
				.timeFormat(TIME_FORMAT)
				.build();
		when(tenantClient.getTenantSettings(anyLong())).thenReturn(ResponseEntity.ok(responseMock));
		// Act
		Optional<TenantSettingDto> tenantSettingDtoOptional = tenantRepository.findSettingById(TENANT_ID);
		// Assert
		assertTrue(tenantSettingDtoOptional.isPresent());
		TenantSettingDto tenantSettingDto = tenantSettingDtoOptional.get();
		assertEquals(TIME_FORMAT, tenantSettingDto.getTimeFormat());
		assertEquals(DATE_FORMAT, tenantSettingDto.getDateFormat());
		assertEquals(CURRENCY_CODE, tenantSettingDto.getCurrency()
				.getCurrencyCode());
		assertEquals(ZONE_ID, tenantSettingDto.getTimeZone()
				.getZoneId());
	}

}
