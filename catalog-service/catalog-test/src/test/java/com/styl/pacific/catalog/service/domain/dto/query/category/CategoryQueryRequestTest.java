/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.domain.dto.query.category;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.styl.pacific.catalog.service.domain.category.dto.query.CategoryFilter;
import com.styl.pacific.catalog.service.domain.category.dto.query.CategoryQuery;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;

/**
 * <AUTHOR>
 */
@TestInstance(Lifecycle.PER_CLASS)
class CategoryQueryRequestTest {

	private Validator validator;

	private static final String QUERY_NAME = "category";
	private static final long QUERY_PARENT_ID = 1L;
	private static final long QUERY_TENANT_ID = 1L;

	@BeforeAll
	void setUp() {
		try (ValidatorFactory factory = Validation.buildDefaultValidatorFactory()) {
			validator = factory.getValidator();
		}
	}

	@Test
	void shouldTrue_whenGetProperties() {
		// Arrange
		CategoryQuery query = CategoryQuery.builder()
				.filter(CategoryFilter.builder()
						.name(QUERY_NAME)
						.parentId(QUERY_PARENT_ID)
						.build())

				.build();
		// Assert
		assertEquals(QUERY_NAME, query.filter()
				.name());
		assertEquals(QUERY_PARENT_ID, query.filter()
				.parentId());
	}
}
