/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.shared.http.product.request.option;

import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigInteger;
import lombok.Builder;

/**
 * <AUTHOR>
 */
@Builder
public record UpdateProductOptionItemRequest(
		@Digits(integer = 21, fraction = 0, message = "id must be a valid number") String id,
		@Size(max = 80, message = "name must not be exceed 80 characters") @NotBlank(message = "name must not be null or empty") String name,
		@NotNull(message = "additionPrice must not be null") @Min(value = 0, message = "additionPrice must be greater than or equal to 0") BigInteger additionPrice,
		boolean active) {

}
