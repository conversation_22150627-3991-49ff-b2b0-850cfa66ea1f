/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.shared.http.nutrition;

import com.styl.pacific.catalog.service.shared.http.nutrition.request.CreateNutritionRequest;
import com.styl.pacific.catalog.service.shared.http.nutrition.request.NutritionQueryRequest;
import com.styl.pacific.catalog.service.shared.http.nutrition.request.PaginationNutritionQueryRequest;
import com.styl.pacific.catalog.service.shared.http.nutrition.request.UpdateNutritionRequest;
import com.styl.pacific.catalog.service.shared.http.nutrition.response.ListNutritionResponse;
import com.styl.pacific.catalog.service.shared.http.nutrition.response.NutritionResponse;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.permissions.PacificApiAuthorized;
import com.styl.pacific.domain.permissions.PacificApiPermissionKey;
import com.styl.pacific.domain.permissions.PlatformApiSecurityLevel;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Digits;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * <AUTHOR>
 */
public interface NutritionApi {

	@GetMapping("/api/catalog/nutrition")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	ListNutritionResponse findAll(@Valid @SpringQueryMap @ModelAttribute NutritionQueryRequest query);

	@GetMapping("/api/catalog/nutrition/page")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	Paging<NutritionResponse> findAllPaging(
			@Valid @SpringQueryMap @ModelAttribute PaginationNutritionQueryRequest query);

	@GetMapping("/api/catalog/nutrition/{id}")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	NutritionResponse findById(
			@PathVariable @Digits(integer = 21, fraction = 0, message = "id must be a valid number") String id);

	@PostMapping("/api/catalog/nutrition")
	@ResponseStatus(HttpStatus.CREATED)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.PRODUCT_MGMT_NUTRITION)
	NutritionResponse create(@Valid @RequestBody CreateNutritionRequest request);

	@PutMapping("/api/catalog/nutrition/{id}")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.PRODUCT_MGMT_NUTRITION)
	NutritionResponse update(
			@PathVariable @Digits(integer = 21, fraction = 0, message = "id must be a valid number") String id,
			@Valid @RequestBody UpdateNutritionRequest request);

	@DeleteMapping("/api/catalog/nutrition/{id}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.PRODUCT_MGMT_NUTRITION)
	void deleteById(@PathVariable @Digits(integer = 21, fraction = 0, message = "id must be a valid number") String id);
}
