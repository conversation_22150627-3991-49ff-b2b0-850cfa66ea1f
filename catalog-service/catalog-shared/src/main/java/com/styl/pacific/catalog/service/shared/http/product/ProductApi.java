/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.shared.http.product;

import com.styl.pacific.catalog.service.shared.http.allergen.response.AllergenResponse;
import com.styl.pacific.catalog.service.shared.http.product.request.CreateProductRequest;
import com.styl.pacific.catalog.service.shared.http.product.request.PaginationProductQueryRequest;
import com.styl.pacific.catalog.service.shared.http.product.request.UpdateProductRequest;
import com.styl.pacific.catalog.service.shared.http.product.request.allergen.UpdateProductAllergenRequest;
import com.styl.pacific.catalog.service.shared.http.product.request.nutrition.UpdateProductNutritionRequest;
import com.styl.pacific.catalog.service.shared.http.product.response.ProductResponse;
import com.styl.pacific.catalog.service.shared.http.product.response.nutrition.ListProductNutritionResponse;
import com.styl.pacific.domain.dto.Content;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.permissions.PacificApiAuthorized;
import com.styl.pacific.domain.permissions.PacificApiPermissionKey;
import com.styl.pacific.domain.permissions.PlatformApiSecurityLevel;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Digits;
import java.util.List;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * <AUTHOR>
 */
public interface ProductApi {
	@GetMapping("/api/catalog/products")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	Paging<ProductResponse> findAllPaging(@Valid @SpringQueryMap @ModelAttribute PaginationProductQueryRequest query);

	@GetMapping("/api/catalog/products/{id}")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	ProductResponse findById(
			@PathVariable @Digits(integer = 21, fraction = 0, message = "id must be a valid number") String id);

	@PostMapping("/api/catalog/products")
	@ResponseStatus(HttpStatus.CREATED)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.PRODUCT_MGMT_ADD)
	ProductResponse create(@Valid @RequestBody CreateProductRequest request);

	@PutMapping("/api/catalog/products/{id}")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.PRODUCT_MGMT_UPDATE)
	ProductResponse update(
			@PathVariable @Digits(integer = 21, fraction = 0, message = "id must be a valid number") String id,
			@RequestBody UpdateProductRequest request);

	@PatchMapping("/api/catalog/products/{id}/activate")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.PRODUCT_MGMT_UPDATE)
	void activate(@PathVariable @Digits(integer = 21, fraction = 0, message = "id must be a valid number") String id);

	@PatchMapping("/api/catalog/products/{id}/deactivate")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.PRODUCT_MGMT_UPDATE)
	void deactivate(@PathVariable @Digits(integer = 21, fraction = 0, message = "id must be a valid number") String id);

	@PatchMapping("/api/catalog/products/{id}/archive")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.PRODUCT_MGMT_UPDATE)
	void archive(@PathVariable @Digits(integer = 21, fraction = 0, message = "id must be a valid number") String id);

	@DeleteMapping("/api/catalog/products/{id}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.PRODUCT_MGMT_DELETE)
	void delete(@PathVariable @Digits(integer = 21, fraction = 0, message = "id must be a valid number") String id);

	@GetMapping("/api/catalog/products/{id}/nutrition")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	ListProductNutritionResponse findAllRelatedNutrition(
			@PathVariable @Digits(integer = 21, fraction = 0, message = "id must be a valid number") String id);

	@PostMapping("/api/catalog/products/{id}/nutrition")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	ListProductNutritionResponse updateRelatedNutrition(
			@PathVariable @Digits(integer = 21, fraction = 0, message = "id must be a valid number") String id,
			@Valid @RequestBody List<UpdateProductNutritionRequest> request);

	@GetMapping("/api/catalog/products/{id}/allergens")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	Content<AllergenResponse> findAllRelatedAllergens(
			@PathVariable @Digits(integer = 21, fraction = 0, message = "id must be a valid number") String id);

	@PostMapping("/api/catalog/products/{id}/allergens")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	Content<AllergenResponse> updateRelatedAllergens(
			@PathVariable @Digits(integer = 21, fraction = 0, message = "id must be a valid number") String id,
			@Valid @RequestBody List<UpdateProductAllergenRequest> allergens);

	@GetMapping("/api/catalog/products/name")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	List<String> getAllProductName(@RequestParam(required = false) String name);
}
