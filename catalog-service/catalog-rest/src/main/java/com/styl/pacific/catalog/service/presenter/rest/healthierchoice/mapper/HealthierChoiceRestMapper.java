/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.presenter.rest.healthierchoice.mapper;

import com.styl.pacific.aws.s3.mapper.Mapstruct3SMapper;
import com.styl.pacific.catalog.service.domain.common.mapper.CommonDataMapper;
import com.styl.pacific.catalog.service.domain.healthierchoice.dto.command.CreateHealthierChoiceCommand;
import com.styl.pacific.catalog.service.domain.healthierchoice.dto.command.UpdateHealthierChoiceCommand;
import com.styl.pacific.catalog.service.domain.healthierchoice.dto.query.HealthierChoiceFilter;
import com.styl.pacific.catalog.service.domain.healthierchoice.dto.query.HealthierChoiceQuery;
import com.styl.pacific.catalog.service.domain.healthierchoice.dto.query.PaginationHealthierChoiceQuery;
import com.styl.pacific.catalog.service.domain.healthierchoice.entity.HealthierChoice;
import com.styl.pacific.catalog.service.shared.http.healthierchoice.request.CreateHealthierChoiceRequest;
import com.styl.pacific.catalog.service.shared.http.healthierchoice.request.HealthierChoiceFilterRequest;
import com.styl.pacific.catalog.service.shared.http.healthierchoice.request.HealthierChoicesQueryRequest;
import com.styl.pacific.catalog.service.shared.http.healthierchoice.request.PaginationHealthierChoiceQueryRequest;
import com.styl.pacific.catalog.service.shared.http.healthierchoice.request.UpdateHealthierChoiceRequest;
import com.styl.pacific.catalog.service.shared.http.healthierchoice.response.HealthierChoiceResponse;
import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class, Mapstruct3SMapper.class,
		CommonDataMapper.class, MapstructCommonDomainMapper.class })
public interface HealthierChoiceRestMapper {
	HealthierChoiceRestMapper INSTANCE = Mappers.getMapper(HealthierChoiceRestMapper.class);

	@Mapping(target = "id", source = "model.id", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "tenantId", source = "model.tenantId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "symbol", source = "model.symbolPath", qualifiedByName = "pathToFileResponse")
	@Mapping(target = "createdAt", source = "model.createdAt", qualifiedByName = "instantToLong")
	@Mapping(target = "updatedAt", source = "model.updatedAt", qualifiedByName = "instantToLong")
	HealthierChoiceResponse healthierChoiceToResponse(HealthierChoice model);

	@Mapping(target = "name", qualifiedByName = "stringToClearance")
	@Mapping(target = "description", qualifiedByName = "stringToClearance")
	CreateHealthierChoiceCommand createHealthierChoiceRequestToCommand(CreateHealthierChoiceRequest request);

	@Mapping(target = "name", qualifiedByName = "stringToClearance")
	@Mapping(target = "description", qualifiedByName = "stringToClearance")
	UpdateHealthierChoiceCommand updateHealthierChoiceRequestToCommand(UpdateHealthierChoiceRequest request);

	HealthierChoiceFilter toFilter(HealthierChoiceFilterRequest request);

	HealthierChoiceQuery toQuery(HealthierChoicesQueryRequest request);

	PaginationHealthierChoiceQuery toQuery(PaginationHealthierChoiceQueryRequest request);
}
