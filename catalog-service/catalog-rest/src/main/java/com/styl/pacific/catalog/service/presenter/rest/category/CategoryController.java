/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.presenter.rest.category;

import com.styl.pacific.application.rest.context.RequestContext;
import com.styl.pacific.catalog.service.domain.category.CategoryService;
import com.styl.pacific.catalog.service.domain.category.dto.CategoryStubDto;
import com.styl.pacific.catalog.service.domain.category.dto.command.CreateCategoryCommand;
import com.styl.pacific.catalog.service.domain.category.dto.command.UpdateCategoryCommand;
import com.styl.pacific.catalog.service.domain.category.dto.query.CategoryQuery;
import com.styl.pacific.catalog.service.domain.category.dto.query.PaginationCategoryQuery;
import com.styl.pacific.catalog.service.domain.category.dto.query.TreeCategoryQuery;
import com.styl.pacific.catalog.service.domain.category.entity.Category;
import com.styl.pacific.catalog.service.presenter.rest.category.mapper.CategoryRestMapper;
import com.styl.pacific.catalog.service.shared.http.category.CategoryApi;
import com.styl.pacific.catalog.service.shared.http.category.request.CategoryQueryRequest;
import com.styl.pacific.catalog.service.shared.http.category.request.CreateCategoryRequest;
import com.styl.pacific.catalog.service.shared.http.category.request.PaginationCategoryQueryRequest;
import com.styl.pacific.catalog.service.shared.http.category.request.TreeCategoryQueryRequest;
import com.styl.pacific.catalog.service.shared.http.category.request.UpdateCategoryRequest;
import com.styl.pacific.catalog.service.shared.http.category.response.CategoryResponse;
import com.styl.pacific.catalog.service.shared.http.category.response.CategoryStubResponse;
import com.styl.pacific.domain.dto.Content;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.CategoryId;
import com.styl.pacific.domain.valueobject.TenantId;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */

@RestController
@Validated
@RequiredArgsConstructor
public class CategoryController implements CategoryApi {
	private final CategoryService categoryService;
	private final RequestContext requestContext;

	@Override
	public Content<CategoryStubResponse> findAll(CategoryQueryRequest query) {
		CategoryQuery queryFind = CategoryRestMapper.INSTANCE.toQuery(query);
		var categories = categoryService.findAll(Optional.ofNullable(requestContext.getTenantId())
				.map(TenantId::new)
				.orElse(null), queryFind);
		return categories.map(CategoryRestMapper.INSTANCE::toResponse);
	}

	@Override
	public Paging<CategoryStubResponse> findAllPaging(PaginationCategoryQueryRequest queryRequest) {
		PaginationCategoryQuery query = CategoryRestMapper.INSTANCE.toQuery(queryRequest);
		var paging = categoryService.findAllPaging(Optional.ofNullable(requestContext.getTenantId())
				.map(TenantId::new)
				.orElse(null), query);
		return Paging.<CategoryStubResponse>builder()
				.content(paging.getContent()
						.stream()
						.map(CategoryRestMapper.INSTANCE::toResponse)
						.toList())
				.page(paging.getPage())
				.sort(paging.getSort())
				.totalElements(paging.getTotalElements())
				.totalPages(paging.getTotalPages())
				.build();
	}

	@Override
	public Content<CategoryResponse> findAllTree(TreeCategoryQueryRequest query) {
		TreeCategoryQuery queryFind = CategoryRestMapper.INSTANCE.toQuery(query);
		Content<Category> categories = categoryService.findAllTree(Optional.ofNullable(requestContext.getTenantId())
				.map(TenantId::new)
				.orElse(null), queryFind);
		return categories.map(CategoryRestMapper.INSTANCE::toResponse);
	}

	@Override
	public CategoryResponse findById(String id) {
		Category category = categoryService.findById(Optional.ofNullable(requestContext.getTenantId())
				.map(TenantId::new)
				.orElse(null), Optional.ofNullable(id)
						.filter(NumberUtils::isCreatable)
						.map(NumberUtils::createLong)
						.map(CategoryId::new)
						.orElse(null));
		return CategoryRestMapper.INSTANCE.toResponse(category);
	}

	@Override
	public CategoryStubResponse create(CreateCategoryRequest request) {
		CreateCategoryCommand command = CategoryRestMapper.INSTANCE.toCommand(request);
		CategoryStubDto category = categoryService.create(Optional.ofNullable(requestContext.getTenantId())
				.map(TenantId::new)
				.orElse(null), command);
		return CategoryRestMapper.INSTANCE.toResponse(category);
	}

	@Override
	public CategoryStubResponse update(String id, UpdateCategoryRequest request) {
		UpdateCategoryCommand command = CategoryRestMapper.INSTANCE.toCommand(request);
		CategoryStubDto category = categoryService.update(Optional.ofNullable(requestContext.getTenantId())
				.map(TenantId::new)
				.orElse(null), Optional.ofNullable(id)
						.filter(NumberUtils::isCreatable)
						.map(NumberUtils::createLong)
						.map(CategoryId::new)
						.orElse(null), command);
		return CategoryRestMapper.INSTANCE.toResponse(category);
	}

	@Override
	public void deleteById(String id) {
		categoryService.deleteById(Optional.ofNullable(requestContext.getTenantId())
				.map(TenantId::new)
				.orElse(null), Optional.ofNullable(id)
						.filter(NumberUtils::isCreatable)
						.map(NumberUtils::createLong)
						.map(CategoryId::new)
						.orElse(null));
	}
}
