/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.presenter.rest.product.mapper;

import com.styl.pacific.aws.s3.mapper.Mapstruct3SMapper;
import com.styl.pacific.catalog.service.domain.common.mapper.CommonDataMapper;
import com.styl.pacific.catalog.service.domain.product.dto.command.CreateProductWithDetailsCommand;
import com.styl.pacific.catalog.service.domain.product.dto.command.UpdateProductWithDetailsCommand;
import com.styl.pacific.catalog.service.presenter.rest.allergen.mapper.AllergenRestMapper;
import com.styl.pacific.catalog.service.presenter.rest.category.mapper.CategoryRestMapper;
import com.styl.pacific.catalog.service.presenter.rest.healthierchoice.mapper.HealthierChoiceRestMapper;
import com.styl.pacific.catalog.service.shared.http.product.request.CreateProductWithDetailsRequest;
import com.styl.pacific.catalog.service.shared.http.product.request.UpdateProductWithDetailsRequest;
import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(config = AppMapStructConfiguration.class, uses = { ProductRestMapper.class, HealthierChoiceRestMapper.class,
		CategoryRestMapper.class, AllergenRestMapper.class, ProductOptionRestMapper.class, MapstructCommonMapper.class,
		Mapstruct3SMapper.class, CommonDataMapper.class, MapstructCommonDomainMapper.class })
public interface ProductInternalRestMapper {
	ProductInternalRestMapper INSTANCE = Mappers.getMapper(ProductInternalRestMapper.class);

	CreateProductWithDetailsCommand toCommand(CreateProductWithDetailsRequest request);

	UpdateProductWithDetailsCommand toCommand(UpdateProductWithDetailsRequest request);
}
