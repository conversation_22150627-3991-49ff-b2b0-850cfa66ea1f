/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.domain.nutrition.mapper;

import com.styl.pacific.catalog.service.domain.common.mapper.CommonDataMapper;
import com.styl.pacific.catalog.service.domain.nutrition.dto.NutritionConfigDto;
import com.styl.pacific.catalog.service.domain.nutrition.dto.command.CreateNutritionCommand;
import com.styl.pacific.catalog.service.domain.nutrition.dto.command.UpdateNutritionCommand;
import com.styl.pacific.catalog.service.domain.nutrition.entity.Nutrition;
import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(config = AppMapStructConfiguration.class, uses = { CommonDataMapper.class, MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface NutritionDataMapper {
	NutritionDataMapper INSTANCE = Mappers.getMapper(NutritionDataMapper.class);

	@Mapping(target = "id", ignore = true)
	@Mapping(target = "tenantId", ignore = true)
	@Mapping(target = "createdAt", ignore = true)
	@Mapping(target = "updatedAt", ignore = true)
	Nutrition toModel(CreateNutritionCommand command);

	@Mapping(target = "id", ignore = true)
	@Mapping(target = "tenantId", ignore = true)
	@Mapping(target = "name", source = "command.name", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
	@Mapping(target = "unit", source = "command.unit", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
	@Mapping(target = "createdAt", ignore = true)
	@Mapping(target = "updatedAt", ignore = true)
	void updateNutrition(@MappingTarget Nutrition nutrition, UpdateNutritionCommand command);

	@Mapping(target = "id", ignore = true)
	@Mapping(target = "tenantId", source = "tenantId", qualifiedByName = "longToTenantId")
	@Mapping(target = "createdAt", ignore = true)
	@Mapping(target = "updatedAt", ignore = true)
	Nutrition configToModel(Long tenantId, NutritionConfigDto configDto);
}
