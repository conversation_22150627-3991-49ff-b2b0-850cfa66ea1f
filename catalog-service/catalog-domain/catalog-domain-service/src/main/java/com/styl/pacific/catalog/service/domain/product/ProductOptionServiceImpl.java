/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.domain.product;

import com.styl.pacific.catalog.service.domain.product.dto.command.option.CreateProductOptionCommand;
import com.styl.pacific.catalog.service.domain.product.dto.command.option.UpdateProductOptionCommand;
import com.styl.pacific.catalog.service.domain.product.dto.query.option.ProductOptionQuery;
import com.styl.pacific.catalog.service.domain.product.entity.ProductOption;
import com.styl.pacific.catalog.service.domain.product.handler.ProductOptionCommandHandler;
import com.styl.pacific.catalog.service.domain.product.handler.ProductOptionQueryHandler;
import com.styl.pacific.catalog.service.domain.product.port.input.service.ProductOptionService;
import com.styl.pacific.domain.valueobject.ProductId;
import com.styl.pacific.domain.valueobject.ProductOptionId;
import com.styl.pacific.domain.valueobject.TenantId;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 */
@Validated
@Service
@RequiredArgsConstructor
public class ProductOptionServiceImpl implements ProductOptionService {

	private final ProductOptionCommandHandler commandHandler;
	private final ProductOptionQueryHandler queryHandler;

	@Override
	public List<ProductOption> findAll(TenantId tenantId, ProductId id, ProductOptionQuery query) {
		return queryHandler.findAll(tenantId, id, query);
	}

	@Override
	public ProductOption findById(TenantId tenantId, ProductId productId, ProductOptionId id) {
		return queryHandler.findById(tenantId, productId, id);
	}

	@Override
	public ProductOption create(TenantId tenantId, ProductId productId, CreateProductOptionCommand command) {
		return commandHandler.create(tenantId, productId, command);
	}

	@Override
	public ProductOption update(TenantId tenantId, ProductId productId, ProductOptionId id,
			UpdateProductOptionCommand command) {
		return commandHandler.update(tenantId, productId, id, command);
	}

	@Override
	public void deleteById(TenantId tenantId, ProductId productId, ProductOptionId id) {
		commandHandler.deleteById(tenantId, productId, id);
	}

}
