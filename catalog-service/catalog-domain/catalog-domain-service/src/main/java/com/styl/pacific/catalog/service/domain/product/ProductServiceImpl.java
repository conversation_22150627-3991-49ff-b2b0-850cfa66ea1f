/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.domain.product;

import com.styl.pacific.catalog.service.domain.product.dto.ProductDto;
import com.styl.pacific.catalog.service.domain.product.dto.command.CreateProductCommand;
import com.styl.pacific.catalog.service.domain.product.dto.command.UpdateProductCommand;
import com.styl.pacific.catalog.service.domain.product.dto.query.PaginationProductQuery;
import com.styl.pacific.catalog.service.domain.product.handler.ProductCommandHandler;
import com.styl.pacific.catalog.service.domain.product.handler.ProductQueryHandler;
import com.styl.pacific.catalog.service.domain.product.port.input.service.ProductService;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.ProductId;
import com.styl.pacific.domain.valueobject.TenantId;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 */
@Validated
@Service
@RequiredArgsConstructor
public class ProductServiceImpl implements ProductService {
	private final ProductCommandHandler productCommandHandler;
	private final ProductQueryHandler productTrackQueryHandler;

	@Override
	public ProductDto findById(TenantId tenantId, ProductId id) {
		return productTrackQueryHandler.findById(tenantId, id);
	}

	@Override
	public ProductDto create(TenantId tenantId, CreateProductCommand command) {
		return productCommandHandler.create(tenantId, command);
	}

	@Override
	public ProductDto update(TenantId tenantId, ProductId id, UpdateProductCommand command) {
		return productCommandHandler.update(tenantId, id, command);
	}

	@Override
	public void activate(TenantId tenantId, ProductId id) {
		productCommandHandler.activate(tenantId, id);
	}

	@Override
	public void deactivate(TenantId tenantId, ProductId id) {
		productCommandHandler.deactivate(tenantId, id);

	}

	@Override
	public void archive(TenantId tenantId, ProductId id) {
		productCommandHandler.archive(tenantId, id);
	}

	@Override
	public void deleteById(TenantId tenantId, ProductId id) {
		productCommandHandler.deleteById(tenantId, id);
	}

	@Override
	public Paging<ProductDto> findAllPaging(TenantId tenantId, PaginationProductQuery query) {
		return productTrackQueryHandler.findAllPaging(tenantId, query);
	}

	@Override
	public List<String> getAllProductName(TenantId tenantId, String name) {
		return productTrackQueryHandler.getAllProductName(tenantId, name);
	}
}
