/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.domain.product.port.input.service;

import com.styl.pacific.catalog.service.domain.product.dto.ProductDto;
import com.styl.pacific.catalog.service.domain.product.dto.command.CreateProductWithDetailsCommand;
import com.styl.pacific.catalog.service.domain.product.dto.command.UpdateProductWithDetailsCommand;
import com.styl.pacific.domain.valueobject.ProductId;
import com.styl.pacific.domain.valueobject.TenantId;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
public interface ProductWithDetailsFacade {
	ProductDto createWithDetails(@NotNull(message = "tenantId must not be null") TenantId tenantId,
			@Valid @NotNull(message = "command must not be null") CreateProductWithDetailsCommand command);

	ProductDto updateWithDetails(@NotNull(message = "tenantId must not be null") TenantId tenantId,
			@NotNull(message = "id must not be null") ProductId id,
			@Valid @NotNull(message = "command must not be null") UpdateProductWithDetailsCommand command);
}
