/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.relational.product.repository;

import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.styl.pacific.catalog.service.data.access.relational.product.entity.QProductImageEntity;
import jakarta.annotation.PostConstruct;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class ProductImageQuerydslRepositoryImpl implements ProductImageQuerydslRepository {
	@PersistenceContext
	private EntityManager entityManager;
	private JPAQueryFactory queryFactory;

	@PostConstruct
	public void setup() {
		this.queryFactory = new JPAQueryFactory(entityManager);
	}

	@Override
	public void delete(Predicate predicate) {
		queryFactory.delete(QProductImageEntity.productImageEntity)
				.where(predicate)
				.execute();
	}
}
