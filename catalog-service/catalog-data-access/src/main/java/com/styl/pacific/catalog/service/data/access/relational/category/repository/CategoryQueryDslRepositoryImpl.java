/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.relational.category.repository;

import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.PathBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.styl.pacific.catalog.service.data.access.relational.category.entity.CategoryEntity;
import com.styl.pacific.catalog.service.data.access.relational.category.entity.QCategoryEntity;
import jakarta.annotation.PostConstruct;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import java.util.List;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.support.Querydsl;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public class CategoryQueryDslRepositoryImpl implements CategoryQueryDslRepository {
	@PersistenceContext
	private EntityManager entityManager;
	private JPAQueryFactory queryFactory;
	private Querydsl querydsl;

	@PostConstruct
	public void setup() {
		this.queryFactory = new JPAQueryFactory(entityManager);
		this.querydsl = new Querydsl(entityManager, new PathBuilder<CategoryEntity>(QCategoryEntity.categoryEntity
				.getType(), QCategoryEntity.categoryEntity.getMetadata()));
	}

	@Override
	public List<CategoryEntity> findAllTree(Predicate predicate, Sort sort) {
		QCategoryEntity subCategoryEntity = new QCategoryEntity("subCategoryEntity");
		var query = queryFactory.selectFrom(QCategoryEntity.categoryEntity)
				.leftJoin(QCategoryEntity.categoryEntity.subCategories, subCategoryEntity)
				.fetchJoin()
				.where(predicate)
				.where(QCategoryEntity.categoryEntity.deletedAt.isNull().or(subCategoryEntity.isNull()));
		querydsl.applySorting(sort, query);
		return query.fetch();
	}
}
