/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.relational.category.repository;

import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.PathBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.styl.pacific.catalog.service.data.access.relational.category.entity.CategoryEntity;
import com.styl.pacific.catalog.service.data.access.relational.category.entity.QCategoryEntity;
import jakarta.annotation.PostConstruct;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import java.util.List;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.support.Querydsl;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public class CategoryQueryDslRepositoryImpl implements CategoryQueryDslRepository {
	@PersistenceContext
	private EntityManager entityManager;
	private JPAQueryFactory queryFactory;
	private Querydsl querydsl;

	@PostConstruct
	public void setup() {
		this.queryFactory = new JPAQueryFactory(entityManager);
		this.querydsl = new Querydsl(entityManager, new PathBuilder<CategoryEntity>(QCategoryEntity.categoryEntity
				.getType(), QCategoryEntity.categoryEntity.getMetadata()));
	}

	@Override
	public List<CategoryEntity> findAllTree(Predicate predicate, Sort sort) {
		QCategoryEntity subCategoryEntity = new QCategoryEntity("subCategoryEntity");

		// Create subquery to get categories with their subcategories
		var subQuery = queryFactory.select(subCategoryEntity)
				.from(subCategoryEntity)
				.where(subCategoryEntity.parentId.eq(QCategoryEntity.categoryEntity.id)
						.and(subCategoryEntity.deletedAt.isNull()));

		// Main query with subquery condition
		var query = queryFactory.selectFrom(QCategoryEntity.categoryEntity)
				.where(predicate)
				.where(QCategoryEntity.categoryEntity.deletedAt.isNull()
						.or(subQuery.notExists()));

		querydsl.applySorting(sort, query);

		// Fetch the results and manually load subcategories
		List<CategoryEntity> categories = query.fetch();

		// Load subcategories for each category
		for (CategoryEntity category : categories) {
			List<CategoryEntity> subCategories = queryFactory.selectFrom(subCategoryEntity)
					.where(subCategoryEntity.parentId.eq(category.getId())
							.and(subCategoryEntity.deletedAt.isNull()))
					.fetch();
			category.setSubCategories(subCategories);
		}

		return categories;
	}
}
