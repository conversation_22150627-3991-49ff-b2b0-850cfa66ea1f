/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.catalog.service.data.access.relational.allergen.adapter;

import com.querydsl.core.types.Predicate;
import com.styl.pacific.catalog.service.data.access.relational.allergen.entity.AllergenEntity;
import com.styl.pacific.catalog.service.data.access.relational.allergen.mapper.AllergenDataAccessMapper;
import com.styl.pacific.catalog.service.data.access.relational.allergen.repository.AllergenJpaRepository;
import com.styl.pacific.catalog.service.data.access.relational.allergen.specification.AllergenPredicates;
import com.styl.pacific.catalog.service.data.access.relational.category.entity.CategoryEntity;
import com.styl.pacific.catalog.service.domain.allergen.dto.query.AllergenFilter;
import com.styl.pacific.catalog.service.domain.allergen.dto.query.AllergenQuery;
import com.styl.pacific.catalog.service.domain.allergen.dto.query.PaginationAllergenQuery;
import com.styl.pacific.catalog.service.domain.allergen.entity.Allergen;
import com.styl.pacific.catalog.service.domain.allergen.output.repository.AllergenRepository;
import com.styl.pacific.data.access.utils.JpaPageableUtils;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.AllergenId;
import com.styl.pacific.domain.valueobject.TenantId;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class AllergenRepositoryImpl implements AllergenRepository {
	private final AllergenJpaRepository allergenJpaRepository;

	@Override
	public Optional<Allergen> findById(TenantId tenantId, AllergenId id) {
		Predicate specification = AllergenPredicates.withTenantIdAndId(Optional.ofNullable(tenantId)
				.map(TenantId::getValue)
				.orElse(null), id.getValue());
		return allergenJpaRepository.findOne(specification)
				.map(AllergenDataAccessMapper.INSTANCE::allergenEntityToModel);
	}

	@Override
	public List<Allergen> findAll(TenantId tenantId, AllergenQuery query) {
		Sort sort = JpaPageableUtils.createSort(new HashSet<>(query.sortFields()), query.sortDirection(),
				AllergenEntity.SORTABLE_FIELDS, AllergenEntity.FIELD_ID);
		AllergenFilter filter = Optional.ofNullable(query.filter())
				.orElse(AllergenFilter.builder()
						.build());
		Predicate specification = AllergenPredicates.withMultipleCriteria(Optional.ofNullable(tenantId)
				.map(TenantId::getValue)
				.orElse(null), filter.name(), filter.migrationIds(), filter.description());
		return allergenJpaRepository.findBy(specification, q -> q.sortBy(sort)
				.all())
				.stream()
				.map(AllergenDataAccessMapper.INSTANCE::allergenEntityToModel)
				.toList();
	}

	@Override
	public Paging<Allergen> findAllPaging(TenantId tenantId, PaginationAllergenQuery query) {
		Pageable pageable = JpaPageableUtils.createPageable(query.getPage(), query.getSize(), query.getSortFields(),
				query.getSortDirection(), CategoryEntity.SORTABLE_FIELDS, CategoryEntity.FIELD_ID);
		AllergenFilter filter = Optional.ofNullable(query.getFilter())
				.orElse(AllergenFilter.builder()
						.build());
		Predicate specification = AllergenPredicates.withMultipleCriteria(Optional.ofNullable(tenantId)
				.map(TenantId::getValue)
				.orElse(null), filter.name(), filter.migrationIds(), filter.description());
		Page<Allergen> page = allergenJpaRepository.findAll(specification, pageable)
				.map(AllergenDataAccessMapper.INSTANCE::allergenEntityToModel);
		return Paging.<Allergen>builder()
				.content(page.getContent())
				.totalElements(page.getTotalElements())
				.totalPages(page.getTotalPages())
				.page(page.getPageable()
						.getPageNumber())
				.sort(page.getPageable()
						.getSort()
						.toList()
						.stream()
						.map(Sort.Order::toString)
						.toList())
				.build();
	}

	@Override
	public Allergen create(Allergen model) {
		AllergenEntity entity = AllergenDataAccessMapper.INSTANCE.allergenToEntity(model);
		return AllergenDataAccessMapper.INSTANCE.allergenEntityToModel(allergenJpaRepository.save(entity));
	}

	@Override
	public Allergen update(Allergen model) {
		AllergenEntity entity = AllergenDataAccessMapper.INSTANCE.allergenToEntity(model);
		return AllergenDataAccessMapper.INSTANCE.allergenEntityToModel(allergenJpaRepository.saveAndFlush(entity));
	}

	@Override
	public void deleteById(TenantId tenantId, AllergenId id) {
		allergenJpaRepository.findOne(AllergenPredicates.withTenantIdAndId(Optional.ofNullable(tenantId)
				.map(TenantId::getValue)
				.orElse(null), id.getValue()))
				.ifPresent(allergenJpaRepository::delete);
	}

	@Override
	public boolean existById(TenantId tenantId, AllergenId id) {
		return allergenJpaRepository.exists(AllergenPredicates.withTenantIdAndId(Optional.ofNullable(tenantId)
				.map(TenantId::getValue)
				.orElse(null), id.getValue()));
	}
}
