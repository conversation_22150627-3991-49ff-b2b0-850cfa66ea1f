{"openapi": "3.1.0", "info": {"title": "catalog-service", "description": "Build time: 2025-07-11T04:45:23.402Z", "contact": {"name": "<EMAIL>", "email": "<EMAIL>"}, "version": "1.4.0"}, "servers": [{"url": "http://localhost:9203", "description": "Generated server url"}, {"url": "http://pacific-ii-sit.styl.solutions"}], "paths": {"/api/catalog/products/{productId}/options/{id}": {"get": {"tags": ["product-option-controller"], "operationId": "findById", "parameters": [{"name": "productId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ProductOptionResponse"}}}}}}, "put": {"tags": ["product-option-controller"], "operationId": "update", "parameters": [{"name": "productId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProductOptionRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ProductOptionResponse"}}}}}}, "delete": {"tags": ["product-option-controller"], "operationId": "deleteById", "parameters": [{"name": "productId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "204": {"description": "No Content"}}}}, "/api/catalog/products/{id}": {"get": {"tags": ["product-controller"], "operationId": "findById_1", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ProductResponse"}}}}}}, "put": {"tags": ["product-controller"], "operationId": "update_1", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProductRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ProductResponse"}}}}}}, "delete": {"tags": ["product-controller"], "operationId": "delete", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "204": {"description": "No Content"}}}}, "/api/catalog/nutrition/{id}": {"get": {"tags": ["nutrition-controller"], "operationId": "findById_2", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/NutritionResponse"}}}}}}, "put": {"tags": ["nutrition-controller"], "operationId": "update_2", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateNutritionRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/NutritionResponse"}}}}}}, "delete": {"tags": ["nutrition-controller"], "operationId": "deleteById_1", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "204": {"description": "No Content"}}}}, "/api/catalog/internal/products/{id}": {"put": {"tags": ["product-internal-controller"], "operationId": "update_3", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProductWithDetailsRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ProductResponse"}}}}}}}, "/api/catalog/healthier-choices/{id}": {"get": {"tags": ["healthier-choice-controller"], "operationId": "findById_3", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/HealthierChoiceResponse"}}}}}}, "put": {"tags": ["healthier-choice-controller"], "operationId": "update_4", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateHealthierChoiceRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/HealthierChoiceResponse"}}}}}}, "delete": {"tags": ["healthier-choice-controller"], "operationId": "delete_1", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "204": {"description": "No Content"}}}}, "/api/catalog/categories/{id}": {"get": {"tags": ["category-controller"], "operationId": "findById_4", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CategoryResponse"}}}}}}, "put": {"tags": ["category-controller"], "operationId": "update_5", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCategoryRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CategoryStubResponse"}}}}}}, "delete": {"tags": ["category-controller"], "operationId": "deleteById_2", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "204": {"description": "No Content"}}}}, "/api/catalog/allergens/{id}": {"get": {"tags": ["allergen-controller"], "operationId": "findById_5", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AllergenResponse"}}}}}}, "put": {"tags": ["allergen-controller"], "operationId": "update_6", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAllergenRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AllergenResponse"}}}}}}, "delete": {"tags": ["allergen-controller"], "operationId": "deleteById_3", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "204": {"description": "No Content"}}}}, "/api/catalog/products": {"get": {"tags": ["product-controller"], "operationId": "findAllPaging", "parameters": [{"name": "filter.migrationId", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.ids", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "maxItems": 100, "minItems": 0}}, {"name": "filter.name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.categoryIds", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "maxItems": 100, "minItems": 0}}, {"name": "filter.healthierChoiceIds", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "maxItems": 100, "minItems": 0}}, {"name": "filter.storeId", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.productType", "in": "query", "required": false, "schema": {"type": "string", "enum": ["TENANT_PRODUCT", "STORE_PRODUCT"]}}, {"name": "filter.includeTenantProduct", "in": "query", "required": false, "schema": {"type": "boolean"}}, {"name": "filter.barcode", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.sku", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.statuses", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["ACTIVE", "UNAVAILABLE", "ARCHIVED"]}}}, {"name": "filter.fromUnitPrice", "in": "query", "required": false, "schema": {"type": "integer"}}, {"name": "filter.toUnitPrice", "in": "query", "required": false, "schema": {"type": "integer"}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "sortDirection", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortFields", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PagingProductResponse"}}}}}}, "post": {"tags": ["product-controller"], "operationId": "create", "parameters": [{"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProductRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Created", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ProductResponse"}}}}}}}, "/api/catalog/products/{productId}/options": {"get": {"tags": ["product-option-controller"], "operationId": "findAll", "parameters": [{"name": "productId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "filter.title", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortDirection", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortFields", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ListProductOptionResponse"}}}}}}, "post": {"tags": ["product-option-controller"], "operationId": "create_1", "parameters": [{"name": "productId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProductOptionRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Created", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ProductOptionResponse"}}}}}}}, "/api/catalog/products/{id}/nutrition": {"get": {"tags": ["product-controller"], "operationId": "findAllRelatedNutrition", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ListProductNutritionResponse"}}}}}}, "post": {"tags": ["product-controller"], "operationId": "updateRelatedNutrition", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UpdateProductNutritionRequest"}}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ListProductNutritionResponse"}}}}}}}, "/api/catalog/products/{id}/allergens": {"get": {"tags": ["product-controller"], "operationId": "findAllRelatedAllergens", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ContentAllergenResponse"}}}}}}, "post": {"tags": ["product-controller"], "operationId": "updateRelatedAllergens", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UpdateProductAllergenRequest"}}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ContentAllergenResponse"}}}}}}}, "/api/catalog/nutrition": {"get": {"tags": ["nutrition-controller"], "operationId": "findAll_1", "parameters": [{"name": "filter.name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.unit", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortDirection", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortFields", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ListNutritionResponse"}}}}}}, "post": {"tags": ["nutrition-controller"], "operationId": "create_2", "parameters": [{"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateNutritionRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Created", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/NutritionResponse"}}}}}}}, "/api/catalog/internal/products": {"post": {"tags": ["product-internal-controller"], "operationId": "create_3", "parameters": [{"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProductWithDetailsRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Created", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ProductResponse"}}}}}}}, "/api/catalog/healthier-choices": {"get": {"tags": ["healthier-choice-controller"], "operationId": "findAll_2", "parameters": [{"name": "filter.name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.description", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortDirection", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortFields", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ListHealthierChoicesResponse"}}}}}}, "post": {"tags": ["healthier-choice-controller"], "operationId": "create_4", "parameters": [{"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateHealthierChoiceRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Created", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/HealthierChoiceResponse"}}}}}}}, "/api/catalog/categories": {"get": {"tags": ["category-controller"], "operationId": "findAll_3", "parameters": [{"name": "filter.migrationId", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.parentId", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortDirection", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortFields", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ContentCategoryStubResponse"}}}}}}, "post": {"tags": ["category-controller"], "operationId": "create_5", "parameters": [{"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCategoryRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Created", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/CategoryStubResponse"}}}}}}}, "/api/catalog/allergens": {"get": {"tags": ["allergen-controller"], "operationId": "findAll_4", "parameters": [{"name": "filter.name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.migrationIds", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}, {"name": "filter.description", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortDirection", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortFields", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ContentAllergenResponse"}}}}}}, "post": {"tags": ["allergen-controller"], "operationId": "create_6", "parameters": [{"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAllergenRequest"}}}, "required": true}, "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Created", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AllergenResponse"}}}}}}}, "/api/catalog/products/{id}/deactivate": {"patch": {"tags": ["product-controller"], "operationId": "deactivate", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "204": {"description": "No Content"}}}}, "/api/catalog/products/{id}/archive": {"patch": {"tags": ["product-controller"], "operationId": "archive", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "204": {"description": "No Content"}}}}, "/api/catalog/products/{id}/activate": {"patch": {"tags": ["product-controller"], "operationId": "activate", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "204": {"description": "No Content"}}}}, "/api/catalog/products/name": {"get": {"tags": ["product-controller"], "operationId": "getAllProductName", "parameters": [{"name": "name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/api/catalog/nutrition/page": {"get": {"tags": ["nutrition-controller"], "operationId": "findAllPaging_1", "parameters": [{"name": "filter.name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.unit", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "sortDirection", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortFields", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PagingNutritionResponse"}}}}}}}, "/api/catalog/healthier-choices/page": {"get": {"tags": ["healthier-choice-controller"], "operationId": "findAllPaging_2", "parameters": [{"name": "filter.name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.description", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "sortDirection", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortFields", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PagingHealthierChoiceResponse"}}}}}}}, "/api/catalog/categories/tree": {"get": {"tags": ["category-controller"], "operationId": "findAllTree", "parameters": [{"name": "filter.name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortDirection", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortFields", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ContentCategoryResponse"}}}}}}}, "/api/catalog/categories/page": {"get": {"tags": ["category-controller"], "operationId": "findAllPaging_3", "parameters": [{"name": "filter.migrationId", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.parentId", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "sortDirection", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortFields", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PagingCategoryStubResponse"}}}}}}}, "/api/catalog/allergens/page": {"get": {"tags": ["allergen-controller"], "operationId": "findAllPaging_4", "parameters": [{"name": "filter.name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.migrationIds", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}, {"name": "filter.description", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "sortDirection", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortFields", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "uniqueItems": true}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/PagingAllergenResponse"}}}}}}}}, "components": {"schemas": {"ErrorResponse": {"type": "object", "properties": {"error": {"type": "string"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "string"}}}}, "UpdateProductOptionItemRequest": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string", "maxLength": 80, "minLength": 0}, "additionPrice": {"type": "integer", "minimum": 0}, "active": {"type": "boolean"}}, "required": ["additionPrice"]}, "UpdateProductOptionRequest": {"type": "object", "properties": {"title": {"type": "string", "maxLength": 80, "minLength": 0}, "minimum": {"type": "integer", "format": "int32", "minimum": 0}, "maximum": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/UpdateProductOptionItemRequest"}, "maxItems": 2147483647, "minItems": 1}}, "required": ["items", "minimum"]}, "ProductOptionItemResponse": {"type": "object", "properties": {"id": {"type": "string"}, "optionId": {"type": "string"}, "name": {"type": "string"}, "additionPrice": {"type": "integer"}, "active": {"type": "boolean"}, "createdAt": {"type": "integer", "format": "int64"}, "updatedAt": {"type": "integer", "format": "int64"}}}, "ProductOptionResponse": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "productId": {"type": "string"}, "minimum": {"type": "integer", "format": "int32"}, "maximum": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/ProductOptionItemResponse"}}, "createdAt": {"type": "integer", "format": "int64"}, "updatedAt": {"type": "integer", "format": "int64"}}}, "UpdateProductImageRequest": {"type": "object", "properties": {"id": {"type": "string"}, "imagePath": {"type": "string"}}, "required": ["imagePath"]}, "UpdateProductRequest": {"type": "object", "properties": {"categoryId": {"type": "string"}, "healthierChoiceId": {"type": "string"}, "name": {"type": "string", "maxLength": 100, "minLength": 0}, "briefInformation": {"type": "string", "maxLength": 255, "minLength": 0}, "description": {"type": "string", "maxLength": 255, "minLength": 0}, "ingredients": {"type": "string", "maxLength": 500, "minLength": 0}, "barcode": {"type": "string"}, "sku": {"type": "string", "maxLength": 32, "minLength": 8}, "unitPrice": {"type": "integer", "minimum": 0}, "listingPrice": {"type": "integer", "minimum": 0}, "preparationTime": {"type": "integer", "format": "int32", "minimum": 0}, "images": {"type": "array", "items": {"$ref": "#/components/schemas/UpdateProductImageRequest"}, "maxItems": 10, "minItems": 0}, "keywords": {"type": "array", "items": {"type": "string", "maxLength": 20, "minLength": 0}, "maxItems": 50, "minItems": 0}}, "required": ["categoryId", "preparationTime", "unitPrice"]}, "AllergenResponse": {"type": "object", "properties": {"id": {"type": "string"}, "tenantId": {"type": "string"}, "name": {"type": "string"}, "migrationId": {"type": "string"}, "description": {"type": "string"}, "createdAt": {"type": "integer", "format": "int64"}, "updatedAt": {"type": "integer", "format": "int64"}}}, "CategoryStubResponse": {"type": "object", "properties": {"id": {"type": "string"}, "tenantId": {"type": "string"}, "migrationId": {"type": "string"}, "name": {"type": "string"}, "icon": {"$ref": "#/components/schemas/FileResponse"}, "description": {"type": "string"}, "parentId": {"type": "string"}, "createdAt": {"type": "integer", "format": "int64"}, "updatedAt": {"type": "integer", "format": "int64"}}}, "CurrencyResponse": {"type": "object", "properties": {"displayName": {"type": "string"}, "numericCode": {"type": "integer", "format": "int32"}, "currencyCode": {"type": "string"}, "symbol": {"type": "string"}, "fractionDigits": {"type": "integer", "format": "int32"}}}, "FileResponse": {"type": "object", "properties": {"path": {"type": "string"}, "url": {"type": "string"}}}, "HealthierChoiceResponse": {"type": "object", "properties": {"id": {"type": "string"}, "tenantId": {"type": "string"}, "name": {"type": "string"}, "symbol": {"$ref": "#/components/schemas/FileResponse"}, "description": {"type": "string"}, "createdAt": {"type": "integer", "format": "int64"}, "updatedAt": {"type": "integer", "format": "int64"}}}, "ProductImageResponse": {"type": "object", "properties": {"id": {"type": "string"}, "position": {"type": "integer", "format": "int32"}, "image": {"$ref": "#/components/schemas/FileResponse"}, "createdAt": {"type": "integer", "format": "int64"}, "updatedAt": {"type": "integer", "format": "int64"}}}, "ProductNutritionResponse": {"type": "object", "properties": {"id": {"type": "string"}, "tenantId": {"type": "string"}, "name": {"type": "string"}, "unit": {"type": "string"}, "value": {"type": "number", "format": "double"}, "createdAt": {"type": "integer", "format": "int64"}, "updatedAt": {"type": "integer", "format": "int64"}}}, "ProductResponse": {"type": "object", "properties": {"id": {"type": "string"}, "tenantId": {"type": "string"}, "migrationId": {"type": "string"}, "category": {"$ref": "#/components/schemas/CategoryStubResponse"}, "productType": {"type": "string", "enum": ["TENANT_PRODUCT", "STORE_PRODUCT"]}, "storeId": {"type": "string"}, "healthierChoice": {"$ref": "#/components/schemas/HealthierChoiceResponse"}, "name": {"type": "string"}, "sku": {"type": "string"}, "briefInformation": {"type": "string"}, "description": {"type": "string"}, "ingredients": {"type": "string"}, "barcode": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "UNAVAILABLE", "ARCHIVED"]}, "unitPrice": {"type": "integer"}, "listingPrice": {"type": "integer"}, "currency": {"$ref": "#/components/schemas/CurrencyResponse"}, "preparationTime": {"type": "integer", "format": "int32"}, "images": {"type": "array", "items": {"$ref": "#/components/schemas/ProductImageResponse"}}, "options": {"type": "array", "items": {"$ref": "#/components/schemas/ProductOptionResponse"}}, "nutrition": {"type": "array", "items": {"$ref": "#/components/schemas/ProductNutritionResponse"}}, "allergens": {"type": "array", "items": {"$ref": "#/components/schemas/AllergenResponse"}}, "keywords": {"type": "array", "items": {"type": "string"}}, "createdAt": {"type": "integer", "format": "int64"}, "updatedAt": {"type": "integer", "format": "int64"}}}, "UpdateNutritionRequest": {"type": "object", "properties": {"name": {"type": "string", "maxLength": 80, "minLength": 0}, "unit": {"type": "string", "minLength": 1}}}, "NutritionResponse": {"type": "object", "properties": {"id": {"type": "string"}, "tenantId": {"type": "string"}, "name": {"type": "string"}, "unit": {"type": "string"}, "createdAt": {"type": "integer", "format": "int64"}, "updatedAt": {"type": "integer", "format": "int64"}}}, "UpdateProductAllergenRequest": {"type": "object", "properties": {"allergenId": {"type": "string"}}, "required": ["allergenId"]}, "UpdateProductNutritionRequest": {"type": "object", "properties": {"nutritionId": {"type": "string"}, "value": {"type": "number", "format": "double"}}, "required": ["nutritionId", "value"]}, "UpdateProductWithDetailsRequest": {"type": "object", "properties": {"categoryId": {"type": "string"}, "healthierChoiceId": {"type": "string"}, "name": {"type": "string", "maxLength": 80, "minLength": 0}, "briefInformation": {"type": "string", "maxLength": 255, "minLength": 0}, "description": {"type": "string", "maxLength": 255, "minLength": 0}, "ingredients": {"type": "string", "maxLength": 500, "minLength": 0}, "barcode": {"type": "string"}, "sku": {"type": "string", "maxLength": 32, "minLength": 8}, "unitPrice": {"type": "integer", "minimum": 0}, "listingPrice": {"type": "integer", "minimum": 0}, "preparationTime": {"type": "integer", "format": "int32", "minimum": 0}, "images": {"type": "array", "items": {"$ref": "#/components/schemas/UpdateProductImageRequest"}, "maxItems": 10, "minItems": 0}, "allergens": {"type": "array", "items": {"$ref": "#/components/schemas/UpdateProductAllergenRequest"}}, "nutrition": {"type": "array", "items": {"$ref": "#/components/schemas/UpdateProductNutritionRequest"}}, "keywords": {"type": "array", "items": {"type": "string", "maxLength": 20, "minLength": 0}, "maxItems": 50, "minItems": 0}}, "required": ["allergens", "categoryId", "nutrition", "preparationTime", "unitPrice"]}, "UpdateHealthierChoiceRequest": {"type": "object", "properties": {"name": {"type": "string", "maxLength": 80, "minLength": 0}, "symbolPath": {"type": "string"}, "description": {"type": "string", "maxLength": 255, "minLength": 0}}}, "UpdateCategoryRequest": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1}, "iconPath": {"type": "string"}, "description": {"type": "string", "maxLength": 255, "minLength": 0}, "parentId": {"type": "string"}}}, "UpdateAllergenRequest": {"type": "object", "properties": {"name": {"type": "string", "maxLength": 80, "minLength": 0}, "description": {"type": "string", "maxLength": 250, "minLength": 0}}}, "CreateProductImageRequest": {"type": "object", "properties": {"imagePath": {"type": "string"}}, "required": ["imagePath"]}, "CreateProductRequest": {"type": "object", "properties": {"migrationId": {"type": "string", "maxLength": 100, "minLength": 0}, "categoryId": {"type": "string"}, "storeId": {"type": "string"}, "healthierChoiceId": {"type": "string"}, "name": {"type": "string", "maxLength": 100, "minLength": 0}, "briefInformation": {"type": "string", "maxLength": 255, "minLength": 0}, "description": {"type": "string", "maxLength": 255, "minLength": 0}, "ingredients": {"type": "string", "maxLength": 500, "minLength": 0}, "barcode": {"type": "string"}, "sku": {"type": "string", "maxLength": 32, "minLength": 8}, "unitPrice": {"type": "integer", "minimum": 0}, "listingPrice": {"type": "integer", "minimum": 0}, "preparationTime": {"type": "integer", "format": "int32", "minimum": 0}, "images": {"type": "array", "items": {"$ref": "#/components/schemas/CreateProductImageRequest"}, "maxItems": 10, "minItems": 0}, "keywords": {"type": "array", "items": {"type": "string", "maxLength": 20, "minLength": 0}, "maxItems": 50, "minItems": 0}, "productType": {"type": "string", "enum": ["TENANT_PRODUCT", "STORE_PRODUCT"]}}, "required": ["categoryId", "preparationTime", "productType", "unitPrice"]}, "CreateProductOptionItemRequest": {"type": "object", "properties": {"name": {"type": "string", "maxLength": 80, "minLength": 0}, "additionPrice": {"type": "integer", "minimum": 0}}, "required": ["additionPrice"]}, "CreateProductOptionRequest": {"type": "object", "properties": {"title": {"type": "string", "maxLength": 80, "minLength": 0}, "minimum": {"type": "integer", "format": "int32", "minimum": 0}, "maximum": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/CreateProductOptionItemRequest"}, "maxItems": 2147483647, "minItems": 1}}, "required": ["items", "minimum"]}, "ListProductNutritionResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/ProductNutritionResponse"}}}}, "ContentAllergenResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/AllergenResponse"}}}}, "CreateNutritionRequest": {"type": "object", "properties": {"name": {"type": "string", "maxLength": 80, "minLength": 0}, "unit": {"type": "string", "minLength": 1}}}, "CreateProductWithDetailsRequest": {"type": "object", "properties": {"migrationId": {"type": "string", "maxLength": 100, "minLength": 0}, "categoryId": {"type": "string"}, "storeId": {"type": "string"}, "healthierChoiceId": {"type": "string"}, "name": {"type": "string", "maxLength": 80, "minLength": 0}, "briefInformation": {"type": "string", "maxLength": 255, "minLength": 0}, "description": {"type": "string", "maxLength": 255, "minLength": 0}, "ingredients": {"type": "string", "maxLength": 500, "minLength": 0}, "barcode": {"type": "string"}, "sku": {"type": "string", "maxLength": 32, "minLength": 8}, "unitPrice": {"type": "integer", "minimum": 0}, "listingPrice": {"type": "integer", "minimum": 0}, "preparationTime": {"type": "integer", "format": "int32", "minimum": 0}, "images": {"type": "array", "items": {"$ref": "#/components/schemas/CreateProductImageRequest"}, "maxItems": 10, "minItems": 0}, "allergens": {"type": "array", "items": {"$ref": "#/components/schemas/UpdateProductAllergenRequest"}}, "nutrition": {"type": "array", "items": {"$ref": "#/components/schemas/UpdateProductNutritionRequest"}}, "keywords": {"type": "array", "items": {"type": "string", "maxLength": 20, "minLength": 0}, "maxItems": 50, "minItems": 0}}, "required": ["allergens", "categoryId", "nutrition", "preparationTime", "unitPrice"]}, "CreateHealthierChoiceRequest": {"type": "object", "properties": {"name": {"type": "string", "maxLength": 80, "minLength": 0}, "symbolPath": {"type": "string"}, "description": {"type": "string", "maxLength": 255, "minLength": 0}}}, "CreateCategoryRequest": {"type": "object", "properties": {"migrationId": {"type": "string", "maxLength": 100, "minLength": 0}, "name": {"type": "string", "minLength": 1}, "description": {"type": "string", "maxLength": 255, "minLength": 0}, "iconPath": {"type": "string"}, "parentId": {"type": "string"}}}, "CreateAllergenRequest": {"type": "object", "properties": {"name": {"type": "string", "maxLength": 80, "minLength": 0}, "migrationId": {"type": "string", "maxLength": 100, "minLength": 0}, "description": {"type": "string", "maxLength": 250, "minLength": 0}}}, "PagingProductResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/ProductResponse"}}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"type": "string"}}}}, "ListProductOptionResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/ProductOptionResponse"}}}}, "ListNutritionResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/NutritionResponse"}}}}, "PagingNutritionResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/NutritionResponse"}}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"type": "string"}}}}, "ListHealthierChoicesResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/HealthierChoiceResponse"}}}}, "PagingHealthierChoiceResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/HealthierChoiceResponse"}}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"type": "string"}}}}, "ContentCategoryStubResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryStubResponse"}}}}, "CategoryResponse": {"type": "object", "properties": {"id": {"type": "string"}, "tenantId": {"type": "string"}, "migrationId": {"type": "string"}, "name": {"type": "string"}, "icon": {"$ref": "#/components/schemas/FileResponse"}, "parentId": {"type": "string"}, "subCategories": {"type": "array", "items": {"$ref": "#/components/schemas/SubCategoryResponse"}}, "description": {"type": "string"}, "createdAt": {"type": "integer", "format": "int64"}, "updatedAt": {"type": "integer", "format": "int64"}}}, "SubCategoryResponse": {"type": "object", "properties": {"id": {"type": "string"}, "tenantId": {"type": "string"}, "migrationId": {"type": "string"}, "name": {"type": "string"}, "icon": {"$ref": "#/components/schemas/FileResponse"}, "parentId": {"type": "string"}, "description": {"type": "string"}, "createdAt": {"type": "integer", "format": "int64"}, "updatedAt": {"type": "integer", "format": "int64"}}}, "ContentCategoryResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryResponse"}}}}, "PagingCategoryStubResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryStubResponse"}}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"type": "string"}}}}, "PagingAllergenResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/AllergenResponse"}}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"type": "string"}}}}}}}