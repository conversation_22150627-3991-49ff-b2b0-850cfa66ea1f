{"openapi": "3.1.0", "info": {"title": "wallet-service", "description": "Build time: 2025-05-06T03:33:47.676Z", "contact": {"name": "<EMAIL>", "email": "<EMAIL>"}, "version": "1.2.2"}, "servers": [{"url": "http://localhost:9210", "description": "Generated server url"}, {"url": "http://pacific-ii-sit.styl.solutions"}], "paths": {"/api/wallet/wallets/{id}/spending-limit": {"put": {"tags": ["wallet-controller"], "operationId": "updateSpendingLimit", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSpendingLimitRequest"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/WalletResponse"}}}}}}}, "/api/wallet/fund-sources/{id}": {"get": {"tags": ["fund-source-controller"], "operationId": "getFundSources", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/FundSourceResponse"}}}}}}, "put": {"tags": ["fund-source-controller"], "operationId": "updateFundSource", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateFundSourceRequest"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/FundSourceResponse"}}}}}}}, "/api/wallet/fund-sources-scheduler/{id}": {"put": {"tags": ["fund-source-controller"], "operationId": "updateFundSourceTopupScheduler", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateFundSourceSchedulerRequest"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/FundSourceTopupSchedulerResponse"}}}}}}}, "/api/wallet/wallets": {"get": {"tags": ["wallet-controller"], "operationId": "findWallets", "parameters": [{"name": "filter.ids", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "filter.tenantId", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "filter.name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.customerId", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "filter.sourceFundId", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "filter.statuses", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["ACTIVE", "INACTIVE"]}}}, {"name": "filter.types", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["DEPOSIT", "FUNDED"]}}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "sortDirection", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortFields", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ListWalletResponse"}}}}}}, "post": {"tags": ["wallet-controller"], "operationId": "createWallet", "parameters": [{"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateWalletRequest"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/WalletResponse"}}}}}}}, "/api/wallet/wallets/{walletId}/spending-limit-settings": {"get": {"tags": ["wallet-spending-limit-setting-controller"], "operationId": "getWalletSpendingLimitSetting", "parameters": [{"name": "walletId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "type", "in": "query", "required": true, "schema": {"type": "string", "enum": ["DAY", "WEEK", "MONTH"]}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/WalletSpendingLimitSettingResponse"}}}}}}, "post": {"tags": ["wallet-spending-limit-setting-controller"], "operationId": "saveWalletSpendingLimitSettings", "parameters": [{"name": "walletId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveWalletSpendingLimitSettingRequest"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ListWalletSpendingLimitSettingsResponse"}}}}}}}, "/api/wallet/wallets/{id}/unlock": {"post": {"tags": ["wallet-controller"], "operationId": "unlock", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/WalletResponse"}}}}}}}, "/api/wallet/wallets/{id}/pay": {"post": {"tags": ["wallet-controller"], "operationId": "pay", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreditRequest"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/WalletTransactionResponse"}}}}}}}, "/api/wallet/wallets/{id}/lock": {"post": {"tags": ["wallet-controller"], "operationId": "lock", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/WalletResponse"}}}}}}}, "/api/wallet/wallets/{id}/balance/deduct": {"post": {"tags": ["wallet-controller"], "operationId": "deductBalance", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeductBalanceRequest"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/WalletTransactionResponse"}}}}}}}, "/api/wallet/wallets/{id}/balance/add": {"post": {"tags": ["wallet-controller"], "operationId": "addBalance", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddBalanceRequest"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/WalletTransactionResponse"}}}}}}}, "/api/wallet/wallets/pay": {"post": {"tags": ["wallet-controller"], "operationId": "pay_1", "parameters": [{"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreditMultiWalletRequest"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/WalletTransactionResponse"}}}}}}}, "/api/wallet/topup-sessions": {"post": {"tags": ["topup-session-controller"], "operationId": "createTopupSession", "parameters": [{"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTopupSessionRequest"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TopupSessionResponse"}}}}}}}, "/api/wallet/settings": {"get": {"tags": ["wallet-setting-controller"], "operationId": "getWalletSetting", "parameters": [{"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/WalletSettingResponse"}}}}}}, "post": {"tags": ["wallet-setting-controller"], "operationId": "saveWalletSetting", "parameters": [{"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveWalletSettingRequest"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/WalletSettingResponse"}}}}}}}, "/api/wallet/fund-sources": {"get": {"tags": ["fund-source-controller"], "operationId": "findFundSources", "parameters": [{"name": "filter.name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.ids", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "filter.statuses", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["ACTIVE", "INACTIVE"]}}}, {"name": "filter.topupFrequencies", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["NONE", "DAILY", "WEEKLY", "MONTHLY"]}}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "sortDirection", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortFields", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ListFundSourceResponse"}}}}}}, "post": {"tags": ["fund-source-controller"], "operationId": "createFundSource", "parameters": [{"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateFundSourceRequest"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/FundSourceResponse"}}}}}}, "delete": {"tags": ["fund-source-controller"], "operationId": "deleteFundSource", "parameters": [{"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteFundSourceRequest"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK"}}}}, "/api/wallet/fund-sources/{id}/lock": {"post": {"tags": ["fund-source-controller"], "operationId": "lockFundSource", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/FundSourceResponse"}}}}}}}, "/api/wallet/fund-sources/{id}/activate": {"post": {"tags": ["fund-source-controller"], "operationId": "activateFundSource", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/FundSourceResponse"}}}}}}}, "/api/wallet/fund-sources-scheduler": {"get": {"tags": ["fund-source-controller"], "operationId": "findFundSourceTopupSchedulers", "parameters": [{"name": "request", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/FindFundSourcesSchedulerRequest"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FundSourceTopupSchedulerResponse"}}}}}}}, "post": {"tags": ["fund-source-controller"], "operationId": "createFundSourceTopupScheduler", "parameters": [{"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateFundSourceSchedulerRequest"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/FundSourceTopupSchedulerResponse"}}}}}}, "delete": {"tags": ["fund-source-controller"], "operationId": "deleteFundSourceScheduler", "parameters": [{"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteFundSourceSchedulerRequest"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK"}}}}, "/api/wallet/delegate-settings": {"get": {"tags": ["wallet-delegate-setting-controller"], "operationId": "getWalletDelegateSetting", "parameters": [{"name": "subWalletId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/WalletDelegateSettingResponse"}}}}}}, "post": {"tags": ["wallet-delegate-setting-controller"], "operationId": "saveWalletDelegateSetting", "parameters": [{"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveWalletDelegateSettingRequest"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/WalletDelegateSettingResponse"}}}}}}}, "/api/wallet/beneficiaries": {"get": {"tags": ["beneficiary-controller"], "operationId": "findBeneficiaries", "parameters": [{"name": "request", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/FindBeneficiariesRequest"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ListBeneficiaryResponse"}}}}}}, "post": {"tags": ["beneficiary-controller"], "operationId": "createBeneficiaries", "parameters": [{"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBeneficiariesRequest"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BeneficiaryResponse"}}}}}}}, "delete": {"tags": ["beneficiary-controller"], "operationId": "removeBeneficiaries", "parameters": [{"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RemoveBeneficiariesRequest"}}}, "required": true}, "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK"}}}}, "/api/wallet/wallets/{walletId}/spending-limit-settings/query": {"get": {"tags": ["wallet-spending-limit-setting-controller"], "operationId": "findWalletSpendingLimitSettings", "parameters": [{"name": "walletId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ListWalletSpendingLimitSettingsResponse"}}}}}}}, "/api/wallet/wallets/{id}": {"get": {"tags": ["wallet-controller"], "operationId": "getWallet", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/WalletResponse"}}}}}}, "delete": {"tags": ["wallet-controller"], "operationId": "deleteWallet", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK"}}}}, "/api/wallet/wallets/{id}/expiring": {"get": {"tags": ["wallet-controller"], "operationId": "getExpiringAmount", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "customerId", "in": "query", "required": true, "schema": {"type": "string", "minLength": 1}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/WalletEntryExpireResponse"}}}}}}}, "/api/wallet/wallets/query": {"get": {"tags": ["wallet-controller"], "operationId": "queryIndividualWallets", "parameters": [{"name": "customerId", "in": "query", "required": true, "schema": {"type": "string", "minLength": 1}}, {"name": "types", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["DEPOSIT", "FUNDED"]}}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WalletResponse"}}}}}}}}, "/api/wallet/wallets/find": {"get": {"tags": ["wallet-controller"], "operationId": "getWalletByCustomerId", "parameters": [{"name": "customerId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/WalletResponse"}}}}}}}, "/api/wallet/transactions": {"get": {"tags": ["wallet-transaction-controller"], "operationId": "findWalletTransactions", "parameters": [{"name": "filter.ids", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "filter.customerIds", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "filter.paymentTransactionIds", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "filter.fromAmount", "in": "query", "required": false, "schema": {"type": "integer"}}, {"name": "filter.toAmount", "in": "query", "required": false, "schema": {"type": "integer"}}, {"name": "filter.fromTime", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "filter.toTime", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "filter.transactionTypes", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["DEBIT", "CREDIT", "EXPIRED"]}}}, {"name": "filter.transactionCategories", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["DEPOSIT", "WITHDRAW", "PURCHASE", "TRANSFER", "REFUND", "EXPIRED"]}}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "sortDirection", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortFields", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ListWalletTransactionResponse"}}}}}}}, "/api/wallet/transactions/{id}": {"get": {"tags": ["wallet-transaction-controller"], "operationId": "getWalletTransactionById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/WalletTransactionResponse"}}}}}}}, "/api/wallet/transactions/payment": {"get": {"tags": ["wallet-transaction-controller"], "operationId": "getWalletTransaction", "parameters": [{"name": "paymentSessionId", "in": "query", "required": true, "schema": {"type": "string", "minLength": 1}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/WalletTransactionResponse"}}}}}}}, "/api/wallet/topup-sessions/{id}": {"get": {"tags": ["topup-session-controller"], "operationId": "getTopupSession", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/TopupSessionResponse"}}}}}}}, "/api/wallet/settings/query": {"get": {"tags": ["wallet-setting-controller"], "operationId": "findWalletSettings", "parameters": [{"name": "filter.tenantId", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "sortDirection", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortFields", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ListWalletSettingResponse"}}}}}}}, "/api/wallet/fund-sources-topup-history": {"get": {"tags": ["fund-source-controller"], "operationId": "findFundSourceTopupHistories", "parameters": [{"name": "filter.id", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.fundSourceId", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.fundSourceTopupSchedulerId", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.topupFrequencies", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["NONE", "DAILY", "WEEKLY", "MONTHLY"]}}}, {"name": "filter.statuses", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string", "enum": ["PENDING", "SUCCESS", "FAILED"]}}}, {"name": "filter.fromTime", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "filter.toTime", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "sortDirection", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortFields", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ListFundSourceTopupHistoryResponse"}}}}}}}, "/api/wallet/fund-sources-topup-history-record": {"get": {"tags": ["fund-source-controller"], "operationId": "findFundSourceTopupHistoryRecord", "parameters": [{"name": "filter.id", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.customerId", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.fundSourceTopupHistoryId", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.fundSourceTopupSchedulerId", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.walletTransactionId", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "filter.fromTime", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "filter.toTime", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "sortDirection", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "sortFields", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ListFundSourceTopupTrackingResponse"}}}}}}}, "/api/wallet/delegate-settings/query": {"get": {"tags": ["wallet-delegate-setting-controller"], "operationId": "findWalletDelegateSettings", "parameters": [{"name": "parentCustomerId", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "parentWalletId", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "subCustomerId", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "subWalletId", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ListWalletDelegateSettingsResponse"}}}}}}}, "/api/wallet/beneficiaries/{id}": {"get": {"tags": ["beneficiary-controller"], "operationId": "getBeneficiary", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/BeneficiaryResponse"}}}}}}}, "/api/wallet/delegate-settings/{id}": {"delete": {"tags": ["wallet-delegate-setting-controller"], "operationId": "cancelWalletDelegateSetting", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "X-Tenant-ID", "in": "header"}, {"name": "X-Request-ID", "in": "header"}], "responses": {"403": {"description": "Forbidden", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK"}}}}}, "components": {"schemas": {"ErrorResponse": {"type": "object", "properties": {"error": {"type": "string"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "string"}}}}, "UpdateSpendingLimitRequest": {"type": "object", "properties": {"spendingLimit": {"type": "integer"}}, "required": ["spendingLimit"]}, "CurrencyResponse": {"type": "object", "properties": {"displayName": {"type": "string"}, "numericCode": {"type": "integer", "format": "int32"}, "currencyCode": {"type": "string"}, "symbol": {"type": "string"}, "fractionDigits": {"type": "integer", "format": "int32"}}}, "WalletResponse": {"type": "object", "properties": {"walletId": {"type": "string"}, "name": {"type": "string"}, "tenantId": {"type": "string"}, "customerId": {"type": "string"}, "fundSourceId": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "INACTIVE"]}, "type": {"type": "string", "enum": ["DEPOSIT", "FUNDED"]}, "currency": {"$ref": "#/components/schemas/CurrencyResponse"}, "balance": {"type": "integer"}, "spendingLimit": {"type": "integer"}, "priority": {"type": "integer", "format": "int64"}, "description": {"type": "string"}, "createdAt": {"type": "integer", "format": "int64"}}}, "UpdateFundSourceRequest": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1}, "description": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "INACTIVE"]}, "fundExpiresOn": {"type": "integer", "format": "int64"}, "fundExpiresInMs": {"type": "integer", "format": "int64"}}, "required": ["status"]}, "FundSourceResponse": {"type": "object", "properties": {"fundSourceId": {"type": "string"}, "tenantId": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "INACTIVE"]}, "fundExpiresOn": {"type": "integer", "format": "int64"}, "fundExpiresInMs": {"type": "integer", "format": "int64"}, "createdAt": {"type": "integer", "format": "int64"}}}, "UpdateFundSourceSchedulerRequest": {"type": "object", "properties": {"fundSourceId": {"type": "string", "minLength": 1}, "topupFrequency": {"type": "string", "enum": ["NONE", "DAILY", "WEEKLY", "MONTHLY"]}, "currency": {"type": "string", "minLength": 1}, "topupAmount": {"type": "integer"}, "startTopupDate": {"type": "integer", "format": "int64"}, "endTopupDate": {"type": "integer", "format": "int64"}}, "required": ["startTopupDate", "topupAmount", "topupFrequency"]}, "FundSourceTopupSchedulerResponse": {"type": "object", "properties": {"id": {"type": "string"}, "fundSourceId": {"type": "string"}, "tenantId": {"type": "string"}, "currency": {"$ref": "#/components/schemas/CurrencyResponse"}, "amount": {"type": "integer"}, "topupFrequency": {"type": "string", "enum": ["NONE", "DAILY", "WEEKLY", "MONTHLY"]}, "startTopupDate": {"type": "integer", "format": "int64"}, "nextTopupDate": {"type": "integer", "format": "int64"}, "endTopupDate": {"type": "integer", "format": "int64"}, "createdAt": {"type": "integer", "format": "int64"}}}, "CreateWalletRequest": {"type": "object", "properties": {"customerId": {"type": "integer", "format": "int64"}, "type": {"type": "string", "enum": ["DEPOSIT", "FUNDED"]}, "description": {"type": "string"}, "fundSourceId": {"type": "string"}}, "required": ["customerId", "type"]}, "SaveWalletSpendingLimitSettingRequest": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/SpendingLimitSettingItemRequest"}, "minItems": 1}}}, "SpendingLimitSettingItemRequest": {"type": "object", "properties": {"currency": {"type": "string", "minLength": 1}, "spendingLimit": {"type": "integer"}, "type": {"type": "string", "enum": ["DAY", "WEEK", "MONTH"]}}, "required": ["spendingLimit", "type"]}, "ListWalletSpendingLimitSettingsResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/WalletSpendingLimitSettingResponse"}}}}, "WalletSpendingLimitSettingResponse": {"type": "object", "properties": {"tenantId": {"type": "string"}, "customerId": {"type": "string"}, "walletId": {"type": "string"}, "currency": {"$ref": "#/components/schemas/CurrencyResponse"}, "spendingLimit": {"type": "integer"}, "amount": {"type": "integer"}, "type": {"type": "string", "enum": ["DAY", "WEEK", "MONTH"]}}}, "CreditRequest": {"type": "object", "properties": {"paymentSessionId": {"type": "string", "minLength": 1}, "amount": {"type": "integer"}, "currency": {"type": "string", "minLength": 1}, "deviceId": {"type": "string"}, "description": {"type": "string"}, "acceptDelegatedWallet": {"type": "boolean"}, "requestedAt": {"type": "string"}, "cardId": {"type": "string"}}, "required": ["amount"]}, "WalletTransactionResponse": {"type": "object", "properties": {"transactionId": {"type": "string"}, "tenantId": {"type": "string"}, "customerId": {"type": "string"}, "walletId": {"type": "string"}, "sourceWalletId": {"type": "integer", "format": "int64"}, "destinationWalletId": {"type": "integer", "format": "int64"}, "transactionType": {"type": "string", "enum": ["DEBIT", "CREDIT", "EXPIRED"]}, "transactionCategory": {"type": "string", "enum": ["DEPOSIT", "WITHDRAW", "PURCHASE", "TRANSFER", "REFUND", "EXPIRED"]}, "paymentSessionId": {"type": "string"}, "paymentTransactionId": {"type": "string"}, "currency": {"$ref": "#/components/schemas/CurrencyResponse"}, "cardId": {"type": "string"}, "amount": {"type": "integer"}, "oldBalance": {"type": "integer"}, "balance": {"type": "integer"}, "createdAt": {"type": "integer", "format": "int64"}, "description": {"type": "string"}}}, "DeductBalanceRequest": {"type": "object", "properties": {"amount": {"type": "integer"}, "description": {"type": "string"}}, "required": ["amount"]}, "AddBalanceRequest": {"type": "object", "properties": {"amount": {"type": "integer"}, "expiresOn": {"type": "integer", "format": "int64"}, "description": {"type": "string"}}, "required": ["amount"]}, "CreditMultiWalletRequest": {"type": "object", "properties": {"paymentSessionId": {"type": "string", "minLength": 1}, "amount": {"type": "integer"}, "currency": {"type": "string", "minLength": 1}, "deviceId": {"type": "string"}, "description": {"type": "string"}, "acceptDelegatedWallet": {"type": "boolean"}, "requestedAt": {"type": "string"}, "cardId": {"type": "string"}, "walletIds": {"type": "array", "items": {"type": "string"}, "minItems": 1}}, "required": ["amount"]}, "CreateTopupSessionRequest": {"type": "object", "properties": {"walletId": {"type": "string", "minLength": 1}, "currency": {"type": "string", "minLength": 1}, "amount": {"type": "integer"}, "description": {"type": "string"}}, "required": ["amount"]}, "TopupSessionResponse": {"type": "object", "properties": {"topupSessionId": {"type": "string"}, "tenantId": {"type": "string"}, "walletId": {"type": "string"}, "paymentRef": {"type": "string"}, "currency": {"$ref": "#/components/schemas/CurrencyResponse"}, "amount": {"type": "integer"}, "description": {"type": "string"}, "status": {"type": "string", "enum": ["PENDING", "SUCCESS", "FAILED"]}}}, "SaveWalletSettingRequest": {"type": "object", "properties": {"currency": {"type": "string", "minLength": 1}, "minTopupAmount": {"type": "integer", "minimum": 0}, "maxTopupAmount": {"type": "integer", "minimum": 0}, "presetAmounts": {"type": "array", "items": {"type": "integer"}}, "maxDailyOfflineSpendPerDeviceAmount": {"type": "integer", "minimum": 0}}, "required": ["maxDailyOfflineSpendPerDeviceAmount", "maxTopupAmount", "minTopupAmount", "presetAmounts"]}, "WalletSettingResponse": {"type": "object", "properties": {"id": {"type": "string"}, "tenantId": {"type": "string"}, "currency": {"$ref": "#/components/schemas/CurrencyResponse"}, "minTopupAmount": {"type": "integer"}, "maxTopupAmount": {"type": "integer"}, "maxDailyOfflineSpendPerDeviceAmount": {"type": "integer"}, "presetAmounts": {"type": "array", "items": {"type": "integer"}}}}, "CreateFundSourceRequest": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1}, "description": {"type": "string"}, "status": {"type": "string", "enum": ["ACTIVE", "INACTIVE"]}, "fundExpiresOn": {"type": "integer", "format": "int64"}, "fundExpiresInMs": {"type": "integer", "format": "int64"}}, "required": ["status"]}, "CreateFundSourceSchedulerRequest": {"type": "object", "properties": {"fundSourceId": {"type": "string", "minLength": 1}, "topupFrequency": {"type": "string", "enum": ["NONE", "DAILY", "WEEKLY", "MONTHLY"]}, "currency": {"type": "string", "minLength": 1}, "topupAmount": {"type": "integer"}, "startTopupDate": {"type": "integer", "format": "int64"}, "endTopupDate": {"type": "integer", "format": "int64"}, "fundExpiresOn": {"type": "integer", "format": "int64", "description": "Epoch time in milliseconds"}, "fundExpiresInMs": {"type": "integer", "format": "int64"}}, "required": ["startTopupDate", "topupAmount", "topupFrequency"]}, "SaveWalletDelegateSettingRequest": {"type": "object", "properties": {"parentWalletId": {"type": "string", "minLength": 1}, "subWalletId": {"type": "string", "minLength": 1}}}, "WalletDelegateSettingResponse": {"type": "object", "properties": {"id": {"type": "string"}, "tenantId": {"type": "string"}, "parentWalletId": {"type": "string"}, "parentCustomerId": {"type": "string"}, "subCustomerId": {"type": "string"}, "subWalletId": {"type": "string"}, "enabled": {"type": "boolean"}}}, "CreateBeneficiariesRequest": {"type": "object", "properties": {"customerIds": {"type": "array", "items": {"type": "string"}, "minItems": 1}, "fundSourceId": {"type": "string", "minLength": 1}}}, "BeneficiaryResponse": {"type": "object", "properties": {"beneficiaryId": {"type": "string"}, "customerId": {"type": "string"}, "fundSource": {"$ref": "#/components/schemas/FundSourceResponse"}, "createdAt": {"type": "integer", "format": "int64"}}}, "ListWalletResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/WalletResponse"}}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"type": "string"}}}}, "WalletEntryExpireResponse": {"type": "object", "properties": {"walletId": {"type": "string"}, "amount": {"type": "integer"}, "currency": {"$ref": "#/components/schemas/CurrencyResponse"}, "time": {"type": "integer", "format": "int64"}}}, "ListWalletTransactionResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/WalletTransactionResponse"}}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"type": "string"}}}}, "ListWalletSettingResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/WalletSettingResponse"}}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"type": "string"}}}}, "ListFundSourceResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/FundSourceResponse"}}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"type": "string"}}}}, "FundSourceTopupHistoryResponse": {"type": "object", "properties": {"id": {"type": "string"}, "tenantId": {"type": "string"}, "fundSourceId": {"type": "string"}, "currency": {"$ref": "#/components/schemas/CurrencyResponse"}, "amount": {"type": "integer"}, "topupFrequency": {"type": "string", "enum": ["NONE", "DAILY", "WEEKLY", "MONTHLY"]}, "completedAt": {"type": "integer", "format": "int64"}, "status": {"type": "string", "enum": ["PENDING", "SUCCESS", "FAILED"]}, "fundSourceTopupSchedulerId": {"type": "string"}, "createdAt": {"type": "integer", "format": "int64"}}}, "ListFundSourceTopupHistoryResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/FundSourceTopupHistoryResponse"}}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"type": "string"}}}}, "FundSourceTopupTrackingResponse": {"type": "object", "properties": {"id": {"type": "string"}, "tenantId": {"type": "string"}, "customerId": {"type": "string"}, "currency": {"$ref": "#/components/schemas/CurrencyResponse"}, "amount": {"type": "integer"}, "fundSourceTopupHistoryId": {"type": "string"}, "fundSourceTopupSchedulerId": {"type": "string"}, "walletTransactionId": {"type": "string"}, "createdAt": {"type": "integer", "format": "int64"}}}, "ListFundSourceTopupTrackingResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/FundSourceTopupTrackingResponse"}}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"type": "string"}}}}, "FindFundSourcesSchedulerRequest": {"type": "object", "properties": {"fundSourceId": {"type": "string", "minLength": 1}, "topupFrequency": {"type": "array", "items": {"type": "string", "enum": ["NONE", "DAILY", "WEEKLY", "MONTHLY"]}}}}, "ListWalletDelegateSettingsResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/WalletDelegateSettingResponse"}}}}, "BeneficiariesFilterRequest": {"type": "object", "properties": {"customerIds": {"type": "array", "items": {"type": "string"}}, "fundSourceId": {"type": "string"}}}, "FindBeneficiariesRequest": {"type": "object", "properties": {"filter": {"$ref": "#/components/schemas/BeneficiariesFilterRequest"}, "size": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "sortDirection": {"type": "string"}, "sortFields": {"type": "array", "items": {"type": "string"}}}}, "ListBeneficiaryResponse": {"type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/BeneficiaryResponse"}}, "totalElements": {"type": "integer", "format": "int64"}, "totalPages": {"type": "integer", "format": "int32"}, "page": {"type": "integer", "format": "int32"}, "sort": {"type": "array", "items": {"type": "string"}}}}, "DeleteFundSourceRequest": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "string"}, "minItems": 1}}}, "DeleteFundSourceSchedulerRequest": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "string"}, "minItems": 1}}}, "RemoveBeneficiariesRequest": {"type": "object", "properties": {"customerIds": {"type": "array", "items": {"type": "string"}, "minItems": 1}, "fundSourceId": {"type": "string", "minLength": 1}}}}}}