/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.integration.netsterminal.config;

import com.styl.pacific.domain.valueobject.PaymentProcessorId;
import com.styl.pacific.payment.shared.enums.AcceptedApplication;
import com.styl.pacific.payment.spi.processors.config.SystemProcessorConfiguration;
import java.util.Set;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class SystemNetsTerminalProcessorConfiguration implements SystemProcessorConfiguration {

	@Override
	public PaymentProcessorId getProcessorId() {
		return PaymentProcessorId.NETS_TERMINAL_PAYMENT;
	}

	@Override
	public Set<AcceptedApplication> getAcceptedApplications() {
		return Set.of(AcceptedApplication.POS, AcceptedApplication.SOK, AcceptedApplication.TOP_UP_KIOSK);

	}

	@Override
	public String getProcessorName() {
		return "NETS Terminal Processor";
	}

	@Override
	public String getProcessorDescription() {
		return "NETS payment processor would handle all terminal NETS transactions";
	}
}
