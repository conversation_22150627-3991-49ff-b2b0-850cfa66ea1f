/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.integration.ewallet.settlements.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.payment.service.integration.ewallet.settlements.valueobject.EWalletPaymentSettlementData;
import com.styl.pacific.payment.spi.processors.settlements.events.EWalletTransactionCreatedEvent;
import com.styl.pacific.payment.spi.processors.settlements.events.PaymentSettlementCommandRequestEvent;
import com.styl.pacific.wallet.service.responses.transaction.WalletTransactionResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface EWalletPaymentSettlementMapper {
	EWalletPaymentSettlementMapper INSTANCE = Mappers.getMapper(EWalletPaymentSettlementMapper.class);

	@Mapping(target = "tenantId", source = "tenantId", qualifiedByName = "tenantIdToString")
	@Mapping(target = "paymentSessionId", source = "paymentSessionId", qualifiedByName = "paymentSessionIdToString")
	@Mapping(target = "transactionStatus", expression = "java(com.styl.pacific.domain.enums.PaymentTransactionStatus.SUCCEEDED)")
	@Mapping(target = "paymentProcessorId", expression = "java(com.styl.pacific.domain.valueobject.PaymentProcessorId.E_WALLET_PAYMENT)")
	@Mapping(target = "paidAt", source = "walletTransactionCreatedAt")
	@Mapping(target = "isAsync", ignore = true)
	@Mapping(target = "eventId", ignore = true)
	@Mapping(target = "settlementData", ignore = true)
	@Mapping(target = "extraSettlementData", ignore = true)
	@Mapping(target = "createdAt", ignore = true)
	PaymentSettlementCommandRequestEvent toCommandRequest(EWalletTransactionCreatedEvent event);

	@Mapping(target = "eventId", source = "id")
	@Mapping(target = "processorId", expression = "java(com.styl.pacific.domain.valueobject.PaymentProcessorId.E_WALLET_PAYMENT)")
	@Mapping(target = "walletTransactionId", source = "walletTransactionId", qualifiedByName = "walletTransactionIdToLong")
	@Mapping(target = "walletId", source = "walletId")
	@Mapping(target = "sourceWalletId", source = "sourceWalletId")
	@Mapping(target = "destinationWalletId", source = "destinationWalletId")
	@Mapping(target = "customerId", source = "customerId", qualifiedByName = "userIdToLong")
	EWalletPaymentSettlementData toSettlementClientData(EWalletTransactionCreatedEvent event);

	@Mapping(target = "eventId", ignore = true)
	@Mapping(target = "processorId", expression = "java(com.styl.pacific.domain.valueobject.PaymentProcessorId.E_WALLET_PAYMENT)")
	@Mapping(target = "walletTransactionId", source = "transactionId")
	@Mapping(target = "walletId", source = "walletId")
	@Mapping(target = "sourceWalletId", source = "sourceWalletId")
	@Mapping(target = "destinationWalletId", source = "destinationWalletId")
	@Mapping(target = "walletTransactionCreatedAt", source = "createdAt", qualifiedByName = "longToInstant")
	@Mapping(target = "currency", source = "currency.currencyCode")
	EWalletPaymentSettlementData toSettlementClientData(WalletTransactionResponse transactionResponse);

}