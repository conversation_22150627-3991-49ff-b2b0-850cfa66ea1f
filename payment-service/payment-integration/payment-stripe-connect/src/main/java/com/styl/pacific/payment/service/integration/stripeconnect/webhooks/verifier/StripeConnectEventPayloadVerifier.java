/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.integration.stripeconnect.webhooks.verifier;

import com.stripe.model.Event;
import com.stripe.model.StripeObject;
import com.styl.pacific.payment.spi.processors.webhooks.valueobject.PaymentWebhookEndpointConfig;

public interface StripeConnectEventPayloadVerifier {
	Event verifyAndDecode(PaymentWebhookEndpointConfig webhookEndpoint, String signature, String payload);

	StripeObject getValidStripeObject(Event event);
}
