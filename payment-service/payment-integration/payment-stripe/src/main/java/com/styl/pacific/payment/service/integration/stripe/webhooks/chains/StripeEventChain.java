/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.integration.stripe.webhooks.chains;

import com.stripe.Stripe;
import com.stripe.model.Event;
import com.stripe.model.StripeObject;
import com.styl.pacific.payment.service.integration.stripe.enums.StripeEventType;
import com.styl.pacific.payment.service.integration.stripe.exceptions.StripeEventChainException;
import com.styl.pacific.payment.service.integration.stripe.exceptions.StripeWebhookNotFoundException;
import com.styl.pacific.payment.service.integration.stripe.exceptions.StripeWebhookUnsupportedException;
import com.styl.pacific.payment.service.integration.stripe.webhooks.verifier.StripeEventPayloadVerifier;
import com.styl.pacific.payment.spi.processors.settlements.events.PaymentSettlementCommandRequestEvent;
import com.styl.pacific.payment.spi.processors.webhooks.valueobject.PaymentWebhookEndpointConfig;
import java.util.ArrayList;
import java.util.List;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Predicate;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class StripeEventChain {
	private final PaymentWebhookEndpointConfig webhookEndpoint;

	private final List<SettlementEventHandler> settlementEventHandlers = new ArrayList<>();

	private final StripeEventPayloadVerifier verifier;

	private Consumer<PaymentSettlementCommandRequestEvent> eventPublisher;

	public static StripeEventChain init(PaymentWebhookEndpointConfig webhookEndpoint,
			StripeEventPayloadVerifier verifier) {
		return new StripeEventChain(webhookEndpoint, verifier);
	}

	public StripeEventChain addEventSettlementIfMatched(Predicate<StripeEventType> predicate,
			BiFunction<Event, StripeObject, PaymentSettlementCommandRequestEvent> settlementHandler) {
		settlementEventHandlers.add(SettlementEventHandler.builder()
				.eventTypePredicate(predicate)
				.handler(settlementHandler)
				.build());
		return this;
	}

	public StripeEventChain publish(Consumer<PaymentSettlementCommandRequestEvent> eventPublisher) {
		this.eventPublisher = eventPublisher;
		return this;
	}

	public void execute(StripeEventType expectedEventType, String requestSignature, String eventPayload) {
		if (verifier == null) {
			throw new StripeEventChainException("Verifier is required");
		}
		if (eventPublisher == null) {
			throw new StripeEventChainException("Event publisher is required");
		}

		final var event = verifier.verifyAndDecode(webhookEndpoint, requestSignature, eventPayload);

		if (StringUtils.isBlank(event.getType()) || !expectedEventType.getEvent()
				.equals(event.getType())) {
			throw new StripeWebhookUnsupportedException(String.format("Event [%s] is unsupported", event.getType()));
		}

		if (!Stripe.API_VERSION.equals(event.getApiVersion())) {
			throw new StripeWebhookUnsupportedException(String.format(
					"Webhook API version [%s] is not match with the expected SDK Stripe version [%s]. Please double check Stripe Version and upgrade Webhook version in com.styl.pacific.payment.service.integration.stripe.clients.StripeApiClientImpl.createWebhookEndpoint",
					event.getApiVersion(), Stripe.API_VERSION));
		}

		final var stripeObject = verifier.getValidStripeObject(event);

		final var settlement = settlementEventHandlers.stream()
				.filter(it -> it.eventTypePredicate.test(expectedEventType))
				.findFirst()
				.map(handler -> handler.handler.apply(event, stripeObject))
				.orElseThrow(() -> new StripeWebhookNotFoundException(String.format("Not found handler [%s]", event
						.getType())));

		eventPublisher.accept(settlement);
	}

	@Getter
	@Builder
	@RequiredArgsConstructor
	public static class SettlementEventHandler {
		private final Predicate<StripeEventType> eventTypePredicate;
		private final BiFunction<Event, StripeObject, PaymentSettlementCommandRequestEvent> handler;
	}
}
