/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.integration.stripe.processor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stripe.param.checkout.SessionCreateParams;
import com.styl.pacific.domain.valueobject.PaymentProcessorId;
import com.styl.pacific.payment.service.integration.stripe.clients.StripeApiClient;
import com.styl.pacific.payment.service.integration.stripe.config.StripePaymentMethodConfiguration;
import com.styl.pacific.payment.service.integration.stripe.config.SystemStripeProcessorConfiguration;
import com.styl.pacific.payment.service.integration.stripe.enums.PaymentStatus;
import com.styl.pacific.payment.service.integration.stripe.exceptions.StripeUnableCreateSessionException;
import com.styl.pacific.payment.service.integration.stripe.exceptions.StripeUnableSettleSessionException;
import com.styl.pacific.payment.service.integration.stripe.processor.valueobject.StripePaymentSessionProcessorData;
import com.styl.pacific.payment.service.integration.stripe.sessions.mapper.StripePaymentSessionMetadataMapper;
import com.styl.pacific.payment.service.integration.stripe.sessions.valueobject.StripePaymentSessionParams;
import com.styl.pacific.payment.spi.processors.PaymentConfigParser;
import com.styl.pacific.payment.spi.processors.PaymentProcessor;
import com.styl.pacific.payment.spi.processors.methods.valueobject.PaymentMethodConfiguration;
import com.styl.pacific.payment.spi.processors.sessions.request.InitializePaymentSessionRequest;
import com.styl.pacific.payment.spi.processors.sessions.request.PaymentSessionData;
import com.styl.pacific.payment.spi.processors.sessions.valueobject.PaymentSessionProcessorData;
import com.styl.pacific.payment.spi.processors.settlements.connector.PaymentWebhookConnector;
import com.styl.pacific.payment.spi.processors.settlements.valueobject.PaymentSettlementClientResult;
import jakarta.validation.constraints.NotNull;
import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class StripeProcessor extends PaymentProcessor<StripePaymentMethodConfiguration> {

	private final StripeApiClient stripeApiClient;

	public StripeProcessor(ObjectMapper objectMapper, StripeApiClient stripeApiClient,
			PaymentConfigParser<StripePaymentMethodConfiguration> stripeConfigParser,
			PaymentWebhookConnector stripePaymentWebhookConnector) {
		super(PaymentProcessorId.STRIPE_WEB_PAYMENT, stripeConfigParser, new SystemStripeProcessorConfiguration(),
				objectMapper);
		this.stripeApiClient = stripeApiClient;
		this.webhookConnector = stripePaymentWebhookConnector;
	}

	@Override
	public PaymentSessionData initializePaymentSession(@NotNull PaymentMethodConfiguration configuration,
			@NotNull PaymentSessionProcessorData sessionProcessorData,
			@NotNull InitializePaymentSessionRequest request) {

		if (request.getProcessorParams() == null) {
			throw new StripeUnableCreateSessionException("Payment processor params is required");
		}

		final var params = objectMapper.convertValue(request.getProcessorParams()
				.getParams(), StripePaymentSessionParams.class);
		final var sessionMetadata = StripePaymentSessionMetadataMapper.INSTANCE.toMetadata(sessionProcessorData);

		if (StringUtils.isBlank(params.getCancelRedirectURL()) || params.getCancelRedirectURL()
				.isEmpty()) {
			throw new StripeUnableCreateSessionException("Missing success or cancel redirect URL");
		}

		final var sessionRequest = SessionCreateParams.builder()
				.setMode(SessionCreateParams.Mode.PAYMENT)
				.setSuccessUrl(params.getSuccessRedirectURL())
				.setCancelUrl(params.getCancelRedirectURL())
				.setCurrency(request.getCurrencyCode())
				.setCustomerEmail(request.getCustomerEmail())
				.setAllowPromotionCodes(false)
				.setExpiresAt(getExpiredSessionAt(request.getExpiredInMilliseconds()))
				.setClientReferenceId(sessionProcessorData.getPaymentSessionId())
				.setPaymentIntentData(SessionCreateParams.PaymentIntentData.builder()
						.putAllMetadata(StripePaymentSessionMetadataMapper.INSTANCE.convertToMapValues(objectMapper,
								sessionMetadata))
						.build())
				.addLineItem(SessionCreateParams.LineItem.builder()
						.setQuantity(1L)
						.setPriceData(SessionCreateParams.LineItem.PriceData.builder()
								.setCurrency(request.getCurrencyCode())
								.setProductData(SessionCreateParams.LineItem.PriceData.ProductData.builder()
										.setName(request.getDescription())
										.build())
								.setUnitAmount(request.getAmount())
								.build())
						.build())
				.putAllMetadata(StripePaymentSessionMetadataMapper.INSTANCE.convertToMapValues(objectMapper,
						sessionMetadata))
				.build();

		return stripeApiClient.createSession(configParser.parseConfigMap(configuration), sessionRequest);
	}

	@Override
	public void cancelPaymentSession(PaymentMethodConfiguration configuration, PaymentSessionData processorData) {
		final var sessionData = objectMapper.convertValue(processorData.getData(),
				StripePaymentSessionProcessorData.class);
		final var paymentMethodConfig = configParser.parseConfigMap(configuration);

		stripeApiClient.cancelPaymentSession(paymentMethodConfig, sessionData);
	}

	private Long getExpiredSessionAt(Long expiredInMilliseconds) {
		long minutes = Optional.ofNullable(expiredInMilliseconds)
				.map(it -> Duration.ofMillis(it)
						.toMinutes())
				.orElse(0L);

		if (minutes < 30) {
			minutes = 30;
		}
		if (minutes > 1440) {
			minutes = 1440; // 24H
		}
		return Instant.now()
				.plus(minutes, ChronoUnit.MINUTES)
				.getEpochSecond();

	}

	@Override
	public Optional<PaymentSettlementClientResult> validateSessionSettlement(PaymentMethodConfiguration processorConfig,
			PaymentSessionProcessorData sessionClientData) {
		final var sessionProcessorData = objectMapper.convertValue(sessionClientData.getSessionData()
				.getData(), StripePaymentSessionProcessorData.class);
		final var session = stripeApiClient.getCheckoutPaymentSession(configParser.parseConfigMap(processorConfig),
				sessionProcessorData.getStripeSessionId());

		if (StringUtils.isBlank(session.getPaymentStatus()) || !PaymentStatus.PAID.getValue()
				.equals(session.getPaymentStatus())) {
			throw new StripeUnableSettleSessionException(String.format(
					"Unable to settle Stripe Session due to payment status [%s]", session.getPaymentStatus()));
		}

		if (StringUtils.isBlank(session.getPaymentIntent())) {
			throw new StripeUnableSettleSessionException(
					"Unable to settle Stripe Session due to payment intent is empty");
		}

		final var metadata = StripePaymentSessionMetadataMapper.INSTANCE.parseMetadata(objectMapper, session
				.getMetadata())
				.orElseThrow(() -> new StripeUnableSettleSessionException(
						"Unable to settle Stripe Session due to missing metadata"));

		if (!sessionClientData.getPaymentSessionId()
				.equals(metadata.getPaymentSessionId()) || (StringUtils.isNotBlank(sessionClientData
						.getPaymentReference()) && !sessionClientData.getPaymentReference()
								.equals(metadata.getPaymentReference()))) {
			throw new StripeUnableSettleSessionException(
					"Unable to settle Stripe Session due to mismatch payment reference");
		}

		return Optional.of(PaymentSettlementClientResult.builder()
				.transactionNumber(session.getPaymentIntent())
				.build());
	}

	@Override
	@Async
	public void postSettlementSession(PaymentMethodConfiguration processorConfig, PaymentSessionData sessionData) {
		cancelPaymentSession(processorConfig, sessionData);
	}
}