/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.core.features.methods.entities;

import com.styl.pacific.domain.entity.BaseEntity;
import com.styl.pacific.domain.valueobject.PaymentMethodId;
import com.styl.pacific.domain.valueobject.PaymentProcessorId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.payment.service.core.features.accounts.entities.PaymentConnectedAccount;
import com.styl.pacific.payment.shared.enums.AcceptedApplication;
import com.styl.pacific.payment.spi.processors.methods.valueobject.SimplePaymentMethodConfiguration;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.Set;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.With;

@Getter
@Builder
@With
@RequiredArgsConstructor
public class PaymentMethod extends BaseEntity<PaymentMethodId> {
	private final PaymentMethodId id;
	private final PaymentProcessorId processorId;
	private final TenantId tenantId;
	private final Boolean isActive;
	private final String displayName;
	private final String iconPath;
	private final String description;
	private final String paymentInstruction;
	private final BigDecimal surchargeRate;
	private final Long fixedSurcharge;
	private final String currencyCode;
	private final String surchargeTitle;
	private final boolean isTransactionReversible;

	private final PaymentConnectedAccount connectedAccount;

	private final SimplePaymentMethodConfiguration processorConfig;
	private final Set<AcceptedApplication> acceptedApplications;

	private final Instant createdAt;
	private final Instant updatedAt;
	private final Long createdBy;
	private final Long updatedBy;
}