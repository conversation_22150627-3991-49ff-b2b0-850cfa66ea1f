/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.core.features.methods.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.payment.service.core.features.accounts.entities.PaymentConnectedAccount;
import com.styl.pacific.payment.service.core.features.methods.entities.PaymentMethod;
import com.styl.pacific.payment.service.core.features.methods.request.UpsertPaymentMethodCommand;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface PaymentMethodMapper {
	PaymentMethodMapper INSTANCE = Mappers.getMapper(PaymentMethodMapper.class);

	@Mapping(target = "id", ignore = true)
	@Mapping(target = "createdAt", ignore = true)
	@Mapping(target = "updatedAt", ignore = true)
	@Mapping(target = "createdBy", ignore = true)
	@Mapping(target = "updatedBy", ignore = true)
	@Mapping(target = "connectedAccount", ignore = true)
	@Mapping(target = "isTransactionReversible", ignore = true)
	@Mapping(target = "tenantId", source = "tenantId")
	PaymentMethod toNewEntity(TenantId tenantId, UpsertPaymentMethodCommand command);

	@Mapping(target = "id", source = "method.id")
	@Mapping(target = "tenantId", source = "method.tenantId")
	@Mapping(target = "displayName", source = "command.displayName")
	@Mapping(target = "processorId", source = "method.processorId")
	@Mapping(target = "iconPath", source = "command.iconPath")
	@Mapping(target = "isActive", source = "command.isActive")
	@Mapping(target = "description", source = "command.description")
	@Mapping(target = "paymentInstruction", source = "command.paymentInstruction")
	@Mapping(target = "surchargeRate", source = "command.surchargeRate")
	@Mapping(target = "fixedSurcharge", source = "command.fixedSurcharge")
	@Mapping(target = "currencyCode", source = "command.currencyCode")
	@Mapping(target = "surchargeTitle", source = "command.surchargeTitle")
	@Mapping(target = "processorConfig", source = "command.processorConfig")
	@Mapping(target = "acceptedApplications", source = "command.acceptedApplications")
	@Mapping(target = "connectedAccount", source = "connectedAccount")
	@Mapping(target = "isTransactionReversible", ignore = true)
	@Mapping(target = "createdAt", ignore = true)
	@Mapping(target = "updatedAt", ignore = true)
	PaymentMethod toUpdateEntity(PaymentMethod method, UpsertPaymentMethodCommand command,
			PaymentConnectedAccount connectedAccount);

	@Mapping(target = "displayName", source = "method.displayName")
	@Mapping(target = "processorId", source = "method.processorId")
	@Mapping(target = "iconPath", source = "method.iconPath")
	@Mapping(target = "isActive", source = "method.isActive")
	@Mapping(target = "description", source = "method.description")
	@Mapping(target = "paymentInstruction", source = "method.paymentInstruction")
	@Mapping(target = "surchargeRate", source = "method.surchargeRate")
	@Mapping(target = "fixedSurcharge", source = "method.fixedSurcharge")
	@Mapping(target = "currencyCode", source = "method.currencyCode")
	@Mapping(target = "surchargeTitle", source = "method.surchargeTitle")
	@Mapping(target = "processorConfig", source = "method.processorConfig")
	@Mapping(target = "acceptedApplications", source = "method.acceptedApplications")
	@Mapping(target = "connectedAccountId", source = "connectedAccount.id")
	UpsertPaymentMethodCommand toUpsertCommand(PaymentMethod method, PaymentConnectedAccount connectedAccount);
}