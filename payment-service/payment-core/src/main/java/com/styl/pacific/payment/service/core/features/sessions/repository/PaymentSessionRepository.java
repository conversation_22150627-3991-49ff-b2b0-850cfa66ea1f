/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.core.features.sessions.repository;

import com.styl.pacific.domain.valueobject.PaymentSessionId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.payment.service.core.features.sessions.entities.PaymentSession;
import jakarta.validation.constraints.NotNull;
import java.util.Optional;
import org.springframework.lang.NonNull;

public interface PaymentSessionRepository {
	PaymentSession save(PaymentSession paymentSession);

	Optional<PaymentSession> getSessionBySessionId(@NonNull TenantId tenantId,
			@NotNull PaymentSessionId paymentSessionId);
}
