/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.core.features.methods.mapper;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.payment.service.core.features.accounts.entities.PaymentConnectedAccount;
import com.styl.pacific.payment.service.core.features.accounts.model.ConnectedAccountPaymentMethodConfig;
import com.styl.pacific.payment.shared.http.methods.request.CreatePaymentProcessorConfigurationRequest;
import com.styl.pacific.payment.spi.processors.methods.valueobject.SimplePaymentMethodConfiguration;
import java.util.Optional;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface SimplePaymentMethodConfigurationMapper {

	SimplePaymentMethodConfigurationMapper INSTANCE = Mappers.getMapper(SimplePaymentMethodConfigurationMapper.class);

	@Named("mapToSimplePaymentMethodConfiguration")
	default SimplePaymentMethodConfiguration toUpdateSimplePaymentMethodConfig(
			final CreatePaymentProcessorConfigurationRequest configurationRequest, @Context ObjectMapper mapper) {
		return Optional.ofNullable(configurationRequest)
				.map(config -> SimplePaymentMethodConfiguration.builder()
						.configs(mapper.convertValue(config, new TypeReference<>() {
						}))
						.build())
				.orElse(null);
	}

	@Mapping(target = "paymentConnectedAccountId", source = "id", qualifiedByName = "paymentConnectedAccountIdToString")
	@Mapping(target = "isActive", ignore = true)
	@Mapping(target = "tenantId", ignore = true)
	@Mapping(target = "clientExternalId", ignore = true)
	@Mapping(target = "createdAt", ignore = true)
	@Mapping(target = "updatedAt", ignore = true)
	ConnectedAccountPaymentMethodConfig mapToUpdatePaymentMethod(PaymentConnectedAccount paymentConnectedAccount);

	default SimplePaymentMethodConfiguration toSimplePaymentMethodConfig(ConnectedAccountPaymentMethodConfig source,
			ObjectMapper mapper) {
		return Optional.ofNullable(source)
				.map(config -> SimplePaymentMethodConfiguration.builder()
						.configs(mapper.convertValue(config, new TypeReference<>() {
						}))
						.build())
				.orElse(null);
	}
}
