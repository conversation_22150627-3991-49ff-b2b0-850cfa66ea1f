/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.core.features.settlements.request;

import com.styl.pacific.domain.enums.PaymentTransactionStatus;
import com.styl.pacific.domain.valueobject.PaymentProcessorId;
import com.styl.pacific.domain.valueobject.PaymentSessionId;
import java.time.Instant;
import java.util.Map;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@Builder
@RequiredArgsConstructor
public class SettlePaymentSessionCommand {
	private final PaymentSessionId paymentSessionId;
	private final PaymentProcessorId paymentProcessorId;
	private final Instant paidAt;
	private final Boolean isAsync;
	private final PaymentTransactionStatus transactionStatus;
	private final String transactionNumber;
	private final String deviceTransactionNumber;

	private final Map<String, Object> settlementData;
	private final Map<String, String> extraSettlementData;
}