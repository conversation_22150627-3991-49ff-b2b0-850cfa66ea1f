/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.core.features.transactions.service;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.PaymentTransactionId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.payment.service.core.features.transactions.PaymentTransactionQueryService;
import com.styl.pacific.payment.service.core.features.transactions.PaymentTransactionRepository;
import com.styl.pacific.payment.service.core.features.transactions.entities.PaymentTransaction;
import com.styl.pacific.payment.service.core.features.transactions.request.PaymentTransactionPaginationQuery;
import com.styl.pacific.payment.shared.exceptions.PaymentTransactionNotFoundException;
import com.styl.pacific.payment.shared.exceptions.PaymentTransactionRequiredException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class PaymentTransactionQueryServiceImpl implements PaymentTransactionQueryService {

	private final PaymentTransactionRepository paymentTransactionRepository;

	@Override
	public PaymentTransaction getTransactionByTenantIdAndId(TenantId tenantId,
			PaymentTransactionId paymentTransactionId) {
		if (paymentTransactionId == null) {
			throw new PaymentTransactionRequiredException();
		}
		return paymentTransactionRepository.findTransactionByTenantIdAndId(tenantId, paymentTransactionId)
				.orElseThrow(PaymentTransactionNotFoundException::new);
	}

	@Override
	public Paging<PaymentTransaction> queryTransactions(PaymentTransactionPaginationQuery query) {
		return paymentTransactionRepository.queryTransactions(query);
	}
}
