/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.spi.processors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.domain.valueobject.PaymentProcessorId;
import com.styl.pacific.payment.spi.processors.methods.valueobject.PaymentMethodConfiguration;
import lombok.Getter;

public class PaymentConfigParser<Configuration extends PaymentMethodConfiguration> {

	@Getter
	private final PaymentProcessorId processorId;

	@Getter
	private final Class<Configuration> methodConfigClazz;

	protected final ObjectMapper objectMapper;

	public PaymentConfigParser(PaymentProcessorId processorId, Class<Configuration> methodConfigClazz,
			ObjectMapper objectMapper) {
		this.processorId = processorId;
		this.methodConfigClazz = methodConfigClazz;
		this.objectMapper = objectMapper;
	}

	public Configuration parseConfigMap(PaymentMethodConfiguration configuration) {
		return configuration != null
				? objectMapper.convertValue(configuration.getConfigMap(processorId, objectMapper), methodConfigClazz)
				: null;
	}

}
