/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.spi.processors.config;

import com.styl.pacific.domain.valueobject.PaymentProcessorId;
import com.styl.pacific.payment.shared.enums.AcceptedApplication;
import com.styl.pacific.payment.shared.exceptions.PaymentWebhookEventUnsupportedException;
import java.util.List;
import java.util.Set;

public interface SystemProcessorConfiguration {
	PaymentProcessorId getProcessorId();

	String getProcessorName();

	String getProcessorDescription();

	Set<AcceptedApplication> getAcceptedApplications();

	default List<String> getWebhookEventsConfig() {
		throw new PaymentWebhookEventUnsupportedException();
	}

	default boolean getTransactionReversible() {
		return false;
	}
}
