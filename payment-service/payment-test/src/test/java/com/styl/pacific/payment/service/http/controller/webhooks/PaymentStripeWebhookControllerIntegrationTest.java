/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.http.controller.webhooks;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stripe.Stripe;
import com.stripe.model.Event;
import com.stripe.model.PaymentIntent;
import com.stripe.model.checkout.Session;
import com.styl.pacific.common.test.utils.HeaderGenerator;
import com.styl.pacific.domain.valueobject.PaymentProcessorId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.kafka.payments.avro.model.PaymentTransactionStatus;
import com.styl.pacific.payment.messaging.settlements.publisher.kafka.KafkaPaymentSettlementCommandEventPublisher;
import com.styl.pacific.payment.service.config.IntegrationTestConfiguration;
import com.styl.pacific.payment.service.config.PaymentIntegrationTestContainer;
import com.styl.pacific.payment.service.config.PaymentMethodInitIntegrationSupporter;
import com.styl.pacific.payment.service.config.PaymentSessionInitIntegrationSupporter;
import com.styl.pacific.payment.service.core.features.methods.PaymentMethodCommandService;
import com.styl.pacific.payment.service.core.features.methods.request.UpsertPaymentMethodCommand;
import com.styl.pacific.payment.service.core.features.sessions.PaymentSessionCommandService;
import com.styl.pacific.payment.service.core.features.sessions.mapper.PaymentSessionMapper;
import com.styl.pacific.payment.service.core.features.sessions.request.CreatePaymentSessionCommand;
import com.styl.pacific.payment.service.core.features.webhooks.valueobject.PaymentWebhookClientData;
import com.styl.pacific.payment.service.data.access.clients.tenants.TenantClient;
import com.styl.pacific.payment.service.integration.ewallet.clients.wallets.WalletTransactionClient;
import com.styl.pacific.payment.service.integration.stripe.clients.StripeApiClient;
import com.styl.pacific.payment.service.integration.stripe.config.StripePaymentMethodConfiguration;
import com.styl.pacific.payment.service.integration.stripe.constants.StripeEventConstants;
import com.styl.pacific.payment.service.integration.stripe.processor.valueobject.StripePaymentSessionProcessorData;
import com.styl.pacific.payment.service.integration.stripe.sessions.mapper.StripePaymentSessionMetadataMapper;
import com.styl.pacific.payment.service.integration.stripe.settlements.valueobject.StripePaymentSettlementData;
import com.styl.pacific.payment.service.integration.stripeconnect.clients.StripeConnectApiClient;
import com.styl.pacific.payment.shared.enums.AcceptedApplication;
import com.styl.pacific.payment.shared.enums.PaymentTransactionType;
import com.styl.pacific.payment.shared.http.methods.request.stripe.StripePaymentMethodConfigurationRequest;
import com.styl.pacific.payment.shared.http.sessions.request.stripe.StripeWebPaymentSessionParamsRequest;
import com.styl.pacific.payment.spi.processors.PaymentWebhookEndpointManager;
import com.styl.pacific.payment.spi.processors.methods.valueobject.SimplePaymentMethodConfiguration;
import com.styl.pacific.payment.spi.processors.sessions.valueobject.PaymentSessionParams;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Optional;
import java.util.Set;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = IntegrationTestConfiguration.class)
class PaymentStripeWebhookControllerIntegrationTest extends PaymentIntegrationTestContainer {

	@MockitoBean
	private StripeApiClient stripeApiClient;

	@MockitoBean
	private TenantClient tenantClient;

	@MockitoBean
	private WalletTransactionClient walletTransactionClient;

	@MockitoBean
	private StripeConnectApiClient stripeConnectApiClient;

	@MockitoBean
	private PaymentWebhookEndpointManager webhookEndpointManager;

	@Autowired
	private ObjectMapper objectMapper;

	@Autowired
	private PaymentMethodCommandService methodCommandService;

	@MockitoBean
	private KafkaPaymentSettlementCommandEventPublisher kafkaPaymentSettlementCommandEventPublisher;

	@Autowired
	private PaymentSessionCommandService sessionCommandService;

	private PaymentMethodInitIntegrationSupporter paymentMethodSupporter;

	private PaymentSessionInitIntegrationSupporter paymentSessionSupporter;

	@BeforeEach
	void setUp() {
		paymentMethodSupporter = new PaymentMethodInitIntegrationSupporter(tenantClient, methodCommandService);
		paymentSessionSupporter = new PaymentSessionInitIntegrationSupporter(stripeApiClient, stripeConnectApiClient,
				objectMapper, sessionCommandService);
	}

	@Test
	void testSettleStripePaymentSessionPaymentIntentEventSuccessfullyWhenValidRequest() {
		// Arrange
		final var tenantId = new TenantId(10_000L);
		final var settlementCommandEventCaptor = ArgumentCaptor.forClass(
				com.styl.pacific.kafka.payments.avro.model.PaymentSettlementCommandRequestEventAvroModel.class);

		final var stripePaymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Stripe Payment Method 10000")
				.processorId(PaymentProcessorId.STRIPE_WEB_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.CUSTOMER_PORTAL))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Stripe Payment Description 10000")
				.paymentInstruction("Stripe Payment Instruction 10000")
				.currencyCode("SGD")
				.surchargeRate(BigDecimal.valueOf(0.123))
				.surchargeTitle("Stripe Payment Title 10000")
				.processorConfig(SimplePaymentMethodConfiguration.builder()
						.configs(objectMapper.convertValue(StripePaymentMethodConfigurationRequest.builder()
								.apiKey("API KEY 1234")
								.build(), new TypeReference<>() {
								}))
						.build())
				.build());

		final var stripeParams = StripeWebPaymentSessionParamsRequest.builder()
				.cancelRedirectURL("https://www.cancel.org/current")
				.successRedirectURL("https://www.success.org/current")
				.build();

		final var session = paymentSessionSupporter.initStripePaymentSession(tenantId, CreatePaymentSessionCommand
				.builder()
				.paymentProcessorId(stripePaymentMethod.getProcessorId())
				.paymentMethodId(stripePaymentMethod.getId())
				.description("Payment Order #1234")
				.paymentReference("Payment Reference 01")
				.transactionType(PaymentTransactionType.PURCHASE)
				.amount(2495L)
				.netAmount(2222L)
				.fee(273L)
				.currencyCode("SGD")
				.customerId("1")
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.systemSource("System Source 01")
				.merchantName("Merchant Name")
				.expiredInMilliseconds(Duration.ofMinutes(16)
						.toMillis())
				.processorParams(PaymentSessionParams.builder()
						.params(objectMapper.convertValue(stripeParams, new TypeReference<>() {
						}))
						.build())
				.build());

		final var metadata = StripePaymentSessionMetadataMapper.INSTANCE.toMetadata(PaymentSessionMapper.INSTANCE
				.toSessionProcessorData(session));
		final var sessionData = objectMapper.convertValue(session.getSessionData()
				.getData(), StripePaymentSessionProcessorData.class);

		final var stripeSession = new Session();
		stripeSession.setId(sessionData.getStripeSessionId());
		stripeSession.setPaymentStatus("paid");
		stripeSession.setPaymentIntent("payment-int-" + stripeSession.getId());
		stripeSession.setExpiresAt(Instant.now()
				.plus(16, ChronoUnit.MINUTES)
				.getEpochSecond());
		stripeSession.setMetadata(objectMapper.convertValue(metadata, new TypeReference<>() {
		}));

		when(stripeApiClient.getCheckoutPaymentSession(any(StripePaymentMethodConfiguration.class), eq(stripeSession
				.getId()))).thenReturn(stripeSession);

		final var event = new Event();
		event.setId("event-" + stripeSession.getId());
		event.setType(StripeEventConstants.PAYMENT_INTENT_SUCCEEDED);
		event.setApiVersion(Stripe.API_VERSION);
		when(stripeApiClient.verifyAndDecode(any(), any(), any())).thenReturn(event);

		final var stripeObject = new PaymentIntent();
		stripeObject.setId("payment-int-" + stripeSession.getId());
		stripeObject.setCreated(Instant.now()
				.getEpochSecond());
		stripeObject.setMetadata(objectMapper.convertValue(metadata, new TypeReference<>() {
		}));
		when(stripeApiClient.getValidStripeObject(any())).thenReturn(stripeObject);

		when(webhookEndpointManager.getWebhookEndpointConfig(any(), any())).thenReturn(Optional.of(
				PaymentWebhookClientData.builder()
						.build()));

		final var body = "mocked event body";

		// Act & Assert
		webClient.post()
				.uri("/webhooks/payment/stripe/{endpointId}?"
						+ StripeEventConstants.StripeEventParamsConstants.PAYMENT_INTENT_SUCCEEDED, 12345L)
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(body)
				.headers(header -> HeaderGenerator.generateStripeHeader(header, "ABC"))
				.exchange()
				.expectStatus()
				.isOk()
				.expectBody(Void.class)
				.consumeWith(response -> {
					verify(kafkaPaymentSettlementCommandEventPublisher, times(1)).publish(settlementCommandEventCaptor
							.capture());
					final var settlementCommand = settlementCommandEventCaptor.getValue();

					Assertions.assertEquals(session.getId()
							.getValue(), settlementCommand.getPaymentSessionId());
					Assertions.assertEquals(session.getTenantId()
							.getValue(), settlementCommand.getTenantId());
					Assertions.assertEquals(PaymentTransactionStatus.SUCCEEDED, settlementCommand
							.getTransactionStatus());
					Assertions.assertTrue(settlementCommand.getIsAsync());
					Assertions.assertEquals(Instant.ofEpochSecond(stripeObject.getCreated())
							.toEpochMilli(), settlementCommand.getPaidAt());

					final var actualSettlementData = objectMapper.convertValue(settlementCommand.getSettlementData(),
							StripePaymentSettlementData.class);
					Assertions.assertEquals(PaymentProcessorId.STRIPE_WEB_PAYMENT, actualSettlementData
							.getProcessorId());
					Assertions.assertEquals(stripeObject.getId(), actualSettlementData.getStripePaymentIntent());
					Assertions.assertEquals(StripeEventConstants.PAYMENT_INTENT_SUCCEEDED, actualSettlementData
							.getStripeEventType());
					Assertions.assertEquals(event.getId(), actualSettlementData.getStripeEventId());

				});

	}

	@Test
	void testSettleStripePaymentSessionCheckoutSessionEventSuccessfullyWhenValidRequest() {
		// Arrange
		final var tenantId = new TenantId(10_000L);
		final var settlementCommandEventCaptor = ArgumentCaptor.forClass(
				com.styl.pacific.kafka.payments.avro.model.PaymentSettlementCommandRequestEventAvroModel.class);

		final var stripePaymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Stripe Payment Method 10000")
				.processorId(PaymentProcessorId.STRIPE_WEB_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.CUSTOMER_PORTAL))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Stripe Payment Description 10000")
				.paymentInstruction("Stripe Payment Instruction 10000")
				.currencyCode("SGD")
				.surchargeRate(BigDecimal.valueOf(0.123))
				.surchargeTitle("Stripe Payment Title 10000")
				.processorConfig(SimplePaymentMethodConfiguration.builder()
						.configs(objectMapper.convertValue(StripePaymentMethodConfigurationRequest.builder()
								.apiKey("API KEY 1234")
								.build(), new TypeReference<>() {
								}))
						.build())
				.build());

		final var stripeParams = StripeWebPaymentSessionParamsRequest.builder()
				.cancelRedirectURL("https://www.cancel.org/current")
				.successRedirectURL("https://www.success.org/current")
				.build();

		final var session = paymentSessionSupporter.initStripePaymentSession(tenantId, CreatePaymentSessionCommand
				.builder()
				.paymentProcessorId(stripePaymentMethod.getProcessorId())
				.paymentMethodId(stripePaymentMethod.getId())
				.description("Payment Order #1234")
				.paymentReference("Payment Reference 01")
				.transactionType(PaymentTransactionType.PURCHASE)
				.amount(2495L)
				.netAmount(2222L)
				.fee(273L)
				.currencyCode("SGD")
				.customerId("1")
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.systemSource("System Source 01")
				.merchantName("Merchant Name")
				.expiredInMilliseconds(Duration.ofMinutes(16)
						.toMillis())
				.processorParams(PaymentSessionParams.builder()
						.params(objectMapper.convertValue(stripeParams, new TypeReference<>() {
						}))
						.build())
				.build());

		final var metadata = StripePaymentSessionMetadataMapper.INSTANCE.toMetadata(PaymentSessionMapper.INSTANCE
				.toSessionProcessorData(session));
		final var sessionData = objectMapper.convertValue(session.getSessionData()
				.getData(), StripePaymentSessionProcessorData.class);

		final var stripeSession = new Session();
		stripeSession.setId(sessionData.getStripeSessionId());
		stripeSession.setPaymentStatus("paid");
		stripeSession.setPaymentIntent("payment-int-" + stripeSession.getId());
		stripeSession.setExpiresAt(Instant.now()
				.plus(16, ChronoUnit.MINUTES)
				.getEpochSecond());
		stripeSession.setMetadata(objectMapper.convertValue(metadata, new TypeReference<>() {
		}));

		when(stripeApiClient.getCheckoutPaymentSession(any(StripePaymentMethodConfiguration.class), eq(stripeSession
				.getId()))).thenReturn(stripeSession);

		final var event = new Event();
		event.setId("event-" + stripeSession.getId());
		event.setType(StripeEventConstants.CHECKOUT_SESSION_ASYNC_PAYMENT_SUCCEEDED);
		event.setApiVersion(Stripe.API_VERSION);
		when(stripeApiClient.verifyAndDecode(any(), any(), any())).thenReturn(event);

		final var stripeObject = new Session();
		stripeObject.setPaymentIntent("payment-int-" + stripeSession.getId());
		stripeObject.setCreated(Instant.now()
				.getEpochSecond());
		stripeObject.setMetadata(objectMapper.convertValue(metadata, new TypeReference<>() {
		}));
		when(stripeApiClient.getValidStripeObject(any())).thenReturn(stripeObject);

		when(webhookEndpointManager.getWebhookEndpointConfig(any(), any())).thenReturn(Optional.of(
				PaymentWebhookClientData.builder()
						.build()));

		final var body = "mocked event body";

		// Act & Assert
		webClient.post()
				.uri("/webhooks/payment/stripe/{endpointId}?"
						+ StripeEventConstants.StripeEventParamsConstants.CHECKOUT_SESSION_ASYNC_PAYMENT_SUCCEEDED,
						12345L)
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(body)
				.headers(header -> HeaderGenerator.generateStripeHeader(header, "ABC"))
				.exchange()
				.expectStatus()
				.isOk()
				.expectBody(Void.class)
				.consumeWith(response -> {
					verify(kafkaPaymentSettlementCommandEventPublisher, times(1)).publish(settlementCommandEventCaptor
							.capture());
					final var settlementCommand = settlementCommandEventCaptor.getValue();

					Assertions.assertEquals(session.getId()
							.getValue(), settlementCommand.getPaymentSessionId());
					Assertions.assertEquals(session.getTenantId()
							.getValue(), settlementCommand.getTenantId());
					Assertions.assertEquals(PaymentTransactionStatus.SUCCEEDED, settlementCommand
							.getTransactionStatus());
					Assertions.assertTrue(settlementCommand.getIsAsync());
					Assertions.assertEquals(Instant.ofEpochSecond(stripeObject.getCreated())
							.toEpochMilli(), settlementCommand.getPaidAt());

					final var actualSettlementData = objectMapper.convertValue(settlementCommand.getSettlementData(),
							StripePaymentSettlementData.class);
					Assertions.assertEquals(PaymentProcessorId.STRIPE_WEB_PAYMENT, actualSettlementData
							.getProcessorId());
					Assertions.assertEquals(stripeObject.getPaymentIntent(), actualSettlementData
							.getStripePaymentIntent());
					Assertions.assertEquals(StripeEventConstants.CHECKOUT_SESSION_ASYNC_PAYMENT_SUCCEEDED,
							actualSettlementData.getStripeEventType());
					Assertions.assertEquals(event.getId(), actualSettlementData.getStripeEventId());
				});
	}
}
