/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.http.controller.sessions;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stripe.param.checkout.SessionCreateParams;
import com.styl.pacific.application.rest.dto.ErrorResponse;
import com.styl.pacific.common.test.utils.GenerateHttpHeader;
import com.styl.pacific.common.test.utils.HeaderGenerator;
import com.styl.pacific.domain.enums.PaymentTransactionStatus;
import com.styl.pacific.domain.enums.UserType;
import com.styl.pacific.domain.tokenclaims.UserTokenClaim;
import com.styl.pacific.domain.valueobject.PaymentProcessorId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.payment.service.config.IntegrationTestConfiguration;
import com.styl.pacific.payment.service.config.PaymentConnectedIntegrationSupporter;
import com.styl.pacific.payment.service.config.PaymentIntegrationTestContainer;
import com.styl.pacific.payment.service.config.PaymentMethodInitIntegrationSupporter;
import com.styl.pacific.payment.service.config.PaymentSessionInitIntegrationSupporter;
import com.styl.pacific.payment.service.core.features.accounts.PaymentConnectedAccountCommandService;
import com.styl.pacific.payment.service.core.features.methods.PaymentMethodCommandService;
import com.styl.pacific.payment.service.core.features.methods.PaymentMethodQueryService;
import com.styl.pacific.payment.service.core.features.methods.request.UpsertPaymentMethodCommand;
import com.styl.pacific.payment.service.core.features.sessions.PaymentSessionCommandService;
import com.styl.pacific.payment.service.core.features.sessions.PaymentSessionQueryService;
import com.styl.pacific.payment.service.core.features.sessions.repository.PaymentSessionRepository;
import com.styl.pacific.payment.service.core.features.sessions.request.CreatePaymentSessionCommand;
import com.styl.pacific.payment.service.core.features.settlements.PaymentSettlementCommandService;
import com.styl.pacific.payment.service.core.features.settlements.request.SettlePaymentSessionCommand;
import com.styl.pacific.payment.service.data.access.clients.tenants.TenantClient;
import com.styl.pacific.payment.service.integration.netsterminal.config.NetsTerminalPaymentMethodConfiguration;
import com.styl.pacific.payment.service.integration.stripe.clients.StripeApiClient;
import com.styl.pacific.payment.service.integration.stripe.config.StripePaymentMethodConfiguration;
import com.styl.pacific.payment.service.integration.stripe.processor.valueobject.StripePaymentSessionProcessorData;
import com.styl.pacific.payment.service.integration.stripeconnect.clients.StripeConnectApiClient;
import com.styl.pacific.payment.service.integration.stripeconnect.config.StripeConnectPaymentMethodConfiguration;
import com.styl.pacific.payment.shared.enums.AcceptedApplication;
import com.styl.pacific.payment.shared.enums.NetsFamilyCardType;
import com.styl.pacific.payment.shared.enums.PaymentSessionStatus;
import com.styl.pacific.payment.shared.enums.PaymentTransactionType;
import com.styl.pacific.payment.shared.exceptions.PaymentSessionOptimisticException;
import com.styl.pacific.payment.shared.http.methods.request.stripe.StripePaymentMethodConfigurationRequest;
import com.styl.pacific.payment.shared.http.sessions.request.CreatePaymentSessionRequest;
import com.styl.pacific.payment.shared.http.sessions.request.cash.CashPaymentSessionParamsRequest;
import com.styl.pacific.payment.shared.http.sessions.request.ewallet.EWalletPaymentSessionParamsRequest;
import com.styl.pacific.payment.shared.http.sessions.request.nets.NetsPaymentSessionParamsRequest;
import com.styl.pacific.payment.shared.http.sessions.request.offline.OfflineWebPaymentSessionParamsRequest;
import com.styl.pacific.payment.shared.http.sessions.request.stripe.StripeWebPaymentSessionParamsRequest;
import com.styl.pacific.payment.shared.http.sessions.request.stripeconnect.StripeConnectWebPaymentSessionParamsRequest;
import com.styl.pacific.payment.shared.http.sessions.response.PaymentSessionResponse;
import com.styl.pacific.payment.shared.http.sessions.response.cash.CashPaymentSessionParamsResponse;
import com.styl.pacific.payment.shared.http.sessions.response.ewallet.EWalletPaymentSessionParamsResponse;
import com.styl.pacific.payment.shared.http.sessions.response.nets.NetsPaymentSessionParamsResponse;
import com.styl.pacific.payment.shared.http.sessions.response.offline.OfflinePaymentSessionParamsResponse;
import com.styl.pacific.payment.shared.http.sessions.response.stripe.StripePaymentSessionDataResponse;
import com.styl.pacific.payment.shared.http.sessions.response.stripe.StripeWebPaymentSessionParamsResponse;
import com.styl.pacific.payment.shared.http.sessions.response.stripeconnect.StripeConnectPaymentSessionDataResponse;
import com.styl.pacific.payment.shared.http.sessions.response.stripeconnect.StripeConnectPaymentSessionParamsResponse;
import com.styl.pacific.payment.shared.http.settlement.request.nets.NetsPaymentSettlementSessionRequest;
import com.styl.pacific.payment.spi.processors.methods.valueobject.SimplePaymentMethodConfiguration;
import com.styl.pacific.payment.spi.processors.sessions.request.PaymentSessionData;
import com.styl.pacific.payment.spi.processors.sessions.valueobject.PaymentSessionParams;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.commons.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = IntegrationTestConfiguration.class)
class PaymentSessionControllerIntegrationTest extends PaymentIntegrationTestContainer {

	@MockitoBean
	private StripeApiClient stripeApiClient;

	@MockitoBean
	private StripeConnectApiClient stripeConnectApiClient;

	@MockitoBean
	private TenantClient tenantClient;

	@Autowired
	private ObjectMapper objectMapper;

	@Autowired
	private PaymentMethodCommandService methodCommandService;

	@Autowired
	private PaymentSessionCommandService sessionCommandService;

	@Autowired
	private PaymentSessionQueryService sessionQueryService;

	@Autowired
	private PaymentSettlementCommandService settlementCommandService;

	@Autowired
	private PaymentMethodQueryService queryService;

	@Autowired
	private PaymentConnectedAccountCommandService paymentConnectedAccountCommandService;

	@MockitoSpyBean
	private PaymentSessionRepository paymentSessionRepository;

	private PaymentMethodInitIntegrationSupporter paymentMethodSupporter;

	private PaymentSessionInitIntegrationSupporter paymentSessionSupporter;

	private PaymentConnectedIntegrationSupporter connectedIntegrationSupporter;

	@BeforeEach
	void setUp() {
		paymentMethodSupporter = new PaymentMethodInitIntegrationSupporter(tenantClient, methodCommandService);
		paymentSessionSupporter = new PaymentSessionInitIntegrationSupporter(stripeApiClient, stripeConnectApiClient,
				objectMapper, sessionCommandService);

		connectedIntegrationSupporter = new PaymentConnectedIntegrationSupporter(methodCommandService,
				paymentConnectedAccountCommandService, stripeConnectApiClient);
	}

	@Test
	void testCreateStripePaymentSessionSuccessfullyWhenValidRequest() {
		// Arrange
		final var tenantId = new TenantId(10_000L);
		final var now = Instant.now();

		final var stripePaymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Stripe Payment Method 10000")
				.processorId(PaymentProcessorId.STRIPE_WEB_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.CUSTOMER_PORTAL))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Stripe Payment Description 10000")
				.paymentInstruction("Stripe Payment Instruction 10000")
				.surchargeRate(BigDecimal.valueOf(0.1))
				.fixedSurcharge(10L)
				.currencyCode("SGD")
				.surchargeTitle("Stripe Payment Title 10000")
				.processorConfig(SimplePaymentMethodConfiguration.builder()
						.configs(objectMapper.convertValue(StripePaymentMethodConfigurationRequest.builder()
								.apiKey("API KEY 1234")
								.build(), new TypeReference<>() {
								}))
						.build())
				.build());

		final var stripeParams = StripeWebPaymentSessionParamsRequest.builder()
				.cancelRedirectURL("https://www.cancel.org/current")
				.successRedirectURL("https://www.success.org/current")
				.build();
		final var request = CreatePaymentSessionRequest.builder()
				.paymentMethodId(stripePaymentMethod.getId()
						.getValue())
				.description("Payment Order #123456")
				.paymentReference("Payment Reference 01")
				.transactionType(PaymentTransactionType.PURCHASE)
				.amount(1367L)
				.netAmount(1234L)
				.fee(133L)
				.currencyCode("SGD")
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.systemSource("System Source 01")
				.processorParams(stripeParams)
				.build();

		when(stripeApiClient.createSession(any(StripePaymentMethodConfiguration.class), any(SessionCreateParams.class)))
				.thenReturn(PaymentSessionData.builder()
						.expiredAt(now)
						.createdAt(now)
						.data(objectMapper.convertValue(StripePaymentSessionProcessorData.builder()
								.stripeSessionId("Session ID 1234")
								.paymentUrl("https://checkout.stripe.com/c/pay/cs_test")
								.build(), new TypeReference<>() {
								}))
						.build());

		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId("1")
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.BACK_OFFICE)
				.name("Name")
				.exp(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iat(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.familyName("Name")
				.givenName("Name")
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/payment/sessions")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isCreated()
				.expectBody(PaymentSessionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(stripePaymentMethod.getId()
							.getValue()
							.toString(), actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentSessionStatus.PENDING, actual.getStatus());
					Assertions.assertEquals(PaymentProcessorId.STRIPE_WEB_PAYMENT, actual.getPaymentProcessorId());
					Assertions.assertEquals("Payment Reference 01", actual.getPaymentReference());
					Assertions.assertEquals(userTokenClaim.getUserId(), actual.getCustomerId());
					Assertions.assertEquals("<EMAIL>", actual.getCustomerEmail());
					Assertions.assertEquals("Customer Name 001", actual.getCustomerName());
					Assertions.assertEquals("SGD", actual.getCurrencyCode());
					Assertions.assertEquals(request.getAmount(), actual.getAmount());
					Assertions.assertEquals(request.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(request.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(stripePaymentMethod.getFixedSurcharge(), actual.getAppliedFixedSurcharge());
					Assertions.assertEquals(0, stripePaymentMethod.getSurchargeRate()
							.compareTo(new BigDecimal(actual.getAppliedSurchargeRate())));
					Assertions.assertEquals(request.getDescription(), actual.getDescription());

					final var actualProcessorParams = (StripeWebPaymentSessionParamsResponse) actual
							.getProcessorParams();
					Assertions.assertNotNull(actualProcessorParams);
					Assertions.assertEquals(stripeParams.getSuccessRedirectURL(), actualProcessorParams
							.getSuccessRedirectURL());
					Assertions.assertEquals(stripeParams.getCancelRedirectURL(), actualProcessorParams
							.getCancelRedirectURL());
					Assertions.assertEquals(PaymentProcessorId.STRIPE_WEB_PAYMENT, actualProcessorParams
							.getProcessorId());

					final var actualProcessorData = (StripePaymentSessionDataResponse) actual.getSessionData();
					Assertions.assertNotNull(actualProcessorData);
					Assertions.assertEquals("Session ID 1234", actualProcessorData.getStripeSessionId());
					Assertions.assertEquals("https://checkout.stripe.com/c/pay/cs_test", actualProcessorData
							.getPaymentUrl());
				});

	}

	@Test
	void testCreateStripePaymentSessionSuccessfullyWithoutUserWhenValidRequest() {
		// Arrange
		final var tenantId = new TenantId(10_000L);
		final var now = Instant.now();

		final var stripePaymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Stripe Payment Method 10000")
				.processorId(PaymentProcessorId.STRIPE_WEB_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.CUSTOMER_PORTAL))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Stripe Payment Description 10000")
				.paymentInstruction("Stripe Payment Instruction 10000")
				.surchargeRate(BigDecimal.valueOf(0.1))
				.fixedSurcharge(10L)
				.currencyCode("SGD")
				.surchargeTitle("Stripe Payment Title 10000")
				.processorConfig(SimplePaymentMethodConfiguration.builder()
						.configs(objectMapper.convertValue(StripePaymentMethodConfigurationRequest.builder()
								.apiKey("API KEY 1234")
								.build(), new TypeReference<>() {
								}))
						.build())
				.build());

		final var stripeParams = StripeWebPaymentSessionParamsRequest.builder()
				.cancelRedirectURL("https://www.cancel.org/current")
				.successRedirectURL("https://www.success.org/current")
				.build();
		final var request = CreatePaymentSessionRequest.builder()
				.paymentMethodId(stripePaymentMethod.getId()
						.getValue())
				.paymentReference("Payment Reference 01")
				.transactionType(PaymentTransactionType.PURCHASE)
				.amount(1367L)
				.netAmount(1234L)
				.fee(133L)
				.description("Payment Order #123456")
				.currencyCode("SGD")
				.customerId("1")
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.merchantName("Merchant Name")
				.systemSource("System Source 01")
				//				.expiredInMilliseconds(Duration.ofMinutes(16)
				//						.toMillis())
				.processorParams(stripeParams)
				.build();

		when(stripeApiClient.createSession(any(StripePaymentMethodConfiguration.class), any(SessionCreateParams.class)))
				.thenReturn(PaymentSessionData.builder()
						.expiredAt(now)
						.createdAt(now)
						.data(objectMapper.convertValue(StripePaymentSessionProcessorData.builder()
								.stripeSessionId("Session ID 1234")
								.paymentUrl("https://checkout.stripe.com/c/pay/cs_test")
								.build(), new TypeReference<>() {
								}))
						.build());

		// Act & Assert
		webClient.post()
				.uri("/api/payment/sessions")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isCreated()
				.expectBody(PaymentSessionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(stripePaymentMethod.getId()
							.getValue()
							.toString(), actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentSessionStatus.PENDING, actual.getStatus());
					Assertions.assertEquals(PaymentProcessorId.STRIPE_WEB_PAYMENT, actual.getPaymentProcessorId());
					Assertions.assertEquals("Payment Reference 01", actual.getPaymentReference());
					Assertions.assertEquals("1", actual.getCustomerId());
					Assertions.assertEquals("<EMAIL>", actual.getCustomerEmail());
					Assertions.assertEquals("Customer Name 001", actual.getCustomerName());
					Assertions.assertEquals("SGD", actual.getCurrencyCode());
					Assertions.assertEquals(request.getAmount(), actual.getAmount());
					Assertions.assertEquals(request.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(request.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals("Merchant Name", actual.getMerchantName());
					Assertions.assertEquals(stripePaymentMethod.getFixedSurcharge(), actual.getAppliedFixedSurcharge());
					Assertions.assertEquals(0, stripePaymentMethod.getSurchargeRate()
							.compareTo(new BigDecimal(actual.getAppliedSurchargeRate())));
					Assertions.assertEquals(request.getDescription(), actual.getDescription());

					final var actualProcessorParams = (StripeWebPaymentSessionParamsResponse) actual
							.getProcessorParams();
					Assertions.assertNotNull(actualProcessorParams);
					Assertions.assertEquals(stripeParams.getSuccessRedirectURL(), actualProcessorParams
							.getSuccessRedirectURL());
					Assertions.assertEquals(stripeParams.getCancelRedirectURL(), actualProcessorParams
							.getCancelRedirectURL());
					Assertions.assertEquals(PaymentProcessorId.STRIPE_WEB_PAYMENT, actualProcessorParams
							.getProcessorId());

					final var actualProcessorData = (StripePaymentSessionDataResponse) actual.getSessionData();
					Assertions.assertNotNull(actualProcessorData);
					Assertions.assertEquals("Session ID 1234", actualProcessorData.getStripeSessionId());
					Assertions.assertEquals("https://checkout.stripe.com/c/pay/cs_test", actualProcessorData
							.getPaymentUrl());
				});

	}

	@Test
	void testCreateStripePaymentSessionSuccessfullyWhenZeroFeeRequest() {
		// Arrange
		final var tenantId = new TenantId(10_000L);
		final var now = Instant.now();

		final var stripePaymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Stripe Payment Method 10000")
				.processorId(PaymentProcessorId.STRIPE_WEB_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.CUSTOMER_PORTAL))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Stripe Payment Description 10000")
				.paymentInstruction("Stripe Payment Instruction 10000")
				.currencyCode("SGD")
				.surchargeTitle("Stripe Payment Title 10000")
				.processorConfig(SimplePaymentMethodConfiguration.builder()
						.configs(objectMapper.convertValue(StripePaymentMethodConfigurationRequest.builder()
								.apiKey("API KEY 1234")
								.build(), new TypeReference<>() {
								}))
						.build())
				.build());

		final var stripeParams = StripeWebPaymentSessionParamsRequest.builder()
				.cancelRedirectURL("https://www.cancel.org/current")
				.successRedirectURL("https://www.success.org/current")
				.build();
		final var request = CreatePaymentSessionRequest.builder()
				.paymentMethodId(stripePaymentMethod.getId()
						.getValue())
				.paymentReference("Payment Reference 01")
				.transactionType(PaymentTransactionType.PURCHASE)
				.amount(1367L)
				.netAmount(1367L)
				.fee(0L)
				.currencyCode("SGD")
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.systemSource("System Source 01")
				.merchantName("Merchant Name")
				.description("Payment Order #12345")
				.processorParams(stripeParams)
				.build();

		when(stripeApiClient.createSession(any(StripePaymentMethodConfiguration.class), any(SessionCreateParams.class)))
				.thenReturn(PaymentSessionData.builder()
						.expiredAt(now)
						.createdAt(now)
						.data(objectMapper.convertValue(StripePaymentSessionProcessorData.builder()
								.stripeSessionId("Session ID 1234")
								.paymentUrl("https://checkout.stripe.com/c/pay/cs_test")
								.build(), new TypeReference<>() {
								}))
						.build());

		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId("1")
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.BACK_OFFICE)
				.name("Name")
				.exp(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iat(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.familyName("Name")
				.givenName("Name")
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/payment/sessions")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isCreated()
				.expectBody(PaymentSessionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(stripePaymentMethod.getId()
							.getValue()
							.toString(), actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentSessionStatus.PENDING, actual.getStatus());
					Assertions.assertEquals(PaymentProcessorId.STRIPE_WEB_PAYMENT, actual.getPaymentProcessorId());
					Assertions.assertEquals("Payment Reference 01", actual.getPaymentReference());
					Assertions.assertEquals(userTokenClaim.getUserId(), actual.getCustomerId());
					Assertions.assertEquals("<EMAIL>", actual.getCustomerEmail());
					Assertions.assertEquals("Customer Name 001", actual.getCustomerName());
					Assertions.assertEquals("SGD", actual.getCurrencyCode());
					Assertions.assertEquals(request.getAmount(), actual.getAmount());
					Assertions.assertEquals(request.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(request.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(request.getDescription(), actual.getDescription());
					Assertions.assertEquals("Merchant Name", actual.getMerchantName());

					final var actualProcessorParams = (StripeWebPaymentSessionParamsResponse) actual
							.getProcessorParams();
					Assertions.assertNotNull(actualProcessorParams);
					Assertions.assertEquals(stripeParams.getSuccessRedirectURL(), actualProcessorParams
							.getSuccessRedirectURL());
					Assertions.assertEquals(stripeParams.getCancelRedirectURL(), actualProcessorParams
							.getCancelRedirectURL());
					Assertions.assertEquals(PaymentProcessorId.STRIPE_WEB_PAYMENT, actualProcessorParams
							.getProcessorId());
					Assertions.assertEquals(900_000L, actualProcessorParams.getExpiredInMilliseconds());

					final var actualProcessorData = (StripePaymentSessionDataResponse) actual.getSessionData();
					Assertions.assertNotNull(actualProcessorData);
					Assertions.assertEquals("Session ID 1234", actualProcessorData.getStripeSessionId());
					Assertions.assertEquals("https://checkout.stripe.com/c/pay/cs_test", actualProcessorData
							.getPaymentUrl());
				});

	}

	@Test
	void testCreateCashPaymentSessionSuccessfullyWhenValidRequest() {
		// Arrange
		final var tenantId = new TenantId(10_000L);
		final var now = Instant.now();

		final var paymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Cash Payment Method 10000")
				.processorId(PaymentProcessorId.CASH_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.POS))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Cash Payment Description 10000")
				.paymentInstruction("Cash Payment Instruction 10000")
				.surchargeRate(BigDecimal.valueOf(0.1))
				.fixedSurcharge(10L)
				.currencyCode("SGD")
				.surchargeTitle("Cash Payment Title 10000")
				.build());

		final var request = CreatePaymentSessionRequest.builder()
				.paymentMethodId(paymentMethod.getId()
						.getValue())
				.description("Payment Order #123456")
				.paymentReference("Payment Reference 01")
				.transactionType(PaymentTransactionType.PURCHASE)
				.amount(1367L)
				.netAmount(1234L)
				.fee(133L)
				.currencyCode("SGD")
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.systemSource("System Source 01")
				.processorParams(CashPaymentSessionParamsRequest.builder()
						.expiredInMilliseconds(Duration.ofMinutes(10)
								.toMillis())
						.build())
				.build();

		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId("1")
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.BACK_OFFICE)
				.name("Name")
				.exp(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iat(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.familyName("Name")
				.givenName("Name")
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/payment/sessions")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isCreated()
				.expectBody(PaymentSessionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(paymentMethod.getId()
							.getValue()
							.toString(), actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentSessionStatus.PENDING, actual.getStatus());
					Assertions.assertEquals(PaymentProcessorId.CASH_PAYMENT, actual.getPaymentProcessorId());
					Assertions.assertEquals("Payment Reference 01", actual.getPaymentReference());
					Assertions.assertEquals(userTokenClaim.getUserId(), actual.getCustomerId());
					Assertions.assertEquals("<EMAIL>", actual.getCustomerEmail());
					Assertions.assertEquals("Customer Name 001", actual.getCustomerName());
					Assertions.assertEquals("SGD", actual.getCurrencyCode());
					Assertions.assertEquals(request.getAmount(), actual.getAmount());
					Assertions.assertEquals(request.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(request.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(paymentMethod.getFixedSurcharge(), actual.getAppliedFixedSurcharge());
					Assertions.assertEquals(0, paymentMethod.getSurchargeRate()
							.compareTo(new BigDecimal(actual.getAppliedSurchargeRate())));
					Assertions.assertEquals(request.getDescription(), actual.getDescription());

					Assertions.assertNull(actual.getSessionData());
					Assertions.assertNotNull(actual.getProcessorParams());

					final var params = (CashPaymentSessionParamsResponse) actual.getProcessorParams();
					Assertions.assertEquals(Duration.ofMinutes(10)
							.toMillis(), params.getExpiredInMilliseconds());
				});

	}

	@Test
	void testCreateCashPaymentSessionSuccessfullyWithoutUserWhenValidRequest() {
		// Arrange
		final var tenantId = new TenantId(10_000L);
		final var paymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Cash Payment Method 10000")
				.processorId(PaymentProcessorId.CASH_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.POS))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Cash Payment Description 10000")
				.paymentInstruction("Cash Payment Instruction 10000")
				.surchargeRate(BigDecimal.valueOf(0.1))
				.fixedSurcharge(10L)
				.currencyCode("SGD")
				.surchargeTitle("Cash Payment Title 10000")
				.build());

		final var request = CreatePaymentSessionRequest.builder()
				.paymentMethodId(paymentMethod.getId()
						.getValue())
				.description("Payment Order #123456")
				.paymentReference("Payment Reference 01")
				.transactionType(PaymentTransactionType.PURCHASE)
				.amount(1367L)
				.netAmount(1234L)
				.fee(133L)
				.currencyCode("SGD")
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.systemSource("System Source 01")
				.merchantName("Merchant Name")
				.processorParams(CashPaymentSessionParamsRequest.builder()
						.expiredInMilliseconds(Duration.ofMinutes(10)
								.toMillis())
						.build())
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/payment/sessions")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isCreated()
				.expectBody(PaymentSessionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(paymentMethod.getId()
							.getValue()
							.toString(), actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentSessionStatus.PENDING, actual.getStatus());
					Assertions.assertEquals(PaymentProcessorId.CASH_PAYMENT, actual.getPaymentProcessorId());
					Assertions.assertEquals("Payment Reference 01", actual.getPaymentReference());
					Assertions.assertEquals("<EMAIL>", actual.getCustomerEmail());
					Assertions.assertEquals("Customer Name 001", actual.getCustomerName());
					Assertions.assertEquals("SGD", actual.getCurrencyCode());
					Assertions.assertEquals(request.getAmount(), actual.getAmount());
					Assertions.assertEquals(request.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(request.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals("Merchant Name", actual.getMerchantName());
					Assertions.assertEquals(paymentMethod.getFixedSurcharge(), actual.getAppliedFixedSurcharge());
					Assertions.assertEquals(0, paymentMethod.getSurchargeRate()
							.compareTo(new BigDecimal(actual.getAppliedSurchargeRate())));
					Assertions.assertEquals(request.getDescription(), actual.getDescription());

					Assertions.assertNull(actual.getSessionData());
					Assertions.assertNotNull(actual.getProcessorParams());

					final var params = (CashPaymentSessionParamsResponse) actual.getProcessorParams();
					Assertions.assertEquals(Duration.ofMinutes(10)
							.toMillis(), params.getExpiredInMilliseconds());
				});

	}

	@Test
	void testCreateCashPaymentSessionSuccessfullyWhenZeroFeeRequest() {
		// Arrange

		final var tenantId = new TenantId(10_000L);
		final var paymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Cash Payment Method 10000")
				.processorId(PaymentProcessorId.CASH_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.POS))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Cash Payment Description 10000")
				.paymentInstruction("Cash Payment Instruction 10000")
				.currencyCode("SGD")
				.surchargeTitle("Cash Payment Title 10000")
				.build());

		final var request = CreatePaymentSessionRequest.builder()
				.paymentMethodId(paymentMethod.getId()
						.getValue())
				.description("Payment Order #123456")
				.paymentReference("Payment Reference 01")
				.transactionType(PaymentTransactionType.PURCHASE)
				.amount(1000L)
				.netAmount(1000L)
				.fee(0L)
				.currencyCode("SGD")
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.merchantName("Merchant Name")
				.systemSource("System Source 01")
				.processorParams(CashPaymentSessionParamsRequest.builder()
						.expiredInMilliseconds(Duration.ofMinutes(10)
								.toMillis())
						.build())
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/payment/sessions")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isCreated()
				.expectBody(PaymentSessionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(paymentMethod.getId()
							.getValue()
							.toString(), actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentSessionStatus.PENDING, actual.getStatus());
					Assertions.assertEquals(PaymentProcessorId.CASH_PAYMENT, actual.getPaymentProcessorId());
					Assertions.assertEquals("Payment Reference 01", actual.getPaymentReference());
					Assertions.assertEquals("<EMAIL>", actual.getCustomerEmail());
					Assertions.assertEquals("Customer Name 001", actual.getCustomerName());
					Assertions.assertEquals("SGD", actual.getCurrencyCode());
					Assertions.assertEquals(request.getAmount(), actual.getAmount());
					Assertions.assertEquals(request.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(request.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(request.getDescription(), actual.getDescription());
					Assertions.assertEquals("Merchant Name", actual.getMerchantName());

					Assertions.assertNull(actual.getSessionData());
					Assertions.assertNotNull(actual.getProcessorParams());

					final var params = (CashPaymentSessionParamsResponse) actual.getProcessorParams();
					Assertions.assertEquals(Duration.ofMinutes(10)
							.toMillis(), params.getExpiredInMilliseconds());
				});

	}

	@Test
	void testCreateCashPaymentSessionSuccessfullyWithAcceptedDeviationWhenValidRequest() {
		// Arrange

		final var tenantId = new TenantId(10_000L);
		final var paymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Cash Payment Method 10000")
				.processorId(PaymentProcessorId.CASH_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.POS))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Cash Payment Description 10000")
				.paymentInstruction("Cash Payment Instruction 10000")
				.currencyCode("SGD")
				.surchargeRate(BigDecimal.valueOf(0.1))
				.fixedSurcharge(10L)
				.surchargeTitle("Cash Payment Title 10000")
				.build());

		final var request = CreatePaymentSessionRequest.builder()
				.paymentMethodId(paymentMethod.getId()
						.getValue())
				.description("Payment Order #123456")
				.paymentReference("Payment Reference 01")
				.transactionType(PaymentTransactionType.PURCHASE)
				.amount(1108L)
				.netAmount(1000L)
				.fee(112L)
				.currencyCode("SGD")
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.merchantName("Merchant Name")
				.systemSource("System Source 01")
				.processorParams(CashPaymentSessionParamsRequest.builder()
						.expiredInMilliseconds(Duration.ofMinutes(10)
								.toMillis())
						.build())
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/payment/sessions")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isCreated()
				.expectBody(PaymentSessionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(paymentMethod.getId()
							.getValue()
							.toString(), actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentSessionStatus.PENDING, actual.getStatus());
					Assertions.assertEquals(PaymentProcessorId.CASH_PAYMENT, actual.getPaymentProcessorId());
					Assertions.assertEquals("Payment Reference 01", actual.getPaymentReference());
					Assertions.assertEquals("<EMAIL>", actual.getCustomerEmail());
					Assertions.assertEquals("Customer Name 001", actual.getCustomerName());
					Assertions.assertEquals("SGD", actual.getCurrencyCode());
					Assertions.assertEquals(request.getAmount(), actual.getAmount());
					Assertions.assertEquals(request.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(request.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(request.getDescription(), actual.getDescription());
					Assertions.assertEquals("Merchant Name", actual.getMerchantName());

					Assertions.assertNull(actual.getSessionData());
					Assertions.assertNotNull(actual.getProcessorParams());

					final var params = (CashPaymentSessionParamsResponse) actual.getProcessorParams();
					Assertions.assertEquals(Duration.ofMinutes(10)
							.toMillis(), params.getExpiredInMilliseconds());
				});

	}

	@Test
	void testCreateOfflinePaymentSessionSuccessfullyWhenValidRequest() {
		// Arrange
		final var tenantId = new TenantId(10_000L);

		final var paymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Offline Payment Method 10000")
				.processorId(PaymentProcessorId.OFFLINE_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.POS))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Offline Payment Description 10000")
				.paymentInstruction("Offline Payment Instruction 10000")
				.surchargeRate(BigDecimal.valueOf(0.1))
				.fixedSurcharge(10L)
				.currencyCode("SGD")
				.surchargeTitle("Offline Payment Title 10000")
				.build());

		final var request = CreatePaymentSessionRequest.builder()
				.paymentMethodId(paymentMethod.getId()
						.getValue())
				.description("Payment Order #123456")
				.paymentReference("Payment Reference 01")
				.transactionType(PaymentTransactionType.PURCHASE)
				.amount(1367L)
				.netAmount(1234L)
				.fee(133L)
				.currencyCode("SGD")
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.systemSource("System Source 01")
				.processorParams(OfflineWebPaymentSessionParamsRequest.builder()
						.expiredInMilliseconds(Duration.ofMinutes(10)
								.toMillis())
						.build())
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/payment/sessions")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isCreated()
				.expectBody(PaymentSessionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(paymentMethod.getId()
							.getValue()
							.toString(), actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentSessionStatus.PENDING, actual.getStatus());
					Assertions.assertEquals(PaymentProcessorId.OFFLINE_PAYMENT, actual.getPaymentProcessorId());
					Assertions.assertEquals("Payment Reference 01", actual.getPaymentReference());
					Assertions.assertEquals("<EMAIL>", actual.getCustomerEmail());
					Assertions.assertEquals("Customer Name 001", actual.getCustomerName());
					Assertions.assertEquals("SGD", actual.getCurrencyCode());
					Assertions.assertEquals(request.getAmount(), actual.getAmount());
					Assertions.assertEquals(request.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(request.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(paymentMethod.getFixedSurcharge(), actual.getAppliedFixedSurcharge());
					Assertions.assertEquals(0, paymentMethod.getSurchargeRate()
							.compareTo(new BigDecimal(actual.getAppliedSurchargeRate())));
					Assertions.assertEquals(request.getDescription(), actual.getDescription());

					Assertions.assertNull(actual.getSessionData());
					Assertions.assertNotNull(actual.getProcessorParams());
					final var params = (OfflinePaymentSessionParamsResponse) actual.getProcessorParams();
					Assertions.assertEquals(Duration.ofMinutes(10)
							.toMillis(), params.getExpiredInMilliseconds());
				});

	}

	@Test
	void testCreateOfflinePaymentSessionSuccessfullyWhenZeroFeeRequest() {
		// Arrange

		final var tenantId = new TenantId(10_000L);
		final var paymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Cash Payment Method 10000")
				.processorId(PaymentProcessorId.OFFLINE_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.POS))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Cash Payment Description 10000")
				.paymentInstruction("Cash Payment Instruction 10000")
				.currencyCode("SGD")
				.surchargeTitle("Cash Payment Title 10000")
				.build());

		final var request = CreatePaymentSessionRequest.builder()
				.paymentMethodId(paymentMethod.getId()
						.getValue())
				.description("Payment Order #123456")
				.paymentReference("Payment Reference 01")
				.transactionType(PaymentTransactionType.PURCHASE)
				.amount(1000L)
				.netAmount(1000L)
				.fee(0L)
				.currencyCode("SGD")
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.merchantName("Merchant Name")
				.systemSource("System Source 01")
				.processorParams(OfflineWebPaymentSessionParamsRequest.builder()
						.expiredInMilliseconds(Duration.ofMinutes(10)
								.toMillis())
						.build())
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/payment/sessions")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isCreated()
				.expectBody(PaymentSessionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(paymentMethod.getId()
							.getValue()
							.toString(), actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentSessionStatus.PENDING, actual.getStatus());
					Assertions.assertEquals(PaymentProcessorId.OFFLINE_PAYMENT, actual.getPaymentProcessorId());
					Assertions.assertEquals("Payment Reference 01", actual.getPaymentReference());
					Assertions.assertEquals("<EMAIL>", actual.getCustomerEmail());
					Assertions.assertEquals("Customer Name 001", actual.getCustomerName());
					Assertions.assertEquals("SGD", actual.getCurrencyCode());
					Assertions.assertEquals(request.getAmount(), actual.getAmount());
					Assertions.assertEquals(request.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(request.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(request.getDescription(), actual.getDescription());
					Assertions.assertEquals("Merchant Name", actual.getMerchantName());

					Assertions.assertNull(actual.getSessionData());
					Assertions.assertNotNull(actual.getProcessorParams());

					final var params = (OfflinePaymentSessionParamsResponse) actual.getProcessorParams();
					Assertions.assertEquals(Duration.ofMinutes(10)
							.toMillis(), params.getExpiredInMilliseconds());
				});

	}

	@Test
	void testCreateEWalletPaymentSessionSuccessfullyWhenValidRequest() {
		// Arrange
		final var tenantId = new TenantId(10_000L);

		final var paymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("EWallet Payment Method 10000")
				.processorId(PaymentProcessorId.E_WALLET_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.POS))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("EWallet Payment Description 10000")
				.paymentInstruction("EWallet Payment Instruction 10000")
				.surchargeRate(BigDecimal.valueOf(0.1))
				.fixedSurcharge(10L)
				.currencyCode("SGD")
				.surchargeTitle("EWallet Payment Title 10000")
				.build());

		final var request = CreatePaymentSessionRequest.builder()
				.paymentMethodId(paymentMethod.getId()
						.getValue())
				.description("Payment Order #123456")
				.paymentReference("Payment Reference 01")
				.transactionType(PaymentTransactionType.PURCHASE)
				.amount(1367L)
				.netAmount(1234L)
				.fee(133L)
				.currencyCode("SGD")
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.systemSource("System Source 01")
				.processorParams(EWalletPaymentSessionParamsRequest.builder()
						.expiredInMilliseconds(Duration.ofMinutes(10)
								.toMillis())
						.build())
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/payment/sessions")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isCreated()
				.expectBody(PaymentSessionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(paymentMethod.getId()
							.getValue()
							.toString(), actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentSessionStatus.PENDING, actual.getStatus());
					Assertions.assertEquals(PaymentProcessorId.E_WALLET_PAYMENT, actual.getPaymentProcessorId());
					Assertions.assertEquals("Payment Reference 01", actual.getPaymentReference());
					Assertions.assertEquals("<EMAIL>", actual.getCustomerEmail());
					Assertions.assertEquals("Customer Name 001", actual.getCustomerName());
					Assertions.assertEquals("SGD", actual.getCurrencyCode());
					Assertions.assertEquals(request.getAmount(), actual.getAmount());
					Assertions.assertEquals(request.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(request.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(paymentMethod.getFixedSurcharge(), actual.getAppliedFixedSurcharge());
					Assertions.assertEquals(0, paymentMethod.getSurchargeRate()
							.compareTo(new BigDecimal(actual.getAppliedSurchargeRate())));
					Assertions.assertEquals(request.getDescription(), actual.getDescription());

					Assertions.assertNull(actual.getSessionData());
					Assertions.assertNotNull(actual.getProcessorParams());
					final var params = (EWalletPaymentSessionParamsResponse) actual.getProcessorParams();
					Assertions.assertEquals(Duration.ofMinutes(10)
							.toMillis(), params.getExpiredInMilliseconds());
				});

	}

	@Test
	void testCreateEWalletPaymentSessionSuccessfullyWhenZeroFeeRequest() {
		// Arrange

		final var tenantId = new TenantId(10_000L);
		final var paymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("EWallet Payment Method 10000")
				.processorId(PaymentProcessorId.E_WALLET_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.POS))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("EWallet Payment Description 10000")
				.paymentInstruction("EWallet Payment Instruction 10000")
				.currencyCode("SGD")
				.surchargeTitle("EWallet Payment Title 10000")
				.build());

		final var request = CreatePaymentSessionRequest.builder()
				.paymentMethodId(paymentMethod.getId()
						.getValue())
				.description("Payment Order #123456")
				.paymentReference("Payment Reference 01")
				.transactionType(PaymentTransactionType.PURCHASE)
				.amount(1000L)
				.netAmount(1000L)
				.fee(0L)
				.currencyCode("SGD")
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.merchantName("Merchant Name")
				.systemSource("System Source 01")
				.processorParams(EWalletPaymentSessionParamsRequest.builder()
						.expiredInMilliseconds(Duration.ofMinutes(10)
								.toMillis())
						.build())
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/payment/sessions")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isCreated()
				.expectBody(PaymentSessionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(paymentMethod.getId()
							.getValue()
							.toString(), actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentSessionStatus.PENDING, actual.getStatus());
					Assertions.assertEquals(PaymentProcessorId.E_WALLET_PAYMENT, actual.getPaymentProcessorId());
					Assertions.assertEquals("Payment Reference 01", actual.getPaymentReference());
					Assertions.assertEquals("<EMAIL>", actual.getCustomerEmail());
					Assertions.assertEquals("Customer Name 001", actual.getCustomerName());
					Assertions.assertEquals("SGD", actual.getCurrencyCode());
					Assertions.assertEquals(request.getAmount(), actual.getAmount());
					Assertions.assertEquals(request.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(request.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(request.getDescription(), actual.getDescription());
					Assertions.assertEquals("Merchant Name", actual.getMerchantName());

					Assertions.assertNull(actual.getSessionData());
					Assertions.assertNotNull(actual.getProcessorParams());

					final var params = (EWalletPaymentSessionParamsResponse) actual.getProcessorParams();
					Assertions.assertEquals(Duration.ofMinutes(10)
							.toMillis(), params.getExpiredInMilliseconds());
				});

	}

	@Test
	void testCreateNetsPaymentSessionSuccessfullyWhenValidRequest() {
		// Arrange
		final var tenantId = new TenantId(10_000L);
		final var now = Instant.now();

		final var paymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("NETS Payment Method 10000")
				.processorId(PaymentProcessorId.NETS_TERMINAL_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.POS))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("NETS Payment Description 10000")
				.paymentInstruction("NETS Payment Instruction 10000")
				.surchargeRate(BigDecimal.valueOf(0.1))
				.fixedSurcharge(10L)
				.currencyCode("SGD")
				.surchargeTitle("NETS Payment Title 10000")
				.processorConfig(SimplePaymentMethodConfiguration.builder()
						.configs(objectMapper.convertValue(NetsTerminalPaymentMethodConfiguration.builder()
								.netsFamilyCardType(NetsFamilyCardType.CREDIT_CARD)
								.build(), new TypeReference<>() {
								}))
						.build())
				.build());

		final var request = CreatePaymentSessionRequest.builder()
				.paymentMethodId(paymentMethod.getId()
						.getValue())
				.description("Payment Order #123456")
				.paymentReference("Payment Reference 01")
				.transactionType(PaymentTransactionType.PURCHASE)
				.amount(1367L)
				.netAmount(1234L)
				.fee(133L)
				.currencyCode("SGD")
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.systemSource("System Source 01")
				.processorParams(NetsPaymentSessionParamsRequest.builder()
						.expiredInMilliseconds(Duration.ofMinutes(10)
								.toMillis())
						.build())
				.build();

		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId("1")
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.BACK_OFFICE)
				.name("Name")
				.exp(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iat(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.familyName("Name")
				.givenName("Name")
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/payment/sessions")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isCreated()
				.expectBody(PaymentSessionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(paymentMethod.getId()
							.getValue()
							.toString(), actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentSessionStatus.PENDING, actual.getStatus());
					Assertions.assertEquals(PaymentProcessorId.NETS_TERMINAL_PAYMENT, actual.getPaymentProcessorId());
					Assertions.assertEquals("Payment Reference 01", actual.getPaymentReference());
					Assertions.assertEquals(userTokenClaim.getUserId(), actual.getCustomerId());
					Assertions.assertEquals("<EMAIL>", actual.getCustomerEmail());
					Assertions.assertEquals("Customer Name 001", actual.getCustomerName());
					Assertions.assertEquals("SGD", actual.getCurrencyCode());
					Assertions.assertEquals(request.getAmount(), actual.getAmount());
					Assertions.assertEquals(request.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(request.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(paymentMethod.getFixedSurcharge(), actual.getAppliedFixedSurcharge());
					Assertions.assertEquals(0, paymentMethod.getSurchargeRate()
							.compareTo(new BigDecimal(actual.getAppliedSurchargeRate())));
					Assertions.assertEquals(request.getDescription(), actual.getDescription());

					Assertions.assertNull(actual.getSessionData());
					Assertions.assertNotNull(actual.getProcessorParams());

					final var params = (NetsPaymentSessionParamsResponse) actual.getProcessorParams();
					Assertions.assertEquals(Duration.ofMinutes(10)
							.toMillis(), params.getExpiredInMilliseconds());
				});

	}

	@Test
	void testCreateStripeConnectPaymentSessionSuccessfullyWhenValidRequest() {
		// Arrange
		final var tenantId = new TenantId(10_000L);
		final var now = Instant.now();

		final var stripePaymentMethod = connectedIntegrationSupporter.connect(paymentMethodSupporter.initPaymentMethod(
				tenantId, UpsertPaymentMethodCommand.builder()
						.displayName("Stripe Connect Payment Method 10000")
						.processorId(PaymentProcessorId.STRIPE_CONNECT_STANDARD_WEB_PAYMENT)
						.acceptedApplications(Set.of(AcceptedApplication.CUSTOMER_PORTAL))
						.iconPath("http://localhost/image.jpg")
						.description("Stripe Payment Description 10000")
						.paymentInstruction("Stripe Payment Instruction 10000")
						.surchargeRate(BigDecimal.valueOf(0.1))
						.fixedSurcharge(10L)
						.currencyCode("SGD")
						.surchargeTitle("Stripe Payment Title 10000")
						.build()));

		final var stripeParams = StripeConnectWebPaymentSessionParamsRequest.builder()
				.cancelRedirectURL("https://www.cancel.org/current")
				.successRedirectURL("https://www.success.org/current")
				.build();
		final var request = CreatePaymentSessionRequest.builder()
				.paymentMethodId(stripePaymentMethod.getId()
						.getValue())
				.description("Payment Order #123456")
				.paymentReference("Payment Reference 01")
				.transactionType(PaymentTransactionType.PURCHASE)
				.amount(1367L)
				.netAmount(1234L)
				.fee(133L)
				.currencyCode("SGD")
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.systemSource("System Source 01")
				.processorParams(stripeParams)
				.build();

		when(stripeConnectApiClient.createSession(any(StripeConnectPaymentMethodConfiguration.class), any(
				SessionCreateParams.class))).thenReturn(PaymentSessionData.builder()
						.expiredAt(now)
						.createdAt(now)
						.data(objectMapper.convertValue(StripePaymentSessionProcessorData.builder()
								.stripeSessionId("Session ID 1234")
								.paymentUrl("https://checkout.stripe.com/c/pay/cs_test")
								.build(), new TypeReference<>() {
								}))
						.build());

		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId("1")
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.CUSTOMER)
				.name("Name")
				.exp(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iat(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.familyName("Name")
				.givenName("Name")
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/payment/sessions")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isCreated()
				.expectBody(PaymentSessionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(stripePaymentMethod.getId()
							.getValue()
							.toString(), actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentSessionStatus.PENDING, actual.getStatus());
					Assertions.assertEquals(PaymentProcessorId.STRIPE_CONNECT_STANDARD_WEB_PAYMENT, actual
							.getPaymentProcessorId());
					Assertions.assertEquals("Payment Reference 01", actual.getPaymentReference());
					Assertions.assertEquals(userTokenClaim.getUserId(), actual.getCustomerId());
					Assertions.assertEquals("<EMAIL>", actual.getCustomerEmail());
					Assertions.assertEquals("Customer Name 001", actual.getCustomerName());
					Assertions.assertEquals("SGD", actual.getCurrencyCode());
					Assertions.assertEquals(request.getAmount(), actual.getAmount());
					Assertions.assertEquals(request.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(request.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(stripePaymentMethod.getFixedSurcharge(), actual.getAppliedFixedSurcharge());
					Assertions.assertEquals(0, stripePaymentMethod.getSurchargeRate()
							.compareTo(new BigDecimal(actual.getAppliedSurchargeRate())));
					Assertions.assertEquals(request.getDescription(), actual.getDescription());

					final var actualProcessorParams = (StripeConnectPaymentSessionParamsResponse) actual
							.getProcessorParams();
					Assertions.assertNotNull(actualProcessorParams);
					Assertions.assertEquals(stripeParams.getSuccessRedirectURL(), actualProcessorParams
							.getSuccessRedirectURL());
					Assertions.assertEquals(stripeParams.getCancelRedirectURL(), actualProcessorParams
							.getCancelRedirectURL());
					Assertions.assertEquals(PaymentProcessorId.STRIPE_CONNECT_STANDARD_WEB_PAYMENT,
							actualProcessorParams.getProcessorId());

					final var actualProcessorData = (StripeConnectPaymentSessionDataResponse) actual.getSessionData();
					Assertions.assertNotNull(actualProcessorData);
					Assertions.assertEquals("Session ID 1234", actualProcessorData.getStripeSessionId());
					Assertions.assertEquals("https://checkout.stripe.com/c/pay/cs_test", actualProcessorData
							.getPaymentUrl());
				});

	}

	@Test
	void testCreateStripeConnectPaymentSessionSuccessfullyWhenZeroFeeRequest() {
		// Arrange
		final var tenantId = new TenantId(10_000L);
		final var now = Instant.now();

		final var stripePaymentMethod = connectedIntegrationSupporter.connect(paymentMethodSupporter.initPaymentMethod(
				tenantId, UpsertPaymentMethodCommand.builder()
						.displayName("Stripe Connect Payment Method 10000")
						.processorId(PaymentProcessorId.STRIPE_CONNECT_STANDARD_WEB_PAYMENT)
						.acceptedApplications(Set.of(AcceptedApplication.CUSTOMER_PORTAL))
						.iconPath("http://localhost/image.jpg")
						.description("Stripe Payment Description 10000")
						.paymentInstruction("Stripe Payment Instruction 10000")
						.surchargeRate(BigDecimal.ZERO)
						.fixedSurcharge(0L)
						.currencyCode("SGD")
						.surchargeTitle("Stripe Payment Title 10000")
						.build()));

		final var stripeParams = StripeConnectWebPaymentSessionParamsRequest.builder()
				.cancelRedirectURL("https://www.cancel.org/current")
				.successRedirectURL("https://www.success.org/current")
				.build();
		final var request = CreatePaymentSessionRequest.builder()
				.paymentMethodId(stripePaymentMethod.getId()
						.getValue())
				.description("Payment Order #123456")
				.paymentReference("Payment Reference 01")
				.transactionType(PaymentTransactionType.PURCHASE)
				.amount(1367L)
				.netAmount(1367L)
				.fee(0L)
				.currencyCode("SGD")
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.systemSource("System Source 01")
				.processorParams(stripeParams)
				.build();

		when(stripeConnectApiClient.createSession(any(StripeConnectPaymentMethodConfiguration.class), any(
				SessionCreateParams.class))).thenReturn(PaymentSessionData.builder()
						.expiredAt(now)
						.createdAt(now)
						.data(objectMapper.convertValue(StripePaymentSessionProcessorData.builder()
								.stripeSessionId("Session ID 1234")
								.paymentUrl("https://checkout.stripe.com/c/pay/cs_test")
								.build(), new TypeReference<>() {
								}))
						.build());

		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId("1")
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.CUSTOMER)
				.name("Name")
				.exp(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iat(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.familyName("Name")
				.givenName("Name")
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/payment/sessions")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isCreated()
				.expectBody(PaymentSessionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(stripePaymentMethod.getId()
							.getValue()
							.toString(), actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentSessionStatus.PENDING, actual.getStatus());
					Assertions.assertEquals(PaymentProcessorId.STRIPE_CONNECT_STANDARD_WEB_PAYMENT, actual
							.getPaymentProcessorId());
					Assertions.assertEquals("Payment Reference 01", actual.getPaymentReference());
					Assertions.assertEquals(userTokenClaim.getUserId(), actual.getCustomerId());
					Assertions.assertEquals("<EMAIL>", actual.getCustomerEmail());
					Assertions.assertEquals("Customer Name 001", actual.getCustomerName());
					Assertions.assertEquals("SGD", actual.getCurrencyCode());
					Assertions.assertEquals(request.getAmount(), actual.getAmount());
					Assertions.assertEquals(request.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(request.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(stripePaymentMethod.getFixedSurcharge(), actual.getAppliedFixedSurcharge());
					Assertions.assertEquals(0, stripePaymentMethod.getSurchargeRate()
							.compareTo(new BigDecimal(actual.getAppliedSurchargeRate())));
					Assertions.assertEquals(request.getDescription(), actual.getDescription());

					final var actualProcessorParams = (StripeConnectPaymentSessionParamsResponse) actual
							.getProcessorParams();
					Assertions.assertNotNull(actualProcessorParams);
					Assertions.assertEquals(stripeParams.getSuccessRedirectURL(), actualProcessorParams
							.getSuccessRedirectURL());
					Assertions.assertEquals(stripeParams.getCancelRedirectURL(), actualProcessorParams
							.getCancelRedirectURL());
					Assertions.assertEquals(PaymentProcessorId.STRIPE_CONNECT_STANDARD_WEB_PAYMENT,
							actualProcessorParams.getProcessorId());

					final var actualProcessorData = (StripeConnectPaymentSessionDataResponse) actual.getSessionData();
					Assertions.assertNotNull(actualProcessorData);
					Assertions.assertEquals("Session ID 1234", actualProcessorData.getStripeSessionId());
					Assertions.assertEquals("https://checkout.stripe.com/c/pay/cs_test", actualProcessorData
							.getPaymentUrl());
				});

	}

	@Test
	void testThrowExceptionWhenCreateStripePaymentSessionByNotFoundPaymentMethod() {
		// Arrange
		final var tenantId = new TenantId(10_000L);
		final var now = Instant.now();

		final var stripeParams = StripeWebPaymentSessionParamsRequest.builder()
				.cancelRedirectURL("https://www.cancel.org/current")
				.successRedirectURL("https://www.success.org/current")
				.build();
		final var request = CreatePaymentSessionRequest.builder()
				.paymentMethodId(123456L)
				.description("Payment Order #12345")
				.paymentReference("Payment Reference 01")
				.transactionType(PaymentTransactionType.PURCHASE)
				.amount(1367L)
				.netAmount(1234L)
				.fee(133L)
				.currencyCode("SGD")
				.customerEmail("<EMAIL>")
				.systemSource("System Source 01")
				.merchantName("Merchant Name")
				//				.expiredInMilliseconds(Duration.ofMinutes(16)
				//						.toMillis())
				.processorParams(stripeParams)
				.build();

		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId("1")
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.BACK_OFFICE)
				.name("Name")
				.exp(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iat(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.familyName("Name")
				.givenName("Name")
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/payment/sessions")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isBadRequest()
				.expectBody(ErrorResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(actual.getDetails()
							.contains("Payment method not found"));
				});
	}

	@Test
	void testThrowExceptionWhenCreateStripePaymentSessionByNotActivePaymentMethod() {
		// Arrange
		final var tenantId = new TenantId(10_000L);
		final var now = Instant.now();

		final var stripePaymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Stripe Payment Method 10000")
				.processorId(PaymentProcessorId.STRIPE_WEB_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.CUSTOMER_PORTAL, AcceptedApplication.TOP_UP_KIOSK))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.FALSE)
				.description("Stripe Payment Description 10000")
				.paymentInstruction("Stripe Payment Instruction 10000")
				.currencyCode("SGD")
				.surchargeTitle("Stripe Payment Title 10000")
				.processorConfig(SimplePaymentMethodConfiguration.builder()
						.configs(objectMapper.convertValue(StripePaymentMethodConfigurationRequest.builder()
								.apiKey("API KEY 1234")
								.build(), new TypeReference<>() {
								}))
						.build())
				.build());

		final var stripeParams = StripeWebPaymentSessionParamsRequest.builder()
				.cancelRedirectURL("https://www.cancel.org/current")
				.successRedirectURL("https://www.success.org/current")
				.build();
		final var request = CreatePaymentSessionRequest.builder()
				.paymentMethodId(stripePaymentMethod.getId()
						.getValue())
				.description("Payment Order #12345")
				.paymentReference("Payment Reference 01")
				.transactionType(PaymentTransactionType.PURCHASE)
				.amount(1367L)
				.netAmount(1234L)
				.fee(133L)
				.currencyCode("SGD")
				.customerEmail("<EMAIL>")
				.systemSource("System Source 01")
				.merchantName("Merchant Name")
				//				.expiredInMilliseconds(Duration.ofMinutes(16)
				//						.toMillis())
				.processorParams(stripeParams)
				.build();

		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId("1")
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.BACK_OFFICE)
				.name("Name")
				.exp(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iat(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.familyName("Name")
				.givenName("Name")
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/payment/sessions")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isBadRequest()
				.expectBody(ErrorResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(actual.getDetails()
							.contains("Payment method is not active"));
				});
	}

	@Test
	void testThrowExceptionWhenCreateStripePaymentSessionByNotSupportedCurrency() {
		// Arrange
		final var tenantId = new TenantId(10_000L);
		final var now = Instant.now();

		final var stripePaymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Stripe Payment Method 10000")
				.processorId(PaymentProcessorId.STRIPE_WEB_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.CUSTOMER_PORTAL, AcceptedApplication.TOP_UP_KIOSK))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Stripe Payment Description 10000")
				.paymentInstruction("Stripe Payment Instruction 10000")
				.surchargeRate(BigDecimal.valueOf(0.1))
				.fixedSurcharge(10L)
				.currencyCode("SGD")
				.surchargeTitle("Stripe Payment Title 10000")
				.processorConfig(SimplePaymentMethodConfiguration.builder()
						.configs(objectMapper.convertValue(StripePaymentMethodConfigurationRequest.builder()
								.apiKey("API KEY 1234")
								.build(), new TypeReference<>() {
								}))
						.build())
				.build());

		final var stripeParams = StripeWebPaymentSessionParamsRequest.builder()
				.cancelRedirectURL("https://www.cancel.org/current")
				.successRedirectURL("https://www.success.org/current")
				.build();
		final var request = CreatePaymentSessionRequest.builder()
				.paymentMethodId(stripePaymentMethod.getId()
						.getValue())
				.description("Payment Order #12345")
				.paymentReference("Payment Reference 01")
				.transactionType(PaymentTransactionType.PURCHASE)
				.amount(1367L)
				.netAmount(1234L)
				.fee(100L)
				.currencyCode("VND")
				.customerEmail("<EMAIL>")
				.systemSource("System Source 01")
				//				.expiredInMilliseconds(Duration.ofMinutes(16)
				//						.toMillis())
				.processorParams(stripeParams)
				.build();

		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId("1")
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.BACK_OFFICE)
				.name("Name")
				.exp(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iat(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.familyName("Name")
				.givenName("Name")
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/payment/sessions")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isBadRequest()
				.expectBody(ErrorResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(actual.getDetails()
							.stream()
							.anyMatch(it -> it.contains("Payment method does not support the currency code VND")));
				});
	}

	@Test
	void testThrowExceptionWhenCreateStripePaymentSessionByNotMatchedFeeRequest() {
		// Arrange
		final var tenantId = new TenantId(10_000L);
		final var now = Instant.now();

		final var stripePaymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Stripe Payment Method 10000")
				.processorId(PaymentProcessorId.STRIPE_WEB_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.CUSTOMER_PORTAL, AcceptedApplication.TOP_UP_KIOSK))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Stripe Payment Description 10000")
				.paymentInstruction("Stripe Payment Instruction 10000")
				.surchargeRate(BigDecimal.valueOf(0.1))
				.fixedSurcharge(10L)
				.currencyCode("SGD")
				.surchargeTitle("Stripe Payment Title 10000")
				.processorConfig(SimplePaymentMethodConfiguration.builder()
						.configs(objectMapper.convertValue(StripePaymentMethodConfigurationRequest.builder()
								.apiKey("API KEY 1234")
								.build(), new TypeReference<>() {
								}))
						.build())
				.build());

		final var stripeParams = StripeWebPaymentSessionParamsRequest.builder()
				.cancelRedirectURL("https://www.cancel.org/current")
				.successRedirectURL("https://www.success.org/current")
				.build();
		final var request = CreatePaymentSessionRequest.builder()
				.paymentMethodId(stripePaymentMethod.getId()
						.getValue())
				.description("Payment Order #12345")
				.paymentReference("Payment Reference 01")
				.amount(1367L)
				.netAmount(1234L)
				.fee(100L)
				.transactionType(PaymentTransactionType.PURCHASE)
				.currencyCode("SGD")
				.customerEmail("<EMAIL>")
				.systemSource("System Source 01")
				//				.expiredInMilliseconds(Duration.ofMinutes(16)
				//						.toMillis())
				.processorParams(stripeParams)
				.build();

		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId("1")
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.BACK_OFFICE)
				.name("Name")
				.exp(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iat(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.familyName("Name")
				.givenName("Name")
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/payment/sessions")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isBadRequest()
				.expectBody(ErrorResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(actual.getDetails()
							.stream()
							.anyMatch(it -> it.contains("Mismatched Fee")));
				});
	}

	@Test
	void testThrowExceptionWhenCreateStripePaymentSessionByNotMatchedAmountRequest() {
		// Arrange
		final var tenantId = new TenantId(10_000L);
		final var now = Instant.now();

		final var stripePaymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Stripe Payment Method 10000")
				.processorId(PaymentProcessorId.STRIPE_WEB_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.CUSTOMER_PORTAL, AcceptedApplication.TOP_UP_KIOSK))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Stripe Payment Description 10000")
				.paymentInstruction("Stripe Payment Instruction 10000")
				.surchargeRate(BigDecimal.valueOf(0.2323))
				.fixedSurcharge(2332L)
				.currencyCode("SGD")
				.surchargeTitle("Stripe Payment Title 1111")
				.processorConfig(SimplePaymentMethodConfiguration.builder()
						.configs(objectMapper.convertValue(StripePaymentMethodConfigurationRequest.builder()
								.apiKey("API KEY 1234")
								.build(), new TypeReference<>() {
								}))
						.build())
				.build());

		final var stripeParams = StripeWebPaymentSessionParamsRequest.builder()
				.cancelRedirectURL("https://www.cancel.org/current")
				.successRedirectURL("https://www.success.org/current")
				.build();
		final var request = CreatePaymentSessionRequest.builder()
				.paymentMethodId(stripePaymentMethod.getId()
						.getValue())
				.description("Payment Order #12345")
				.paymentReference("Payment Reference 01")
				.transactionType(PaymentTransactionType.PURCHASE)
				.amount(18516L)
				.netAmount(13131L)
				.fee(5382L)
				.currencyCode("SGD")
				.customerEmail("<EMAIL>")
				.systemSource("System Source 01")
				.processorParams(stripeParams)
				.build();

		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId("1")
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.BACK_OFFICE)
				.name("Name")
				.exp(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iat(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.familyName("Name")
				.givenName("Name")
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/payment/sessions")
				.contentType(MediaType.APPLICATION_JSON)
				.bodyValue(request)
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isBadRequest()
				.expectBody(ErrorResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(actual.getDetails()
							.stream()
							.anyMatch(it -> it.contains("Mismatched Amount")));
				});
	}

	@Test
	void testGetStripePaymentSessionSuccessfullyWhenValidRequest() {
		// Arrange
		final var tenantId = new TenantId(10_000L);
		final var now = Instant.now();

		final var stripePaymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Stripe Payment Method 10000")
				.processorId(PaymentProcessorId.STRIPE_WEB_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.CUSTOMER_PORTAL, AcceptedApplication.TOP_UP_KIOSK))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Stripe Payment Description 10000")
				.paymentInstruction("Stripe Payment Instruction 10000")
				.currencyCode("SGD")
				.surchargeRate(BigDecimal.valueOf(0.123))
				.surchargeTitle("Stripe Payment Title 10000")
				.processorConfig(SimplePaymentMethodConfiguration.builder()
						.configs(objectMapper.convertValue(StripePaymentMethodConfigurationRequest.builder()
								.apiKey("API KEY 1234")
								.build(), new TypeReference<>() {
								}))
						.build())
				.build());

		final var stripeParams = StripeWebPaymentSessionParamsRequest.builder()
				.cancelRedirectURL("https://www.cancel.org/current")
				.successRedirectURL("https://www.success.org/current")
				.build();

		final var session = paymentSessionSupporter.initStripePaymentSession(tenantId, CreatePaymentSessionCommand
				.builder()
				.paymentProcessorId(stripePaymentMethod.getProcessorId())
				.paymentMethodId(stripePaymentMethod.getId())
				.description("Payment Order #12345")
				.paymentReference("Payment Reference 01")
				.amount(2495L)
				.netAmount(2222L)
				.fee(273L)
				.currencyCode("SGD")
				.customerId("1")
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.systemSource("System Source 01")
				.merchantName("Merchant Name")
				.expiredInMilliseconds(Duration.ofMinutes(16)
						.toMillis())
				.processorParams(PaymentSessionParams.builder()
						.params(objectMapper.convertValue(stripeParams, new TypeReference<>() {
						}))
						.build())
				.build());

		final var sessionData = objectMapper.convertValue(session.getSessionData()
				.getData(), StripePaymentSessionProcessorData.class);

		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId("1")
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.BACK_OFFICE)
				.name("Name")
				.exp(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iat(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.familyName("Name")
				.givenName("Name")
				.build();

		// Act & Assert
		webClient.get()
				.uri("/api/payment/sessions/{sessionId}", session.getId()
						.getValue())
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isOk()
				.expectBody(PaymentSessionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(stripePaymentMethod.getId()
							.getValue()
							.toString(), actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentSessionStatus.PENDING, actual.getStatus());
					Assertions.assertEquals(PaymentProcessorId.STRIPE_WEB_PAYMENT, actual.getPaymentProcessorId());
					Assertions.assertEquals("Payment Reference 01", actual.getPaymentReference());
					Assertions.assertEquals(userTokenClaim.getUserId(), actual.getCustomerId());
					Assertions.assertEquals("<EMAIL>", actual.getCustomerEmail());
					Assertions.assertEquals("Customer Name 001", actual.getCustomerName());
					Assertions.assertEquals("SGD", actual.getCurrencyCode());
					Assertions.assertEquals(session.getAmount(), actual.getAmount());
					Assertions.assertEquals(session.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(session.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(session.getDescription(), actual.getDescription());

					final var actualProcessorParams = (StripeWebPaymentSessionParamsResponse) actual
							.getProcessorParams();
					Assertions.assertNotNull(actualProcessorParams);
					Assertions.assertEquals(stripeParams.getSuccessRedirectURL(), actualProcessorParams
							.getSuccessRedirectURL());
					Assertions.assertEquals(stripeParams.getCancelRedirectURL(), actualProcessorParams
							.getCancelRedirectURL());
					Assertions.assertEquals(PaymentProcessorId.STRIPE_WEB_PAYMENT, actualProcessorParams
							.getProcessorId());

					final var actualProcessorData = (StripePaymentSessionDataResponse) actual.getSessionData();
					Assertions.assertNotNull(actualProcessorData);
					Assertions.assertEquals(sessionData.getStripeSessionId(), actualProcessorData.getStripeSessionId());
					Assertions.assertEquals("https://checkout.stripe.com/c/pay/cs_test", actualProcessorData
							.getPaymentUrl());
				});

	}

	@Test
	void testGetCashPaymentSessionSuccessfullyWhenValidRequest() {
		// Arrange
		final var tenantId = new TenantId(10_000L);
		final var paymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Cash Payment Method 10000")
				.processorId(PaymentProcessorId.CASH_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.POS))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Cash Payment Description 10000")
				.paymentInstruction("Cash Payment Instruction 10000")
				.currencyCode("SGD")
				.surchargeRate(BigDecimal.valueOf(0.123))
				.surchargeTitle("Cash Payment Title 10000")
				.build());

		final var session = paymentSessionSupporter.initStripePaymentSession(tenantId, CreatePaymentSessionCommand
				.builder()
				.paymentProcessorId(paymentMethod.getProcessorId())
				.paymentMethodId(paymentMethod.getId())
				.description("Payment Order #12345")
				.paymentReference("Payment Reference 01")
				.amount(2495L)
				.netAmount(2222L)
				.fee(273L)
				.currencyCode("SGD")
				.customerId("1")
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.systemSource("System Source 01")
				.merchantName("Merchant Name")
				.expiredInMilliseconds(Duration.ofMinutes(16)
						.toMillis())
				.build());

		// Act & Assert
		webClient.get()
				.uri("/api/payment/sessions/{sessionId}", session.getId()
						.getValue())
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isOk()
				.expectBody(PaymentSessionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(paymentMethod.getId()
							.getValue()
							.toString(), actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentSessionStatus.PENDING, actual.getStatus());
					Assertions.assertEquals(PaymentProcessorId.CASH_PAYMENT, actual.getPaymentProcessorId());
					Assertions.assertEquals("Payment Reference 01", actual.getPaymentReference());
					Assertions.assertEquals("<EMAIL>", actual.getCustomerEmail());
					Assertions.assertEquals("Customer Name 001", actual.getCustomerName());
					Assertions.assertEquals("1", actual.getCustomerId());
					Assertions.assertEquals("SGD", actual.getCurrencyCode());
					Assertions.assertEquals(session.getAmount(), actual.getAmount());
					Assertions.assertEquals(session.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(session.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(session.getDescription(), actual.getDescription());
				});

	}

	@Test
	void testGetOfflinePaymentSessionSuccessfullyWhenValidRequest() {
		// Arrange
		final var tenantId = new TenantId(10_000L);
		final var paymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Cash Payment Method 10000")
				.processorId(PaymentProcessorId.OFFLINE_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.POS))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Cash Payment Description 10000")
				.paymentInstruction("Cash Payment Instruction 10000")
				.currencyCode("SGD")
				.surchargeRate(BigDecimal.valueOf(0.123))
				.surchargeTitle("Cash Payment Title 10000")
				.build());

		final var session = paymentSessionSupporter.initStripePaymentSession(tenantId, CreatePaymentSessionCommand
				.builder()
				.paymentProcessorId(paymentMethod.getProcessorId())
				.paymentMethodId(paymentMethod.getId())
				.description("Payment Order #12345")
				.paymentReference("Payment Reference 01")
				.amount(2495L)
				.netAmount(2222L)
				.fee(273L)
				.currencyCode("SGD")
				.customerId("1")
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.systemSource("System Source 01")
				.merchantName("Merchant Name")
				.expiredInMilliseconds(Duration.ofMinutes(16)
						.toMillis())
				.build());

		// Act & Assert
		webClient.get()
				.uri("/api/payment/sessions/{sessionId}", session.getId()
						.getValue())
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isOk()
				.expectBody(PaymentSessionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(paymentMethod.getId()
							.getValue()
							.toString(), actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentSessionStatus.PENDING, actual.getStatus());
					Assertions.assertEquals(PaymentProcessorId.OFFLINE_PAYMENT, actual.getPaymentProcessorId());
					Assertions.assertEquals("Payment Reference 01", actual.getPaymentReference());
					Assertions.assertEquals("<EMAIL>", actual.getCustomerEmail());
					Assertions.assertEquals("Customer Name 001", actual.getCustomerName());
					Assertions.assertEquals("1", actual.getCustomerId());
					Assertions.assertEquals("SGD", actual.getCurrencyCode());
					Assertions.assertEquals(session.getAmount(), actual.getAmount());
					Assertions.assertEquals(session.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(session.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(session.getDescription(), actual.getDescription());
				});

	}

	@Test
	void testGetEWalletPaymentSessionSuccessfullyWhenValidRequest() {
		// Arrange
		final var tenantId = new TenantId(10_000L);
		final var paymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("EWallet Payment Method 10000")
				.processorId(PaymentProcessorId.E_WALLET_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.POS))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("EWallet Payment Description 10000")
				.paymentInstruction("EWallet Payment Instruction 10000")
				.currencyCode("SGD")
				.surchargeRate(BigDecimal.valueOf(0.123))
				.surchargeTitle("EWallet Payment Title 10000")
				.build());

		final var session = paymentSessionSupporter.initStripePaymentSession(tenantId, CreatePaymentSessionCommand
				.builder()
				.paymentProcessorId(paymentMethod.getProcessorId())
				.paymentMethodId(paymentMethod.getId())
				.description("Payment Order #12345")
				.paymentReference("Payment Reference 01")
				.amount(2495L)
				.netAmount(2222L)
				.fee(273L)
				.currencyCode("SGD")
				.customerId("1")
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.systemSource("System Source 01")
				.merchantName("Merchant Name")
				.expiredInMilliseconds(Duration.ofMinutes(16)
						.toMillis())
				.build());

		// Act & Assert
		webClient.get()
				.uri("/api/payment/sessions/{sessionId}", session.getId()
						.getValue())
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isOk()
				.expectBody(PaymentSessionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(paymentMethod.getId()
							.getValue()
							.toString(), actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentSessionStatus.PENDING, actual.getStatus());
					Assertions.assertEquals(PaymentProcessorId.E_WALLET_PAYMENT, actual.getPaymentProcessorId());
					Assertions.assertEquals("Payment Reference 01", actual.getPaymentReference());
					Assertions.assertEquals("<EMAIL>", actual.getCustomerEmail());
					Assertions.assertEquals("Customer Name 001", actual.getCustomerName());
					Assertions.assertEquals("1", actual.getCustomerId());
					Assertions.assertEquals("SGD", actual.getCurrencyCode());
					Assertions.assertEquals(session.getAmount(), actual.getAmount());
					Assertions.assertEquals(session.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(session.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(session.getDescription(), actual.getDescription());
				});

	}

	@Test
	void testGetNetsPaymentSessionSuccessfullyWhenValidRequest() {
		// Arrange
		final var tenantId = new TenantId(10_000L);

		final var paymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Nets Payment Method 10000")
				.processorId(PaymentProcessorId.NETS_TERMINAL_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.POS))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Nets Payment Description 10000")
				.paymentInstruction("Nets Payment Instruction 10000")
				.currencyCode("SGD")
				.surchargeRate(BigDecimal.valueOf(0.123))
				.surchargeTitle("Nets Payment Title 10000")
				.processorConfig(SimplePaymentMethodConfiguration.builder()
						.configs(objectMapper.convertValue(NetsTerminalPaymentMethodConfiguration.builder()
								.netsFamilyCardType(NetsFamilyCardType.CREDIT_CARD)
								.build(), new TypeReference<>() {
								}))
						.build())
				.build());

		final var session = paymentSessionSupporter.initStripePaymentSession(tenantId, CreatePaymentSessionCommand
				.builder()
				.paymentProcessorId(paymentMethod.getProcessorId())
				.paymentMethodId(paymentMethod.getId())
				.transactionType(PaymentTransactionType.PURCHASE)
				.description("Payment Order #12345")
				.paymentReference("Payment Reference 01")
				.amount(2495L)
				.netAmount(2222L)
				.fee(273L)
				.currencyCode("SGD")
				.customerId("1")
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.systemSource("System Source 01")
				.merchantName("Merchant Name")
				.expiredInMilliseconds(Duration.ofMinutes(16)
						.toMillis())
				.build());

		final var netSettlements = NetsPaymentSettlementSessionRequest.builder()
				.approvalCode("123")
				.merchantId("123")
				.terminalId("terminalId")
				.stan("stan")
				.cardName("cardName")
				.canNumber("canNumber")
				.balance("balance")
				.newBalance("newBalance")
				.purchaseAmount("purchaseAmount")
				.batchNumber("batchNumber")
				.invoiceNumber("invoiceNumber")
				.rrn("rrn")
				.tvr("tvr")
				.entryType("entryType")
				.issuerName("issuerName")
				.date("date")
				.time("time")
				.build();

		settlementCommandService.settlePaymentSession(tenantId, SettlePaymentSessionCommand.builder()
				.paymentSessionId(session.getId())
				.paymentProcessorId(session.getPaymentProcessorId())
				.transactionStatus(PaymentTransactionStatus.SUCCEEDED)
				.isAsync(Boolean.FALSE)
				.settlementData(objectMapper.convertValue(netSettlements, new TypeReference<>() {
				}))
				.build());

		// Act & Assert
		webClient.get()
				.uri("/api/payment/sessions/{sessionId}", session.getId()
						.getValue())
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isOk()
				.expectBody(PaymentSessionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(PaymentSessionStatus.COMPLETED, actual.getStatus());
					Assertions.assertEquals(PaymentProcessorId.NETS_TERMINAL_PAYMENT, actual.getPaymentProcessorId());
					Assertions.assertEquals("Payment Reference 01", actual.getPaymentReference());
					Assertions.assertEquals("<EMAIL>", actual.getCustomerEmail());
					Assertions.assertEquals("Customer Name 001", actual.getCustomerName());
					Assertions.assertEquals("SGD", actual.getCurrencyCode());
					Assertions.assertEquals(session.getAmount(), actual.getAmount());
					Assertions.assertEquals(session.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(session.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(session.getDescription(), actual.getDescription());
				});

	}

	@Test
	void testThrowExceptionWhenGetPaymentSessionByNotFound() {
		// Arrange
		final var tenantId = new TenantId(10_000L);
		final var now = Instant.now();

		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId("1")
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.BACK_OFFICE)
				.name("Name")
				.exp(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iat(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.familyName("Name")
				.givenName("Name")
				.build();

		// Act & Assert
		webClient.get()
				.uri("/api/payment/sessions/{sessionId}", "1234567")
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isBadRequest()
				.expectBody(ErrorResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(actual.getDetails()
							.contains("Payment session not found"));
				});

	}

	@Test
	void testCancelPaymentSessionWhenValidRequest() {
		// Arrange
		final var tenantId = new TenantId(10_000L);
		final var now = Instant.now();

		final var stripePaymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Stripe Payment Method 10000")
				.processorId(PaymentProcessorId.STRIPE_WEB_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.CUSTOMER_PORTAL, AcceptedApplication.TOP_UP_KIOSK))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Stripe Payment Description 10000")
				.paymentInstruction("Stripe Payment Instruction 10000")
				.currencyCode("SGD")
				.surchargeRate(BigDecimal.valueOf(0.123))
				.surchargeTitle("Stripe Payment Title 10000")
				.processorConfig(SimplePaymentMethodConfiguration.builder()
						.configs(objectMapper.convertValue(StripePaymentMethodConfigurationRequest.builder()
								.apiKey("API KEY 1234")
								.build(), new TypeReference<>() {
								}))
						.build())
				.build());

		final var stripeParams = StripeWebPaymentSessionParamsRequest.builder()
				.cancelRedirectURL("https://www.cancel.org/current")
				.successRedirectURL("https://www.success.org/current")
				.build();

		final var session = paymentSessionSupporter.initStripePaymentSession(tenantId, CreatePaymentSessionCommand
				.builder()
				.paymentProcessorId(stripePaymentMethod.getProcessorId())
				.description("Payment Order #12345")
				.paymentMethodId(stripePaymentMethod.getId())
				.paymentReference("Payment Reference 01")
				.amount(2495L)
				.netAmount(2222L)
				.fee(273L)
				.currencyCode("SGD")
				.customerId("1")
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.systemSource("System Source 01")
				.merchantName("Merchant Name")
				.expiredInMilliseconds(Duration.ofMinutes(16)
						.toMillis())
				.processorParams(PaymentSessionParams.builder()
						.params(objectMapper.convertValue(stripeParams, new TypeReference<>() {
						}))
						.build())
				.build());

		final var sessionData = objectMapper.convertValue(session.getSessionData()
				.getData(), StripePaymentSessionProcessorData.class);

		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId("1")
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.BACK_OFFICE)
				.name("Name")
				.exp(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iat(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.familyName("Name")
				.givenName("Name")
				.build();

		// Act & Assert
		webClient.post()
				.uri("/api/payment/sessions/{sessionId}/cancel", session.getId()
						.getValue())
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isOk()
				.expectBody(PaymentSessionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(stripePaymentMethod.getId()
							.getValue()
							.toString(), actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentSessionStatus.CANCELLED, actual.getStatus());
					Assertions.assertEquals(PaymentProcessorId.STRIPE_WEB_PAYMENT, actual.getPaymentProcessorId());
					Assertions.assertEquals("Payment Reference 01", actual.getPaymentReference());
					Assertions.assertEquals(userTokenClaim.getUserId(), actual.getCustomerId());
					Assertions.assertEquals("<EMAIL>", actual.getCustomerEmail());
					Assertions.assertEquals("Customer Name 001", actual.getCustomerName());
					Assertions.assertEquals("SGD", actual.getCurrencyCode());
					Assertions.assertEquals(session.getAmount(), actual.getAmount());
					Assertions.assertEquals(session.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(session.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(session.getDescription(), actual.getDescription());

					Assertions.assertEquals(stripePaymentMethod.getFixedSurcharge(), actual.getAppliedFixedSurcharge());
					Assertions.assertEquals(0, stripePaymentMethod.getSurchargeRate()
							.compareTo(new BigDecimal(actual.getAppliedSurchargeRate())));
					Assertions.assertEquals("Merchant Name", actual.getMerchantName());

					final var actualProcessorParams = (StripeWebPaymentSessionParamsResponse) actual
							.getProcessorParams();
					Assertions.assertNotNull(actualProcessorParams);
					Assertions.assertEquals(stripeParams.getSuccessRedirectURL(), actualProcessorParams
							.getSuccessRedirectURL());
					Assertions.assertEquals(stripeParams.getCancelRedirectURL(), actualProcessorParams
							.getCancelRedirectURL());
					Assertions.assertEquals(PaymentProcessorId.STRIPE_WEB_PAYMENT, actualProcessorParams
							.getProcessorId());

					final var actualProcessorData = (StripePaymentSessionDataResponse) actual.getSessionData();
					Assertions.assertNotNull(actualProcessorData);
					Assertions.assertEquals(sessionData.getStripeSessionId(), actualProcessorData.getStripeSessionId());
					Assertions.assertEquals("https://checkout.stripe.com/c/pay/cs_test", actualProcessorData
							.getPaymentUrl());
				});

	}

	@Test
	void testCancelStripePaymentSessionWhenAlreadyCancelled() {
		// Arrange
		final var tenantId = new TenantId(10_000L);
		final var now = Instant.now();

		final var stripePaymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Stripe Payment Method 10000")
				.processorId(PaymentProcessorId.STRIPE_WEB_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.CUSTOMER_PORTAL, AcceptedApplication.TOP_UP_KIOSK))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Stripe Payment Description 10000")
				.paymentInstruction("Stripe Payment Instruction 10000")
				.currencyCode("SGD")
				.surchargeRate(BigDecimal.valueOf(0.123))
				.surchargeTitle("Stripe Payment Title 10000")
				.processorConfig(SimplePaymentMethodConfiguration.builder()
						.configs(objectMapper.convertValue(StripePaymentMethodConfigurationRequest.builder()
								.apiKey("API KEY 1234")
								.build(), new TypeReference<>() {
								}))
						.build())
				.build());

		final var stripeParams = StripeWebPaymentSessionParamsRequest.builder()
				.cancelRedirectURL("https://www.cancel.org/current")
				.successRedirectURL("https://www.success.org/current")
				.build();

		final var session = paymentSessionSupporter.initStripePaymentSession(tenantId, CreatePaymentSessionCommand
				.builder()
				.paymentProcessorId(stripePaymentMethod.getProcessorId())
				.description("Payment Order #123")
				.paymentMethodId(stripePaymentMethod.getId())
				.paymentReference("Payment Reference 01")
				.amount(2495L)
				.netAmount(2222L)
				.fee(273L)
				.currencyCode("SGD")
				.customerId("1")
				.customerEmail("<EMAIL>")
				.systemSource("System Source 01")
				.merchantName("Merchant Name")
				.expiredInMilliseconds(Duration.ofMinutes(16)
						.toMillis())
				.processorParams(PaymentSessionParams.builder()
						.params(objectMapper.convertValue(stripeParams, new TypeReference<>() {
						}))
						.build())
				.build());

		final var sessionData = objectMapper.convertValue(session.getSessionData()
				.getData(), StripePaymentSessionProcessorData.class);

		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId("1")
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.BACK_OFFICE)
				.name("Name")
				.exp(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iat(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.familyName("Name")
				.givenName("Name")
				.build();

		sessionCommandService.cancelPaymentSession(tenantId, session.getId());

		// Act & Assert
		webClient.post()
				.uri("/api/payment/sessions/{sessionId}/cancel", session.getId()
						.getValue())
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isOk()
				.expectBody(PaymentSessionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);

					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(stripePaymentMethod.getId()
							.getValue()
							.toString(), actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentSessionStatus.CANCELLED, actual.getStatus());
					Assertions.assertEquals(PaymentProcessorId.STRIPE_WEB_PAYMENT, actual.getPaymentProcessorId());
					Assertions.assertEquals("Payment Reference 01", actual.getPaymentReference());
					Assertions.assertEquals(userTokenClaim.getUserId(), actual.getCustomerId());
					Assertions.assertEquals("<EMAIL>", actual.getCustomerEmail());
					Assertions.assertEquals("SGD", actual.getCurrencyCode());
					Assertions.assertEquals(session.getAmount(), actual.getAmount());
					Assertions.assertEquals(session.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(session.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(session.getDescription(), actual.getDescription());

					final var actualProcessorParams = (StripeWebPaymentSessionParamsResponse) actual
							.getProcessorParams();
					Assertions.assertNotNull(actualProcessorParams);
					Assertions.assertEquals(stripeParams.getSuccessRedirectURL(), actualProcessorParams
							.getSuccessRedirectURL());
					Assertions.assertEquals(stripeParams.getCancelRedirectURL(), actualProcessorParams
							.getCancelRedirectURL());
					Assertions.assertEquals(PaymentProcessorId.STRIPE_WEB_PAYMENT, actualProcessorParams
							.getProcessorId());

					final var actualProcessorData = (StripePaymentSessionDataResponse) actual.getSessionData();
					Assertions.assertNotNull(actualProcessorData);
					Assertions.assertEquals(sessionData.getStripeSessionId(), actualProcessorData.getStripeSessionId());
					Assertions.assertEquals("https://checkout.stripe.com/c/pay/cs_test", actualProcessorData
							.getPaymentUrl());
				});

	}

	@Test
	void testCancelCashPaymentSessionWhenValidRequest() {
		// Arrange
		final var tenantId = new TenantId(10_000L);

		final var paymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Cash Payment Method 10000")
				.processorId(PaymentProcessorId.CASH_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.POS))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Cash Payment Description 10000")
				.paymentInstruction("Cash Payment Instruction 10000")
				.currencyCode("SGD")
				.surchargeRate(BigDecimal.valueOf(0.123))
				.surchargeTitle("Cash Payment Title 10000")
				.build());

		final var session = paymentSessionSupporter.initStripePaymentSession(tenantId, CreatePaymentSessionCommand
				.builder()
				.paymentProcessorId(paymentMethod.getProcessorId())
				.description("Payment Order #12345")
				.paymentMethodId(paymentMethod.getId())
				.paymentReference("Payment Reference 01")
				.amount(2495L)
				.netAmount(2222L)
				.fee(273L)
				.currencyCode("SGD")
				.customerId("1")
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.systemSource("System Source 01")
				.merchantName("Merchant Name")
				.expiredInMilliseconds(Duration.ofMinutes(16)
						.toMillis())
				.build());

		// Act & Assert
		webClient.post()
				.uri("/api/payment/sessions/{sessionId}/cancel", session.getId()
						.getValue())
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isOk()
				.expectBody(PaymentSessionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(paymentMethod.getId()
							.getValue()
							.toString(), actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentSessionStatus.CANCELLED, actual.getStatus());
					Assertions.assertEquals(PaymentProcessorId.CASH_PAYMENT, actual.getPaymentProcessorId());
					Assertions.assertEquals("Payment Reference 01", actual.getPaymentReference());
					Assertions.assertEquals("<EMAIL>", actual.getCustomerEmail());
					Assertions.assertEquals("Customer Name 001", actual.getCustomerName());
					Assertions.assertEquals("SGD", actual.getCurrencyCode());
					Assertions.assertEquals(session.getAmount(), actual.getAmount());
					Assertions.assertEquals(session.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(session.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(session.getDescription(), actual.getDescription());

					Assertions.assertEquals(paymentMethod.getFixedSurcharge(), actual.getAppliedFixedSurcharge());
					Assertions.assertEquals(0, paymentMethod.getSurchargeRate()
							.compareTo(new BigDecimal(actual.getAppliedSurchargeRate())));
					Assertions.assertEquals("Merchant Name", actual.getMerchantName());
				});

	}

	@Test
	void testCancelOfflinePaymentSessionWhenValidRequest() {
		// Arrange
		final var tenantId = new TenantId(10_000L);

		final var paymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Cash Payment Method 10000")
				.processorId(PaymentProcessorId.OFFLINE_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.POS))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Cash Payment Description 10000")
				.paymentInstruction("Cash Payment Instruction 10000")
				.currencyCode("SGD")
				.surchargeRate(BigDecimal.valueOf(0.123))
				.surchargeTitle("Cash Payment Title 10000")
				.build());

		final var session = paymentSessionSupporter.initStripePaymentSession(tenantId, CreatePaymentSessionCommand
				.builder()
				.paymentProcessorId(paymentMethod.getProcessorId())
				.description("Payment Order #12345")
				.paymentMethodId(paymentMethod.getId())
				.paymentReference("Payment Reference 01")
				.amount(2495L)
				.netAmount(2222L)
				.fee(273L)
				.currencyCode("SGD")
				.customerId("1")
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.systemSource("System Source 01")
				.merchantName("Merchant Name")
				.expiredInMilliseconds(Duration.ofMinutes(16)
						.toMillis())
				.build());

		// Act & Assert
		webClient.post()
				.uri("/api/payment/sessions/{sessionId}/cancel", session.getId()
						.getValue())
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isOk()
				.expectBody(PaymentSessionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(paymentMethod.getId()
							.getValue()
							.toString(), actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentSessionStatus.CANCELLED, actual.getStatus());
					Assertions.assertEquals(PaymentProcessorId.OFFLINE_PAYMENT, actual.getPaymentProcessorId());
					Assertions.assertEquals("Payment Reference 01", actual.getPaymentReference());
					Assertions.assertEquals("<EMAIL>", actual.getCustomerEmail());
					Assertions.assertEquals("Customer Name 001", actual.getCustomerName());
					Assertions.assertEquals("SGD", actual.getCurrencyCode());
					Assertions.assertEquals(session.getAmount(), actual.getAmount());
					Assertions.assertEquals(session.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(session.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(session.getDescription(), actual.getDescription());

					Assertions.assertEquals(paymentMethod.getFixedSurcharge(), actual.getAppliedFixedSurcharge());
					Assertions.assertEquals(0, paymentMethod.getSurchargeRate()
							.compareTo(new BigDecimal(actual.getAppliedSurchargeRate())));
					Assertions.assertEquals("Merchant Name", actual.getMerchantName());
				});

	}

	@Test
	void testCancelNetsPaymentSessionWhenValidRequest() {
		// Arrange
		final var tenantId = new TenantId(10_000L);

		final var paymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Nets Payment Method 10000")
				.processorId(PaymentProcessorId.NETS_TERMINAL_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.POS))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Nets Payment Description 10000")
				.paymentInstruction("Nest Payment Instruction 10000")
				.currencyCode("SGD")
				.surchargeRate(BigDecimal.valueOf(0.123))
				.surchargeTitle("Nets Payment Title 10000")
				.processorConfig(SimplePaymentMethodConfiguration.builder()
						.configs(objectMapper.convertValue(NetsTerminalPaymentMethodConfiguration.builder()
								.netsFamilyCardType(NetsFamilyCardType.NETS_CARD)
								.build(), new TypeReference<>() {
								}))
						.build())
				.build());

		final var session = paymentSessionSupporter.initStripePaymentSession(tenantId, CreatePaymentSessionCommand
				.builder()
				.paymentProcessorId(paymentMethod.getProcessorId())
				.description("Payment Order #12345")
				.paymentMethodId(paymentMethod.getId())
				.paymentReference("Payment Reference 01")
				.amount(2495L)
				.netAmount(2222L)
				.fee(273L)
				.currencyCode("SGD")
				.customerId("1")
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.systemSource("System Source 01")
				.merchantName("Merchant Name")
				.expiredInMilliseconds(Duration.ofMinutes(16)
						.toMillis())
				.build());

		// Act & Assert
		webClient.post()
				.uri("/api/payment/sessions/{sessionId}/cancel", session.getId()
						.getValue())
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isOk()
				.expectBody(PaymentSessionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(paymentMethod.getId()
							.getValue()
							.toString(), actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentSessionStatus.CANCELLED, actual.getStatus());
					Assertions.assertEquals(PaymentProcessorId.NETS_TERMINAL_PAYMENT, actual.getPaymentProcessorId());
					Assertions.assertEquals("Payment Reference 01", actual.getPaymentReference());
					Assertions.assertEquals("<EMAIL>", actual.getCustomerEmail());
					Assertions.assertEquals("Customer Name 001", actual.getCustomerName());
					Assertions.assertEquals("SGD", actual.getCurrencyCode());
					Assertions.assertEquals(session.getAmount(), actual.getAmount());
					Assertions.assertEquals(session.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(session.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(session.getDescription(), actual.getDescription());

					Assertions.assertEquals(paymentMethod.getFixedSurcharge(), actual.getAppliedFixedSurcharge());
					Assertions.assertEquals(0, paymentMethod.getSurchargeRate()
							.compareTo(new BigDecimal(actual.getAppliedSurchargeRate())));
					Assertions.assertEquals("Merchant Name", actual.getMerchantName());
				});
	}

	@Test
	void testCancelEWalletPaymentWhenValidRequest() {
		// Arrange
		final var tenantId = new TenantId(10_000L);

		final var paymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("EWallet Payment Method 10000")
				.processorId(PaymentProcessorId.E_WALLET_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.POS))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("EWallet Payment Description 10000")
				.paymentInstruction("EWallet Payment Instruction 10000")
				.currencyCode("SGD")
				.surchargeRate(BigDecimal.valueOf(0.123))
				.surchargeTitle("EWallet Payment Title 10000")
				.processorConfig(SimplePaymentMethodConfiguration.builder()
						.configs(objectMapper.convertValue(NetsTerminalPaymentMethodConfiguration.builder()
								.netsFamilyCardType(NetsFamilyCardType.NETS_CARD)
								.build(), new TypeReference<>() {
								}))
						.build())
				.build());

		final var session = paymentSessionSupporter.initStripePaymentSession(tenantId, CreatePaymentSessionCommand
				.builder()
				.paymentProcessorId(paymentMethod.getProcessorId())
				.description("Payment Order #12345")
				.paymentMethodId(paymentMethod.getId())
				.paymentReference("Payment Reference 01")
				.amount(2495L)
				.netAmount(2222L)
				.fee(273L)
				.currencyCode("SGD")
				.customerId("1")
				.customerEmail("<EMAIL>")
				.customerName("Customer Name 001")
				.systemSource("System Source 01")
				.merchantName("Merchant Name")
				.expiredInMilliseconds(Duration.ofMinutes(16)
						.toMillis())
				.build());

		// Act & Assert
		webClient.post()
				.uri("/api/payment/sessions/{sessionId}/cancel", session.getId()
						.getValue())
				.headers(header -> HeaderGenerator.generateHttpHeaders(GenerateHttpHeader.builder()
						.header(header)
						.objectMapper(objectMapper)
						.requestId(1L)
						.tenantId(tenantId.getValue())
						.build()))
				.exchange()
				.expectStatus()
				.isOk()
				.expectBody(PaymentSessionResponse.class)
				.consumeWith(response -> {
					final var actual = response.getResponseBody();
					Assertions.assertNotNull(actual);
					Assertions.assertTrue(StringUtils.isNotBlank(actual.getPaymentSessionId()));
					Assertions.assertEquals(paymentMethod.getId()
							.getValue()
							.toString(), actual.getPaymentMethodId());
					Assertions.assertEquals(PaymentSessionStatus.CANCELLED, actual.getStatus());
					Assertions.assertEquals(PaymentProcessorId.E_WALLET_PAYMENT, actual.getPaymentProcessorId());
					Assertions.assertEquals("Payment Reference 01", actual.getPaymentReference());
					Assertions.assertEquals("<EMAIL>", actual.getCustomerEmail());
					Assertions.assertEquals("Customer Name 001", actual.getCustomerName());
					Assertions.assertEquals("SGD", actual.getCurrencyCode());
					Assertions.assertEquals(session.getAmount(), actual.getAmount());
					Assertions.assertEquals(session.getNetAmount(), actual.getNetAmount());
					Assertions.assertEquals(session.getSystemSource(), actual.getSystemSource());
					Assertions.assertEquals(session.getDescription(), actual.getDescription());

					Assertions.assertEquals(paymentMethod.getFixedSurcharge(), actual.getAppliedFixedSurcharge());
					Assertions.assertEquals(0, paymentMethod.getSurchargeRate()
							.compareTo(new BigDecimal(actual.getAppliedSurchargeRate())));
					Assertions.assertEquals("Merchant Name", actual.getMerchantName());
				});
	}

	@Test
	void testCancelPaymentSessionInOptimisticWhenValidRequest() {
		// Arrange
		final var tenantId = new TenantId(10_000L);

		final var stripePaymentMethod = paymentMethodSupporter.initPaymentMethod(tenantId, UpsertPaymentMethodCommand
				.builder()
				.displayName("Stripe Payment Method 10000")
				.processorId(PaymentProcessorId.STRIPE_WEB_PAYMENT)
				.acceptedApplications(Set.of(AcceptedApplication.CUSTOMER_PORTAL, AcceptedApplication.TOP_UP_KIOSK))
				.iconPath("http://localhost/image.jpg")
				.isActive(Boolean.TRUE)
				.description("Stripe Payment Description 10000")
				.paymentInstruction("Stripe Payment Instruction 10000")
				.currencyCode("SGD")
				.surchargeRate(BigDecimal.valueOf(0.123))
				.surchargeTitle("Stripe Payment Title 10000")
				.processorConfig(SimplePaymentMethodConfiguration.builder()
						.configs(objectMapper.convertValue(StripePaymentMethodConfigurationRequest.builder()
								.apiKey("API KEY 1234")
								.build(), new TypeReference<>() {
								}))
						.build())
				.build());

		final var stripeParams = StripeWebPaymentSessionParamsRequest.builder()
				.cancelRedirectURL("https://www.cancel.org/current")
				.successRedirectURL("https://www.success.org/current")
				.build();

		final var session = paymentSessionSupporter.initStripePaymentSession(tenantId, CreatePaymentSessionCommand
				.builder()
				.paymentProcessorId(stripePaymentMethod.getProcessorId())
				.description("Payment Order #12345")
				.paymentMethodId(stripePaymentMethod.getId())
				.paymentReference("Payment Reference 01")
				.amount(2495L)
				.netAmount(2222L)
				.fee(273L)
				.transactionType(PaymentTransactionType.PURCHASE)
				.currencyCode("SGD")
				.customerId("1")
				.customerEmail("<EMAIL>")
				.systemSource("System Source 01")
				.expiredInMilliseconds(Duration.ofMinutes(16)
						.toMillis())
				.processorParams(PaymentSessionParams.builder()
						.params(objectMapper.convertValue(stripeParams, new TypeReference<>() {
						}))
						.build())
				.build());

		AtomicLong counter = new AtomicLong(0);
		doAnswer(a -> {
			if (counter.getAndIncrement() == 0) {
				sessionCommandService.cancelPaymentSession(tenantId, session.getId());
				return a.callRealMethod();
			}
			return a.callRealMethod();
		}).when(paymentSessionRepository)
				.save(any());

		assertThrows(PaymentSessionOptimisticException.class, () -> sessionCommandService.cancelPaymentSession(tenantId,
				session.getId()));

	}

}
