/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.service.data.access.dynamo.sessions.entities;

import com.styl.pacific.aws.dynamodb.extensions.annotations.DynamoDbAuditingCreator;
import com.styl.pacific.aws.dynamodb.extensions.annotations.DynamoDbAuditingUpdater;
import com.styl.pacific.aws.dynamodb.utils.converter.EpochMillisFormatConverter;
import com.styl.pacific.common.validator.dynamodb.DynamoDBTable;
import com.styl.pacific.domain.valueobject.PaymentProcessorId;
import com.styl.pacific.payment.shared.enums.PaymentSessionStatus;
import com.styl.pacific.payment.shared.enums.PaymentTransactionType;
import java.math.BigDecimal;
import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import software.amazon.awssdk.enhanced.dynamodb.extensions.annotations.DynamoDbAutoGeneratedTimestampAttribute;
import software.amazon.awssdk.enhanced.dynamodb.extensions.annotations.DynamoDbVersionAttribute;
import software.amazon.awssdk.enhanced.dynamodb.mapper.UpdateBehavior;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbConvertedBy;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbUpdateBehavior;

@Data
@Builder
@DynamoDbBean
@DynamoDBTable(name = PaymentSessionEntity.TABLE_NAME)
@NoArgsConstructor
@AllArgsConstructor
public class PaymentSessionEntity {

	public static final String TABLE_NAME = "payment_sessions";
	public static final String PARTITION_KEY_PATTERN = "${TENANT_ID}#${PAYMENT_SESSION_ID}";

	@Getter(onMethod_ = { @DynamoDbPartitionKey, @DynamoDbAttribute("partition_key") })
	public String partitionKey;

	@Getter(onMethod_ = { @DynamoDbAttribute("payment_session_id") })
	public String paymentSessionId;

	@Getter(onMethod_ = { @DynamoDbAttribute("tenant_id") })
	private Long tenantId;

	@Getter(onMethod_ = { @DynamoDbAttribute("payment_method_id") })
	private Long paymentMethodId;

	@Getter(onMethod_ = { @DynamoDbAttribute("payment_processor_id") })
	private PaymentProcessorId paymentProcessorId;

	@Getter(onMethod_ = { @DynamoDbAttribute("status") })
	private PaymentSessionStatus status;

	@Getter(onMethod_ = { @DynamoDbAttribute("transaction_type") })
	private PaymentTransactionType transactionType;

	@Getter(onMethod_ = { @DynamoDbAttribute("description") })
	private String description;

	@Getter(onMethod_ = { @DynamoDbAttribute("payment_reference") })
	private String paymentReference;

	@Getter(onMethod_ = { @DynamoDbAttribute("merchant_name") })
	private String merchantName;

	@Getter(onMethod_ = { @DynamoDbAttribute("customer_id") })
	private String customerId;

	@Getter(onMethod_ = { @DynamoDbAttribute("customer_email") })
	private String customerEmail;

	@Getter(onMethod_ = { @DynamoDbAttribute("customer_name") })
	private String customerName;

	@Getter(onMethod_ = { @DynamoDbAttribute("currency_code") })
	private String currencyCode;

	@Getter(onMethod_ = { @DynamoDbAttribute("fee") })
	private Long fee;

	@Getter(onMethod_ = { @DynamoDbAttribute("amount") })
	private Long amount;

	@Getter(onMethod_ = { @DynamoDbAttribute("net_amount") })
	private Long netAmount;

	@Getter(onMethod_ = { @DynamoDbAttribute("system_source") })
	private String systemSource;

	@Getter(onMethod_ = { @DynamoDbAttribute("device_id") })
	private String deviceId;

	@Getter(onMethod_ = { @DynamoDbAttribute("expired_in_milliseconds") })
	private Long expiredInMilliseconds;

	@Getter(onMethod_ = { @DynamoDbAttribute("applied_surcharge_rate") })
	private BigDecimal appliedSurchargeRate;

	@Getter(onMethod_ = { @DynamoDbAttribute("applied_fixed_surcharge") })
	private Long appliedFixedSurcharge;

	@Getter(onMethod_ = { @DynamoDbAttribute("expired_at"),
			@DynamoDbConvertedBy(value = EpochMillisFormatConverter.class) })
	private Instant expiredAt;

	@Getter(onMethod_ = { @DynamoDbAttribute("processor_params") })
	private PaymentSessionParamsEntity processorParams;

	@Getter(onMethod_ = { @DynamoDbAttribute("session_data") })
	private PaymentSessionProcessorDataEntity sessionData;

	@Getter(onMethod_ = { @DynamoDbAttribute("settled_transaction_id") })
	private Long settledTransactionId;

	@Getter(onMethod_ = { @DynamoDbVersionAttribute, @DynamoDbAttribute("locking_version") })
	private Long lockingVersion;

	@Getter(onMethod_ = { @DynamoDbAutoGeneratedTimestampAttribute,
			@DynamoDbUpdateBehavior(UpdateBehavior.WRITE_IF_NOT_EXISTS),
			@DynamoDbConvertedBy(value = EpochMillisFormatConverter.class), @DynamoDbAttribute("created_at") })
	private Instant createdAt;

	@Getter(onMethod_ = { @DynamoDbAutoGeneratedTimestampAttribute,
			@DynamoDbConvertedBy(value = EpochMillisFormatConverter.class), @DynamoDbAttribute("updated_at") })
	private Instant updatedAt;

	@Getter(onMethod_ = { @DynamoDbAuditingCreator, @DynamoDbUpdateBehavior(UpdateBehavior.WRITE_IF_NOT_EXISTS),
			@DynamoDbAttribute("created_by") })
	private Long createdBy;

	@Getter(onMethod_ = { @DynamoDbAuditingUpdater, @DynamoDbAttribute("updated_by") })
	private Long updatedBy;
}