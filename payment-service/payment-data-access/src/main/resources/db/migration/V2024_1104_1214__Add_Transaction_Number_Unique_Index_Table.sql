-- device_transaction_number supports for tracking local device transaction number.
ALTER TABLE
    IF EXISTS payment_transactions ADD COLUMN IF NOT EXISTS device_transaction_number VARCHAR(255);

CREATE
    UNIQUE INDEX IF NOT EXISTS payment_transactions_device_id_method_txn_number_uidx ON
    payment_transactions(
        payment_method_id,
        device_id,
        transaction_number,
        device_transaction_number
    );
