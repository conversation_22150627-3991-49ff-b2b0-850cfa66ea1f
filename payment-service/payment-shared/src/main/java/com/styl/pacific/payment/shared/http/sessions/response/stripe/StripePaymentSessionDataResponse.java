/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.shared.http.sessions.response.stripe;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.styl.pacific.domain.valueobject.PaymentProcessorId;
import com.styl.pacific.payment.shared.constants.PaymentProcessorConstants;
import com.styl.pacific.payment.shared.http.sessions.response.PaymentSessionDataResponse;
import java.time.Instant;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
@JsonTypeName(PaymentProcessorConstants.PaymentProcessorIds.STRIPE_WEB_PAYMENT)
@JsonIgnoreProperties(ignoreUnknown = true)
public class StripePaymentSessionDataResponse extends PaymentSessionDataResponse {

	private final String stripeSessionId;

	private final String paymentUrl;

	@JsonCreator
	@Builder
	public StripePaymentSessionDataResponse(String stripeSessionId, String paymentUrl, Instant expiredAt,
			Instant createdAt) {
		super(PaymentProcessorId.STRIPE_WEB_PAYMENT, expiredAt, createdAt);
		this.stripeSessionId = stripeSessionId;
		this.paymentUrl = paymentUrl;
	}
}
