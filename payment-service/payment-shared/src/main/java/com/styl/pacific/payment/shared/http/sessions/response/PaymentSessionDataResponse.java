/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.shared.http.sessions.response;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.styl.pacific.domain.valueobject.PaymentProcessorId;
import com.styl.pacific.payment.shared.constants.PaymentProcessorConstants;
import com.styl.pacific.payment.shared.http.sessions.response.stripe.StripePaymentSessionDataResponse;
import com.styl.pacific.payment.shared.http.sessions.response.stripeconnect.StripeConnectPaymentSessionDataResponse;
import java.time.Instant;
import lombok.Getter;

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = PaymentSessionDataResponse.TYPE_FIELD_NAME, visible = true)
@JsonSubTypes({
		@JsonSubTypes.Type(value = StripePaymentSessionDataResponse.class, name = PaymentProcessorConstants.PaymentProcessorIds.STRIPE_WEB_PAYMENT),
		@JsonSubTypes.Type(value = StripeConnectPaymentSessionDataResponse.class, name = PaymentProcessorConstants.PaymentProcessorIds.STRIPE_CONNECT_STANDARD_WEB_PAYMENT),

})
@Getter
public abstract class PaymentSessionDataResponse {
	public static final String TYPE_FIELD_NAME = "processorId";
	private final PaymentProcessorId processorId;

	private final Instant expiredAt;

	private final Instant createdAt;

	protected PaymentSessionDataResponse(PaymentProcessorId processorId, Instant expiredAt, Instant createdAt) {
		this.processorId = processorId;
		this.expiredAt = expiredAt;
		this.createdAt = createdAt;
	}
}
