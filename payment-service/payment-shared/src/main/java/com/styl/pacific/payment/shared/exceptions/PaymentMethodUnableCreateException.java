/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.shared.exceptions;

import com.styl.pacific.domain.exception.GlobalErrorCode;
import com.styl.pacific.domain.processor.ErrorCodeValue;

@ErrorCodeValue(GlobalErrorCode.PAYMENT_METHOD_UNABLE_CREATE)
public class PaymentMethodUnableCreateException extends PaymentDomainException {

	public PaymentMethodUnableCreateException(String message, Throwable cause) {
		super(message, cause);
	}

	public PaymentMethodUnableCreateException(String message) {
		super(message);
	}

	public PaymentMethodUnableCreateException() {
		super("Unable to create payment method");
	}
}
