/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.rest.sessions;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.application.rest.context.RequestContext;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.domain.dto.TokenClaim;
import com.styl.pacific.payment.rest.sessions.mapper.PaymentSessionRequestMapper;
import com.styl.pacific.payment.rest.sessions.mapper.PaymentSessionResponseMapper;
import com.styl.pacific.payment.rest.transactions.mapper.PaymentTransactionResponseMapper;
import com.styl.pacific.payment.service.core.features.sessions.PaymentSessionCommandService;
import com.styl.pacific.payment.service.core.features.sessions.PaymentSessionQueryService;
import com.styl.pacific.payment.service.core.features.settlements.PaymentSettlementCommandService;
import com.styl.pacific.payment.shared.http.apis.PaymentSessionApi;
import com.styl.pacific.payment.shared.http.sessions.request.CreatePaymentSessionRequest;
import com.styl.pacific.payment.shared.http.sessions.response.PaymentSessionResponse;
import com.styl.pacific.payment.shared.http.settlement.request.SettlePaymentSessionRequest;
import com.styl.pacific.payment.shared.http.transactions.response.PaymentTransactionResponse;
import jakarta.validation.Valid;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class PaymentSessionController implements PaymentSessionApi {

	private final RequestContext requestContext;
	private final PaymentSessionCommandService commandService;
	private final PaymentSettlementCommandService settlementCommandService;
	private final PaymentSessionQueryService queryService;
	private final ObjectMapper objectMapper;

	@Override
	public PaymentSessionResponse createPaymentSession(@Valid CreatePaymentSessionRequest request) {
		return PaymentSessionResponseMapper.INSTANCE.toResponse(commandService.createPaymentSession(
				MapstructCommonDomainMapper.INSTANCE.longToTenantId(requestContext.getTenantId()),
				PaymentSessionRequestMapper.INSTANCE.toCreatePaymentSessionCommand(request, Optional.ofNullable(
						requestContext.getTokenClaim())
						.map(TokenClaim::getUserId)
						.filter(StringUtils::isNotBlank)
						.orElse(null), objectMapper)), objectMapper);

	}

	@Override
	public PaymentSessionResponse getPaymentSession(String sessionId) {
		return PaymentSessionResponseMapper.INSTANCE.toResponse(queryService.getSessionBySessionId(
				MapstructCommonDomainMapper.INSTANCE.longToTenantId(requestContext.getTenantId()),
				MapstructCommonDomainMapper.INSTANCE.stringToPaymentSessionId(sessionId)), objectMapper);
	}

	@Override
	public PaymentSessionResponse cancelPaymentSession(String sessionId) {
		return PaymentSessionResponseMapper.INSTANCE.toResponse(commandService.cancelPaymentSession(
				MapstructCommonDomainMapper.INSTANCE.longToTenantId(requestContext.getTenantId()),
				MapstructCommonDomainMapper.INSTANCE.stringToPaymentSessionId(sessionId)), objectMapper);
	}

	@Override
	public PaymentTransactionResponse settlePaymentSession(String paymentSessionId,
			@Valid SettlePaymentSessionRequest request) {

		final var transaction = settlementCommandService.settlePaymentSession(MapstructCommonDomainMapper.INSTANCE
				.longToTenantId(requestContext.getTenantId()), PaymentSessionRequestMapper.INSTANCE
						.toSettlePaymentSessionCommand(paymentSessionId, request, Boolean.FALSE, objectMapper));
		return PaymentTransactionResponseMapper.INSTANCE.toResponse(transaction, objectMapper);
	}
}
