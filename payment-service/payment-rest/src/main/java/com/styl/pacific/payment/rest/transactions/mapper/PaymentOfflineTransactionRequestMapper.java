/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.payment.rest.transactions.mapper;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.payment.rest.sessions.mapper.PaymentSettlementDataSupportMapper;
import com.styl.pacific.payment.shared.http.transactions.request.CreateOfflineTransactionRequest;
import com.styl.pacific.payment.spi.processors.offline.request.CreateOfflineTransactionCommand;
import java.util.Map;
import java.util.stream.Collectors;
import org.mapstruct.AfterMapping;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class, PaymentTransactionRequestMapper.class,
		PaymentTransactionResponseMapper.class })
public interface PaymentOfflineTransactionRequestMapper extends PaymentSettlementDataSupportMapper {
	PaymentOfflineTransactionRequestMapper INSTANCE = Mappers.getMapper(PaymentOfflineTransactionRequestMapper.class);

	@Mapping(target = "paymentMethodId", source = "paymentMethodId", qualifiedByName = "longToPaymentMethodId")
	@Mapping(target = "offlineIdempotencyKey", source = "offlineIdempotencyKey", qualifiedByName = "stringToIdempotencyKey")
	@Mapping(target = "settlementData", ignore = true)
	@Mapping(target = "extraSettlementData", ignore = true)
	@Mapping(target = "transactionType", ignore = true)
	@Mapping(target = "terminalId", ignore = true)
	CreateOfflineTransactionCommand toOfflineTransactionCommand(CreateOfflineTransactionRequest request,
			@Context ObjectMapper objectMapper);

	@AfterMapping
	default void afterMappingOfflineTransactionCommand(
			@MappingTarget CreateOfflineTransactionCommand.CreateOfflineTransactionCommandBuilder builder,
			@Context ObjectMapper objectMapper, CreateOfflineTransactionRequest request) {
		if (request.getSettlementData() != null) {
			builder.settlementData(settlementDataRequestToMap(objectMapper, request.getSettlementData()));
		}
		if (request.getExtraSettlementData() != null) {
			builder.extraSettlementData(request.getExtraSettlementData()
					.entrySet()
					.stream()
					.collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));
		}
	}

}
