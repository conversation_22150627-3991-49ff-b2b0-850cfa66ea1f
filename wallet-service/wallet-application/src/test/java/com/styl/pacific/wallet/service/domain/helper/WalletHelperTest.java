/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.domain.helper;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.styl.pacific.domain.valueobject.Money;
import com.styl.pacific.wallet.service.entity.WalletEntry;
import com.styl.pacific.wallet.service.exception.InsufficientAmountEntryException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class WalletHelperTest {

	@InjectMocks
	private WalletHelper walletHelper;

	@Test
	void testValidateWalletEntries_SumGreaterThanAmount() {
		WalletEntry entry1 = mock(WalletEntry.class);
		WalletEntry entry2 = mock(WalletEntry.class);
		when(entry1.getRemainingBalance()).thenReturn(new Money(60));
		when(entry2.getRemainingBalance()).thenReturn(new Money(50));
		List<WalletEntry> entries = Arrays.asList(entry1, entry2);
		Money amount = new Money(100);
		assertDoesNotThrow(() -> walletHelper.validateWalletEntries(entries, amount));
	}

	@Test
	void testValidateWalletEntries_SumEqualToAmount() {
		WalletEntry entry1 = mock(WalletEntry.class);
		WalletEntry entry2 = mock(WalletEntry.class);
		when(entry1.getRemainingBalance()).thenReturn(new Money(40));
		when(entry2.getRemainingBalance()).thenReturn(new Money(60));
		List<WalletEntry> entries = Arrays.asList(entry1, entry2);
		Money amount = new Money(100);
		assertDoesNotThrow(() -> walletHelper.validateWalletEntries(entries, amount));
	}

	@Test
	void testValidateWalletEntries_SumLessThanAmount() {
		WalletEntry entry1 = mock(WalletEntry.class);
		WalletEntry entry2 = mock(WalletEntry.class);
		when(entry1.getRemainingBalance()).thenReturn(new Money(30));
		when(entry2.getRemainingBalance()).thenReturn(new Money(40));
		List<WalletEntry> entries = Arrays.asList(entry1, entry2);
		Money amount = new Money(100);
		assertThrows(InsufficientAmountEntryException.class, () -> walletHelper.validateWalletEntries(entries, amount));
	}

	@Test
	void testValidateWalletEntries_EmptyEntries_ShouldThrow() {
		List<WalletEntry> entries = Collections.emptyList();
		Money amount = new Money(10);
		assertThrows(InsufficientAmountEntryException.class, () -> walletHelper.validateWalletEntries(entries, amount));
	}
}
