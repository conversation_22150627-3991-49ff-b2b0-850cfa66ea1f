/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.rest.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.domain.valueobject.Money;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.TopupSessionId;
import com.styl.pacific.domain.valueobject.WalletId;
import com.styl.pacific.wallet.service.config.MvcTestConfiguration;
import com.styl.pacific.wallet.service.domain.dto.topupsession.CreateTopupSessionCommand;
import com.styl.pacific.wallet.service.domain.dto.topupsession.GetTopupSessionQuery;
import com.styl.pacific.wallet.service.domain.port.input.service.TopupSessionDomainService;
import com.styl.pacific.wallet.service.entity.TopupSession;
import com.styl.pacific.wallet.service.enums.TopupSessionStatus;
import com.styl.pacific.wallet.service.requests.topupsession.CreateTopupSessionRequest;
import com.styl.pacific.wallet.service.responses.topupsession.TopupSessionResponse;
import java.math.BigInteger;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.web.servlet.MockMvc;

/**
 * <AUTHOR>
 */

@WebMvcTest(controllers = TopupSessionController.class)
@Import({ TopupSessionController.class })
@ContextConfiguration(classes = { MvcTestConfiguration.class })
class TopupSessionControllerTest {

	private static final String WALLET_PATH = "/api/wallet/topup-sessions";

	private static final Long TENANT_ID = 1L;

	private static final String REQUEST_ID_HEADER = "X-Request-ID";

	private static final String TENANT_ID_HEADER = "X-Tenant-ID";

	private static final Long WALLET_ID = 123L;

	private static final Long TOP_UP_SESSION_ID = 1L;

	@Autowired
	private MockMvc mockMvc;

	@Autowired
	private ObjectMapper objectMapper;

	@MockBean
	private TopupSessionDomainService topupSessionDomainService;

	@Test
	void shouldSuccess_whenCreateTopup() throws Exception {
		// Arrange
		CreateTopupSessionRequest createStoreRequest = CreateTopupSessionRequest.builder()
				.amount(BigInteger.valueOf(100000))
				.currency("VND")
				.walletId("12345")
				.build();

		TopupSession topupSession = TopupSession.Builder.builder()
				.id(new TopupSessionId(TOP_UP_SESSION_ID))
				.walletId(new WalletId(WALLET_ID))
				.tenantId(new TenantId(TENANT_ID))
				.amount(new Money(BigInteger.valueOf(1000)))
				.tenantId(new TenantId(TENANT_ID))
				.paymentRef("abcdetref")
				.status(TopupSessionStatus.PENDING)
				.build();

		TopupSessionResponse topupSessionResponse = TopupSessionResponse.builder()
				.topupSessionId(TOP_UP_SESSION_ID.toString())
				.tenantId(TENANT_ID.toString())
				.amount(BigInteger.valueOf(1000))
				.walletId(WALLET_ID.toString())
				.paymentRef("abcdetref")
				.status(TopupSessionStatus.PENDING)
				.build();

		when(topupSessionDomainService.createTopupSession(any(CreateTopupSessionCommand.class))).thenReturn(
				topupSession);
		// Act && Assert
		mockMvc.perform(post(WALLET_PATH).headers(getHttpHeaders())
				.content(objectMapper.writeValueAsString(createStoreRequest))
				.contentType(MediaType.APPLICATION_JSON))
				.andExpect(status().isOk())
				.andExpect(content().json(objectMapper.writeValueAsString(topupSessionResponse)));
	}

	@Test
	void shouldReturnSuccess_whenGetTopupSession() throws Exception {
		// Arrange
		TopupSession topupSession = TopupSession.Builder.builder()
				.id(new TopupSessionId(TOP_UP_SESSION_ID))
				.walletId(new WalletId(WALLET_ID))
				.tenantId(new TenantId(TENANT_ID))
				.amount(new Money(BigInteger.valueOf(1000)))
				.tenantId(new TenantId(TENANT_ID))
				.paymentRef("abcdetref")
				.status(TopupSessionStatus.PENDING)
				.build();

		TopupSessionResponse topupSessionResponse = TopupSessionResponse.builder()
				.topupSessionId(TOP_UP_SESSION_ID.toString())
				.tenantId(TENANT_ID.toString())
				.amount(BigInteger.valueOf(1000))
				.walletId(WALLET_ID.toString())
				.paymentRef("abcdetref")
				.status(TopupSessionStatus.PENDING)
				.build();
		when(topupSessionDomainService.getTopupSession(any(GetTopupSessionQuery.class))).thenReturn(topupSession);
		// Act
		mockMvc.perform(get(WALLET_PATH + "/{id}", TOP_UP_SESSION_ID).contentType(MediaType.APPLICATION_JSON)
				.headers(getHttpHeaders()))
				.andExpect(status().isOk())
				.andExpect(content().json(objectMapper.writeValueAsString(topupSessionResponse)));
	}

	private HttpHeaders getHttpHeaders() {
		Map<String, String> map = new HashMap<>();
		map.put(REQUEST_ID_HEADER, Long.toString(new Random().nextLong()));
		map.put(TENANT_ID_HEADER, Long.toString(TENANT_ID));
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.setAll(map);
		return httpHeaders;
	}
}
