/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.config;

import com.github.tomakehurst.wiremock.WireMockServer;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.cloud.openfeign.FeignAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * <AUTHOR>
 */
@Configuration
@EnableFeignClients
@ImportAutoConfiguration({ FeignAutoConfiguration.class })
@Profile("local")
public class FeignClientTestConfiguration {
	@Bean(name = "tenant-service", initMethod = "start", destroyMethod = "stop")
	public WireMockServer mockDMSService() {
		return new WireMockServer(9201);
	}

	@Bean(name = "user-service", initMethod = "start", destroyMethod = "stop")
	public WireMockServer mockUserService() {
		return new WireMockServer(9202);
	}
}
