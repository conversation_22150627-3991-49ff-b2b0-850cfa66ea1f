/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.data.access.repository;

import com.styl.pacific.wallet.service.data.access.entity.FundSourceTopupTrackingEntity;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

/**
 * <AUTHOR>
 */
public interface FundSourceTopupTrackingJpaRepository extends JpaRepository<FundSourceTopupTrackingEntity, Long>,
		JpaSpecificationExecutor<FundSourceTopupTrackingEntity> {

	@Query("SELECT t FROM FundSourceTopupTrackingEntity t WHERE t.tenantId = :tenantId AND t.fundSourceTopupHistoryId = :topupHistoryId AND t.customerId = :customerId")
	Optional<FundSourceTopupTrackingEntity> findExistingTopupTracking(Long tenantId, Long topupHistoryId,
			Long customerId);
}
