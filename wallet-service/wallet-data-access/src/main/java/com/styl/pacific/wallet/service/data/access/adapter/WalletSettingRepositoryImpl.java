/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.data.access.adapter;

import com.styl.pacific.domain.dto.pagination.PaginationQuery;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.wallet.service.data.access.entity.WalletSettingEntity;
import com.styl.pacific.wallet.service.data.access.mapper.WalletSettingDataAccessMapper;
import com.styl.pacific.wallet.service.data.access.repository.WalletSettingJpaRepository;
import com.styl.pacific.wallet.service.data.access.specification.WalletSettingEntitySpecification;
import com.styl.pacific.wallet.service.domain.dto.walletsetting.GetWalletSettingQuery;
import com.styl.pacific.wallet.service.domain.dto.walletsetting.WalletSettingsFilterQuery;
import com.styl.pacific.wallet.service.domain.output.repository.WalletSettingRepository;
import com.styl.pacific.wallet.service.entity.WalletSetting;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Component
public class WalletSettingRepositoryImpl implements WalletSettingRepository {

	private final WalletSettingJpaRepository walletSettingJpaRepository;

	@Override
	public WalletSetting save(WalletSetting walletSetting) {
		WalletSettingEntity walletSettingEntity = WalletSettingDataAccessMapper.INSTANCE
				.walletSettingToWalletSettingEntity(walletSetting);
		WalletSettingEntity result = walletSettingJpaRepository.saveAndFlush(walletSettingEntity);
		return WalletSettingDataAccessMapper.INSTANCE.walletSettingEntityToWalletSetting(result);
	}

	@Override
	public Optional<WalletSetting> getWalletSetting(GetWalletSettingQuery query) {
		WalletSettingEntitySpecification specs = WalletSettingDataAccessMapper.INSTANCE
				.toWalletSettingEntitySpecification(query);
		return walletSettingJpaRepository.findOne(specs)
				.map(WalletSettingDataAccessMapper.INSTANCE::walletSettingEntityToWalletSetting);
	}

	@Override
	public Paging<WalletSetting> findWalletSettings(PaginationQuery<WalletSettingsFilterQuery> query) {
		WalletSettingEntitySpecification specification = WalletSettingDataAccessMapper.INSTANCE
				.toWalletSettingEntitySpecification(query.getFilter());
		PageRequest pageRequest = PageRequest.of(query.getPage(), query.getSize(), Sort.Direction.fromString(query
				.getSortDirection()), query.getSortFields()
						.toArray(new String[0]));
		Page<WalletSetting> result = walletSettingJpaRepository.findAll(specification, pageRequest)
				.map(WalletSettingDataAccessMapper.INSTANCE::walletSettingEntityToWalletSetting);
		return new Paging<>(result.getContent(), result.getTotalElements(), result.getTotalPages(), result.getNumber(),
				result.getSort()
						.stream()
						.map(order -> order.getProperty() + ","
								+ order.getDirection()
										.name())
						.toList());
	}
}
