/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.data.access.specification;

import com.styl.pacific.data.access.jpa.specification.BaseSpecification;
import com.styl.pacific.wallet.service.data.access.entity.FundSourceTopupTrackingEntity;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.io.Serial;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import lombok.Builder;

/**
 * <AUTHOR>
 */
@Builder
public class FundSourceTopupTrackingSpecification extends BaseSpecification<FundSourceTopupTrackingEntity> {
	@Serial
	private static final long serialVersionUID = 1L;

	private Long id;

	private Long customerId;

	private Long tenantId;

	private Long fundSourceTopupSchedulerId;

	private Long fundSourceTopupHistoryId;

	private Long walletTransactionId;

	private Instant fromTime;

	private Instant toTime;

	@Override
	public Predicate toPredicate(Root<FundSourceTopupTrackingEntity> root, CriteriaQuery<?> query,
			CriteriaBuilder criteriaBuilder) {
		List<Predicate> predicates = new ArrayList<>();

		if (isNotNull(id)) {
			predicates.add(equals(criteriaBuilder, root.get("id"), id));
		}

		if (isNotNull(fundSourceTopupSchedulerId)) {
			predicates.add(equals(criteriaBuilder, root.get("fundSourceTopupSchedulerId"), fundSourceTopupSchedulerId));
		}

		if (isNotNull(tenantId)) {
			predicates.add(equals(criteriaBuilder, root.get("tenantId"), tenantId));
		}

		if (isNotNull(fundSourceTopupHistoryId)) {
			predicates.add(equals(criteriaBuilder, root.get("fundSourceTopupHistoryId"), fundSourceTopupHistoryId));
		}

		if (isNotNull(customerId)) {
			predicates.add(equals(criteriaBuilder, root.get("customerId"), customerId));
		}

		if (isNotNull(walletTransactionId)) {
			predicates.add(equals(criteriaBuilder, root.get("walletTransactionId"), walletTransactionId));
		}

		if (isNotNull(fromTime)) {
			predicates.add(greaterThanOrEqualTo(criteriaBuilder, root.get("createdAt"), fromTime));
		}

		if (isNotNull(toTime)) {
			predicates.add(lessThanOrEqualTo(criteriaBuilder, root.get("createdAt"), toTime));
		}

		return and(criteriaBuilder, predicates);
	}
}
