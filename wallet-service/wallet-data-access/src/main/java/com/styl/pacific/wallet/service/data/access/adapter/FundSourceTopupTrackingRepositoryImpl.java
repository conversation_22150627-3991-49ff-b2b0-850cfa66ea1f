/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.data.access.adapter;

import com.styl.pacific.domain.dto.pagination.PaginationQuery;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.wallet.service.data.access.entity.FundSourceTopupTrackingEntity;
import com.styl.pacific.wallet.service.data.access.mapper.FundSourceTopupDataAccessMapper;
import com.styl.pacific.wallet.service.data.access.repository.FundSourceTopupTrackingJpaRepository;
import com.styl.pacific.wallet.service.data.access.specification.FundSourceTopupTrackingSpecification;
import com.styl.pacific.wallet.service.domain.dto.fundsource.FundSourcesTopupTrackingFilterQuery;
import com.styl.pacific.wallet.service.domain.output.repository.FundSourceTopupTrackingRepository;
import com.styl.pacific.wallet.service.entity.FundSourceTopupTracking;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Component
public class FundSourceTopupTrackingRepositoryImpl implements FundSourceTopupTrackingRepository {

	private final FundSourceTopupTrackingJpaRepository fundSourceTopupTrackingJpaRepository;

	@Override
	public FundSourceTopupTracking save(FundSourceTopupTracking fundSourceTopupTracking) {
		FundSourceTopupTrackingEntity fundSourceTopupTrackingEntity = FundSourceTopupDataAccessMapper.INSTANCE
				.toFundSourceTopupTrackingEntity(fundSourceTopupTracking);
		FundSourceTopupTrackingEntity result = fundSourceTopupTrackingJpaRepository.saveAndFlush(
				fundSourceTopupTrackingEntity);
		return FundSourceTopupDataAccessMapper.INSTANCE.entityToFundSourceTopupTracking(result);
	}

	@Override
	public Optional<FundSourceTopupTracking> findTopupTracking(Long tenantId, Long topupHistoryId, Long customerId) {
		return fundSourceTopupTrackingJpaRepository.findExistingTopupTracking(tenantId, topupHistoryId, customerId)
				.map(FundSourceTopupDataAccessMapper.INSTANCE::entityToFundSourceTopupTracking);
	}

	@Override
	public Paging<FundSourceTopupTracking> findFundSourceTopupTracking(
			PaginationQuery<FundSourcesTopupTrackingFilterQuery> query) {
		FundSourceTopupTrackingSpecification specification = FundSourceTopupDataAccessMapper.INSTANCE
				.toTopupTrackingSpecification(query.getFilter());
		PageRequest pageRequest = PageRequest.of(query.getPage(), query.getSize(), Sort.Direction.fromString(query
				.getSortDirection()), query.getSortFields()
						.toArray(new String[0]));
		Page<FundSourceTopupTracking> result = fundSourceTopupTrackingJpaRepository.findAll(specification, pageRequest)
				.map(FundSourceTopupDataAccessMapper.INSTANCE::entityToFundSourceTopupTracking);
		return new Paging<>(result.getContent(), result.getTotalElements(), result.getTotalPages(), result.getNumber(),
				result.getSort()
						.stream()
						.map(order -> order.getProperty() + ","
								+ order.getDirection()
										.name())
						.toList());
	}
}
