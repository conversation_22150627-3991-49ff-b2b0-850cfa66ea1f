/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.data.access.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.wallet.service.data.access.entity.WalletSettingEntity;
import com.styl.pacific.wallet.service.data.access.specification.WalletSettingEntitySpecification;
import com.styl.pacific.wallet.service.domain.dto.walletsetting.GetWalletSettingQuery;
import com.styl.pacific.wallet.service.domain.dto.walletsetting.WalletSettingsFilterQuery;
import com.styl.pacific.wallet.service.domain.mapper.WalletDataCommonMapper;
import com.styl.pacific.wallet.service.entity.WalletSetting;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class, WalletDataCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface WalletSettingDataAccessMapper {

	WalletSettingDataAccessMapper INSTANCE = Mappers.getMapper(WalletSettingDataAccessMapper.class);

	@Mapping(target = "id", qualifiedByName = "longToWalletSettingId")
	@Mapping(target = "tenantId", qualifiedByName = "longToTenantId")
	@Mapping(target = "minTopupAmount", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "maxTopupAmount", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "presetAmounts", qualifiedByName = "bigIntegerToMoney")
	@Mapping(target = "maxDailyOfflineSpendPerDeviceAmount", qualifiedByName = "bigIntegerToMoney")
	WalletSetting walletSettingEntityToWalletSetting(WalletSettingEntity walletEntity);

	@Mapping(target = "id", qualifiedByName = "walletSettingIdToLong")
	@Mapping(target = "tenantId", qualifiedByName = "tenantIdToLong")
	@Mapping(target = "minTopupAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "maxTopupAmount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "presetAmounts", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "maxDailyOfflineSpendPerDeviceAmount", qualifiedByName = "moneyToBigInteger")
	WalletSettingEntity walletSettingToWalletSettingEntity(WalletSetting walletSetting);

	WalletSettingEntitySpecification toWalletSettingEntitySpecification(GetWalletSettingQuery query);

	@Mapping(target = "id", ignore = true)
	WalletSettingEntitySpecification toWalletSettingEntitySpecification(WalletSettingsFilterQuery filterQuery);
}
