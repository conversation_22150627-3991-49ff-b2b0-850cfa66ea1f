/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.data.access.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.wallet.service.data.access.entity.BeneficiaryEntity;
import com.styl.pacific.wallet.service.data.access.specification.BeneficiaryEntitySpecification;
import com.styl.pacific.wallet.service.domain.dto.fundsourcedistribution.BeneficiariesFilterQuery;
import com.styl.pacific.wallet.service.domain.mapper.WalletDataCommonMapper;
import com.styl.pacific.wallet.service.entity.Beneficiary;
import com.styl.pacific.wallet.service.entity.BeneficiaryDto;
import com.styl.pacific.wallet.service.entity.FundSource;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class, WalletDataCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface BeneficiaryDataAccessMapper {

	BeneficiaryDataAccessMapper INSTANCE = Mappers.getMapper(BeneficiaryDataAccessMapper.class);

	@Mapping(target = "fundSourceId", qualifiedByName = "longToFundSourceId")
	@Mapping(target = "tenantId", qualifiedByName = "longToTenantId")
	@Mapping(target = "id", qualifiedByName = "longToBeneficiaryId")
	@Mapping(target = "customerId", qualifiedByName = "longToUserId")
	Beneficiary beneficiaryEntityToBeneficiary(BeneficiaryEntity beneficiaryEntity);

	@Mapping(target = "fundSourceEntity", ignore = true)
	@Mapping(target = "id", qualifiedByName = "beneficiaryIdToLong")
	@Mapping(target = "tenantId", qualifiedByName = "tenantIdToLong")
	@Mapping(target = "fundSourceId", qualifiedByName = "fundSourceIdToLong")
	@Mapping(target = "customerId", qualifiedByName = "userIdToLong")
	BeneficiaryEntity beneficicaryTobeneficiaryEntity(Beneficiary beneficiary);

	@Mapping(target = "createdAt", source = "beneficiary.createdAt")
	@Mapping(target = "fundSource", source = "fundSource")
	@Mapping(target = "id", source = "beneficiary.id")
	BeneficiaryDto toBeneficiaryDto(Beneficiary beneficiary, FundSource fundSource);

	@Mapping(target = "beneficiaryId", ignore = true)
	BeneficiaryEntitySpecification beneficiariesFilterQueryToBeneficiaryEntitySpecification(
			BeneficiariesFilterQuery filterQuery);
}
