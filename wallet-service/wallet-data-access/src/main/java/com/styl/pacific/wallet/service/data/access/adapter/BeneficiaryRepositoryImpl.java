/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.data.access.adapter;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.wallet.service.data.access.entity.BeneficiaryEntity;
import com.styl.pacific.wallet.service.data.access.mapper.BeneficiaryDataAccessMapper;
import com.styl.pacific.wallet.service.data.access.mapper.FundSourceDataAccessMapper;
import com.styl.pacific.wallet.service.data.access.repository.BeneficiaryJpaRepository;
import com.styl.pacific.wallet.service.data.access.specification.BeneficiaryEntitySpecification;
import com.styl.pacific.wallet.service.domain.dto.fundsourcedistribution.FindBeneficiariesQuery;
import com.styl.pacific.wallet.service.domain.dto.fundsourcedistribution.GetBeneficiaryQuery;
import com.styl.pacific.wallet.service.domain.output.repository.BeneficiaryRepository;
import com.styl.pacific.wallet.service.entity.Beneficiary;
import com.styl.pacific.wallet.service.entity.BeneficiaryDto;
import com.styl.pacific.wallet.service.entity.FundSource;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Component
public class BeneficiaryRepositoryImpl implements BeneficiaryRepository {

	private final BeneficiaryJpaRepository beneficiaryJpaRepository;

	@Override
	public Beneficiary save(Beneficiary beneficiary) {
		BeneficiaryEntity beneficiaryEntity = BeneficiaryDataAccessMapper.INSTANCE.beneficicaryTobeneficiaryEntity(
				beneficiary);
		BeneficiaryEntity result = beneficiaryJpaRepository.saveAndFlush(beneficiaryEntity);
		return BeneficiaryDataAccessMapper.INSTANCE.beneficiaryEntityToBeneficiary(result);
	}

	@Override
	public Optional<BeneficiaryDto> getBeneficiary(GetBeneficiaryQuery query) {
		BeneficiaryEntitySpecification specs = BeneficiaryEntitySpecification.builder()
				.tenantId(query.getTenantId())
				.beneficiaryId(query.getBeneficiaryId())
				.fundSourceId(query.getFundSourceId())
				.customerIds(List.of(query.getCustomerId()))
				.build();
		Optional<BeneficiaryEntity> beneficiaryEntityOptional = beneficiaryJpaRepository.findOne(specs);
		if (beneficiaryEntityOptional.isEmpty()) {
			return Optional.empty();
		}
		Beneficiary beneficiary = BeneficiaryDataAccessMapper.INSTANCE.beneficiaryEntityToBeneficiary(
				beneficiaryEntityOptional.get());
		FundSource fundSource = FundSourceDataAccessMapper.INSTANCE.fundSourceEntityToFundSource(
				beneficiaryEntityOptional.get()
						.getFundSourceEntity());
		return Optional.of(BeneficiaryDataAccessMapper.INSTANCE.toBeneficiaryDto(beneficiary, fundSource));
	}

	@Override
	public Paging<BeneficiaryDto> findBeneficiaries(FindBeneficiariesQuery query) {
		BeneficiaryEntitySpecification specs = BeneficiaryDataAccessMapper.INSTANCE
				.beneficiariesFilterQueryToBeneficiaryEntitySpecification(query.getFilter());

		PageRequest pageRequest = PageRequest.of(query.getPage(), query.getSize(), Sort.Direction.fromString(query
				.getSortDirection()), query.getSortFields()
						.toArray(new String[0]));
		Page<BeneficiaryDto> result = beneficiaryJpaRepository.findAll(specs, pageRequest)
				.map(beneficiaryEntity -> {
					Beneficiary beneficiary = BeneficiaryDataAccessMapper.INSTANCE.beneficiaryEntityToBeneficiary(
							beneficiaryEntity);
					FundSource fundSource = FundSourceDataAccessMapper.INSTANCE.fundSourceEntityToFundSource(
							beneficiaryEntity.getFundSourceEntity());
					return BeneficiaryDataAccessMapper.INSTANCE.toBeneficiaryDto(beneficiary, fundSource);
				});
		return new Paging<>(result.getContent(), result.getTotalElements(), result.getTotalPages(), result.getNumber(),
				result.getSort()
						.stream()
						.map(order -> order.getProperty() + ","
								+ order.getDirection()
										.name())
						.collect(Collectors.toList()));
	}

	@Override
	public List<Beneficiary> getBeneficiaryBy(Long fundSourceId, Integer size, Instant nextTopupDate, Long schedulerId,
			Long tenantId, Set<Long> excludeIds) {
		return beneficiaryJpaRepository.getBeneficiariesBy(fundSourceId, size, nextTopupDate, schedulerId, tenantId,
				excludeIds)
				.stream()
				.map(BeneficiaryDataAccessMapper.INSTANCE::beneficiaryEntityToBeneficiary)
				.toList();
	}

	@Override
	public void removeBeneficiary(Long id) {
		beneficiaryJpaRepository.deleteById(id);
	}

	@Override
	public List<Beneficiary> getBeneficiaryBy(Long fundSourceId, Long tenantId) {
		return beneficiaryJpaRepository.getBeneficiariesBy(fundSourceId, tenantId)
				.stream()
				.map(BeneficiaryDataAccessMapper.INSTANCE::beneficiaryEntityToBeneficiary)
				.toList();
	}
}
