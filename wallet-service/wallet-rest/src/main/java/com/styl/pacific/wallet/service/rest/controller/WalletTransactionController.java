/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.rest.controller;

import com.styl.pacific.application.rest.context.RequestContext;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.wallet.service.apis.WalletTransactionsApi;
import com.styl.pacific.wallet.service.domain.dto.transaction.FindWalletTransactionsQuery;
import com.styl.pacific.wallet.service.domain.dto.transaction.WalletTransactionsFilterQuery;
import com.styl.pacific.wallet.service.domain.dto.wallet.GetWalletTransactionQuery;
import com.styl.pacific.wallet.service.domain.port.input.service.WalletTransactionDomainService;
import com.styl.pacific.wallet.service.entity.WalletTransaction;
import com.styl.pacific.wallet.service.requests.transaction.FindWalletTransactionsRequest;
import com.styl.pacific.wallet.service.requests.transaction.GetWalletTransactionRequest;
import com.styl.pacific.wallet.service.responses.transaction.ListWalletTransactionResponse;
import com.styl.pacific.wallet.service.responses.transaction.WalletTransactionResponse;
import com.styl.pacific.wallet.service.rest.mapper.WalletControllerMapper;
import com.styl.pacific.wallet.service.rest.mapper.WalletTransactionControllerMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */

@RestController
@RequiredArgsConstructor
public class WalletTransactionController implements WalletTransactionsApi {

	private final WalletTransactionDomainService walletTransactionDomainService;

	private final RequestContext requestContext;

	@Override
	public WalletTransactionResponse getWalletTransaction(GetWalletTransactionRequest request) {
		GetWalletTransactionQuery query = WalletTransactionControllerMapper.INSTANCE.toGetWalletTransactionQuery(
				request);
		WalletTransaction walletTransaction = walletTransactionDomainService.getWalletTransactionByPaymentSessionId(
				query);
		return WalletTransactionControllerMapper.INSTANCE.walletTransactionToWalletTransactionResponse(
				walletTransaction);
	}

	public ListWalletTransactionResponse findWalletTransactions(FindWalletTransactionsRequest request) {
		WalletTransactionsFilterQuery filterQuery = WalletControllerMapper.INSTANCE
				.walletTransactionsFilterRequestToWalletTransactionsFilterQuery(request.getFilter(), requestContext
						.getTenantId());
		FindWalletTransactionsQuery query = WalletControllerMapper.INSTANCE
				.findsWalletTransactionsRequestToFindsWalletTransactionsQuery(request, filterQuery);
		Paging<WalletTransaction> walletTransactions = walletTransactionDomainService.findWalletTransactions(query,
				requestContext.getTokenClaim());
		return new ListWalletTransactionResponse(walletTransactions.getContent()
				.stream()
				.map(WalletTransactionControllerMapper.INSTANCE::walletTransactionToWalletTransactionResponse)
				.toList(), walletTransactions.getTotalElements(), walletTransactions.getTotalPages(), walletTransactions
						.getPage(), walletTransactions.getSort());
	}

	@Override
	public WalletTransactionResponse getWalletTransactionById(long id) {
		GetWalletTransactionQuery query = GetWalletTransactionQuery.builder()
				.tenantId(requestContext.getTenantId())
				.id(id)
				.build();
		WalletTransaction walletTransaction = walletTransactionDomainService.getWalletTransactionById(query);
		return WalletTransactionControllerMapper.INSTANCE.walletTransactionToWalletTransactionResponse(
				walletTransaction);
	}
}
