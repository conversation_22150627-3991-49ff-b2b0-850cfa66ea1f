/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.rest.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.wallet.service.domain.dto.fundsource.CreateFundSourceCommand;
import com.styl.pacific.wallet.service.domain.dto.fundsource.CreateFundSourceTopupSchedulerCommand;
import com.styl.pacific.wallet.service.domain.dto.fundsource.FindFundSourceTopupHistoryQuery;
import com.styl.pacific.wallet.service.domain.dto.fundsource.FindFundSourceTopupTrackingQuery;
import com.styl.pacific.wallet.service.domain.dto.fundsource.FindFundSourcesQuery;
import com.styl.pacific.wallet.service.domain.dto.fundsource.FundSourcesFilterQuery;
import com.styl.pacific.wallet.service.domain.dto.fundsource.FundSourcesTopupHistoryFilterQuery;
import com.styl.pacific.wallet.service.domain.dto.fundsource.FundSourcesTopupTrackingFilterQuery;
import com.styl.pacific.wallet.service.domain.dto.fundsource.UpdateFundSourceCommand;
import com.styl.pacific.wallet.service.domain.dto.fundsource.UpdateFundSourceTopupSchedulerCommand;
import com.styl.pacific.wallet.service.domain.mapper.WalletDataCommonMapper;
import com.styl.pacific.wallet.service.entity.FundSource;
import com.styl.pacific.wallet.service.entity.FundSourceTopupHistory;
import com.styl.pacific.wallet.service.entity.FundSourceTopupScheduler;
import com.styl.pacific.wallet.service.entity.FundSourceTopupTracking;
import com.styl.pacific.wallet.service.requests.fundsource.CreateFundSourceRequest;
import com.styl.pacific.wallet.service.requests.fundsource.CreateFundSourceSchedulerRequest;
import com.styl.pacific.wallet.service.requests.fundsource.FindFundSourceRequest;
import com.styl.pacific.wallet.service.requests.fundsource.FundSourceTopupHistoryFilterRequest;
import com.styl.pacific.wallet.service.requests.fundsource.FundSourceTopupTrackingFilterRequest;
import com.styl.pacific.wallet.service.requests.fundsource.FundSourcesFilterRequest;
import com.styl.pacific.wallet.service.requests.fundsource.UpdateFundSourceRequest;
import com.styl.pacific.wallet.service.requests.fundsource.UpdateFundSourceSchedulerRequest;
import com.styl.pacific.wallet.service.responses.fundsource.FindFundSourceTopupHistoriesRequest;
import com.styl.pacific.wallet.service.responses.fundsource.FindFundSourceTopupTrackingRequest;
import com.styl.pacific.wallet.service.responses.fundsource.FundSourceResponse;
import com.styl.pacific.wallet.service.responses.fundsource.FundSourceTopupHistoryResponse;
import com.styl.pacific.wallet.service.responses.fundsource.FundSourceTopupSchedulerResponse;
import com.styl.pacific.wallet.service.responses.fundsource.FundSourceTopupTrackingResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class, WalletDataCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface FundSourceControllerMapper {

	FundSourceControllerMapper INSTANCE = Mappers.getMapper(FundSourceControllerMapper.class);

	@Mapping(target = "fundExpiresOn", qualifiedByName = "longToInstant")
	CreateFundSourceCommand createFundSourceRequestToCreateFundSourceCommand(CreateFundSourceRequest request,
			Long tenantId);

	@Mapping(target = "fundExpiresOn", qualifiedByName = "longToInstant")
	UpdateFundSourceCommand updateFundSourceRequestToCreateFundSourceCommand(UpdateFundSourceRequest request,
			Long tenantId, Long fundSourceId);

	@Mapping(target = "fundSourceId", source = "id", qualifiedByName = "fundSourceIdToLong")
	@Mapping(target = "fundExpiresOn", qualifiedByName = "instantToLong")
	@Mapping(target = "tenantId", qualifiedByName = "tenantIdToLong")
	@Mapping(target = "createdAt", qualifiedByName = "instantToLong")
	FundSourceResponse fundSourceToFundSourceResponse(FundSource fundSource);

	@Mapping(target = "filter", source = "filter")
	FindFundSourcesQuery findsFundSourceRequestToFindsFundSourceQuery(FindFundSourceRequest request,
			FundSourcesFilterQuery filter);

	@Mapping(target = "tenantId", source = "tenantId")
	@Mapping(target = "name", source = "filter.name")
	@Mapping(target = "statuses", source = "filter.statuses")
	FundSourcesFilterQuery fundSourcesFilterRequestToFundSourcesFilterQuery(FundSourcesFilterRequest filter,
			Long tenantId);

	@Mapping(target = "startTopupDate", qualifiedByName = "longToInstant")
	@Mapping(target = "endTopupDate", qualifiedByName = "longToInstant")
	CreateFundSourceTopupSchedulerCommand requestToCreateFundSourceTopupSchedulerCommand(
			CreateFundSourceSchedulerRequest request, Long tenantId);

	@Mapping(target = "startTopupDate", qualifiedByName = "instantToLong")
	@Mapping(target = "endTopupDate", qualifiedByName = "instantToLong")
	@Mapping(target = "nextTopupDate", qualifiedByName = "instantToLong")
	@Mapping(target = "fundSourceId", qualifiedByName = "fundSourceIdToLong")
	@Mapping(target = "id", qualifiedByName = "fundSourceTopupSchedulerIdToLong")
	@Mapping(target = "tenantId", qualifiedByName = "tenantIdToLong")
	@Mapping(target = "createdAt", qualifiedByName = "instantToLong")
	@Mapping(target = "amount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "currency", qualifiedByName = "currencyCodeToResponse")
	FundSourceTopupSchedulerResponse fundSourceSchedulerToFundSourceSchedulerResponse(
			FundSourceTopupScheduler fundSourceTopupScheduler);

	@Mapping(target = "startTopupDate", qualifiedByName = "longToInstant")
	@Mapping(target = "endTopupDate", qualifiedByName = "longToInstant")
	UpdateFundSourceTopupSchedulerCommand requestToUpdateFundSourceTopupSchedulerCommand(
			UpdateFundSourceSchedulerRequest request, Long tenantId, Long id);

	@Mapping(target = "filter", source = "filter")
	FindFundSourceTopupHistoryQuery findsFundSourceTopupHistoriesRequestToFindsFundSourceTopupHistoriesQuery(
			FindFundSourceTopupHistoriesRequest request, FundSourcesTopupHistoryFilterQuery filter);

	@Mapping(target = "fromTime", source = "filter.fromTime", qualifiedByName = "longToInstant")
	@Mapping(target = "toTime", source = "filter.toTime", qualifiedByName = "longToInstant")
	FundSourcesTopupHistoryFilterQuery fundSourcesTopupHistoryFilterRequestToFilterQuery(
			FundSourceTopupHistoryFilterRequest filter, Long tenantId);

	@Mapping(target = "fundSourceId", qualifiedByName = "fundSourceIdToString")
	@Mapping(target = "id", qualifiedByName = "fundSourceTopupHistoryIdToString")
	@Mapping(target = "fundSourceTopupSchedulerId", qualifiedByName = "fundSourceTopupSchedulerIdToString")
	@Mapping(target = "tenantId", qualifiedByName = "tenantIdToLong")
	@Mapping(target = "amount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "currency", qualifiedByName = "currencyCodeToResponse")
	@Mapping(target = "completedAt", qualifiedByName = "instantToLong")
	@Mapping(target = "createdAt", qualifiedByName = "instantToLong")
	FundSourceTopupHistoryResponse toFundSourceTopupHistoryResponse(FundSourceTopupHistory fundSourceTopupHistory);

	@Mapping(target = "filter", source = "filter")
	FindFundSourceTopupTrackingQuery findsFundSourceTopupTrackingRequestToFindsFundSourceTopupTrackingQuery(
			FindFundSourceTopupTrackingRequest request, FundSourcesTopupTrackingFilterQuery filter);

	@Mapping(target = "fromTime", source = "filter.fromTime", qualifiedByName = "longToInstant")
	@Mapping(target = "toTime", source = "filter.toTime", qualifiedByName = "longToInstant")
	FundSourcesTopupTrackingFilterQuery fundSourcesTopupTrackingFilterRequestToFilterQuery(
			FundSourceTopupTrackingFilterRequest filter, Long tenantId);

	@Mapping(target = "id", qualifiedByName = "fundSourceTopupTrackingIdToString")
	@Mapping(target = "fundSourceTopupHistoryId", qualifiedByName = "fundSourceTopupHistoryIdToString")
	@Mapping(target = "fundSourceTopupSchedulerId", qualifiedByName = "fundSourceTopupSchedulerIdToString")
	@Mapping(target = "walletTransactionId", qualifiedByName = "walletTransactionIdToString")
	@Mapping(target = "tenantId", qualifiedByName = "tenantIdToLong")
	@Mapping(target = "customerId", qualifiedByName = "userIdToLong")
	@Mapping(target = "amount", qualifiedByName = "moneyToBigInteger")
	@Mapping(target = "currency", qualifiedByName = "currencyCodeToResponse")
	@Mapping(target = "createdAt", qualifiedByName = "instantToLong")
	FundSourceTopupTrackingResponse toFundSourceTopupTrackingResponse(FundSourceTopupTracking fundSourceTopupTracking);
}
