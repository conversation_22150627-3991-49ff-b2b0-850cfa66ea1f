/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.rest.controller;

import com.styl.pacific.application.rest.context.RequestContext;
import com.styl.pacific.wallet.service.apis.WalletSpendingLimitSettingsApi;
import com.styl.pacific.wallet.service.domain.dto.walletspendinglimitsetting.FindWalletSpendingLimitSettingsQuery;
import com.styl.pacific.wallet.service.domain.dto.walletspendinglimitsetting.GetWalletSpendingLimitSettingQuery;
import com.styl.pacific.wallet.service.domain.dto.walletspendinglimitsetting.SaveWalletSpendingLimitSettingCommand;
import com.styl.pacific.wallet.service.domain.port.input.service.WalletSpendingLimitSettingDomainService;
import com.styl.pacific.wallet.service.entity.WalletSpendingLimitSetting;
import com.styl.pacific.wallet.service.enums.SpendingLimitType;
import com.styl.pacific.wallet.service.requests.spendinglimitsetting.SaveWalletSpendingLimitSettingRequest;
import com.styl.pacific.wallet.service.responses.spendinglimitsetting.ListWalletSpendingLimitSettingsResponse;
import com.styl.pacific.wallet.service.responses.spendinglimitsetting.WalletSpendingLimitSettingResponse;
import com.styl.pacific.wallet.service.rest.mapper.WalletSpendingLimitSettingControllerMapper;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */

@RestController
@RequiredArgsConstructor
public class WalletSpendingLimitSettingController implements WalletSpendingLimitSettingsApi {

	private final WalletSpendingLimitSettingDomainService walletSpendingLimitSettingDomainService;
	private final RequestContext requestContext;

	@Override
	public WalletSpendingLimitSettingResponse getWalletSpendingLimitSetting(long walletId, SpendingLimitType type) {
		WalletSpendingLimitSetting walletSpendingLimitSetting = walletSpendingLimitSettingDomainService
				.getWalletSpendingLimitSetting(GetWalletSpendingLimitSettingQuery.builder()
						.walletId(walletId)
						.tenantId(requestContext.getTenantId())
						.type(type)
						.build());
		return WalletSpendingLimitSettingControllerMapper.INSTANCE.toWalletSpendingLimitSettingResponse(
				walletSpendingLimitSetting);
	}

	@Override
	public ListWalletSpendingLimitSettingsResponse findWalletSpendingLimitSettings(long walletId) {
		List<WalletSpendingLimitSettingResponse> walletSpendingLimitSettingResponses = walletSpendingLimitSettingDomainService
				.findWalletSpendingLimitSettings(FindWalletSpendingLimitSettingsQuery.builder()
						.walletId(walletId)
						.tenantId(requestContext.getTenantId())
						.build())

				.stream()
				.map(WalletSpendingLimitSettingControllerMapper.INSTANCE::toWalletSpendingLimitSettingResponse)
				.toList();
		return new ListWalletSpendingLimitSettingsResponse(walletSpendingLimitSettingResponses);
	}

	@Override
	public ListWalletSpendingLimitSettingsResponse saveWalletSpendingLimitSettings(long walletId,
			SaveWalletSpendingLimitSettingRequest request) {
		SaveWalletSpendingLimitSettingCommand command = WalletSpendingLimitSettingControllerMapper.INSTANCE
				.toSaveWalletSpendingLimitSettingCommand(request, requestContext.getTenantId(), walletId);
		return new ListWalletSpendingLimitSettingsResponse(walletSpendingLimitSettingDomainService
				.saveWalletSpendingLimitSettings(command)
				.stream()
				.map(WalletSpendingLimitSettingControllerMapper.INSTANCE::toWalletSpendingLimitSettingResponse)
				.toList());
	}
}
