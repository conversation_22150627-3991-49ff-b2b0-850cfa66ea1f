/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.rest.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.wallet.service.domain.dto.topupsession.CreateTopupSessionCommand;
import com.styl.pacific.wallet.service.domain.mapper.WalletDataCommonMapper;
import com.styl.pacific.wallet.service.entity.TopupSession;
import com.styl.pacific.wallet.service.requests.topupsession.CreateTopupSessionRequest;
import com.styl.pacific.wallet.service.responses.topupsession.TopupSessionResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class, WalletDataCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface TopupSessionControllerMapper {

	TopupSessionControllerMapper INSTANCE = Mappers.getMapper(TopupSessionControllerMapper.class);

	@Mapping(target = "amount", qualifiedByName = "bigIntegerToMoney")
	CreateTopupSessionCommand toTopupSessionResponse(CreateTopupSessionRequest request, Long tenantId);

	@Mapping(target = "topupSessionId", source = "topupSession.id", qualifiedByName = "topupSessionIdToLong")
	@Mapping(target = "tenantId", qualifiedByName = "tenantIdToLong")
	@Mapping(target = "currency", qualifiedByName = "currencyCodeToResponse")
	@Mapping(target = "walletId", qualifiedByName = "walletIdToLong")
	@Mapping(target = "amount", qualifiedByName = "moneyToBigInteger")
	TopupSessionResponse toTopupSessionResponse(TopupSession topupSession);
}
