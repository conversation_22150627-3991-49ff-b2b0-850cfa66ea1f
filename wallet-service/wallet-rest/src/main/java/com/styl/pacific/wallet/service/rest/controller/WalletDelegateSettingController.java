/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.rest.controller;

import com.styl.pacific.application.rest.context.RequestContext;
import com.styl.pacific.wallet.service.apis.WalletDelegateSettingsApi;
import com.styl.pacific.wallet.service.domain.dto.walletsetting.GetWalletDelegateSettingQuery;
import com.styl.pacific.wallet.service.domain.dto.walletsetting.SaveWalletDelegateSettingCommand;
import com.styl.pacific.wallet.service.domain.port.input.service.WalletDelegateSettingDomainService;
import com.styl.pacific.wallet.service.entity.WalletDelegateSetting;
import com.styl.pacific.wallet.service.requests.walletsetting.FindWalletDelegateSettingsRequest;
import com.styl.pacific.wallet.service.requests.walletsetting.SaveWalletDelegateSettingRequest;
import com.styl.pacific.wallet.service.responses.walletsetting.ListWalletDelegateSettingsResponse;
import com.styl.pacific.wallet.service.responses.walletsetting.WalletDelegateSettingResponse;
import com.styl.pacific.wallet.service.rest.mapper.WalletDelegateSettingControllerMapper;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */

@RestController
@RequiredArgsConstructor
public class WalletDelegateSettingController implements WalletDelegateSettingsApi {

	private final WalletDelegateSettingDomainService walletDelegateSettingDomainService;
	private final RequestContext requestContext;

	@Override
	public WalletDelegateSettingResponse getWalletDelegateSetting(String subWalletId) {
		WalletDelegateSetting walletDelegateSetting = walletDelegateSettingDomainService.getWalletDelegateSetting(
				GetWalletDelegateSettingQuery.builder()
						.subWalletId(Long.parseLong(subWalletId))
						.tenantId(requestContext.getTenantId())
						.build());
		return WalletDelegateSettingControllerMapper.INSTANCE.toWalletDelegateSettingResponse(walletDelegateSetting);
	}

	@Override
	public ListWalletDelegateSettingsResponse findWalletDelegateSettings(FindWalletDelegateSettingsRequest request) {
		List<WalletDelegateSettingResponse> walletDelegateSettings = walletDelegateSettingDomainService
				.getWalletDelegateSettings(WalletDelegateSettingControllerMapper.INSTANCE
						.toFindWalletDelegateSettingsQuery(request, requestContext.getTenantId()))
				.stream()
				.map(WalletDelegateSettingControllerMapper.INSTANCE::toWalletDelegateSettingResponse)
				.toList();
		return new ListWalletDelegateSettingsResponse(walletDelegateSettings);
	}

	@Override
	public WalletDelegateSettingResponse saveWalletDelegateSetting(SaveWalletDelegateSettingRequest request) {
		SaveWalletDelegateSettingCommand command = WalletDelegateSettingControllerMapper.INSTANCE
				.toSaveWalletDelegateSettingCommand(request, requestContext.getTenantId());
		WalletDelegateSetting walletDelegateSetting = walletDelegateSettingDomainService.saveWalletDelegateSetting(
				command);
		return WalletDelegateSettingControllerMapper.INSTANCE.toWalletDelegateSettingResponse(walletDelegateSetting);
	}

	@Override
	public void cancelWalletDelegateSetting(long id) {
		walletDelegateSettingDomainService.cancelWalletDelegateSetting(id, requestContext.getTenantId());
	}
}
