/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.entity;

import com.styl.pacific.domain.entity.AggregateRoot;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.domain.valueobject.WalletDelegateSettingId;
import com.styl.pacific.domain.valueobject.WalletId;
import java.time.Instant;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public class WalletDelegateSetting extends AggregateRoot<WalletDelegateSettingId> {

	private TenantId tenantId;

	private WalletId parentWalletId;

	private UserId parentCustomerId;

	private WalletId subWalletId;

	private UserId subCustomerId;

	private Instant createdAt;

	private Instant updatedAt;

	private Instant deletedAt;

	public static Builder builder() {
		return new Builder();
	}

	private WalletDelegateSetting(Builder builder) {
		setId(builder.id);
		tenantId = builder.tenantId;
		parentWalletId = builder.parentWalletId;
		parentCustomerId = builder.parentCustomerId;
		subWalletId = builder.subWalletId;
		subCustomerId = builder.subCustomerId;
		createdAt = builder.createdAt;
		updatedAt = builder.updatedAt;
		deletedAt = builder.deletedAt;
	}

	public static final class Builder {
		private WalletDelegateSettingId id;
		private TenantId tenantId;
		private WalletId parentWalletId;
		private UserId parentCustomerId;
		private WalletId subWalletId;
		private UserId subCustomerId;
		private Instant createdAt;
		private Instant updatedAt;
		private Instant deletedAt;

		public Builder() {
			super();
		}

		public Builder tenantId(TenantId tenantId) {
			this.tenantId = tenantId;
			return this;
		}

		public Builder parentWalletId(WalletId parentWalletId) {
			this.parentWalletId = parentWalletId;
			return this;
		}

		public Builder parentCustomerId(UserId parentCustomerId) {
			this.parentCustomerId = parentCustomerId;
			return this;
		}

		public Builder subWalletId(WalletId subWalletId) {
			this.subWalletId = subWalletId;
			return this;
		}

		public Builder subCustomerId(UserId subCustomerId) {
			this.subCustomerId = subCustomerId;
			return this;
		}

		public Builder createdAt(Instant createdAt) {
			this.createdAt = createdAt;
			return this;
		}

		public Builder updatedAt(Instant updatedAt) {
			this.updatedAt = updatedAt;
			return this;
		}

		public Builder deletedAt(Instant deletedAt) {
			this.deletedAt = deletedAt;
			return this;
		}

		public Builder id(WalletDelegateSettingId id) {
			this.id = id;
			return this;
		}

		public WalletDelegateSetting build() {
			return new WalletDelegateSetting(this);
		}
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (!(o instanceof WalletDelegateSetting that))
			return false;
		if (!super.equals(o))
			return false;
		return Objects.equals(tenantId, that.tenantId) && Objects.equals(parentWalletId, that.parentWalletId) && Objects
				.equals(parentCustomerId, that.parentCustomerId) && Objects.equals(subWalletId, that.subWalletId)
				&& Objects.equals(subCustomerId, that.subCustomerId) && Objects.equals(createdAt, that.createdAt)
				&& Objects.equals(updatedAt, that.updatedAt) && Objects.equals(deletedAt, that.deletedAt);
	}

	@Override
	public int hashCode() {
		return Objects.hash(super.hashCode(), tenantId, parentWalletId, parentCustomerId, subWalletId, subCustomerId,
				createdAt, updatedAt, deletedAt);
	}
}
