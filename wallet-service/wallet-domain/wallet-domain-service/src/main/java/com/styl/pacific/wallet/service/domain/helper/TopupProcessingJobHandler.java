/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.domain.helper;

import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.wallet.service.domain.dto.wallet.AddBalanceCommand;
import com.styl.pacific.wallet.service.domain.dto.wallet.CreateWalletCommandDto;
import com.styl.pacific.wallet.service.domain.dto.wallet.GetWalletQuery;
import com.styl.pacific.wallet.service.domain.hander.WalletBalanceHandler;
import com.styl.pacific.wallet.service.domain.output.repository.BeneficiaryRepository;
import com.styl.pacific.wallet.service.domain.output.repository.FundSourceTopupTrackingRepository;
import com.styl.pacific.wallet.service.entity.Beneficiary;
import com.styl.pacific.wallet.service.entity.FundSource;
import com.styl.pacific.wallet.service.entity.FundSourceTopupHistory;
import com.styl.pacific.wallet.service.entity.FundSourceTopupTracking;
import com.styl.pacific.wallet.service.entity.Wallet;
import com.styl.pacific.wallet.service.entity.WalletTransaction;
import com.styl.pacific.wallet.service.entity.id.FundSourceTopupTrackingIdGenerator;
import com.styl.pacific.wallet.service.entity.id.WalletIdGenerator;
import com.styl.pacific.wallet.service.enums.WalletType;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 */

@Component
@AllArgsConstructor
public class TopupProcessingJobHandler {
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
	private final WalletBalanceHandler walletBalanceHandler;
	private final WalletHelper walletHelper;
	private final NotificationHelper notificationHelper;
	private final BeneficiaryRepository beneficiaryRepository;
	private final FundSourceTopupTrackingIdGenerator fundSourceTopupTrackingIdGenerator;
	private final WalletIdGenerator walletIdGenerator;
	private final FundSourceTopupHelper fundSourceTopupHelper;

	private final FundSourceTopupTrackingRepository fundSourceTopupTrackingRepository;

	public void processTopupToBeneficiariesWallet(FundSource fundSource, FundSourceTopupHistory history) {
		logger.info("Processing topup to beneficiaries wallet for fundSource: {} - Scheduler History: {}", fundSource
				.getId()
				.getValue(), history.getId());
		Long tenantId = fundSource.getTenantId()
				.getValue();

		List<Beneficiary> beneficiaries = beneficiaryRepository.getBeneficiaryBy(fundSource.getId()
				.getValue(), tenantId);
		logger.info("FundSource: {} - Scheduler History: {} - Beneficiaries: {}", fundSource.getId()
				.getValue(), history.getId(), beneficiaries.size());
		if (CollectionUtils.isEmpty(beneficiaries)) {
			logger.info("No beneficiaries found for fundSource: {}", fundSource.getId()
					.getValue());
			return;
		}

		for (Beneficiary dto : beneficiaries) {
			Wallet fundedWallet = createFundedWalletIfNotExist(tenantId, fundSource, history.getCurrency(), dto
					.getCustomerId()
					.getValue());

			FundSourceTopupTracking topupTracking = fundSourceTopupTrackingRepository.findTopupTracking(tenantId,
					history.getId()
							.getValue(), dto.getCustomerId()
									.getValue())
					.orElse(null);

			if (topupTracking == null) {
				WalletTransaction walletTransaction = walletBalanceHandler.addBalance(AddBalanceCommand.builder()
						.tenantId(tenantId)
						.walletId(fundedWallet.getId()
								.getValue())
						.amount(history.getAmount())
						.description(String.format("Scheduled Top-up from %s", fundSource.getName()))
						.expiresOn(history.getFundExpiresOn())
						.build());

				// save topupTracking
				fundSourceTopupHelper.saveFundSourceTopupTracking(FundSourceTopupTracking.builder()
						.id(fundSourceTopupTrackingIdGenerator.nextId())
						.fundSourceTopupHistoryId(history.getId())
						.fundSourceTopupSchedulerId(history.getFundSourceTopupSchedulerId())
						.walletTransactionId(walletTransaction.getId())
						.tenantId(new TenantId(tenantId))
						.customerId(dto.getCustomerId())
						.amount(history.getAmount())
						.currency(history.getCurrency())
						.build());

				// send notification balance changed
				notificationHelper.sendNotificationBalanceChange(walletTransaction);
			}

		}
	}

	private Wallet createFundedWalletIfNotExist(Long tenantId, FundSource fundSource, String currency,
			Long customerId) {
		Wallet wallet = walletHelper.getWallet(GetWalletQuery.builder()
				.customerId(customerId)
				.tenantId(tenantId)
				.type(WalletType.FUNDED)
				.fundSourceId(fundSource.getId()
						.getValue())
				.build());

		return Optional.ofNullable(wallet)
				.orElseGet(() -> walletHelper.createFundedWallet(CreateWalletCommandDto.builder()
						.tenantId(tenantId)
						.fundSourceId(fundSource.getId()
								.getValue())
						.customerId(customerId)
						.type(WalletType.FUNDED)
						.build(), currency, walletIdGenerator.nextId(), fundSource.getName()));
	}
}
