/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.domain;

import com.styl.pacific.wallet.service.domain.dto.payment.SettlementDto;
import com.styl.pacific.wallet.service.domain.dto.topupsession.CreateTopupSessionCommand;
import com.styl.pacific.wallet.service.domain.dto.topupsession.GetTopupSessionQuery;
import com.styl.pacific.wallet.service.domain.dto.wallet.GetWalletQuery;
import com.styl.pacific.wallet.service.domain.hander.TopupSettlementHandler;
import com.styl.pacific.wallet.service.domain.helper.NotificationHelper;
import com.styl.pacific.wallet.service.domain.helper.TopupSessionHelper;
import com.styl.pacific.wallet.service.domain.helper.WalletHelper;
import com.styl.pacific.wallet.service.domain.port.input.service.TopupSessionDomainService;
import com.styl.pacific.wallet.service.entity.TopupSession;
import com.styl.pacific.wallet.service.entity.Wallet;
import com.styl.pacific.wallet.service.entity.WalletTransaction;
import com.styl.pacific.wallet.service.enums.TopupSessionStatus;
import com.styl.pacific.wallet.service.enums.WalletType;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */

@Component
@AllArgsConstructor
@Service
public class TopupSessionDomainServiceImpl implements TopupSessionDomainService {

	private final Logger logger = LoggerFactory.getLogger(this.getClass());

	private final WalletHelper walletHelper;

	private final TopupSessionHelper topupSessionHelper;

	private final NotificationHelper notificationHelper;

	private final TopupSettlementHandler topupSettlementHandler;

	@Override
	@Transactional
	public TopupSession createTopupSession(CreateTopupSessionCommand command) {
		Wallet wallet = walletHelper.getWalletOrThrowNotFound(GetWalletQuery.builder()
				.walletId(command.getWalletId())
				.tenantId(command.getTenantId())
				.type(WalletType.DEPOSIT)
				.build());
		return topupSessionHelper.persistNewTopupSession(command, wallet.getId());
	}

	@Override
	@Transactional(readOnly = true)
	public TopupSession getTopupSession(GetTopupSessionQuery query) {
		return topupSessionHelper.getTopupSessionOrThrowNotFound(query);
	}

	@Override
	public void updateTopupSettlement(SettlementDto dto) {

		// validate topupSession, update status topupSession
		TopupSession topupSession = topupSessionHelper.getAndValidateTopupSession(GetTopupSessionQuery.builder()
				.paymentRef(dto.getPaymentRef())
				.tenantId(dto.getTenantId())
				.build(), dto);

		// idempotent
		if (topupSession.getStatus()
				.equals(TopupSessionStatus.SUCCESS)) {
			logger.info("TopupSession was success with id: {}", topupSession.getId()
					.getValue());
			return;
		}

		WalletTransaction walletTransaction = topupSettlementHandler.handle(dto, topupSession);

		// send notification balance changed
		notificationHelper.sendNotificationBalanceChange(walletTransaction);
	}

}
