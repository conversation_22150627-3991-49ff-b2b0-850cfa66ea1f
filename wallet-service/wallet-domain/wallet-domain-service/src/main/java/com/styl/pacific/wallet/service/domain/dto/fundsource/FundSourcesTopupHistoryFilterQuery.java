/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.domain.dto.fundsource;

import com.styl.pacific.wallet.service.enums.FundSourceTopupStatus;
import com.styl.pacific.wallet.service.enums.TopupFrequency;
import java.time.Instant;
import java.util.List;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Builder
@Data
public class FundSourcesTopupHistoryFilterQuery {

	private Long id;

	private Long tenantId;

	private Long fundSourceId;

	private Long fundSourceTopupSchedulerId;

	private List<TopupFrequency> topupFrequencies;

	private List<FundSourceTopupStatus> statuses;

	private final Instant fromTime;

	private final Instant toTime;

}
