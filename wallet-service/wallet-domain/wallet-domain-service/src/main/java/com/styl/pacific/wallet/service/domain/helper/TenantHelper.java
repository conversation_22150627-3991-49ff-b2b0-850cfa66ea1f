/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.domain.helper;

import com.styl.pacific.wallet.service.domain.output.repository.TenantRepository;
import com.styl.pacific.wallet.service.dto.TenantDto;
import com.styl.pacific.wallet.service.exception.WalletDomainException;
import java.util.Objects;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class TenantHelper {
	private final TenantRepository tenantRepository;

	public TenantDto getTenantById(Long tenantId) {
		return tenantRepository.getTenantById(tenantId);
	}

	public void validateCurrency(Long tenantId, String currency) {
		TenantDto tenantDto = tenantRepository.getTenantById(tenantId);
		if (Objects.isNull(tenantDto.getSettings()) || !Objects.equals(currency, tenantDto.getSettings()
				.getCurrency())) {
			throw new WalletDomainException("Currency does not match configuration with tenantId: " + tenantId);
		}
	}
}
