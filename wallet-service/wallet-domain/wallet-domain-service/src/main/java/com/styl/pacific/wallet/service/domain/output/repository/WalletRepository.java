/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.domain.output.repository;

import com.styl.pacific.domain.dto.pagination.PaginationQuery;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.wallet.service.domain.dto.wallet.GetWalletQuery;
import com.styl.pacific.wallet.service.domain.dto.wallet.WalletsFilterQuery;
import com.styl.pacific.wallet.service.domain.dto.walletentry.FindWalletEntriesQuery;
import com.styl.pacific.wallet.service.entity.Wallet;
import com.styl.pacific.wallet.service.entity.WalletEntry;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 *
 */
public interface WalletRepository {

	Wallet save(Wallet wallet);

	Optional<Wallet> getWallet(GetWalletQuery query);

	Paging<Wallet> findWallets(PaginationQuery<WalletsFilterQuery> query);

	WalletEntry save(WalletEntry walletEntry);

	List<WalletEntry> findAvailableWalletEntries(FindWalletEntriesQuery query);

	Optional<Wallet> getWalletByIdAndLocking(Long id, Long tenantId);

	void delete(Wallet wallet);

	List<WalletEntry> getExpiredWalletEntries(Integer size, Set<Long> excludeIds);

	boolean isExpiredWalletEntriesExisting(Set<Long> excludeIds);

	List<Wallet> findAllWallets(WalletsFilterQuery query);

	Optional<Wallet> getDepositWalletByCustomerIdAndLocking(Long customerId, Long tenantId);

	List<WalletEntry> findAvailableWalletEntriesExpiredInRangeTime(FindWalletEntriesQuery query);

	Optional<WalletEntry> getSoonestExpiringEntry(Long tenantId, Long customerId, Long walletId);
}
