/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.domain;

import static com.styl.pacific.wallet.service.domain.utils.UserTokenHelper.isCustomerToken;

import com.styl.pacific.domain.dto.TokenClaim;
import com.styl.pacific.domain.dto.pagination.PaginationQuery;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.wallet.service.domain.dto.transaction.UpdatePaymentTransactionCommand;
import com.styl.pacific.wallet.service.domain.dto.transaction.WalletTransactionsFilterQuery;
import com.styl.pacific.wallet.service.domain.dto.wallet.GetWalletTransactionQuery;
import com.styl.pacific.wallet.service.domain.output.repository.WalletTransactionRepository;
import com.styl.pacific.wallet.service.domain.port.input.service.WalletTransactionDomainService;
import com.styl.pacific.wallet.service.entity.WalletTransaction;
import com.styl.pacific.wallet.service.exception.WalletTransactionNotFoundException;
import java.util.Objects;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 */

@Component
@AllArgsConstructor
@Service
public class WalletTransactionDomainServiceImpl implements WalletTransactionDomainService {

	private final Logger logger = LoggerFactory.getLogger(this.getClass());

	private final WalletTransactionRepository walletTransactionRepository;

	@Override
	@Transactional
	public void updatePaymentTransaction(UpdatePaymentTransactionCommand command) {
		Optional<WalletTransaction> walletTransactionOptional = walletTransactionRepository.getWalletTransaction(
				GetWalletTransactionQuery.builder()
						.paymentSessionId(command.getPaymentSessionId())
						.tenantId(command.getTenantId())
						.build());

		// This check supports for Idempotency or Payment Transaction Failed
		if (walletTransactionOptional.isEmpty() || Objects.nonNull(walletTransactionOptional.get()
				.getPaymentTransactionId())) {
			return;
		}

		final var walletTransaction = walletTransactionOptional.get();
		walletTransaction.setPaymentTransactionId(command.getPaymentTransactionId());

		walletTransactionRepository.save(walletTransaction);
	}

	@Override
	@Transactional(readOnly = true)
	public WalletTransaction getWalletTransactionByPaymentSessionId(GetWalletTransactionQuery query) {
		return getWalletTransactionOrThrowNotFound(query);
	}

	@Override
	@Transactional(readOnly = true)
	public Paging<WalletTransaction> findWalletTransactions(PaginationQuery<WalletTransactionsFilterQuery> query,
			TokenClaim tokenClaim) {
		if (isCustomerToken(tokenClaim) && CollectionUtils.isEmpty(query.getFilter()
				.getCustomerIds())) {
			query.getFilter()
					.getCustomerIds()
					.add(Long.parseLong(tokenClaim.getUserId()));
		}
		return walletTransactionRepository.findWalletTransactions(query);
	}

	@Override
	public WalletTransaction getWalletTransactionById(GetWalletTransactionQuery query) {
		Optional<WalletTransaction> walletTransaction = walletTransactionRepository.getWalletTransaction(query);
		if (walletTransaction.isEmpty()) {
			throw new WalletTransactionNotFoundException("Wallet transaction not found with id: " + query.getId());
		}
		return walletTransaction.get();
	}

	private WalletTransaction getWalletTransactionOrThrowNotFound(GetWalletTransactionQuery query) {
		Optional<WalletTransaction> walletTransaction = walletTransactionRepository.getWalletTransaction(query);
		if (walletTransaction.isEmpty()) {
			throw new WalletTransactionNotFoundException("Wallet transaction not found with paymentSessionId: " + query
					.getPaymentSessionId());
		}
		return walletTransaction.get();
	}
}
