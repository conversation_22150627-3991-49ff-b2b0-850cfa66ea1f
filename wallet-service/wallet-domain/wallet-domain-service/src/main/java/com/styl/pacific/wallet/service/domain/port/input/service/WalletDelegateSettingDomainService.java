/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.domain.port.input.service;

import com.styl.pacific.wallet.service.domain.dto.walletsetting.FindWalletDelegateSettingsQuery;
import com.styl.pacific.wallet.service.domain.dto.walletsetting.GetWalletDelegateSettingQuery;
import com.styl.pacific.wallet.service.domain.dto.walletsetting.SaveWalletDelegateSettingCommand;
import com.styl.pacific.wallet.service.entity.WalletDelegateSetting;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
public interface WalletDelegateSettingDomainService {

	WalletDelegateSetting saveWalletDelegateSetting(SaveWalletDelegateSettingCommand command);

	List<WalletDelegateSetting> getWalletDelegateSettings(FindWalletDelegateSettingsQuery query);

	WalletDelegateSetting getWalletDelegateSetting(GetWalletDelegateSettingQuery query);

	void cancelWalletDelegateSetting(Long id, Long tenantId);
}
