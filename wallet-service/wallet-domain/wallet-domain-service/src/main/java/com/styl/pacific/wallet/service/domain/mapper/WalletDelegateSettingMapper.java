/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.domain.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.wallet.service.domain.dto.walletsetting.SaveWalletDelegateSettingCommand;
import com.styl.pacific.wallet.service.entity.WalletDelegateSetting;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(config = AppMapStructConfiguration.class, uses = { WalletDataCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface WalletDelegateSettingMapper {
	WalletDelegateSettingMapper INSTANCE = Mappers.getMapper(WalletDelegateSettingMapper.class);

	@Mapping(target = "tenantId", source = "setting.tenantId")
	@Mapping(target = "parentCustomerId", source = "parentCustomerId")
	@Mapping(target = "subCustomerId", source = "subCustomerId")
	@Mapping(target = "parentWalletId", source = "command.parentWalletId", qualifiedByName = "longToWalletId")
	@Mapping(target = "subWalletId", source = "command.subWalletId", qualifiedByName = "longToWalletId")
	WalletDelegateSetting saveWalletDelegateCommandToWalletDelegateSetting(SaveWalletDelegateSettingCommand command,
			WalletDelegateSetting setting, UserId parentCustomerId, UserId subCustomerId);

	@Mapping(target = "updatedAt", ignore = true)
	@Mapping(target = "subCustomerId", ignore = true)
	@Mapping(target = "parentCustomerId", ignore = true)
	@Mapping(target = "id", ignore = true)
	@Mapping(target = "deletedAt", ignore = true)
	@Mapping(target = "createdAt", ignore = true)
	@Mapping(target = "tenantId", qualifiedByName = "longToTenantId")
	@Mapping(target = "parentWalletId", ignore = true)
	@Mapping(target = "subWalletId", ignore = true)
	WalletDelegateSetting toWalletDelegateSettingDefault(Long tenantId);
}
