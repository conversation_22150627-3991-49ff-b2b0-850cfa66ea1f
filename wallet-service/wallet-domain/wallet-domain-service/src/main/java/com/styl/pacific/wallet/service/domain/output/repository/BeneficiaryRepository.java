/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.domain.output.repository;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.wallet.service.domain.dto.fundsourcedistribution.FindBeneficiariesQuery;
import com.styl.pacific.wallet.service.domain.dto.fundsourcedistribution.GetBeneficiaryQuery;
import com.styl.pacific.wallet.service.entity.Beneficiary;
import com.styl.pacific.wallet.service.entity.BeneficiaryDto;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface BeneficiaryRepository {

	Beneficiary save(Beneficiary beneficiary);

	Optional<BeneficiaryDto> getBeneficiary(GetBeneficiaryQuery query);

	Paging<BeneficiaryDto> findBeneficiaries(FindBeneficiariesQuery query);

	List<Beneficiary> getBeneficiaryBy(Long fundSourceId, Integer size, Instant nextTopupDate, Long schedulerId,
			Long tenantId, Set<Long> excludeIds);

	void removeBeneficiary(Long id);

	List<Beneficiary> getBeneficiaryBy(Long fundSourceId, Long tenantId);
}
