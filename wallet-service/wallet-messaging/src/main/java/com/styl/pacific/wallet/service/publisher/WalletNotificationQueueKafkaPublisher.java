/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.wallet.service.publisher;

import com.styl.pacific.kafka.notification.avro.model.NotificationCreatedAvroEvent;
import com.styl.pacific.kafka.producer.service.KafkaProducer;
import com.styl.pacific.notification.service.constant.Action;
import com.styl.pacific.notification.service.constant.EmailNotificationKey;
import com.styl.pacific.notification.service.constant.params.BalanceChangeTemplateKey;
import com.styl.pacific.wallet.service.domain.config.WalletKafkaConfigProperties;
import com.styl.pacific.wallet.service.domain.output.publisher.WalletNotificationQueuePublisher;
import com.styl.pacific.wallet.service.entity.WalletTransaction;
import com.styl.pacific.wallet.service.entity.WalletTransactionNotification;
import java.io.Serializable;
import java.text.DecimalFormat;
import java.time.Instant;
import java.util.Currency;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import lombok.AllArgsConstructor;
import org.apache.avro.specific.SpecificRecordBase;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class WalletNotificationQueueKafkaPublisher implements WalletNotificationQueuePublisher {

	private static final Logger logger = LoggerFactory.getLogger(WalletNotificationQueueKafkaPublisher.class);
	private final KafkaProducer<Serializable, SpecificRecordBase> kafkaProducer;
	private final WalletKafkaConfigProperties walletKafkaConfigProperties;

	@Async
	@Override
	public void publish(WalletTransactionNotification transactionNotification) {
		WalletTransaction walletTransaction = transactionNotification.getWalletTransaction();
		String balance = walletTransaction.getBalance()
				.formatByCurrency((Currency.getInstance(walletTransaction.getCurrency())))
				.toPlainString();
		String oldBalance = walletTransaction.getOldBalance()
				.formatByCurrency((Currency.getInstance(walletTransaction.getCurrency())))
				.toPlainString();
		String amount = walletTransaction.getAmount()
				.formatByCurrency((Currency.getInstance(walletTransaction.getCurrency())))
				.toPlainString();
		DecimalFormat decimalFormat = new DecimalFormat();

		NotificationCreatedAvroEvent notificationCreatedAvroEvent = NotificationCreatedAvroEvent.newBuilder()
				.setId(UUID.randomUUID())
				.setAction(Action.BALANCE_CHANGE)
				.setTenantId(walletTransaction.getTenantId()
						.getValue())
				.setUserId(walletTransaction.getCustomerId()
						.getValue())
				.setSource("wallet-service")
				.setData(Map.of(BalanceChangeTemplateKey.BALANCE, decimalFormat.format(Double.parseDouble(balance)),
						BalanceChangeTemplateKey.OLD_BALANCE, decimalFormat.format(Double.parseDouble(oldBalance)),
						BalanceChangeTemplateKey.AMOUNT, decimalFormat.format(Double.parseDouble(amount)),
						BalanceChangeTemplateKey.CURRENCY, walletTransaction.getCurrency(),
						BalanceChangeTemplateKey.DATE_CHANGE, String.valueOf(Instant.now()
								.toEpochMilli()), BalanceChangeTemplateKey.CUSTOMER_NAME, transactionNotification
										.getCustomerDto()
										.getName(), EmailNotificationKey.TO, Optional.ofNullable(transactionNotification
												.getCustomerDto()
												.getEmail())
												.orElse(""), BalanceChangeTemplateKey.DESCRIPTION, Optional.ofNullable(
														transactionNotification.getWalletTransaction()
																.getDescription())
														.orElse("")))
				.setCreatedAt(Instant.now()
						.toEpochMilli())
				.build();
		kafkaProducer.send(walletKafkaConfigProperties.getWalletNotificationTopic(), notificationCreatedAvroEvent
				.getId(), notificationCreatedAvroEvent, (result, error) -> {
					if (error == null) {
						logger.info("Sent QueueWalletNotificationAvroEvent with eventId: {}",
								notificationCreatedAvroEvent.getId());
					} else {
						logger.error("Error in sending message to topic: [{}] with eventId: {}",
								walletKafkaConfigProperties.getWalletNotificationTopic(), notificationCreatedAvroEvent
										.getId());
						logger.error(error.getMessage(), error);
					}
				});
	}
}
