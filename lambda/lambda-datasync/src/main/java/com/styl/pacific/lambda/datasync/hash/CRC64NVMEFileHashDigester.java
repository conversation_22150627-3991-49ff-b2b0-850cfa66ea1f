/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.lambda.datasync.hash;

import com.styl.pacific.lambda.datasync.exceptions.DataSyncDomainException;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Path;
import software.amazon.awssdk.crt.checksums.CRC64NVME;

public class CRC64NVMEFileHashDigester implements FileHashDigester {

	private static final int BUFFER_SIZE = 8192;

	@Override
	public String digestToHex(Path path) {
		try {
			final var hashValue = calculateCRC64NVME(new FileInputStream(path.toFile()));
			return Long.toHexString(hashValue);
		} catch (IOException e) {
			throw new DataSyncDomainException(e.getMessage(), e);
		}
	}

	private long calculateCRC64NVME(InputStream input) throws IOException {
		CRC64NVME crc = new CRC64NVME();
		crc.reset();
		byte[] buffer = new byte[BUFFER_SIZE];
		int bytesRead;
		while ((bytesRead = input.read(buffer)) != -1) {
			crc.update(buffer, 0, bytesRead);
		}
		return crc.getValue();
	}
}
