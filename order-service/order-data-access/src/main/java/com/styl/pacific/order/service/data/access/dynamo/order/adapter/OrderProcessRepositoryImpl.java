/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.dynamo.order.adapter;

import com.styl.pacific.order.service.data.acces.dynamodb.features.order.repository.OrderDynamoDbRepository;
import com.styl.pacific.order.service.data.access.jpa.features.mealtime.repository.MealTimeJpaRepository;
import com.styl.pacific.order.service.domain.features.order.dto.OrderDto;
import com.styl.pacific.order.service.domain.features.order.entity.Order;
import com.styl.pacific.order.service.domain.features.order.entity.OrderLight;
import com.styl.pacific.order.service.domain.features.order.ports.output.repository.OrderProcessRepository;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.NotImplementedException;

//@Component
@RequiredArgsConstructor
public class OrderProcessRepositoryImpl implements OrderProcessRepository {
	private final OrderDynamoDbRepository orderDynamoDbRepository;
	private final MealTimeJpaRepository mealTimeJpaRepository;

	@Override
	public Order save(Order order) {
		//		OrderDocument schemaEntity = OrderDataAccessDynamoMapper.INSTANCE.orderToOrderSchemaEntity(order);
		//		schemaEntity.setPartitionKey(OrderKeyGenerator.getPartitionKey(order.getTenantId()
		//				.getValue()));
		//		schemaEntity.setSortKey(OrderKeyGenerator.getSortKey(order.getId()
		//				.getValue()));
		//		orderDynamoDbRepository.save(schemaEntity);
		//		return orderDynamoDbRepository.findById(Key.builder()
		//				.partitionValue(schemaEntity.partitionKey)
		//				.sortValue(schemaEntity.sortKey)
		//				.build())
		//				.map(OrderDataAccessDynamoMapper.INSTANCE::orderSchemaEntityToOrder)
		//				.orElseThrow(() -> new OrderNotFoundException("Order not found"));
		throw new NotImplementedException("Not implemented");
	}

	@Override
	public Order update(Order order) {
		//		OrderDocument schemaEntity = OrderDataAccessDynamoMapper.INSTANCE.orderToOrderSchemaEntity(order);
		//		schemaEntity.setPartitionKey(OrderKeyGenerator.getPartitionKey(order.getTenantId()
		//				.getValue()));
		//		schemaEntity.setSortKey(OrderKeyGenerator.getSortKey(order.getId()
		//				.getValue()));
		//		return OrderDataAccessDynamoMapper.INSTANCE.orderSchemaEntityToOrder(orderDynamoDbRepository.update(
		//				schemaEntity));
		throw new NotImplementedException("Not implemented");
	}

	@Override
	public OrderLight updateLight(OrderLight order) {
		throw new NotImplementedException("Not implemented");
	}

	@Override
	public OrderDto updateDto(Order order) {
		throw new NotImplementedException("Not implemented");
	}

	@Override
	public List<Order> updateAll(List<Order> orders) {

		throw new NotImplementedException("Not implemented");
	}

	@Override
	public List<OrderLight> updateAllLight(List<OrderLight> orders) {
		throw new NotImplementedException("Not implemented");
	}
}
