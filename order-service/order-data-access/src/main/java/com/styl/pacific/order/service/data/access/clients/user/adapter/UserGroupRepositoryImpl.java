/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.clients.user.adapter;

import com.styl.pacific.common.feign.exception.FeignBadRequestException;
import com.styl.pacific.domain.valueobject.UserGroupId;
import com.styl.pacific.order.service.data.access.clients.user.UserGroupClient;
import com.styl.pacific.order.service.data.access.clients.user.mapper.UserFeignClientMapper;
import com.styl.pacific.order.service.domain.features.user.dto.UserGroupDto;
import com.styl.pacific.order.service.domain.features.user.port.output.repository.UserGroupRepository;
import com.styl.pacific.user.shared.http.groups.response.UserGroupResponse;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class UserGroupRepositoryImpl implements UserGroupRepository {
	private static final Logger log = LoggerFactory.getLogger(UserGroupRepositoryImpl.class);
	private final UserGroupClient userGroupClient;

	@Override
	public List<UserGroupDto> findByIds(List<UserGroupId> ids) {
		List<UserGroupDto> userGroups = new ArrayList<>();
		ids.forEach(id -> {
			try {
				UserGroupResponse userGroupNodeResponse = userGroupClient.getUserGroup(id.getValue());
				if (userGroupNodeResponse != null) {
					userGroups.add(UserFeignClientMapper.INSTANCE.userGroupResponseToUserGroupDto(
							userGroupNodeResponse));
				}
			} catch (FeignBadRequestException e) {
				log.info(e.getMessage(), e);
			}

		});
		return userGroups;
	}
}
