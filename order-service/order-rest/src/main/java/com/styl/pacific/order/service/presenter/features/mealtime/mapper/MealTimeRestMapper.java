/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.presenter.features.mealtime.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.order.service.domain.features.common.mapper.CommonDataMapper;
import com.styl.pacific.order.service.domain.features.mealtime.dto.command.CreateMealTimeCommand;
import com.styl.pacific.order.service.domain.features.mealtime.dto.command.UpdateMealTimeCommand;
import com.styl.pacific.order.service.domain.features.mealtime.dto.query.MealTimeQuery;
import com.styl.pacific.order.service.domain.features.mealtime.dto.query.PaginationMealTimeQuery;
import com.styl.pacific.order.service.domain.features.mealtime.entity.MealTime;
import com.styl.pacific.order.service.shared.http.mealtime.request.CreateMealTimeRequest;
import com.styl.pacific.order.service.shared.http.mealtime.request.UpdateMealTimeRequest;
import com.styl.pacific.order.service.shared.http.mealtime.request.query.MealTimeQueryRequest;
import com.styl.pacific.order.service.shared.http.mealtime.request.query.PaginationMealTimeQueryRequest;
import com.styl.pacific.order.service.shared.http.mealtime.response.MealTimeResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(config = AppMapStructConfiguration.class, uses = { CommonDataMapper.class, MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface MealTimeRestMapper {
	MealTimeRestMapper INSTANCE = Mappers.getMapper(MealTimeRestMapper.class);

	CreateMealTimeCommand createMealTimeRequestToCommand(CreateMealTimeRequest request);

	UpdateMealTimeCommand updateMealTimeRequestToCommand(UpdateMealTimeRequest request);

	@Mapping(target = "id", source = "model.id", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "tenantId", source = "model.tenantId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "createdAt", source = "model.createdAt", qualifiedByName = "instantToLong")
	@Mapping(target = "updatedAt", source = "model.updatedAt", qualifiedByName = "instantToLong")
	@Mapping(target = "color", source = "model.color", qualifiedByName = "colorToString")
	MealTimeResponse mealTimeToMealTimeResponse(MealTime model);

	MealTimeQuery toQuery(MealTimeQueryRequest request);

	PaginationMealTimeQuery toQuery(PaginationMealTimeQueryRequest request);
}
