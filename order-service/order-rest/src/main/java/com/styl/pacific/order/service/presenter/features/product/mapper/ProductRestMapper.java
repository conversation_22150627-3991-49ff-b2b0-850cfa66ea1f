/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.presenter.features.product.mapper;

import com.styl.pacific.aws.s3.mapper.Mapstruct3SMapper;
import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.domain.valueobject.ProductId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.domain.features.common.mapper.CommonDataMapper;
import com.styl.pacific.order.service.domain.features.inventory.dto.InventoryDto;
import com.styl.pacific.order.service.domain.features.inventory.entity.Inventory;
import com.styl.pacific.order.service.domain.features.product.dto.ProductImageDto;
import com.styl.pacific.order.service.domain.features.product.dto.ProductStubDto;
import com.styl.pacific.order.service.presenter.features.category.mapper.CategoryRestMapper;
import com.styl.pacific.order.service.presenter.features.healthierchoice.mapper.HealthierChoiceRestMapper;
import com.styl.pacific.order.service.presenter.features.inventory.mapper.InventoryRestMapper;
import com.styl.pacific.order.service.shared.http.inventory.response.InventoryStubResponse;
import com.styl.pacific.order.service.shared.http.product.response.ProductImageResponse;
import com.styl.pacific.order.service.shared.http.product.response.ProductStubResponse;
import java.util.Objects;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR>
 */
@Mapper(config = AppMapStructConfiguration.class, uses = { CategoryRestMapper.class, HealthierChoiceRestMapper.class,
		InventoryRestMapper.class, MapstructCommonMapper.class, Mapstruct3SMapper.class, CommonDataMapper.class,
		MapstructCommonDomainMapper.class })
public interface ProductRestMapper {

	@Mapping(target = "image", source = "model.imagePath", qualifiedByName = "pathToFileResponse")
	@Mapping(target = "createdAt", source = "model.createdAt", qualifiedByName = "instantToLong")
	@Mapping(target = "updatedAt", source = "model.updatedAt", qualifiedByName = "instantToLong")
	ProductImageResponse productImageDtoToResponse(ProductImageDto model);

	@Mapping(target = "currency", source = "currencyCode", qualifiedByName = "currencyCodeToResponse")
	@Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "instantToLong")
	@Mapping(target = "updatedAt", source = "updatedAt", qualifiedByName = "instantToLong")
	@Mapping(target = "inventory", expression = "java(processInventory(dto.tenantId(), dto.id(), dto.inventory()))")
	ProductStubResponse productStubDtoToProductStubResponse(ProductStubDto dto);

	default InventoryStubResponse processInventory(Long tenantId, Long productId, InventoryDto inventory) {
		if (Objects.isNull(inventory)) {
			return InventoryRestMapper.INSTANCE.modelToStubResponse(Inventory.defaultValue(new TenantId(tenantId),
					new ProductId(productId)));
		}
		return InventoryRestMapper.INSTANCE.modelDtoToStubResponse(inventory);
	}
}
