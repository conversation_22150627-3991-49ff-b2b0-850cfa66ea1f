/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.presenter.features.servicecharge;

import com.styl.pacific.application.rest.context.RequestContext;
import com.styl.pacific.domain.dto.Content;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.domain.features.servicecharge.dto.command.CreateServiceChargeCommand;
import com.styl.pacific.order.service.domain.features.servicecharge.dto.command.UpdateServiceChargeCommand;
import com.styl.pacific.order.service.domain.features.servicecharge.dto.model.ServiceChargeDetailDTO;
import com.styl.pacific.order.service.domain.features.servicecharge.dto.query.ServiceChargeQuery;
import com.styl.pacific.order.service.domain.features.servicecharge.entity.ServiceCharge;
import com.styl.pacific.order.service.domain.features.servicecharge.ports.input.ServiceChargeService;
import com.styl.pacific.order.service.domain.features.servicecharge.valueobjects.ServiceChargeId;
import com.styl.pacific.order.service.presenter.features.servicecharge.mapper.ServiceChargeRestMapper;
import com.styl.pacific.order.service.shared.http.servicecharge.ServiceChargeApi;
import com.styl.pacific.order.service.shared.http.servicecharge.request.CreateServiceChargeRequest;
import com.styl.pacific.order.service.shared.http.servicecharge.request.ServiceChargeQueryRequest;
import com.styl.pacific.order.service.shared.http.servicecharge.request.UpdateServiceChargeRequest;
import com.styl.pacific.order.service.shared.http.servicecharge.response.ServiceChargeDetailDTOResponse;
import com.styl.pacific.order.service.shared.http.servicecharge.response.ServiceChargeResponse;
import jakarta.validation.Valid;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Validated
@RequiredArgsConstructor
public class ServiceChargeController implements ServiceChargeApi {
	private final RequestContext requestContext;
	private final ServiceChargeService serviceChargeService;

	@Override
	public Content<ServiceChargeDetailDTOResponse> findDetailAll(ServiceChargeQueryRequest request) {
		ServiceChargeQuery query = ServiceChargeRestMapper.INSTANCE.toQuery(request);
		Content<ServiceChargeDetailDTO> content = serviceChargeService.findDetailsAll(Optional.ofNullable(
				requestContext)
				.map(RequestContext::getTenantId)
				.map(TenantId::new)
				.orElse(null), query);
		return content.map(ServiceChargeRestMapper.INSTANCE::toResponse);
	}

	@Override
	public Content<ServiceChargeResponse> findAll(
			@Valid @SpringQueryMap @ModelAttribute ServiceChargeQueryRequest request) {
		ServiceChargeQuery query = ServiceChargeRestMapper.INSTANCE.toQuery(request);
		Content<ServiceCharge> content = serviceChargeService.findAll(Optional.ofNullable(requestContext)
				.map(RequestContext::getTenantId)
				.map(TenantId::new)
				.orElse(null), query);
		return content.map(ServiceChargeRestMapper.INSTANCE::toResponse);
	}

	@Override
	public ServiceChargeDetailDTOResponse findById(String id) {
		ServiceChargeDetailDTO serviceChargeDetailDTO = serviceChargeService.findById(Optional.ofNullable(
				requestContext)
				.map(RequestContext::getTenantId)
				.map(TenantId::new)
				.orElse(null), Optional.ofNullable(id)
						.filter(NumberUtils::isCreatable)
						.map(NumberUtils::createLong)
						.map(ServiceChargeId::new)
						.orElse(null));
		return ServiceChargeRestMapper.INSTANCE.toResponse(serviceChargeDetailDTO);
	}

	@Override
	public ServiceChargeDetailDTOResponse create(CreateServiceChargeRequest request) {
		CreateServiceChargeCommand command = ServiceChargeRestMapper.INSTANCE.toCommand(request);
		ServiceChargeDetailDTO serviceChargeDetailDTO = serviceChargeService.create(Optional.ofNullable(requestContext)
				.map(RequestContext::getTenantId)
				.map(TenantId::new)
				.orElse(null), command);
		return ServiceChargeRestMapper.INSTANCE.toResponse(serviceChargeDetailDTO);
	}

	@Override
	public ServiceChargeDetailDTOResponse update(String id, UpdateServiceChargeRequest request) {
		UpdateServiceChargeCommand command = ServiceChargeRestMapper.INSTANCE.toCommand(request);
		ServiceChargeDetailDTO serviceChargeDetailDTO = serviceChargeService.update(Optional.ofNullable(requestContext)
				.map(RequestContext::getTenantId)
				.map(TenantId::new)
				.orElse(null), Optional.ofNullable(id)
						.filter(NumberUtils::isCreatable)
						.map(NumberUtils::createLong)
						.map(ServiceChargeId::new)
						.orElse(null), command);

		return ServiceChargeRestMapper.INSTANCE.toResponse(serviceChargeDetailDTO);
	}

	@Override
	public void deleteById(String id) {
		serviceChargeService.deleteById(Optional.ofNullable(requestContext)
				.map(RequestContext::getTenantId)
				.map(TenantId::new)
				.orElse(null), Optional.ofNullable(id)
						.filter(NumberUtils::isCreatable)
						.map(NumberUtils::createLong)
						.map(ServiceChargeId::new)
						.orElse(null));
	}
}
