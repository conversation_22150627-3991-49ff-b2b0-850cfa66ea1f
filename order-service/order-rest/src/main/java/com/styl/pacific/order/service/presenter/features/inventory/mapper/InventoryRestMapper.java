/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.presenter.features.inventory.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.order.service.domain.features.common.mapper.CommonDataMapper;
import com.styl.pacific.order.service.domain.features.inventory.dto.InventoryDto;
import com.styl.pacific.order.service.domain.features.inventory.dto.command.UpdateInventoryCommand;
import com.styl.pacific.order.service.domain.features.inventory.dto.query.PaginationInventoryQuery;
import com.styl.pacific.order.service.domain.features.inventory.entity.Inventory;
import com.styl.pacific.order.service.shared.http.inventory.request.PaginationInventoryQueryRequest;
import com.styl.pacific.order.service.shared.http.inventory.request.UpdateInventoryRequest;
import com.styl.pacific.order.service.shared.http.inventory.response.InventoryResponse;
import com.styl.pacific.order.service.shared.http.inventory.response.InventoryStubResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class, CommonDataMapper.class,
		MapstructCommonDomainMapper.class })
public interface InventoryRestMapper {
	InventoryRestMapper INSTANCE = Mappers.getMapper(InventoryRestMapper.class);

	UpdateInventoryCommand updateRequestToQuery(UpdateInventoryRequest request);

	PaginationInventoryQuery queryRequestToQuery(PaginationInventoryQueryRequest request);

	@Mapping(target = "productId", source = "id", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "tenantId", source = "tenantId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "instantToLong")
	@Mapping(target = "updatedAt", source = "updatedAt", qualifiedByName = "instantToLong")
	InventoryResponse modelToResponse(Inventory model);

	InventoryStubResponse modelDtoToStubResponse(InventoryDto model);

	@Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "instantToLong")
	@Mapping(target = "updatedAt", source = "updatedAt", qualifiedByName = "instantToLong")
	InventoryStubResponse modelToStubResponse(Inventory model);
}
