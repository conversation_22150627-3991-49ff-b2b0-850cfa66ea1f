/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.messaging.features.inventory.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.kafka.inventory.avro.model.InventoryReservedAvroEvent;
import com.styl.pacific.kafka.inventory.avro.model.ProductQuantityAvro;
import com.styl.pacific.order.service.domain.features.common.mapper.CommonDataMapper;
import com.styl.pacific.order.service.domain.features.inventory.dto.command.ReserveInventoryCommand;
import com.styl.pacific.order.service.domain.features.inventory.event.InventoryReservedEvent;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(config = AppMapStructConfiguration.class, uses = { CommonDataMapper.class, MapstructCommonDomainMapper.class,
		MapstructCommonMapper.class })
public interface InventoryMessagingMapper {
	InventoryMessagingMapper INSTANCE = Mappers.getMapper(InventoryMessagingMapper.class);

	@Mapping(target = "tenantId", source = "tenantId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "orderId", source = "orderId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "storeId", source = "storeId", qualifiedByName = "baseLongIdToLong")
	@Mapping(target = "eventTime", source = "createdAt", qualifiedByName = "zonedDateTimeToLong")
	InventoryReservedAvroEvent eventToAvro(InventoryReservedEvent event);

	@Mapping(target = "productId", source = "productId", qualifiedByName = "baseLongIdToLong")
	ProductQuantityAvro productQuantityToAvro(InventoryReservedEvent.ProductQuantity productQuantity);

	ReserveInventoryCommand avroToCommand(InventoryReservedAvroEvent event);

	ReserveInventoryCommand.ProductQuantityCommand avroToCommand(ProductQuantityAvro productQuantity);
}
