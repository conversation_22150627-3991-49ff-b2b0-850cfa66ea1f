/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.messaging.features.order.publisher;

import com.styl.pacific.kafka.order.avro.model.OrderCreatedAvroEvent;
import com.styl.pacific.kafka.producer.service.KafkaProducer;
import com.styl.pacific.order.messaging.OrderMessagingProducerConfiguration;
import com.styl.pacific.order.messaging.features.order.mapper.OrderMessagingMapper;
import com.styl.pacific.order.service.domain.features.order.event.OrderCreatedEvent;
import com.styl.pacific.order.service.domain.features.order.ports.output.producer.OrderCreatedEventProducer;
import java.io.Serializable;
import lombok.RequiredArgsConstructor;
import org.apache.avro.specific.SpecificRecordBase;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class OrderCreatedEventKafkaPublisher implements OrderCreatedEventProducer {
	private static final Logger logger = LoggerFactory.getLogger(OrderCreatedEventKafkaPublisher.class);

	private final KafkaProducer<Serializable, SpecificRecordBase> kafkaProducer;
	private final OrderMessagingProducerConfiguration orderMessagingProducerConfiguration;

	@Override
	@Async
	public void publish(OrderCreatedEvent event) {
		OrderCreatedAvroEvent avroEvent = OrderMessagingMapper.INSTANCE.eventToAvro(event);
		kafkaProducer.send(orderMessagingProducerConfiguration.getOrderCreatedEvent()
				.getTopicName(), avroEvent.getId(), avroEvent, (result, error) -> {
					if (error != null) {
						logger.error("Error publishing OrderCreatedEvent to Kafka: {}", error.getMessage());
						logger.error(error.getMessage(), error);
					} else {
						logger.info("OrderCreatedEvent published to Kafka: {}", result);
					}
				});
	}
}
