/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.mealtime.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.domain.features.common.mapper.CommonDataMapper;
import com.styl.pacific.order.service.domain.features.mealtime.dto.command.CreateMealTimeCommand;
import com.styl.pacific.order.service.domain.features.mealtime.dto.command.UpdateMealTimeCommand;
import com.styl.pacific.order.service.domain.features.mealtime.entity.MealTime;
import com.styl.pacific.order.service.domain.features.mealtime.mapper.MealTimeDataMapper;
import com.styl.pacific.order.service.domain.features.mealtime.valueobjects.MealTimeId;
import java.awt.Color;
import java.time.Instant;
import java.time.LocalTime;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
class MealTimeDataMapperTest {

	private static final Long ID = 1L;
	private static final Long TENANT_ID = 2L;
	private static final String NAME = "name";
	private static final LocalTime START_TIME = LocalTime.now();
	private static final LocalTime END_TIME = LocalTime.now();
	private static final Color COLOR = Color.BLACK;
	private static final Instant CREATED_AT = Instant.now();
	private static final Instant UPDATED_AT = Instant.now();

	@Test
	void shouldReturnMealTime_whenMapFromCreateMealTimeCommand() {
		// Arrange
		CreateMealTimeCommand command = CreateMealTimeCommand.builder()
				.name(NAME)
				.startTime(START_TIME.toString())
				.endTime(END_TIME.toString())
				.color("#000000")
				.build();
		// Act
		MealTime mealTime = MealTimeDataMapper.INSTANCE.createMealTimeCommandToMealTime(TENANT_ID, command);
		// Assert
		assertEquals(TENANT_ID, mealTime.getTenantId()
				.getValue());
		assertEquals(NAME, mealTime.getName());
		assertEquals(START_TIME, mealTime.getStartTime());
		assertEquals(END_TIME, mealTime.getEndTime());
		assertEquals(COLOR.toString(), mealTime.getColor()
				.toString());
	}

	@Test
	void shouldReturnMealTime_whenMapFromUpdateMealTimeCommand() {
		// Arrange
		MealTime mealTimeMock = getMealTime();
		String newName = "New name";
		LocalTime newStartTime = LocalTime.MAX;
		LocalTime newEndTime = LocalTime.MAX;
		String newColor = "#832c76";
		UpdateMealTimeCommand command = UpdateMealTimeCommand.builder()
				.name(newName)
				.startTime(newStartTime.toString())
				.endTime(newEndTime.toString())
				.color(newColor)
				.build();
		// Act
		MealTimeDataMapper.INSTANCE.updateMealTimeCommandToMealTime(mealTimeMock, command);
		// Assert
		assertEquals(newName, mealTimeMock.getName());
		assertEquals(newStartTime, mealTimeMock.getStartTime());
		assertEquals(newEndTime, mealTimeMock.getEndTime());
		assertEquals(Mappers.getMapper(CommonDataMapper.class)
				.stringToColor(newColor)
				.toString(), mealTimeMock.getColor()
						.toString());
	}

	private MealTime getMealTime() {
		return MealTime.builder()
				.id(new MealTimeId(ID))
				.tenantId(new TenantId(TENANT_ID))
				.name(NAME)
				.startTime(START_TIME)
				.endTime(END_TIME)
				.color(COLOR)
				.createdAt(CREATED_AT)
				.updatedAt(UPDATED_AT)
				.build();
	}
}
