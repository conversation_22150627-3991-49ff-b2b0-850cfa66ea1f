/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.relations.preorder.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.styl.pacific.domain.enums.PreOrderMenuType;
import com.styl.pacific.domain.valueobject.PreOrderMenuId;
import com.styl.pacific.domain.valueobject.ProductId;
import com.styl.pacific.domain.valueobject.StoreId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.data.access.jpa.features.mealtime.entity.MealTimeEntity;
import com.styl.pacific.order.service.data.access.jpa.features.preorder.menu.entity.PreOrderMenuEntity;
import com.styl.pacific.order.service.data.access.jpa.features.preorder.menu.entity.PreOrderMenuItemEntity;
import com.styl.pacific.order.service.data.access.jpa.features.product.entity.ProductEntity;
import com.styl.pacific.order.service.data.access.relations.features.preorder.menu.mapper.PreOrderMenuDataAccessMapper;
import com.styl.pacific.order.service.domain.features.common.exception.enums.DateOfWeek;
import com.styl.pacific.order.service.domain.features.mealtime.valueobjects.MealTimeId;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.model.PreOrderMenuDto;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.model.PreOrderMenuItemDto;
import com.styl.pacific.order.service.domain.features.preorder.menu.entity.PreOrderMenu;
import com.styl.pacific.order.service.domain.features.preorder.menu.entity.PreOrderMenuItem;
import com.styl.pacific.order.service.domain.features.preorder.menu.valueobjects.PreOrderMenuItemChainId;
import com.styl.pacific.order.service.domain.features.preorder.menu.valueobjects.PreOrderMenuItemId;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 */
class PreOrderMenuDataAccessMapperTest {
	private static final Long PRE_ORDER_MENU_ID = 1L;
	private static final Long TENANT_ID = 2L;
	private static final Long STORE_ID = 2L;
	private static final String NAME = "name";
	private static final String DESCRIPTION = "description";
	private static final PreOrderMenuType TYPE = PreOrderMenuType.COLLECTION;

	private static final Long PRE_ORDER_MENU_ITEM_ID = 1L;
	private static final Long CHAIN_ID = 2L;
	private static final Long MEAL_TIME_ID = 2L;
	private static final Long PRODUCT_ID = 3L;
	private static final LocalDate DATE = LocalDate.now();
	private static final Integer CAPACITY = 10;
	private static final Integer ORDERED = 5;
	private static final List<DateOfWeek> AVAILABLE_ON = List.of(DateOfWeek.FRIDAY);
	private static final Instant CREATED_AT = Instant.now();
	private static final Instant UPDATED_AT = Instant.now();
	private static final Instant DELETED_AT = Instant.now();

	@Test
	void shouldReturnPreOrderMenuEntity_whenMapFromModel() {
		// Arrange
		PreOrderMenu modelMock = getPreOrderMenu();
		// Act
		PreOrderMenuEntity entity = PreOrderMenuDataAccessMapper.INSTANCE.toEntity(modelMock);
		// Assert
		assertEquals(modelMock.getId()
				.getValue(), entity.getId());
		assertEquals(modelMock.getTenantId()
				.getValue(), entity.getTenantId());
		assertEquals(modelMock.getStoreId()
				.getValue(), entity.getStoreId());
		assertEquals(modelMock.getName(), entity.getName());
		assertEquals(modelMock.getDescription(), entity.getDescription());
		assertEquals(modelMock.getType(), entity.getType());
		assertEquals(modelMock.getItems()
				.size(), entity.getItems()
						.size());
		assertEquals(modelMock.getCreatedAt(), entity.getCreatedAt());
		assertEquals(modelMock.getUpdatedAt(), entity.getUpdatedAt());
	}

	@Test
	void shouldReturnPreOrderMenu_whenMapToModel() {
		// Arrange
		PreOrderMenuEntity entityMock = getPreOrderMenuEntity();
		// Act
		PreOrderMenu model = PreOrderMenuDataAccessMapper.INSTANCE.toModel(entityMock);
		// Assert
		assertEquals(entityMock.getId(), model.getId()
				.getValue());
		assertEquals(entityMock.getTenantId(), model.getTenantId()
				.getValue());
		assertEquals(entityMock.getStoreId(), model.getStoreId()
				.getValue());
		assertEquals(entityMock.getName(), model.getName());
		assertEquals(entityMock.getDescription(), model.getDescription());
		assertEquals(entityMock.getType(), model.getType());
		assertEquals(entityMock.getItems()
				.size(), model.getItems()
						.size());
		assertEquals(entityMock.getCreatedAt(), model.getCreatedAt());
		assertEquals(entityMock.getUpdatedAt(), model.getUpdatedAt());
	}

	@Test
	void shouldReturnPreOrderMenuItemEntity_whenMapFromModel() {
		// Arrange
		PreOrderMenuItem modelMock = getPreOrderMenuItem();
		// Act
		PreOrderMenuItemEntity entity = PreOrderMenuDataAccessMapper.INSTANCE.toEntity(modelMock);
		// Assert
		assertEquals(modelMock.getId()
				.getValue(), entity.getId());
		assertEquals(modelMock.getPreOrderMenuId()
				.getValue(), entity.getPreOrderMenuId());
		assertEquals(modelMock.getMealTimeId()
				.getValue(), entity.getMealTimeId());
		assertEquals(modelMock.getProductId()
				.getValue(), entity.getProductId());
		assertEquals(modelMock.getDate(), entity.getDate());
		assertEquals(modelMock.getCapacity(), entity.getCapacity());

		assertEquals(modelMock.getCreatedAt(), entity.getCreatedAt());
		assertEquals(modelMock.getUpdatedAt(), entity.getUpdatedAt());
	}

	@Test
	void shouldReturnPreOrderMenuItem_whenMapToModel() {
		// Arrange
		PreOrderMenuItemEntity entityMock = getPreOrderMenuItemEntity();
		// Act
		PreOrderMenuItem model = PreOrderMenuDataAccessMapper.INSTANCE.toModel(entityMock);
		// Assert
		assertEquals(entityMock.getId(), model.getId()
				.getValue());
		assertEquals(entityMock.getPreOrderMenuId(), model.getPreOrderMenuId()
				.getValue());
		assertEquals(entityMock.getMealTimeId(), model.getMealTimeId()
				.getValue());
		assertEquals(entityMock.getProductId(), model.getProductId()
				.getValue());
		assertEquals(entityMock.getDate(), model.getDate());
		assertEquals(entityMock.getCapacity(), model.getCapacity());
		assertEquals(entityMock.getOrdered(), model.getOrdered());
		assertEquals(entityMock.getCreatedAt(), model.getCreatedAt());
		assertEquals(entityMock.getUpdatedAt(), model.getUpdatedAt());
	}

	@Test
	void shouldReturnPreOrderMenuDto_whenMapFromEntity() {
		// Arrange
		PreOrderMenuEntity entityMock = getPreOrderMenuEntity();
		// Act
		PreOrderMenuDto dto = PreOrderMenuDataAccessMapper.INSTANCE.toDto(entityMock);
		// Assert
		assertEquals(entityMock.getId(), dto.id()
				.getValue());
		assertEquals(entityMock.getStoreId(), dto.storeId()
				.getValue());
		assertEquals(entityMock.getName(), dto.name());
		assertEquals(entityMock.getDescription(), dto.description());
		assertEquals(entityMock.getType(), dto.type());
		assertEquals(entityMock.getCreatedAt(), dto.createdAt());
		assertEquals(entityMock.getUpdatedAt(), dto.updatedAt());
	}

	@Test
	void shouldReturnPreOrderMenuItemDto_whenMapFromEntity() {
		// Arrange
		PreOrderMenuItemEntity entityMock = getPreOrderMenuItemEntity();
		// Act
		PreOrderMenuItemDto dto = PreOrderMenuDataAccessMapper.INSTANCE.toDto(entityMock);
		// Assert
		assertEquals(entityMock.getId(), dto.id()
				.getValue());
		assertEquals(entityMock.getPreOrderMenuId(), dto.preOrderMenuId()
				.getValue());
		assertEquals(entityMock.getMealTimeId(), dto.mealTimeId()
				.getValue());
		assertEquals(entityMock.getProductId(), dto.product()
				.id());
		assertEquals(entityMock.getDate(), dto.date());
		assertEquals(entityMock.getCapacity(), dto.capacity());
		assertEquals(entityMock.getOrdered(), dto.ordered());
		assertEquals(entityMock.getCreatedAt(), dto.createdAt());
		assertEquals(entityMock.getUpdatedAt(), dto.updatedAt());
	}

	private PreOrderMenuEntity getPreOrderMenuEntity() {
		PreOrderMenuItemEntity entity = getPreOrderMenuItemEntity();
		return PreOrderMenuEntity.builder()
				.id(PRE_ORDER_MENU_ID)
				.tenantId(TENANT_ID)
				.storeId(STORE_ID)
				.name(NAME)
				.description(DESCRIPTION)
				.type(TYPE)
				.items(List.of(entity))
				.createdAt(CREATED_AT)
				.updatedAt(UPDATED_AT)
				.deletedAt(DELETED_AT)
				.build();
	}

	private PreOrderMenuItemEntity getPreOrderMenuItemEntity() {
		return PreOrderMenuItemEntity.builder()
				.id(PRE_ORDER_MENU_ITEM_ID)
				.chainId(CHAIN_ID)
				.preOrderMenuId(PRE_ORDER_MENU_ID)
				.mealTime(MealTimeEntity.builder()
						.id(MEAL_TIME_ID)
						.build())
				.mealTimeId(MEAL_TIME_ID)
				.product(ProductEntity.builder()
						.id(PRODUCT_ID)
						.build())
				.productId(PRODUCT_ID)
				.date(DATE)
				.capacity(CAPACITY)
				.ordered(ORDERED)
				.createdAt(CREATED_AT)
				.updatedAt(UPDATED_AT)
				.deletedAt(DELETED_AT)
				.build();
	}

	private PreOrderMenuItem getPreOrderMenuItem() {
		return PreOrderMenuItem.builder()
				.id(new PreOrderMenuItemId(PRE_ORDER_MENU_ITEM_ID))
				.chainId(new PreOrderMenuItemChainId(CHAIN_ID))
				.preOrderMenuId(new PreOrderMenuId(PRE_ORDER_MENU_ID))
				.productId(new ProductId(PRODUCT_ID))
				.mealTimeId(new MealTimeId(MEAL_TIME_ID))
				.date(DATE)
				.capacity(CAPACITY)
				.ordered(ORDERED)
				.createdAt(CREATED_AT)
				.updatedAt(UPDATED_AT)
				.build();
	}

	private PreOrderMenu getPreOrderMenu() {
		PreOrderMenuItem preOrderMenuItem = getPreOrderMenuItem();
		return PreOrderMenu.builder()
				.id(new PreOrderMenuId(PRE_ORDER_MENU_ID))
				.tenantId(new TenantId(TENANT_ID))
				.storeId(new StoreId(STORE_ID))
				.name(NAME)
				.description(DESCRIPTION)
				.type(TYPE)
				.items(List.of(preOrderMenuItem))
				.createdAt(CREATED_AT)
				.updatedAt(UPDATED_AT)
				.build();
	}
}
