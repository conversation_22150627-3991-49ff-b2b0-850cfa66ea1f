/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.rest.mealtime;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.styl.pacific.common.constant.PacificRestConstants;
import com.styl.pacific.common.test.BaseWebClientWithDbTest;
import com.styl.pacific.common.test.container.DynamoDbContainerTest;
import com.styl.pacific.common.test.container.KafkaContainerTest;
import com.styl.pacific.domain.exception.GlobalErrorCode;
import com.styl.pacific.order.service.config.IntegrationTestConfiguration;
import com.styl.pacific.order.service.data.access.jpa.features.mealtime.repository.MealTimeJpaRepository;
import com.styl.pacific.order.service.data.access.jpa.features.mealtime.specification.MealTimeSpecifications;
import com.styl.pacific.order.service.shared.http.mealtime.request.CreateMealTimeRequest;
import com.styl.pacific.order.service.shared.http.mealtime.request.UpdateMealTimeRequest;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantResponse;
import java.util.Random;
import java.util.concurrent.atomic.AtomicReference;
import org.apache.commons.lang3.math.NumberUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.reactive.server.WebTestClient;

/**
 * <AUTHOR>
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = IntegrationTestConfiguration.class)
public class MealtimeControllerIntegrationTest extends BaseWebClientWithDbTest implements DynamoDbContainerTest,
		KafkaContainerTest {
	private static final String BASE_URL = "/api/order/mealtimes";

	private static final Long TENANT_ID = 1L;
	private static final String NAME = "Breakfast";
	private static final String FROM_TIME = "06:00:00";
	private static final String TO_TIME = "10:00:00";
	private static final String COLOR = "#FFFFFF";

	@Autowired
	private ObjectMapper objectMapper;

	@Autowired
	@Qualifier("tenantService")
	private WireMockServer tenantServiceMock;

	@Autowired
	private MealTimeJpaRepository mealTimeJpaRepository;

	private static final AtomicReference<Long> idValue = new AtomicReference<>();

	@BeforeEach
	public void setup() throws JsonProcessingException {
		TenantResponse tenantResponse = TenantResponse.builder()
				.tenantId(TENANT_ID)
				.build();
		tenantServiceMock.stubFor(WireMock.get(WireMock.urlPathMatching("/api/tenant/tenants/" + TENANT_ID))
				.willReturn(WireMock.aResponse()
						.withStatus(HttpStatus.OK.value())
						.withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
						.withBody(objectMapper.writeValueAsString(tenantResponse))));
	}

	@Order(0)
	@Test
	public void shouldReturnMealTime_whenCreate() {
		// Arrange
		CreateMealTimeRequest requestMock = CreateMealTimeRequest.builder()
				.name(NAME)
				.startTime(FROM_TIME)
				.endTime(TO_TIME)
				.color(COLOR)
				.build();
		// Act
		WebTestClient.ResponseSpec responseSpec = webClient.post()
				.uri(uriBuilder -> {
					uriBuilder.path(BASE_URL);
					return uriBuilder.build();
				})
				.headers(this::setHeaders)
				.bodyValue(requestMock)
				.exchange();
		// Assert

		responseSpec.expectStatus()
				.isCreated()
				.expectBody()
				.jsonPath("$.id")
				.value(id -> {
					Long idValueData = NumberUtils.createLong(id.toString());
					idValue.set(idValueData);
				})
				.jsonPath("$.tenantId")
				.isEqualTo(TENANT_ID)
				.jsonPath("$.name")
				.isEqualTo(NAME)
				.jsonPath("$.startTime")
				.isEqualTo(FROM_TIME)
				.jsonPath("$.endTime")
				.isEqualTo(TO_TIME)
				.jsonPath("$.color")
				.isEqualTo(COLOR)
				.jsonPath("$.createdAt")
				.isNotEmpty()
				.jsonPath("$.updatedAt")
				.isNotEmpty();
		assertTrue(mealTimeJpaRepository.existsById(idValue.get()));
	}

	@Order(0)
	@Test
	public void shouldReturn400_whenCreateWithWrongTime() {
		// Arrange
		CreateMealTimeRequest requestMock = CreateMealTimeRequest.builder()
				.name(NAME)
				.startTime(TO_TIME)
				.endTime(FROM_TIME)
				.color(COLOR)
				.build();
		// Act
		WebTestClient.ResponseSpec responseSpec = webClient.post()
				.uri(uriBuilder -> {
					uriBuilder.path(BASE_URL);
					return uriBuilder.build();
				})
				.headers(this::setHeaders)
				.bodyValue(requestMock)
				.exchange();
		// Assert

		responseSpec.expectStatus()
				.isBadRequest()
				.expectBody()
				.jsonPath("$.code")
				.isEqualTo(GlobalErrorCode.TIME_OF_MEAL_TIME_INVALID.getValue());
	}

	@Order(1)
	@Test
	public void shouldReturnNewMealTime_whenUpdate() {
		// Arrange
		String newName = "Lunch";
		String newFromTime = "12:00:00";
		String newToTime = "14:00:00";
		String newColor = "#000000";
		UpdateMealTimeRequest requestMock = UpdateMealTimeRequest.builder()
				.name(newName)
				.startTime(newFromTime)
				.endTime(newToTime)
				.color(newColor)
				.build();
		// Act
		WebTestClient.ResponseSpec responseSpec = webClient.put()
				.uri(uriBuilder -> {
					uriBuilder.path(BASE_URL)
							.pathSegment(idValue.get()
									.toString());
					return uriBuilder.build();
				})
				.headers(this::setHeaders)
				.bodyValue(requestMock)
				.exchange();
		// Assert
		responseSpec.expectStatus()
				.isOk()
				.expectBody()
				.jsonPath("$.id")
				.isEqualTo(idValue.get())
				.jsonPath("$.tenantId")
				.isEqualTo(TENANT_ID)
				.jsonPath("$.name")
				.isEqualTo(newName)
				.jsonPath("$.startTime")
				.isEqualTo(newFromTime)
				.jsonPath("$.endTime")
				.isEqualTo(newToTime)
				.jsonPath("$.color")
				.isEqualTo(newColor)
				.jsonPath("$.createdAt")
				.isNotEmpty()
				.jsonPath("$.updatedAt")
				.isNotEmpty();
	}

	@Order(2)
	@Test
	public void shouldRemoveMealTime_whenDelete() {
		// Act
		WebTestClient.ResponseSpec responseSpec = webClient.delete()
				.uri(uriBuilder -> {
					uriBuilder.path(BASE_URL)
							.pathSegment(idValue.get()
									.toString());
					return uriBuilder.build();
				})
				.headers(this::setHeaders)
				.exchange();
		// Assert
		responseSpec.expectStatus()
				.isNoContent();
		assertFalse(mealTimeJpaRepository.exists(MealTimeSpecifications.byTenantIdAndId(TENANT_ID, idValue.get())));
	}

	private void setHeaders(HttpHeaders headers) {
		headers.set(PacificRestConstants.PlatformHeader.HEADER_X_REQUEST_ID, Long.toString(new Random().nextLong()));
		headers.set(PacificRestConstants.PlatformHeader.HEADER_X_TENANT_ID, Long.toString(TENANT_ID));
	}
}
