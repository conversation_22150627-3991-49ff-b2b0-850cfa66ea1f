/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.label;

import com.styl.pacific.order.service.data.access.relations.features.order.adapter.OrderLabelRepositoryImpl;
import java.math.RoundingMode;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

public class LabelGenerationTest {

	// Parameterized test
	@ParameterizedTest
	@CsvSource({ "1118.8, 296, UP", "30.2, 8, DOWN", "151.1, 40, DOWN" })
	public void testToPxConverter(float px, int mm, RoundingMode mode) {
		Assertions.assertEquals(px, OrderLabelRepositoryImpl.toPx(mm, RoundingMode.valueOf(mode.name())));
	}
}
