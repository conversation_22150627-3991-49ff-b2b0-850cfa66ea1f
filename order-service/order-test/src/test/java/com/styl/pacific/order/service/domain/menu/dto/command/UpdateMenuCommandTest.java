/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.menu.dto.command;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.styl.pacific.order.service.domain.features.menu.dto.command.UpdateMenuCommand;
import com.styl.pacific.order.service.domain.features.menu.enums.MenuStatus;
import com.styl.pacific.order.service.domain.features.menu.enums.MenuType;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 */
class UpdateMenuCommandTest {
	private static final String NAME = "name";
	private static final MenuStatus STATUS = MenuStatus.ACTIVE;
	private static final MenuType TYPE = MenuType.APP;
	private static final String DESCRIPTION = "description";

	private Validator validator;

	@BeforeEach
	public void setUp() {
		try (ValidatorFactory factory = Validation.buildDefaultValidatorFactory()) {
			validator = factory.getValidator();
		}
	}

	@Test
	void shouldReturnValue_whenBuilder() {
		// Act
		UpdateMenuCommand command = new UpdateMenuCommand(NAME, STATUS, TYPE, DESCRIPTION);
		// Assert
		assertEquals(NAME, command.name());
		assertEquals(STATUS, command.status());
		assertEquals(TYPE, command.type());
		assertEquals(DESCRIPTION, command.description());
	}

	@Test
	void whenCommand_shouldValid() {
		// Arrange
		UpdateMenuCommand command = new UpdateMenuCommand(NAME, STATUS, TYPE, DESCRIPTION);

		// Act
		Set<ConstraintViolation<UpdateMenuCommand>> violations = validator.validate(command);

		// Assert
		assertTrue(violations.isEmpty());
	}

	@Test
	void whenCommandWithMissingProperties_shouldNotValid() {
		// Arrange
		int violationsExpect = 3;
		UpdateMenuCommand command = new UpdateMenuCommand(null, null, null, null);
		// Act
		Set<ConstraintViolation<UpdateMenuCommand>> violations = validator.validate(command);
		// Assert
		assertEquals(violationsExpect, violations.size());
	}
}
