/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.relations.preorder.adapter;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.PreOrderMenuId;
import com.styl.pacific.domain.valueobject.ProductId;
import com.styl.pacific.order.service.data.access.jpa.features.mealtime.entity.MealTimeEntity;
import com.styl.pacific.order.service.data.access.jpa.features.preorder.menu.entity.PreOrderMenuItemEntity;
import com.styl.pacific.order.service.data.access.jpa.features.preorder.menu.repository.PreOrderMenuItemJpaRepository;
import com.styl.pacific.order.service.data.access.jpa.features.product.entity.ProductEntity;
import com.styl.pacific.order.service.data.access.relations.features.preorder.menu.adapter.PreOrderMenuItemRepositoryImpl;
import com.styl.pacific.order.service.domain.features.mealtime.valueobjects.MealTimeId;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.model.PreOrderMenuItemDto;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.query.PreOrderMenuItemFilter;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.query.PreOrderMenuItemPagingQuery;
import com.styl.pacific.order.service.domain.features.preorder.menu.entity.PreOrderMenuItem;
import com.styl.pacific.order.service.domain.features.preorder.menu.valueobjects.PreOrderMenuItemChainId;
import com.styl.pacific.order.service.domain.features.preorder.menu.valueobjects.PreOrderMenuItemId;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class PreOrderMenuItemRepositoryImplTest {
	private static final Long ID = 1L;
	private static final Long CHAIN_ID = 2L;
	private static final Long PRE_ORDER_MENU_ID = 1L;
	private static final Long MEAL_TIME_ID = 2L;
	private static final Long PRODUCT_ID = 3L;
	private static final LocalDate DATE = LocalDate.now();
	private static final Integer CAPACITY = 10;
	private static final Integer ORDERED = 5;
	private static final boolean REPEATED = true;
	private static final Instant CREATED_AT = Instant.now();
	private static final Instant UPDATED_AT = Instant.now();
	private static final Instant DELETED_AT = Instant.now();

	@Mock
	private PreOrderMenuItemJpaRepository preOrderMenuItemJpaRepository;

	@InjectMocks
	private PreOrderMenuItemRepositoryImpl preOrderMenuItemRepository;

	@Test
	void shouldReturnPreOrderMenuItem_whenFindById() {
		// Arrange
		PreOrderMenuItemEntity entityMock = getPreOrderMenuItemEntity();
		when(preOrderMenuItemJpaRepository.findOne(any(Specification.class))).thenReturn(Optional.of(entityMock));
		// Act
		Optional<PreOrderMenuItem> result = preOrderMenuItemRepository.findById(new PreOrderMenuId(PRE_ORDER_MENU_ID),
				new PreOrderMenuItemId(ID));
		// Assert
		assert result.isPresent();
		verify(preOrderMenuItemJpaRepository, times(1)).findOne(any(Specification.class));
		assertEqualsEntityVsModel(entityMock, result.get());
	}

	@Test
	void shouldReturnPaging_whenFindDtoAll() {
		// Arrange
		String nameQuery = "nameQuery";
		Long categoryIdQuery = 1L;
		int page = 0;
		int size = 10;
		String sortField = "id";
		String sortDirection = "ASC";
		PreOrderMenuItemPagingQuery queryMock = new PreOrderMenuItemPagingQuery(PreOrderMenuItemFilter.builder()
				.name(nameQuery)
				.categoryId(categoryIdQuery)
				.build(), size, page, sortDirection, Set.of(sortField));

		Pageable pageableMock = PageRequest.of(page, size);
		PreOrderMenuItemEntity entityMock = getPreOrderMenuItemEntity();
		Page<PreOrderMenuItemEntity> pageMock = new PageImpl<>(List.of(entityMock), pageableMock, 1);
		when(preOrderMenuItemJpaRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(pageMock);
		// Act
		Paging<PreOrderMenuItemDto> result = preOrderMenuItemRepository.findDtoAll(new PreOrderMenuId(
				PRE_ORDER_MENU_ID), queryMock);
		// Assert
		assertEquals(pageMock.getTotalElements(), result.getTotalElements());
		assertEquals(pageMock.getTotalPages(), result.getTotalPages());
		assertEquals(pageMock.getNumber(), result.getPage());
		assertEquals(pageMock.getContent()
				.size(), result.getContent()
						.size());
	}

	@Test
	void shouldReturnDto_whenFindDtoById() {
		// Arrange
		PreOrderMenuItemEntity entityMock = getPreOrderMenuItemEntity();
		when(preOrderMenuItemJpaRepository.findOne(any(Specification.class))).thenReturn(Optional.of(entityMock));
		// Act
		Optional<PreOrderMenuItemDto> result = preOrderMenuItemRepository.findDtoById(new PreOrderMenuId(
				PRE_ORDER_MENU_ID), new PreOrderMenuItemId(ID));
		// Assert
		assert result.isPresent();
		verify(preOrderMenuItemJpaRepository, times(1)).findOne(any(Specification.class));
		assertEqualsEntityVsDto(entityMock, result.get());
	}

	@Test
	void shouldReturnDto_whenCreate() {
		// Arrange
		PreOrderMenuItemEntity entityMock = getPreOrderMenuItemEntity();
		when(preOrderMenuItemJpaRepository.save(any())).thenReturn(entityMock);
		PreOrderMenuItem menuItemMock = getPreOrderMenuItem();
		// Act
		PreOrderMenuItemDto result = preOrderMenuItemRepository.createDto(menuItemMock);
		// Assert
		verify(preOrderMenuItemJpaRepository, times(1)).save(any());
		assertEqualsEntityVsDto(entityMock, result);
	}

	@Test
	void shouldReturnDto_whenUpdate() {
		// Arrange
		PreOrderMenuItemEntity entityMock = getPreOrderMenuItemEntity();
		when(preOrderMenuItemJpaRepository.saveAndFlush(any())).thenReturn(entityMock);
		PreOrderMenuItem menuItemMock = getPreOrderMenuItem();
		// Act
		PreOrderMenuItemDto result = preOrderMenuItemRepository.updateDto(menuItemMock);
		// Assert
		verify(preOrderMenuItemJpaRepository, times(1)).saveAndFlush(any());
		assertEqualsEntityVsDto(entityMock, result);
	}

	@Test
	void shouldRemove_whenDeleteById() {
		// Act
		preOrderMenuItemRepository.deleteById(new PreOrderMenuItemId(ID));
		// Assert
		verify(preOrderMenuItemJpaRepository, times(1)).delete(any(Specification.class));
	}

	@Test
    void shouldReturnBoolean_whenCheckExistsById() {
        // Arrange
        when(preOrderMenuItemJpaRepository.exists(any(Specification.class))).thenReturn(true);
        // Act
        boolean result = preOrderMenuItemRepository.existsById(new PreOrderMenuId(PRE_ORDER_MENU_ID),
                new PreOrderMenuItemId(ID));
        // Assert
        verify(preOrderMenuItemJpaRepository, times(1)).exists(any(Specification.class));
        assertTrue(result);
    }

	private void assertEqualsEntityVsDto(PreOrderMenuItemEntity mock, PreOrderMenuItemDto actual) {
		assertEquals(mock.getId(), actual.id()
				.getValue());
		assertEquals(mock.getChainId(), actual.chainId()
				.getValue());
		assertEquals(mock.getPreOrderMenuId(), actual.preOrderMenuId()
				.getValue());
		assertEquals(mock.getMealTimeId(), actual.mealTimeId()
				.getValue());
		assertEquals(mock.getProductId(), actual.product()
				.id());
		assertEquals(mock.getDate(), actual.date());
		assertEquals(mock.getCapacity(), actual.capacity());
		assertEquals(mock.getOrdered(), actual.ordered());
		assertEquals(mock.getCreatedAt(), actual.createdAt());
		assertEquals(mock.getUpdatedAt(), actual.updatedAt());
	}

	private void assertEqualsEntityVsModel(PreOrderMenuItemEntity mock, PreOrderMenuItem actual) {
		assertEquals(mock.getId(), actual.getId()
				.getValue());
		assertEquals(mock.getChainId(), actual.getChainId()
				.getValue());
		assertEquals(mock.getPreOrderMenuId(), actual.getPreOrderMenuId()
				.getValue());
		assertEquals(mock.getMealTimeId(), actual.getMealTimeId()
				.getValue());
		assertEquals(mock.getProductId(), actual.getProductId()
				.getValue());
		assertEquals(mock.getDate(), actual.getDate());
		assertEquals(mock.getCapacity(), actual.getCapacity());
		assertEquals(mock.getOrdered(), actual.getOrdered());

		assertEquals(mock.getCreatedAt(), actual.getCreatedAt());
		assertEquals(mock.getUpdatedAt(), actual.getUpdatedAt());
	}

	private PreOrderMenuItem getPreOrderMenuItem() {
		return PreOrderMenuItem.builder()
				.id(new PreOrderMenuItemId(ID))
				.chainId(new PreOrderMenuItemChainId(CHAIN_ID))
				.preOrderMenuId(new PreOrderMenuId(PRE_ORDER_MENU_ID))
				.productId(new ProductId(PRODUCT_ID))
				.mealTimeId(new MealTimeId(MEAL_TIME_ID))
				.date(DATE)
				.capacity(CAPACITY)
				.ordered(ORDERED)
				.createdAt(CREATED_AT)
				.updatedAt(UPDATED_AT)
				.build();
	}

	private PreOrderMenuItemEntity getPreOrderMenuItemEntity() {
		return PreOrderMenuItemEntity.builder()
				.id(ID)
				.chainId(CHAIN_ID)
				.preOrderMenuId(PRE_ORDER_MENU_ID)
				.mealTime(MealTimeEntity.builder()
						.id(MEAL_TIME_ID)
						.build())
				.mealTimeId(MEAL_TIME_ID)
				.product(ProductEntity.builder()
						.id(PRODUCT_ID)
						.build())
				.productId(PRODUCT_ID)
				.date(DATE)
				.capacity(CAPACITY)
				.ordered(ORDERED)
				.createdAt(CREATED_AT)
				.updatedAt(UPDATED_AT)
				.deletedAt(DELETED_AT)
				.build();
	}
}
