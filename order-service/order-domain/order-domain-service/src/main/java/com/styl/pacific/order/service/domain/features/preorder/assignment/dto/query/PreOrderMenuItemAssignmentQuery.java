/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.preorder.assignment.dto.query;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.Collection;
import java.util.Collections;
import lombok.Builder;
import lombok.Getter;
import lombok.With;

/**
 * <AUTHOR>
 */
@Getter
@Builder
@With
public class PreOrderMenuItemAssignmentQuery {
	@Valid
	@NotNull(message = "filter must not be null")
	private PreOrderMenuItemAssignmentFilter filter;
	@Builder.Default
	private String sortDirection = "ASC";
	@Builder.Default
	private Collection<String> sortFields = Collections.emptySet();
}
