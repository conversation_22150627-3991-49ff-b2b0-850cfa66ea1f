/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.inventory.dto.query;

import com.styl.pacific.domain.dto.pagination.PaginationQuery;
import jakarta.validation.Valid;
import java.util.Collection;

/**
 * <AUTHOR>
 */
public class PaginationInventoryQuery extends PaginationQuery<InventoryFilter> {
	@Override
	@Valid
	public InventoryFilter getFilter() {
		return super.getFilter();
	}

	public PaginationInventoryQuery(InventoryFilter filter, Integer size, Integer page, String sortDirection,
			Collection<String> sortFields) {
		super(filter, size, page, sortDirection, sortFields);
	}
}
