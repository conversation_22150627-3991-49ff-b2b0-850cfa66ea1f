/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.servicecharge.ports.input;

import com.styl.pacific.domain.dto.Content;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.domain.features.servicecharge.dto.command.CreateServiceChargeCommand;
import com.styl.pacific.order.service.domain.features.servicecharge.dto.command.UpdateServiceChargeCommand;
import com.styl.pacific.order.service.domain.features.servicecharge.dto.model.ServiceChargeDetailDTO;
import com.styl.pacific.order.service.domain.features.servicecharge.dto.query.ServiceChargeQuery;
import com.styl.pacific.order.service.domain.features.servicecharge.entity.ServiceCharge;
import com.styl.pacific.order.service.domain.features.servicecharge.valueobjects.ServiceChargeId;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
public interface ServiceChargeService {

	Content<ServiceChargeDetailDTO> findDetailsAll(@NotNull(message = "tenantId must not be null") TenantId tenantId,
			@Valid ServiceChargeQuery query);

	Content<ServiceCharge> findAll(@NotNull(message = "tenantId must not be null") TenantId tenantId,
			@Valid ServiceChargeQuery query);

	ServiceChargeDetailDTO findById(@NotNull(message = "tenantId must not be null") TenantId tenantId,
			@NotNull(message = "id must not be null") ServiceChargeId id);

	ServiceChargeDetailDTO create(@NotNull(message = "tenantId must not be null") TenantId tenantId,
			@Valid @NotNull(message = "command must not be null") CreateServiceChargeCommand command);

	ServiceChargeDetailDTO update(@NotNull(message = "tenantId must not be null") TenantId tenantId,
			@NotNull(message = "id must not be null") ServiceChargeId id,
			@Valid @NotNull(message = "command must not be null") UpdateServiceChargeCommand command);

	void deleteById(@NotNull(message = "tenantId must not be null") TenantId tenantId,
			@NotNull(message = "id must not be null") ServiceChargeId id);
}
