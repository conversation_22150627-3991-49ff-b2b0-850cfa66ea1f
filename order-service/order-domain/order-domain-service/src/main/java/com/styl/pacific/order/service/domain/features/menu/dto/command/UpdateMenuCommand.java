/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.menu.dto.command;

import com.styl.pacific.order.service.domain.features.menu.enums.MenuStatus;
import com.styl.pacific.order.service.domain.features.menu.enums.MenuType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * <AUTHOR>
 */
public record UpdateMenuCommand(
		@Size(max = 80, message = "name must be less than or equal to 80 characters") @NotBlank(message = "name must not be empty or null") String name,
		@NotNull(message = "status must not be null") MenuStatus status,
		@NotNull(message = "type must not be null") MenuType type,
		@Size(max = 255, message = "description must be less than or equal to 255 characters") String description) {
}
