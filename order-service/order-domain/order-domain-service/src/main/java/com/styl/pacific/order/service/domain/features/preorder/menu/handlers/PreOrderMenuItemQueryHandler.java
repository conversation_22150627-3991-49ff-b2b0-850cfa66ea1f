/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.preorder.menu.handlers;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.PreOrderMenuId;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.model.PreOrderMenuItemDto;
import com.styl.pacific.order.service.domain.features.preorder.menu.dto.query.PreOrderMenuItemPagingQuery;
import com.styl.pacific.order.service.domain.features.preorder.menu.exception.PreOrderMenuItemNotFoundException;
import com.styl.pacific.order.service.domain.features.preorder.menu.ports.output.repository.PreOrderMenuItemRepository;
import com.styl.pacific.order.service.domain.features.preorder.menu.valueobjects.PreOrderMenuItemId;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class PreOrderMenuItemQueryHandler {
	private static final Logger log = LoggerFactory.getLogger(PreOrderMenuItemQueryHandler.class);
	private final PreOrderMenuItemRepository preOrderMenuItemRepository;

	@Transactional(readOnly = true)
	public PreOrderMenuItemDto findById(PreOrderMenuId preOrderMenuId, PreOrderMenuItemId id) {
		return preOrderMenuItemRepository.findDtoById(preOrderMenuId, id)
				.orElseThrow(() -> {
					log.info("Pre-order Menu Item {} not found", id);
					return new PreOrderMenuItemNotFoundException(String.format("Pre-order Menu Item %s not found", id));
				});
	}

	@Transactional(readOnly = true)
	public Paging<PreOrderMenuItemDto> findAllPaging(PreOrderMenuId preOrderMenuId, PreOrderMenuItemPagingQuery query) {
		return preOrderMenuItemRepository.findDtoAll(preOrderMenuId, query);
	}
}
