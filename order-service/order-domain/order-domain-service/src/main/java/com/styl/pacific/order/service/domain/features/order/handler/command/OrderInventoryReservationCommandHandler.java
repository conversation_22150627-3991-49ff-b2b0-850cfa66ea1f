/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.order.handler.command;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.enums.order.OrderStatus;
import com.styl.pacific.domain.enums.order.OrderType;
import com.styl.pacific.domain.valueobject.OrderId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.domain.features.inventory.entity.InventoryReservation;
import com.styl.pacific.order.service.domain.features.inventory.enums.ReservationStatus;
import com.styl.pacific.order.service.domain.features.inventory.handler.InventoryReservationCommandHandler;
import com.styl.pacific.order.service.domain.features.inventory.ports.output.repository.InventoryReservationRepository;
import com.styl.pacific.order.service.domain.features.order.dto.query.OrderFilter;
import com.styl.pacific.order.service.domain.features.order.dto.query.OrderPagingQuery;
import com.styl.pacific.order.service.domain.features.order.entity.OrderLight;
import com.styl.pacific.order.service.domain.features.order.ports.output.repository.OrderQueryRepository;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class OrderInventoryReservationCommandHandler {
	private static final Logger log = LoggerFactory.getLogger(OrderInventoryReservationCommandHandler.class);
	private final OrderQueryRepository orderQueryRepository;
	private final OrderMarkReservationExpiredCommandHandler orderMarkReservationExpiredCommandHandler;
	private final InventoryReservationCommandHandler inventoryReservationCommandHandler;
	private final InventoryReservationRepository inventoryReservationRepository;

	@Transactional
	public List<OrderLight> processInventoryReservationExpired(Duration reservationExpiryDuration) {
		OrderFilter filter = OrderFilter.builder()
				.statuses(List.of(OrderStatus.CREATED))
				.types(List.of(OrderType.INSTANT_ORDER, OrderType.ONSITE_ORDER))
				.build();
		OrderPagingQuery query = new OrderPagingQuery(filter, 50, 0, null, null);
		Paging<OrderLight> paging = orderQueryRepository.findAllLightPageable(null, query);
		List<OrderLight> ordersResult = paging.getContent();
		if (ordersResult.isEmpty()) {
			return ordersResult;
		}
		List<OrderLight> orders = new ArrayList<>(ordersResult);
		List<InventoryReservation> reservations = inventoryReservationRepository.findAllByOrderIds(orders.stream()
				.map(OrderLight::getId)
				.toList());
		Map<OrderId, InventoryReservation> reservationMap = reservations.stream()
				.collect(Collectors.toMap(InventoryReservation::getOrderId, Function.identity()));

		cancelOrderDoNotHaveReservation(orders, reservationMap);
		cancelFailedReservations(orders, reservationMap);
		cancelReservationExpired(orders, reservationMap, reservationExpiryDuration);
		confirmSuccessfulReservations(orders, reservationMap);
		return ordersResult;
	}

	private void cancelOrderDoNotHaveReservation(List<OrderLight> orders,
			Map<OrderId, InventoryReservation> reservationMap) {
		List<OrderLight> ordersCanNotReserveInventory = List.copyOf(orders.stream()
				.filter(order -> !reservationMap.containsKey(order.getId()))
				.toList());
		if (ordersCanNotReserveInventory.isEmpty()) {
			return;
		}
		orders.removeAll(ordersCanNotReserveInventory);
		Map<TenantId, List<OrderLight>> tenantOrders = ordersCanNotReserveInventory.stream()
				.collect(Collectors.groupingBy(OrderLight::getTenantId));
		tenantOrders.forEach((tenantId, value) -> {
			try {
				orderMarkReservationExpiredCommandHandler.markReservationFailed(value);
			} catch (Exception e) {
				log.warn("Error when cancel order without reservation", e);
			}
		});

	}

	private void cancelFailedReservations(List<OrderLight> orders, Map<OrderId, InventoryReservation> reservationMap) {
		List<InventoryReservation> failedReservations = List.copyOf(reservationMap.values()
				.stream()
				.filter(reservation -> reservation.getStatus()
						.equals(ReservationStatus.FAILED))
				.toList());
		List<OrderLight> ordersToCancel = List.copyOf(orders.stream()
				.filter(order -> failedReservations.stream()
						.anyMatch(reservation -> reservation.getOrderId()
								.equals(order.getId())))
				.toList());
		if (ordersToCancel.isEmpty()) {
			return;
		}
		orders.removeAll(ordersToCancel);
		try {
			orderMarkReservationExpiredCommandHandler.markReservationFailed(ordersToCancel);
		} catch (Exception e) {
			log.warn("Error when cancel failed reservation", e);
		}
	}

	private void cancelReservationExpired(List<OrderLight> orders, Map<OrderId, InventoryReservation> reservationMap,
			Duration reservationExpiryDuration) {
		List<OrderId> orderIds = List.copyOf(orders.stream()
				.map(OrderLight::getId)
				.toList());
		Map<OrderId, InventoryReservation> reservationExpired = reservationMap.entrySet()
				.stream()
				.filter(entry -> orderIds.contains(entry.getKey()))
				.filter(entry -> ReservationStatus.SUCCESS.equals(entry.getValue()
						.getStatus()))
				.filter(entry -> Instant.now()
						.isAfter(entry.getValue()
								.getCreatedAt()
								.plus(reservationExpiryDuration)))
				.collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
		List<OrderLight> ordersToCancelByTime = List.copyOf(orders.stream()
				.filter(order -> reservationExpired.containsKey(order.getId()))
				.toList());
		if (ordersToCancelByTime.isEmpty()) {
			return;
		}
		orders.removeAll(ordersToCancelByTime);
		reservationExpired.forEach((orderId, inventoryReservation) -> {
			reservationMap.remove(orderId);
		});
		try {
			inventoryReservationCommandHandler.cancelBulkInventoryReservations(reservationExpired.values()
					.stream()
					.toList());
		} catch (Exception e) {
			log.warn("Error when cancel expired order", e);
		}

	}

	private void confirmSuccessfulReservations(List<OrderLight> orders,
			Map<OrderId, InventoryReservation> reservationMap) {
		List<OrderId> orderIds = List.copyOf(orders.stream()
				.map(OrderLight::getId)
				.toList());
		Map<OrderId, InventoryReservation> successReservations = reservationMap.entrySet()
				.stream()
				.filter(entry -> orderIds.contains(entry.getKey()) && ReservationStatus.SUCCESS.equals(entry.getValue()
						.getStatus()))
				.collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
		if (successReservations.isEmpty()) {
			return;
		}
		try {
			inventoryReservationCommandHandler.confirmBulkInventoryReservations(successReservations.values()
					.stream()
					.toList());
		} catch (Exception e) {
			log.warn("Error when confirm successful reservation", e);
		}
	}
}
