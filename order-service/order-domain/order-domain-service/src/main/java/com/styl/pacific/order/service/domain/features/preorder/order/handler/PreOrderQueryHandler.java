/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.preorder.order.handler;

import static java.time.temporal.ChronoUnit.DAYS;

import com.google.common.collect.Lists;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.enums.order.OrderStatus;
import com.styl.pacific.domain.enums.order.OrderType;
import com.styl.pacific.domain.valueobject.OrderId;
import com.styl.pacific.domain.valueobject.PreOrderId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.order.service.domain.features.common.exception.OrderDomainException;
import com.styl.pacific.order.service.domain.features.mealtime.valueobjects.MealTimeId;
import com.styl.pacific.order.service.domain.features.order.dto.OrderLightDto;
import com.styl.pacific.order.service.domain.features.order.dto.OrderLineItemDto;
import com.styl.pacific.order.service.domain.features.order.dto.query.OrderFilter;
import com.styl.pacific.order.service.domain.features.order.dto.query.OrderLineItemFilter;
import com.styl.pacific.order.service.domain.features.order.dto.query.OrderLineItemPagingQuery;
import com.styl.pacific.order.service.domain.features.order.dto.query.OrderPagingQuery;
import com.styl.pacific.order.service.domain.features.order.exception.OrderNotFoundException;
import com.styl.pacific.order.service.domain.features.order.ports.output.repository.OrderLineItemProcessRepository;
import com.styl.pacific.order.service.domain.features.order.ports.output.repository.OrderQueryRepository;
import com.styl.pacific.order.service.domain.features.preorder.menu.valueobjects.PreOrderMenuItemId;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.PreOrderLightDto;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.PreOrderSummaryDto;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.query.PreOrderHistorySummaryFilter;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.query.PreOrderPagingQuery;
import com.styl.pacific.order.service.domain.features.preorder.order.dto.query.PreOrderSummaryQuery;
import com.styl.pacific.order.service.domain.features.preorder.order.enums.PreOrderStatus;
import com.styl.pacific.order.service.domain.features.preorder.order.mapper.PreOrderDataMapper;
import com.styl.pacific.order.service.domain.features.preorder.order.ports.output.repository.PreOrderRepository;
import com.styl.pacific.order.service.domain.features.tenant.dto.TenantDto;
import com.styl.pacific.order.service.domain.features.tenant.output.repository.TenantRepository;
import com.styl.pacific.order.service.domain.utils.TenantHelper;
import com.styl.pacific.utils.fallback.FallbackFlow;
import java.time.Duration;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.tuple.Triple;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class PreOrderQueryHandler {
	private static final Logger log = LoggerFactory.getLogger(PreOrderQueryHandler.class);
	private final PreOrderRepository preOrderRepository;
	private final OrderQueryRepository orderQueryRepository;
	private final OrderLineItemProcessRepository orderLineItemProcessRepository;
	private final TenantRepository tenantRepository;

	@Transactional(readOnly = true)
	public Paging<PreOrderLightDto> findDtoPaging(TenantId id, PreOrderPagingQuery query) {
		return preOrderRepository.getDtoPaging(id, query);
	}

	@Transactional(readOnly = true)
	public PreOrderLightDto findPreOrderLightDtoByUserAndId(UserId userId, TenantId tenantId, PreOrderId id) {
		return preOrderCheck(tenantId, id, userId);
	}

	@Transactional(readOnly = true)
	public Paging<PreOrderSummaryDto> findSummaryPaging(TenantId tenantId, PreOrderSummaryQuery query) {
		return findAllDetailSummary(tenantId, query);
	}

	public Paging<PreOrderSummaryDto> findAllDetailSummary(TenantId tenantId, PreOrderSummaryQuery query) {
		TenantDto tenant = tenantCheck(tenantId.getValue());
		PreOrderSummaryQuery queryFinal = Optional.ofNullable(query)
				.orElse(new PreOrderSummaryQuery(null, null, null, null, null));
		OrderFilter filter = filterFromQuery(tenant, queryFinal);

		// Create a final filter for pre-orders, setting default statuses if empty and removing CANCELED status if not empty
		final var finalFilter = filter.withTypes(List.of(OrderType.PRE_ORDER))
				.withStatuses(FallbackFlow.<List<OrderStatus>, List<OrderStatus>>builder()
						.addFallBack(List::isEmpty, orderStatuses -> List.of(OrderStatus.PENDING, OrderStatus.CONFIRMED,
								OrderStatus.PAID, OrderStatus.COMPLETED, OrderStatus.COLLECTED))
						.addFallBack(orderStatuses -> !orderStatuses.isEmpty(), orderStatuses -> {
							List<OrderStatus> statuses = new ArrayList<>(orderStatuses);
							statuses.removeAll(List.of(OrderStatus.CANCELLED, OrderStatus.CREATED));
							return statuses;
						})
						.execute(Optional.ofNullable(filter.statuses())
								.orElse(List.of())));

		OrderPagingQuery orderPagingQuery = new OrderPagingQuery(finalFilter, queryFinal.getSize(), queryFinal
				.getPage(), queryFinal.getSortDirection(), queryFinal.getSortFields());
		return getSubOrderSummary(tenantId, orderPagingQuery);
	}

	private Paging<PreOrderSummaryDto> getSubOrderSummary(TenantId tenantId, OrderPagingQuery query) {
		Paging<OrderLightDto> pagingLight = orderQueryRepository.findAllLightDtoPageable(tenantId, query);
		Set<Long> orderIds = pagingLight.getContent()
				.stream()
				.map(OrderLightDto::id)
				.map(OrderId::getValue)
				.collect(Collectors.toSet());
		if (orderIds.isEmpty()) {
			return Paging.empty();
		}
		List<OrderLineItemDto> items = new ArrayList<>();
		Lists.partition(new ArrayList<>(orderIds), 100)
				.forEach(longs -> {
					int page = 0;
					int totalPage = Integer.MIN_VALUE;
					while (true) {
						Paging<OrderLineItemDto> pagingItem = orderLineItemProcessRepository.findAllPaging(tenantId,
								new OrderLineItemPagingQuery(OrderLineItemFilter.builder()
										.orderIds(new ArrayList<>(longs))
										.build(), query.getSize(), page, null, null));
						if (totalPage < pagingItem.getTotalPages()) {
							totalPage = pagingItem.getTotalPages();
						}
						if (pagingItem.getContent()
								.isEmpty()) {
							break;
						}
						items.addAll(pagingItem.getContent());
						if (pagingItem.getPage() + 1 == totalPage) {
							break;
						}
						page++;
					}
				});

		Map<Triple<OrderId, LocalDate, MealTimeId>, List<OrderLineItemDto>> itemMap = items.stream()
				.collect(Collectors.groupingBy(orderLineItemDto -> Triple.of(orderLineItemDto.orderId(),
						orderLineItemDto.collectionDate(), orderLineItemDto.mealTimeId())));
		return pagingLight.map(orderLightDto -> {
			List<OrderLineItemDto> lineItems = itemMap.getOrDefault(Triple.of(orderLightDto.id(), orderLightDto
					.collectionDate(), orderLightDto.mealTimeId()), new ArrayList<>());
			Map<PreOrderMenuItemId, Long> itemQuantities = lineItems.stream()
					.filter(orderLineItemLightDto -> {
						PreOrderMenuItemId id = orderLineItemLightDto.preOrderMenuItemId();
						if (Objects.isNull(id)) {
							log.info("PreOrderMenuItemId is null for line item: {}", orderLineItemLightDto.id());
						}
						return Objects.nonNull(id);
					})
					.collect(Collectors.groupingBy(OrderLineItemDto::preOrderMenuItemId, Collectors.summingLong(
							OrderLineItemDto::quantity)));

			return PreOrderSummaryDto.builder()
					.subOrderId(orderLightDto.id())
					.preOrderId(orderLightDto.preOrderId())
					.collectionDate(orderLightDto.collectionDate())
					.mealTimeId(orderLightDto.mealTimeId())
					.status(orderLightDto.status())
					.lineItems(itemQuantities.entrySet()
							.stream()
							.map(entry -> PreOrderSummaryDto.PreOrderMenuItemPlacementDto.builder()
									.id(entry.getKey())
									.quantity(entry.getValue())
									.build())
							.collect(Collectors.toList()))
					.build();
		});
	}

	private OrderFilter filterFromQuery(TenantDto tenant, PreOrderSummaryQuery query) {
		// Get the ZoneId from the tenant settings
		ZoneId zoneId = TenantHelper.getZoneId(tenant);

		// Parse the fromDate from the query filter and convert it to an Instant
		LocalDate from = Optional.ofNullable(query.getFilter())
				.map(PreOrderHistorySummaryFilter::fromDate)
				.map(LocalDate::parse)
				.orElse(null);

		// Parse the toDate from the query filter and convert it to an Instant
		LocalDate to = Optional.ofNullable(query.getFilter())
				.map(PreOrderHistorySummaryFilter::toDate)
				.map(LocalDate::parse)
				.orElse(null);

		// Validate the date range if both from and to dates are present
		if (Objects.nonNull(from) && Objects.nonNull(to)) {
			// Check if fromDate is after toDate
			if (from.isAfter(to)) {
				log.info("fromDate must be before toDate compare from: {} to: {}", from, to);
				throw new OrderDomainException("fromDate must be before toDate");
				// Check if the date range exceeds 60 days
			} else if (Duration.ofDays(DAYS.between(from, to))
					.toDays() > 60) {
				log.info("Date range must be less than 60 days compare from: {} to: {}", from, to);
				throw new OrderDomainException("Date range must be less than 60 days");
			}
		}

		// Build and return the PreOrderFilter object
		return OrderFilter.builder()
				.fromCollectionDate(Optional.ofNullable(from)
						.map(LocalDate::toString)
						.orElse(null))
				.toCollectionDate(Optional.ofNullable(to)
						.map(LocalDate::toString)
						.orElse(null))
				.statuses(Optional.ofNullable(query.getFilter())
						.map(PreOrderHistorySummaryFilter::statuses)
						.map(statuses -> statuses.isEmpty()
								? new ArrayList<>(List.of(PreOrderStatus.values()))
								: statuses)
						.map(statuses -> statuses.stream()
								.filter(preOrderStatus -> !PreOrderStatus.CANCELLED.equals(preOrderStatus))
								.toList())
						.map(statuses -> statuses.stream()
								.map(PreOrderDataMapper.INSTANCE::toOrderStatus)
								.toList())
						.orElse(null))
				.customerIds(Optional.ofNullable(query.getFilter())
						.map(PreOrderHistorySummaryFilter::customerIds)
						.orElse(null))
				.build();
	}

	private PreOrderLightDto preOrderCheck(TenantId tenantId, PreOrderId id, UserId userId) {
		return preOrderRepository.findDtoById(tenantId, id)
				.orElseThrow(() -> {
					log.info("PreOrder not found with id: {}", id);
					return new OrderNotFoundException(String.format("PreOrder not found with id: %s", id.getValue()));
				});
	}

	private TenantDto tenantCheck(Long tenantId) {
		return tenantRepository.findById(tenantId)
				.orElseThrow(() -> {
					log.warn("Tenant {} not found", tenantId);
					return new OrderDomainException(String.format("Tenant %s not found", tenantId));
				});
	}
}
