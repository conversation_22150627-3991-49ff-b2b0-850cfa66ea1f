/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.menuboard.ports;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.MenuBoardId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.domain.features.common.exception.OrderDomainException;
import com.styl.pacific.order.service.domain.features.menuboard.dto.MenuBoardSessionDetailDto;
import com.styl.pacific.order.service.domain.features.menuboard.dto.command.ActivateSessionMenuBoardCommand;
import com.styl.pacific.order.service.domain.features.menuboard.dto.command.GenerateActivationCodeCommand;
import com.styl.pacific.order.service.domain.features.menuboard.dto.query.PaginationMenuBoardSessionQuery;
import com.styl.pacific.order.service.domain.features.menuboard.entity.MenuBoard;
import com.styl.pacific.order.service.domain.features.menuboard.entity.MenuBoardActivationCodeSession;
import com.styl.pacific.order.service.domain.features.menuboard.entity.MenuBoardSession;
import com.styl.pacific.order.service.domain.features.menuboard.exception.MenuBoardCodeInvalidException;
import com.styl.pacific.order.service.domain.features.menuboard.exception.MenuBoardNotFoundException;
import com.styl.pacific.order.service.domain.features.menuboard.exception.MenuBoardSessionNotFoundException;
import com.styl.pacific.order.service.domain.features.menuboard.ports.input.service.MenuBoardSessionService;
import com.styl.pacific.order.service.domain.features.menuboard.ports.output.repository.MenuBoardActivationCodeSessionRepository;
import com.styl.pacific.order.service.domain.features.menuboard.ports.output.repository.MenuBoardRepository;
import com.styl.pacific.order.service.domain.features.menuboard.ports.output.repository.MenuBoardSessionRepository;
import com.styl.pacific.order.service.domain.features.menuboard.valueobjects.MenuBoardActivationCodeSessionId;
import com.styl.pacific.order.service.domain.features.menuboard.valueobjects.MenuBoardSessionId;
import java.security.SecureRandom;
import java.time.Duration;
import java.time.Instant;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 */
@Service
@Validated
@RequiredArgsConstructor
public class MenuBoardSessionServiceImpl implements MenuBoardSessionService {
	private static final Logger log = LoggerFactory.getLogger(MenuBoardSessionServiceImpl.class);
	private final MenuBoardRepository menuBoardRepository;
	private final MenuBoardSessionRepository menuBoardSessionRepository;
	private final MenuBoardActivationCodeSessionRepository menuBoardActivationCodeSessionRepository;

	public static final Duration AUTHORIZED_CODE_EXPIRED_DURATION = Duration.ofMinutes(5);

	@Override
	@Transactional(readOnly = true)
	public MenuBoardSessionDetailDto findById(TenantId tenantId, MenuBoardSessionId id) {
		return menuBoardSessionRepository.findDetailById(tenantId, id)
				.orElseThrow(() -> {
					log.error("Menu board session {} not found", id);
					return new MenuBoardSessionNotFoundException(String.format("Menu board session %s not found", id
							.getValue()));
				});
	}

	@Override
	@Transactional(readOnly = true)
	public Paging<MenuBoardSession> findAllPaging(TenantId tenantId, PaginationMenuBoardSessionQuery query) {
		return menuBoardSessionRepository.findAllPaging(tenantId, query);
	}

	@Override
	@Transactional
	public MenuBoardActivationCodeSession generateActivateSessionCode(GenerateActivationCodeCommand command) {
		return menuBoardActivationCodeSessionRepository.findById(new MenuBoardActivationCodeSessionId(command.uuid()))
				.map(existingCode -> {
					if (Instant.now()
							.isAfter(existingCode.getExpiredAt())) {
						existingCode.setName(command.name());
						existingCode.setCode(generateRandomCode());
						existingCode.setExpiredAt(Instant.now()
								.plus(AUTHORIZED_CODE_EXPIRED_DURATION));
						return menuBoardActivationCodeSessionRepository.update(existingCode);
					}
					return existingCode;
				})
				.orElseGet(() -> {
					MenuBoardActivationCodeSession newCode = MenuBoardActivationCodeSession.builder()
							.id(new MenuBoardActivationCodeSessionId(command.uuid()))
							.name(command.name())
							.code(generateRandomCode())
							.expiredAt(Instant.now()
									.plus(AUTHORIZED_CODE_EXPIRED_DURATION))
							.build();
					return menuBoardActivationCodeSessionRepository.save(newCode);
				});
	}

	@Override
	@Transactional
	public MenuBoardSessionDetailDto verifyActivationCodeMenuBoard(TenantId tenantId,
			ActivateSessionMenuBoardCommand command) {
		menuBoardCheckExist(tenantId, new MenuBoardId(command.menuBoardId()));

		Optional<MenuBoardActivationCodeSession> codeOptional = menuBoardActivationCodeSessionRepository.findByCode(
				command.code());
		if (codeOptional.isEmpty()) {
			log.info("Activation code {} not found", command.code());
			throw new MenuBoardCodeInvalidException("Activation code is invalid or expired");
		}

		MenuBoardActivationCodeSession code = codeOptional.get();
		if (Instant.now()
				.isAfter(code.getExpiredAt())) {
			log.info("Activation code {} is expired", code.getId()
					.getValue());
			throw new MenuBoardCodeInvalidException("Activation code is invalid or expired");
		}
		menuBoardActivationCodeSessionRepository.deleteById(code.getId()
				.getValue());

		MenuBoardSession session = MenuBoardSession.builder()
				.id(new MenuBoardSessionId(code.getId()
						.getValue()))
				.tenantId(tenantId)
				.name(code.getName())
				.menuBoardId(new MenuBoardId(command.menuBoardId()))
				.build();
		return menuBoardSessionRepository.saveDetail(session);
	}

	@Override
	@Transactional
	public void revokeSession(TenantId tenantId, MenuBoardSessionId id) {
		if (Boolean.FALSE.equals(menuBoardSessionRepository.existsById(tenantId, id))) {
			log.info("Menu board session with id {} not found", id.getValue());
			throw new MenuBoardSessionNotFoundException(String.format("Menu board session with id %s not found", id
					.getValue()));
		}
		menuBoardSessionRepository.deleteById(tenantId, id);
	}

	private String generateRandomCode() {
		int times = 0;
		SecureRandom random = new SecureRandom();
		while (times++ < 100) {
			String code = String.valueOf(random.nextInt(900000) + 100000);
			if (menuBoardActivationCodeSessionRepository.findByCode(code)
					.isEmpty()) {
				return code;
			}
		}
		throw new OrderDomainException("Cannot generate activation code");
	}

	private MenuBoard menuBoardCheck(TenantId tenantId, MenuBoardId id) {
		return menuBoardRepository.findById(tenantId, id)
				.orElseThrow(() -> {
					log.error("Menu board with id {} not found", id.getValue());
					return new MenuBoardNotFoundException(String.format("Menu board with id %s not found", id
							.getValue()));
				});
	}

	private void menuBoardCheckExist(TenantId tenantId, MenuBoardId id) {
		if (!menuBoardRepository.existsById(tenantId, id)) {
			log.error("Menu board with id {} not found", id.getValue());
			throw new MenuBoardNotFoundException(String.format("Menu board with id %s not found", id.getValue()));
		}
	}
}
