/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.order.handler.command;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.enums.order.OrderPaymentStatus;
import com.styl.pacific.domain.enums.order.OrderStatus;
import com.styl.pacific.order.service.domain.features.order.dto.query.OrderFilter;
import com.styl.pacific.order.service.domain.features.order.dto.query.OrderPagingQuery;
import com.styl.pacific.order.service.domain.features.order.entity.OrderLight;
import com.styl.pacific.order.service.domain.features.order.mapper.OrderDataMapper;
import com.styl.pacific.order.service.domain.features.order.ports.output.repository.OrderQueryRepository;
import com.styl.pacific.order.service.domain.features.payment.event.PaymentReverseEvent;
import com.styl.pacific.order.service.domain.features.payment.port.output.producer.PaymentReversalCommandEventPublisher;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class OrderTriggerRefundCommandHandler {
	private final OrderQueryRepository orderQueryRepository;
	private final PaymentReversalCommandEventPublisher paymentReversalCommandEventPublisher;

	@Transactional
	public List<OrderLight> processOrderRefund() {
		OrderFilter filter = OrderFilter.builder()
				.statuses(List.of(OrderStatus.CANCELLED))
				.paymentStatuses(List.of(OrderPaymentStatus.REFUNDING))
				.refundable(true)
				.build();
		OrderPagingQuery query = new OrderPagingQuery(filter, 50, 0, null, null);
		Paging<OrderLight> paging = orderQueryRepository.findAllLightPageable(null, query);
		List<OrderLight> orders = paging.getContent();
		orders.forEach(order -> {
			try {
				if (Boolean.TRUE.equals(order.getRefundable())) {
					PaymentReverseEvent event = OrderDataMapper.INSTANCE.toReverseEvent(order, null);
					paymentReversalCommandEventPublisher.publish(event);
				}
			} catch (Exception ignored) {
			}
		});
		return orders;
	}
}
