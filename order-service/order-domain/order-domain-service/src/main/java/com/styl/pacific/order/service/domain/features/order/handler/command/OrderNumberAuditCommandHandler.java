/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.order.handler.command;

import com.styl.pacific.order.service.domain.features.order.entity.OrderNumberAuditSequence;
import com.styl.pacific.order.service.domain.features.order.ports.output.repository.OrderNumberAuditRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class OrderNumberAuditCommandHandler {
	private final OrderNumberAuditRepository orderNumberAuditRepository;

	@Transactional
	public OrderNumberAuditSequence save(OrderNumberAuditSequence orderNumberAuditSequence) {
		return orderNumberAuditRepository.save(orderNumberAuditSequence);
	}

	@Transactional
	public OrderNumberAuditSequence update(OrderNumberAuditSequence orderNumberAuditSequence) {
		return orderNumberAuditRepository.update(orderNumberAuditSequence);
	}
}
