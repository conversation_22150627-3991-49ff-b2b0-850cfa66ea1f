/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.order.handler.command;

import com.styl.pacific.domain.enums.order.OrderStatus;
import com.styl.pacific.domain.valueobject.BaseId;
import com.styl.pacific.order.service.domain.features.inventory.ports.output.repository.InventoryRepository;
import com.styl.pacific.order.service.domain.features.order.entity.Order;
import com.styl.pacific.order.service.domain.features.order.entity.OrderLineItem;
import com.styl.pacific.order.service.domain.features.order.ports.output.repository.OrderProcessRepository;
import com.styl.pacific.order.service.domain.features.preorder.menu.ports.output.repository.PreOrderMenuItemRepository;
import com.styl.pacific.order.service.domain.features.preorder.menu.valueobjects.PreOrderMenuItemId;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class OrderRestoreCommandHandler {
	private final OrderProcessRepository orderProcessRepository;
	private final PreOrderMenuItemRepository preOrderMenuItemRepository;
	private final InventoryRepository inventoryRepository;

	@Transactional
	public void restoreInventoryOnCancelOrder(Order order) {
		switch (order.getType()) {
		case INSTANT_ORDER, ONSITE_ORDER -> restoreInventory(order);
		case PRE_ORDER -> restorePreOrderMenuItem(order);
		}
		order.setStatus(OrderStatus.CANCELLED);
		orderProcessRepository.update(order);
	}

	private void restoreInventory(Order order) {
		List<OrderLineItem> lineItemsReverse = new ArrayList<>(order.getLineItems()
				.stream()
				.filter(orderLineItem -> !Boolean.FALSE.equals(orderLineItem.getReversible()))
				.toList());
		lineItemsReverse.sort(Comparator.comparing(orderLineItem -> Optional.ofNullable(orderLineItem.getProductId())
				.map(BaseId::getValue)
				.orElse(0L)));
		for (OrderLineItem lineItem : lineItemsReverse) {
			inventoryRepository.addQuantity(order.getTenantId(), lineItem.getProductId(), lineItem.getQuantity());
		}
	}

	private void restorePreOrderMenuItem(Order order) {
		List<OrderLineItem> lineItems = order.getLineItems();
		lineItems.sort(Comparator.comparing(orderLineItem -> Optional.ofNullable(orderLineItem.getPreOrderMenuItemId())
				.map(PreOrderMenuItemId::getValue)
				.orElse(0L)));

		lineItems.forEach(orderLineItem -> {
			if (orderLineItem.getPreOrderMenuItemId() != null) {
				preOrderMenuItemRepository.addOrderedQuantity(orderLineItem.getPreOrderMenuItemId(), -orderLineItem
						.getQuantity()
						.intValue());
			}
		});
	}
}
