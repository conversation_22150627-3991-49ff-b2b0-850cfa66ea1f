/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.order.ports;

import com.styl.pacific.domain.enums.order.OrderPaymentStatus;
import com.styl.pacific.order.service.domain.features.order.dto.command.refund.MarkOrderRefundedCommand;
import com.styl.pacific.order.service.domain.features.order.entity.OrderLight;
import com.styl.pacific.order.service.domain.features.order.handler.command.OrderCommandHandler;
import com.styl.pacific.order.service.domain.features.order.handler.command.OrderTriggerRefundCommandHandler;
import com.styl.pacific.order.service.domain.features.order.handler.query.OrderRefundingQueryHandler;
import com.styl.pacific.order.service.domain.features.order.ports.input.service.OrderRefundService;
import java.time.Instant;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 */
@Service
@Validated
@RequiredArgsConstructor
public class OrderRefundServiceImpl implements OrderRefundService {
	private static final Logger log = LoggerFactory.getLogger(OrderRefundServiceImpl.class);
	private final OrderRefundingQueryHandler orderRefundingQueryHandler;
	private final OrderCommandHandler orderCommandHandler;
	private final OrderTriggerRefundCommandHandler orderTriggerRefundCommandHandler;

	@Override
	public void processRefundingOrder(int orderBatchSize, Instant timeout) {
		int totalRecord = 0;
		do {
			List<OrderLight> orders = orderTriggerRefundCommandHandler.processOrderRefund();
			if (Objects.isNull(orders) || orders.isEmpty()) {
				break;
			}
			totalRecord += orders.size();
		} while (Instant.now()
				.isBefore(timeout) && totalRecord < orderBatchSize);
		log.info("Order refund check finished, total record: {}", totalRecord);
	}

	@Override
	public void markOrderRefunded(MarkOrderRefundedCommand command) {
		OrderLight order = orderRefundingQueryHandler.findLightById(command.tenantId(), command.idempotencyKey(),
				command.paymentTransactionId());
		if (OrderPaymentStatus.REFUNDED.equals(order.getPaymentStatus())) {
			return;
		}
		order.setPaymentStatus(OrderPaymentStatus.REFUNDED);
		orderCommandHandler.update(order);
	}
}
