/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.order.dto.command.cancel;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 */
@Builder
@Getter
@AllArgsConstructor
public class CancelOrderCommand {
	@NotNull(message = "ids must not be null")
	@Size(min = 1, max = 50, message = "ids must be at least 1 and at most 50 items")
	private List<Long> ids;
	@Length(max = 255, message = "reason must not exceed 255 characters")
	@NotBlank(message = "reason must not be blank")
	private String reason;
	@Builder.Default
	private Boolean refundable = Boolean.TRUE;
}
