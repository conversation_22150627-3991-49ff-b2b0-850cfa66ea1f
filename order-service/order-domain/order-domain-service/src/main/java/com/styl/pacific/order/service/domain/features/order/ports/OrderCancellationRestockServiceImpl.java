/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.order.ports;

import com.styl.pacific.order.service.domain.features.order.entity.OrderLight;
import com.styl.pacific.order.service.domain.features.order.handler.command.OrderCancellingCommandHandler;
import com.styl.pacific.order.service.domain.features.order.ports.input.service.OrderCancellationRestockService;
import java.time.Instant;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class OrderCancellationRestockServiceImpl implements OrderCancellationRestockService {
	private static final Logger log = LoggerFactory.getLogger(OrderCancellationRestockServiceImpl.class);
	private final OrderCancellingCommandHandler orderCancellingCommandHandler;

	@Override
	public void processCancellingOrder(int orderBatchSize, Instant timeout) {
		int totalRecord = 0;
		do {
			List<OrderLight> orders = orderCancellingCommandHandler.processOrderCancelling();
			if (Objects.isNull(orders) || orders.isEmpty()) {
				break;
			}
			totalRecord += orders.size();
		} while (Instant.now()
				.isBefore(timeout) && totalRecord < orderBatchSize);
		log.info("Order cancellation restock finished, total record: {}", totalRecord);
	}
}
