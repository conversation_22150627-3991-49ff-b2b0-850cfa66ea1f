/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.order.dto.command.offline.place;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigInteger;
import java.util.List;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Builder
@Data
public class ProductMetadataOfflineCommand {
	@Size(max = 80, message = "name must not exceed 80 characters")
	@NotBlank(message = "name must not be blank")
	private String name;
	@Valid
	private HealthierChoiceMetadataOfflineCommand healthierChoice;
	@Valid
	@NotNull(message = "category must not be null")
	private CategoryMetadataOfflineCommand category;
	@Size(max = 80, message = "sku must not be greater than 80 characters")
	@NotBlank(message = "sku must not be blank")
	private String sku;
	private String barcode;
	private String ingredients;
	@NotNull(message = "unitPrice must not be null")
	@Min(value = 0, message = "unitPrice must be greater than or equal to 0")
	private BigInteger unitPrice;
	@Min(value = 0, message = "listingPrice must be greater than or equal to 0")
	private BigInteger listingPrice;
	@Valid
	private List<ProductImageMetadataOfflineCommand> images;
}
