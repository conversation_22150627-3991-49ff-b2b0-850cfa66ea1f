/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.order.dto;

import com.styl.pacific.domain.enums.PreOrderMenuType;
import com.styl.pacific.domain.enums.order.OrderCancellationType;
import com.styl.pacific.domain.enums.order.OrderPaymentStatus;
import com.styl.pacific.domain.enums.order.OrderStatus;
import com.styl.pacific.domain.enums.order.OrderType;
import com.styl.pacific.domain.valueobject.Money;
import com.styl.pacific.domain.valueobject.OrderId;
import com.styl.pacific.domain.valueobject.PaymentMethodId;
import com.styl.pacific.domain.valueobject.PaymentTransactionId;
import com.styl.pacific.domain.valueobject.PreOrderId;
import com.styl.pacific.domain.valueobject.StoreId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.order.service.domain.features.mealtime.entity.MealTime;
import com.styl.pacific.order.service.domain.features.mealtime.valueobjects.MealTimeId;
import com.styl.pacific.order.service.domain.features.order.entity.OrderServiceCharge;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
public record OrderLightDto(OrderId id,
		TenantId tenantId,
		String idempotencyKey,
		String systemSource,
		Long version,
		Long eventVersion,
		String orderNumber,
		StoreId storeId,
		PreOrderId preOrderId,
		PreOrderMenuType preOrderMenuType,
		MealTimeId mealTimeId,
		MealTime mealTime,
		LocalDate collectionDate,
		Instant collectionDateTime,
		Instant collectedAt,
		String collectorStaffCode,
		String collectorStaffName,
		String collectorName,
		String staffCode,
		String staffName,
		UserId issuerId,
		UserId customerId,
		String customerEmail,
		String customerName,
		OrderStatus status,
		OrderType type,
		Boolean isOffline,
		String note,
		List<OrderServiceCharge> serviceCharges,
		String taxName,
		BigDecimal taxRate,
		Boolean taxInclude,
		Money taxAmount,
		Money subtotalAmount,
		Money serviceChargeAmount,
		Money discountAmount,
		Money totalAmount,
		String currencyCode,
		PaymentMethodId paymentMethodId,
		String paymentMethodName,
		PaymentTransactionId paymentTransactionId,
		OrderPaymentStatus paymentStatus,
		String paymentRef,
		String transactionRef,
		String terminalId,
		Boolean refundable,
		Instant cancellationDueAt,
		OrderCancellationType cancellationType,
		String cancelReason,
		Instant canceledAt,
		Instant expiredAt,
		Instant createdAt,
		Instant updatedAt,
		Instant orderedAt) {
}
