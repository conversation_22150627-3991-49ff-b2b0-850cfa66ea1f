/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.order.dto.command.place;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigInteger;
import java.util.List;
import lombok.Builder;

@Builder
public record ProductMetadataCommand(@NotBlank(message = "name must not be empty or null") String name,
		@Valid HealthierChoiceMetadataCommand healthierChoice,
		@Valid @NotNull(message = "category must not be null") CategoryMetadataCommand category,
		@NotBlank(message = "sku must not be empty or null") String sku,
		String barcode,
		String ingredients,
		@NotNull(message = "unitPrice must not be null") @Min(value = 0, message = "unitPrice must be greater than or equal to 0") BigInteger unitPrice,
		@Min(value = 0, message = "listingPrice must be greater than or equal to 0") BigInteger listingPrice,
		@Valid List<ProductOptionMetadataCommand> options,
		@Valid List<ProductImageMetadataCommand> images) {
}
