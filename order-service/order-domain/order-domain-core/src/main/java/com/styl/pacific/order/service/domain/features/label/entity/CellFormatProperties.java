/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.label.entity;

import com.styl.pacific.order.service.domain.features.label.enums.BorderStyle;
import com.styl.pacific.order.service.domain.features.label.enums.HorizontalAlignment;
import com.styl.pacific.order.service.domain.features.label.enums.VerticalAlignment;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class CellFormatProperties {
	private Double fontSize;
	private VerticalAlignment verticalAlignment;
	private HorizontalAlignment horizontalAlignment;
	private Integer areaPaddingTop;
	private Integer areaPaddingBottom;
	private Integer areaPaddingLeft;
	private Integer areaPaddingRight;
	private BorderStyle borderType;

	private CellFormatProperties(Builder builder) {
		setFontSize(builder.fontSize);
		setVerticalAlignment(builder.verticalAlignment);
		setHorizontalAlignment(builder.horizontalAlignment);
		setAreaPaddingTop(builder.areaPaddingTop);
		setAreaPaddingBottom(builder.areaPaddingBottom);
		setAreaPaddingLeft(builder.areaPaddingLeft);
		setAreaPaddingRight(builder.areaPaddingRight);
		setBorderType(builder.borderType);
	}

	public static Builder builder() {
		return new Builder();
	}

	public Double getFontSize() {
		return fontSize;
	}

	public void setFontSize(Double fontSize) {
		this.fontSize = fontSize;
	}

	public VerticalAlignment getVerticalAlignment() {
		return verticalAlignment;
	}

	public void setVerticalAlignment(VerticalAlignment verticalAlignment) {
		this.verticalAlignment = verticalAlignment;
	}

	public HorizontalAlignment getHorizontalAlignment() {
		return horizontalAlignment;
	}

	public void setHorizontalAlignment(HorizontalAlignment horizontalAlignment) {
		this.horizontalAlignment = horizontalAlignment;
	}

	public Integer getAreaPaddingTop() {
		return areaPaddingTop;
	}

	public void setAreaPaddingTop(Integer areaPaddingTop) {
		this.areaPaddingTop = areaPaddingTop;
	}

	public Integer getAreaPaddingBottom() {
		return areaPaddingBottom;
	}

	public void setAreaPaddingBottom(Integer areaPaddingBottom) {
		this.areaPaddingBottom = areaPaddingBottom;
	}

	public Integer getAreaPaddingLeft() {
		return areaPaddingLeft;
	}

	public void setAreaPaddingLeft(Integer areaPaddingLeft) {
		this.areaPaddingLeft = areaPaddingLeft;
	}

	public Integer getAreaPaddingRight() {
		return areaPaddingRight;
	}

	public void setAreaPaddingRight(Integer areaPaddingRight) {
		this.areaPaddingRight = areaPaddingRight;
	}

	public BorderStyle getBorderType() {
		return borderType;
	}

	public void setBorderType(BorderStyle borderType) {
		this.borderType = borderType;
	}

	public static final class Builder {
		private Double fontSize;
		private VerticalAlignment verticalAlignment;
		private HorizontalAlignment horizontalAlignment;
		private Integer areaPaddingTop;
		private Integer areaPaddingBottom;
		private Integer areaPaddingLeft;
		private Integer areaPaddingRight;
		private BorderStyle borderType;

		private Builder() {
		}

		public Builder fontSize(Double fontSize) {
			this.fontSize = fontSize;
			return this;
		}

		public Builder verticalAlignment(VerticalAlignment verticalAlignment) {
			this.verticalAlignment = verticalAlignment;
			return this;
		}

		public Builder horizontalAlignment(HorizontalAlignment horizontalAlignment) {
			this.horizontalAlignment = horizontalAlignment;
			return this;
		}

		public Builder areaPaddingTop(Integer areaPaddingTop) {
			this.areaPaddingTop = areaPaddingTop;
			return this;
		}

		public Builder areaPaddingBottom(Integer areaPaddingBottom) {
			this.areaPaddingBottom = areaPaddingBottom;
			return this;
		}

		public Builder areaPaddingLeft(Integer areaPaddingLeft) {
			this.areaPaddingLeft = areaPaddingLeft;
			return this;
		}

		public Builder areaPaddingRight(Integer areaPaddingRight) {
			this.areaPaddingRight = areaPaddingRight;
			return this;
		}

		public Builder borderType(BorderStyle borderType) {
			this.borderType = borderType;
			return this;
		}

		public CellFormatProperties build() {
			return new CellFormatProperties(this);
		}
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (!(o instanceof CellFormatProperties that))
			return false;
		return Objects.equals(fontSize, that.fontSize) && verticalAlignment == that.verticalAlignment
				&& horizontalAlignment == that.horizontalAlignment && Objects.equals(areaPaddingTop,
						that.areaPaddingTop) && Objects.equals(areaPaddingBottom, that.areaPaddingBottom) && Objects
								.equals(areaPaddingLeft, that.areaPaddingLeft) && Objects.equals(areaPaddingRight,
										that.areaPaddingRight) && borderType == that.borderType;
	}

	@Override
	public int hashCode() {
		return Objects.hash(fontSize, verticalAlignment, horizontalAlignment, areaPaddingTop, areaPaddingBottom,
				areaPaddingLeft, areaPaddingRight, borderType);
	}
}
