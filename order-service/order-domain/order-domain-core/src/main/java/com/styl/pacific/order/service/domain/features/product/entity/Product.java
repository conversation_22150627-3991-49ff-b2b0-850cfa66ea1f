/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.product.entity;

import com.styl.pacific.domain.entity.AggregateRoot;
import com.styl.pacific.domain.enums.ProductType;
import com.styl.pacific.domain.valueobject.CategoryId;
import com.styl.pacific.domain.valueobject.HealthierChoiceId;
import com.styl.pacific.domain.valueobject.Money;
import com.styl.pacific.domain.valueobject.ProductId;
import com.styl.pacific.domain.valueobject.StoreId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.order.service.domain.features.inventory.entity.Inventory;
import com.styl.pacific.order.service.domain.features.product.enums.ProductStatus;
import java.time.Instant;
import java.util.List;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

/**
 * <AUTHOR>
 */
public class Product extends AggregateRoot<ProductId> {
	private String migrationId;
	private ProductType productType;
	private StoreId storeId;
	private String name;
	private String sku;
	private String briefInformation;
	private String description;
	private String ingredients;
	private String barcode;
	private ProductStatus status;
	private Money unitPrice;
	private Money listingPrice;
	private String currencyCode;
	private Inventory inventory;
	private CategoryId categoryId;
	private HealthierChoiceId healthierChoiceId;
	private List<ProductImage> images;
	private List<ProductAllergen> allergens;
	private List<ProductNutrition> nutrition;
	private List<ProductOption> options;
	private Instant createdAt;
	private Instant updatedAt;

	private Product(Builder builder) {
		setId(builder.id);
		setTenantId(builder.tenantId);
		setMigrationId(builder.migrationId);
		setProductType(builder.productType);
		setStoreId(builder.storeId);
		setName(builder.name);
		setSku(builder.sku);
		setBriefInformation(builder.briefInformation);
		setDescription(builder.description);
		setIngredients(builder.ingredients);
		setBarcode(builder.barcode);
		setStatus(builder.status);
		setUnitPrice(builder.unitPrice);
		setListingPrice(builder.listingPrice);
		setCurrencyCode(builder.currencyCode);
		setInventory(builder.inventory);
		setCategoryId(builder.categoryId);
		setHealthierChoiceId(builder.healthierChoiceId);
		setImages(builder.images);
		setAllergens(builder.allergens);
		setNutrition(builder.nutrition);
		setOptions(builder.options);
		setCreatedAt(builder.createdAt);
		setUpdatedAt(builder.updatedAt);
	}

	public static Builder builder() {
		return new Builder();
	}

	public String getMigrationId() {
		return migrationId;
	}

	public void setMigrationId(String migrationId) {
		this.migrationId = migrationId;
	}

	public ProductType getProductType() {
		return productType;
	}

	public void setProductType(ProductType productType) {
		this.productType = productType;
	}

	public StoreId getStoreId() {
		return storeId;
	}

	public void setStoreId(StoreId storeId) {
		this.storeId = storeId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getSku() {
		return sku;
	}

	public void setSku(String sku) {
		this.sku = sku;
	}

	public String getBriefInformation() {
		return briefInformation;
	}

	public void setBriefInformation(String briefInformation) {
		this.briefInformation = briefInformation;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getIngredients() {
		return ingredients;
	}

	public void setIngredients(String ingredients) {
		this.ingredients = ingredients;
	}

	public String getBarcode() {
		return barcode;
	}

	public void setBarcode(String barcode) {
		this.barcode = barcode;
	}

	public ProductStatus getStatus() {
		return status;
	}

	public void setStatus(ProductStatus status) {
		this.status = status;
	}

	public Money getUnitPrice() {
		return unitPrice;
	}

	public void setUnitPrice(Money unitPrice) {
		this.unitPrice = unitPrice;
	}

	public Money getListingPrice() {
		return listingPrice;
	}

	public void setListingPrice(Money listingPrice) {
		this.listingPrice = listingPrice;
	}

	public String getCurrencyCode() {
		return currencyCode;
	}

	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}

	public Inventory getInventory() {
		return inventory;
	}

	public void setInventory(Inventory inventory) {
		this.inventory = inventory;
	}

	public Instant getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Instant createdAt) {
		this.createdAt = createdAt;
	}

	public Instant getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Instant updatedAt) {
		this.updatedAt = updatedAt;
	}

	public CategoryId getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(CategoryId categoryId) {
		this.categoryId = categoryId;
	}

	public HealthierChoiceId getHealthierChoiceId() {
		return healthierChoiceId;
	}

	public void setHealthierChoiceId(HealthierChoiceId healthierChoiceId) {
		this.healthierChoiceId = healthierChoiceId;
	}

	public List<ProductImage> getImages() {
		return images;
	}

	public void setImages(List<ProductImage> images) {
		this.images = images;
	}

	public List<ProductAllergen> getAllergens() {
		return allergens;
	}

	public void setAllergens(List<ProductAllergen> allergens) {
		this.allergens = allergens;
	}

	public List<ProductNutrition> getNutrition() {
		return nutrition;
	}

	public void setNutrition(List<ProductNutrition> nutrition) {
		this.nutrition = nutrition;
	}

	public List<ProductOption> getOptions() {
		return options;
	}

	public void setOptions(List<ProductOption> options) {
		this.options = options;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;

		if (!(o instanceof Product product))
			return false;

		return new EqualsBuilder().appendSuper(super.equals(o))
				.append(migrationId, product.migrationId)
				.append(productType, product.productType)
				.append(storeId, product.storeId)
				.append(name, product.name)
				.append(sku, product.sku)
				.append(briefInformation, product.briefInformation)
				.append(description, product.description)
				.append(ingredients, product.ingredients)
				.append(barcode, product.barcode)
				.append(status, product.status)
				.append(unitPrice, product.unitPrice)
				.append(listingPrice, product.listingPrice)
				.append(currencyCode, product.currencyCode)
				.append(inventory, product.inventory)
				.append(categoryId, product.categoryId)
				.append(healthierChoiceId, product.healthierChoiceId)
				.append(images, product.images)
				.append(allergens, product.allergens)
				.append(nutrition, product.nutrition)
				.append(options, product.options)
				.append(createdAt, product.createdAt)
				.append(updatedAt, product.updatedAt)
				.isEquals();
	}

	@Override
	public int hashCode() {
		return new HashCodeBuilder(17, 37).appendSuper(super.hashCode())
				.append(migrationId)
				.append(productType)
				.append(storeId)
				.append(name)
				.append(sku)
				.append(briefInformation)
				.append(description)
				.append(ingredients)
				.append(barcode)
				.append(status)
				.append(unitPrice)
				.append(listingPrice)
				.append(currencyCode)
				.append(inventory)
				.append(categoryId)
				.append(healthierChoiceId)
				.append(images)
				.append(allergens)
				.append(nutrition)
				.append(options)
				.append(createdAt)
				.append(updatedAt)
				.toHashCode();
	}

	public static final class Builder {
		private TenantId tenantId;
		private String migrationId;
		private ProductType productType;
		private StoreId storeId;
		private String name;
		private String sku;
		private String briefInformation;
		private String description;
		private String ingredients;
		private String barcode;
		private ProductStatus status;
		private Money unitPrice;
		private Money listingPrice;
		private String currencyCode;
		private Inventory inventory;
		private Instant createdAt;
		private Instant updatedAt;
		private CategoryId categoryId;
		private HealthierChoiceId healthierChoiceId;
		private List<ProductImage> images;
		private List<ProductAllergen> allergens;
		private List<ProductNutrition> nutrition;
		private List<ProductOption> options;
		private ProductId id;

		private Builder() {
		}

		public Builder id(ProductId id) {
			this.id = id;
			return this;
		}

		public Builder tenantId(TenantId tenantId) {
			this.tenantId = tenantId;
			return this;
		}

		public Builder migrationId(String migrationId) {
			this.migrationId = migrationId;
			return this;
		}

		public Builder productType(ProductType productType) {
			this.productType = productType;
			return this;
		}

		public Builder storeId(StoreId storeId) {
			this.storeId = storeId;
			return this;
		}

		public Builder name(String name) {
			this.name = name;
			return this;
		}

		public Builder sku(String sku) {
			this.sku = sku;
			return this;
		}

		public Builder briefInformation(String briefInformation) {
			this.briefInformation = briefInformation;
			return this;
		}

		public Builder description(String description) {
			this.description = description;
			return this;
		}

		public Builder ingredients(String ingredients) {
			this.ingredients = ingredients;
			return this;
		}

		public Builder barcode(String barcode) {
			this.barcode = barcode;
			return this;
		}

		public Builder status(ProductStatus status) {
			this.status = status;
			return this;
		}

		public Builder unitPrice(Money unitPrice) {
			this.unitPrice = unitPrice;
			return this;
		}

		public Builder listingPrice(Money listingPrice) {
			this.listingPrice = listingPrice;
			return this;
		}

		public Builder currencyCode(String currencyCode) {
			this.currencyCode = currencyCode;
			return this;
		}

		public Builder inventory(Inventory inventory) {
			this.inventory = inventory;
			return this;
		}

		public Builder createdAt(Instant createdAt) {
			this.createdAt = createdAt;
			return this;
		}

		public Builder updatedAt(Instant updatedAt) {
			this.updatedAt = updatedAt;
			return this;
		}

		public Builder categoryId(CategoryId categoryId) {
			this.categoryId = categoryId;
			return this;
		}

		public Builder healthierChoiceId(HealthierChoiceId healthierChoiceId) {
			this.healthierChoiceId = healthierChoiceId;
			return this;
		}

		public Builder images(List<ProductImage> images) {
			this.images = images;
			return this;
		}

		public Builder allergens(List<ProductAllergen> allergens) {
			this.allergens = allergens;
			return this;
		}

		public Builder nutrition(List<ProductNutrition> nutrition) {
			this.nutrition = nutrition;
			return this;
		}

		public Builder options(List<ProductOption> options) {
			this.options = options;
			return this;
		}

		public Product build() {
			return new Product(this);
		}
	}
}
