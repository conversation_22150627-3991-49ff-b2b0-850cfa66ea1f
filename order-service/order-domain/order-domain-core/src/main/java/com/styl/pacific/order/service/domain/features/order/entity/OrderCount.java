/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.order.entity;

import java.time.LocalDate;
import java.util.Objects;

public class OrderCount {
	private LocalDate collectionDate;
	private Long totalOrders;

	public OrderCount(LocalDate collectionDate, Long totalOrders) {
		this.collectionDate = collectionDate;
		this.totalOrders = totalOrders;
	}

	public LocalDate getCollectionDate() {
		return collectionDate;
	}

	public void setCollectionDate(LocalDate collectionDate) {
		this.collectionDate = collectionDate;
	}

	public Long getTotalOrders() {
		return totalOrders;
	}

	public void setTotalOrders(Long totalOrders) {
		this.totalOrders = totalOrders;
	}

	@Override
	public boolean equals(Object o) {
		if (o == null || getClass() != o.getClass())
			return false;
		OrderCount that = (OrderCount) o;
		return Objects.equals(collectionDate, that.collectionDate) && Objects.equals(totalOrders, that.totalOrders);
	}

	@Override
	public int hashCode() {
		return Objects.hash(collectionDate, totalOrders);
	}
}
