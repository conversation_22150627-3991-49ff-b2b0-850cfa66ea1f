/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.inventory.entity;

import com.styl.pacific.domain.valueobject.ProductId;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class ReservationQuantity {
	private ProductId productId;
	private Long quantity;

	private ReservationQuantity(Builder builder) {
		setProductId(builder.productId);
		setQuantity(builder.quantity);
	}

	public static Builder builder() {
		return new Builder();
	}

	public ProductId getProductId() {
		return productId;
	}

	public void setProductId(ProductId productId) {
		this.productId = productId;
	}

	public Long getQuantity() {
		return quantity;
	}

	public void setQuantity(Long quantity) {
		this.quantity = quantity;
	}

	public static final class Builder {
		private ProductId productId;
		private Long quantity;

		private Builder() {
		}

		public Builder productId(ProductId productId) {
			this.productId = productId;
			return this;
		}

		public Builder quantity(Long quantity) {
			this.quantity = quantity;
			return this;
		}

		public ReservationQuantity build() {
			return new ReservationQuantity(this);
		}
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (!(o instanceof ReservationQuantity that))
			return false;
		return Objects.equals(productId, that.productId) && Objects.equals(quantity, that.quantity);
	}

	@Override
	public int hashCode() {
		return Objects.hash(productId, quantity);
	}
}
