/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.domain.features.order;

import com.styl.pacific.domain.valueobject.Money;
import com.styl.pacific.order.service.domain.features.order.entity.Order;
import com.styl.pacific.order.service.domain.features.order.entity.OrderLineItem;
import com.styl.pacific.order.service.domain.features.order.entity.OrderServiceCharge;
import com.styl.pacific.order.service.domain.features.order.event.OrderCalculatedEvent;
import com.styl.pacific.order.service.domain.features.order.event.OrderValidatedEvent;
import com.styl.pacific.order.service.domain.features.order.exception.InvalidOrderAmountException;
import com.styl.pacific.order.service.domain.features.order.exception.InvalidOrderException;
import com.styl.pacific.order.service.domain.features.order.exception.InvalidOrderLineItemAmountException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Collections;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class OrderDomainCoreImpl implements OrderDomainCore {
	private static final Logger log = LoggerFactory.getLogger(OrderDomainCoreImpl.class);

	@Override
	public OrderValidatedEvent validateOrder(Order order) {
		validateOrderLineItemAmount(order);
		validateOrderServiceChargeAmount(order);
		validateOrderAmount(order);
		return new OrderValidatedEvent(order);
	}

	private void validateOrderServiceChargeAmount(Order order) {
		Money subtotalAmount = order.getSubtotalAmount();
		Money discountAmount = order.getDiscountAmount();
		Money calServiceChargeAmount = new Money(BigInteger.ZERO);
		for (OrderServiceCharge serviceCharge : Optional.ofNullable(order.getServiceCharges())
				.orElse(Collections.emptyList())) {
			Money chargeFixedAmount = serviceCharge.getChargeFixedAmount();
			BigDecimal chargeRate = serviceCharge.getChargeRate();
			Money amount = serviceCharge.getAmount();
			if (Objects.isNull(chargeFixedAmount)) {
				throw new InvalidOrderException(String.format("Service charge %s chargeFixedAmount must not be null",
						serviceCharge.getName()));
			}
			if (Objects.isNull(chargeRate)) {
				throw new InvalidOrderException(String.format("Service charge %s chargeRate must not be null",
						serviceCharge.getName()));
			}
			if (Objects.isNull(amount)) {
				throw new InvalidOrderException(String.format("Service charge %s amount must not be null", serviceCharge
						.getName()));
			}

			Money calculatedAmount = chargeFixedAmount.add(subtotalAmount.subtract(discountAmount)
					.multiply(chargeRate));

			if (Boolean.FALSE.equals(amount.compareWithAcceptedTolerance(calculatedAmount))) {
				throw new InvalidOrderAmountException(String.format(
						"Invalid order service charge: %s amount expect %s but found %s", serviceCharge.getName(),
						calculatedAmount.getAmount(), amount.getAmount()));
			}

			calServiceChargeAmount = calServiceChargeAmount.add(amount);
		}
		if (Boolean.FALSE.equals(calServiceChargeAmount.compareWithAcceptedTolerance(order.getServiceChargeAmount()))) {
			log.warn("Calculated service charge amount: {} compare with service charge amount: {}",
					calServiceChargeAmount, order.getServiceChargeAmount());
			throw new InvalidOrderAmountException(String.format(
					"Invalid order service charge amount expect %s but found %s", calServiceChargeAmount.getAmount(),
					order.getServiceChargeAmount()
							.getAmount()));
		}
	}

	private void validateOrderAmount(Order order) {
		Money subtotalAmount = order.getSubtotalAmount();
		Money serviceChargeAmount = order.getServiceChargeAmount();
		Money discountAmount = order.getDiscountAmount();
		BigDecimal taxRate = Optional.ofNullable(order.getTaxRate())
				.orElse(BigDecimal.ZERO);
		Money taxAmount = order.getTaxAmount();
		Money totalAmount = order.getTotalAmount();
		if (Objects.isNull(subtotalAmount)) {
			throw new InvalidOrderException("subtotalAmount must not be null");
		}
		if (Objects.isNull(serviceChargeAmount)) {
			throw new InvalidOrderException("serviceChargeAmount must not be null");
		}
		if (Objects.isNull(discountAmount)) {
			throw new InvalidOrderException("discountAmount must not be null");
		}
		if (Objects.isNull(taxAmount)) {
			throw new InvalidOrderException("taxAmount must not be null");
		}
		if (Objects.isNull(totalAmount)) {
			throw new InvalidOrderException("totalAmount must not be null");
		}

		// Calculate
		Money calTotalAmount = new Money(BigInteger.ZERO);
		Money calTaxAmount = new Money(BigInteger.ZERO);
		Money calGrandTotalAmount = new Money(BigInteger.ZERO);

		calTotalAmount = calTotalAmount.add(subtotalAmount.add(serviceChargeAmount)
				.subtract(discountAmount));

		if (calTotalAmount.isLessThanZero()) {
			log.info("Order Price is not valid, subtotalAmount: {}, serviceChargeAmount: {}, discountAmount: {}",
					subtotalAmount, serviceChargeAmount, discountAmount);
			throw new InvalidOrderAmountException("Order price is not valid");
		}

		if (Boolean.TRUE.equals(order.getTaxInclude())) {
			calTaxAmount = calTaxAmount.add(calTotalAmount.subtract(calTotalAmount.divide(BigDecimal.valueOf(1)
					.add(taxRate))));
		} else {
			calTaxAmount = calTaxAmount.add(calTotalAmount.multiply(taxRate));
		}
		if (Boolean.FALSE.equals(calTaxAmount.compareWithAcceptedTolerance(taxAmount))) {
			log.info("Calculated tax amount: {} compare with tax amount: {}", calTaxAmount, taxAmount);
			throw new InvalidOrderAmountException(String.format("Invalid order tax amount expect %s but found %s",
					calTaxAmount.getAmount(), taxAmount.getAmount()));
		}

		if (Boolean.TRUE.equals(order.getTaxInclude())) {
			calGrandTotalAmount = calGrandTotalAmount.add(calTotalAmount);
		} else {
			calGrandTotalAmount = calGrandTotalAmount.add(calTotalAmount.add(calTaxAmount));
		}

		if (Boolean.FALSE.equals(calGrandTotalAmount.compareWithAcceptedTolerance(totalAmount))) {
			log.info("Calculated total amount: {} compare with total amount: {}", calGrandTotalAmount, totalAmount);
			throw new InvalidOrderAmountException(String.format("Invalid order total amount expect %s but found %s",
					calGrandTotalAmount.getAmount(), totalAmount.getAmount()));
		}
	}

	private void validateOrderLineItemAmount(Order order) {
		BigDecimal taxRate = Optional.ofNullable(order.getTaxRate())
				.orElse(BigDecimal.ZERO);
		Money calSubtotalAmount = new Money(BigInteger.ZERO);
		for (OrderLineItem lineItem : order.getLineItems()) {
			Long quantity = lineItem.getQuantity();
			Money unitPrice = lineItem.getUnitPrice();
			Money optionPrice = lineItem.getOptionPrice();
			Money totalDiscount = lineItem.getTotalDiscount();
			Money totalAmount = lineItem.getTotalAmount();
			if (Objects.isNull(unitPrice)) {
				throw new InvalidOrderLineItemAmountException("unitPrice must not be null");
			}
			if (Objects.isNull(optionPrice)) {
				throw new InvalidOrderLineItemAmountException("optionPrice must not be null");
			}
			if (Objects.isNull(totalDiscount)) {
				throw new InvalidOrderLineItemAmountException("totalDiscount must not be null");
			}
			if (Objects.isNull(totalAmount)) {
				throw new InvalidOrderLineItemAmountException("totalAmount must not be null");
			}

			Money calculatedTotalAmount = unitPrice.add(optionPrice)
					.multiply(BigDecimal.valueOf(quantity))
					.subtract(totalDiscount);
			if (Boolean.FALSE.equals(totalAmount.compareWithAcceptedTolerance(calculatedTotalAmount))) {
				throw new InvalidOrderLineItemAmountException(String.format(
						"Invalid order line item %s, expected %s but found %s", lineItem.getProductName(),
						calculatedTotalAmount.getAmount(), totalAmount.getAmount()));
			}

			// Subtotal Amount
			calSubtotalAmount = calSubtotalAmount.add(totalAmount);
		}
		if (Boolean.FALSE.equals(calSubtotalAmount.compareWithAcceptedTolerance(order.getSubtotalAmount()))) {
			log.info("Calculate subtotal amount: {} compare with subtotal amount: {}", calSubtotalAmount, order
					.getSubtotalAmount());
			throw new InvalidOrderAmountException(String.format("Invalid order subtotal amount, expected %s but %s",
					calSubtotalAmount.getAmount(), order.getSubtotalAmount()
							.getAmount()));
		}

	}

	@Override
	public OrderCalculatedEvent calculateOrder(Order order) {
		BigDecimal taxRate = Objects.requireNonNullElse(order.getTaxRate(), BigDecimal.ZERO);
		Money calTaxAmount = new Money(BigInteger.ZERO);
		Money calDiscountAmount = Objects.requireNonNullElse(order.getDiscountAmount(), new Money(BigInteger.ZERO));
		Money calSubTotalAmount = new Money(BigInteger.ZERO);
		Money calServiceChargeAmount = new Money(BigInteger.ZERO);
		Money calTotalAmount = new Money(BigInteger.ZERO);
		Money calGrandTotalAmount = new Money(BigInteger.ZERO);

		for (OrderLineItem lineItem : order.getLineItems()) {
			Long quantity = lineItem.getQuantity();
			Money unitPrice = lineItem.getUnitPrice();
			Money optionPrice = Objects.requireNonNullElse(lineItem.getOptionPrice(), new Money(BigInteger.ZERO));
			Money totalDiscount = Objects.requireNonNullElse(lineItem.getTotalDiscount(), new Money(BigInteger.ZERO));

			Money itemTotalAmount = unitPrice.add(optionPrice)
					.multiply(BigDecimal.valueOf(quantity))
					.subtract(totalDiscount);

			lineItem.setTotalDiscount(totalDiscount);
			lineItem.setTotalAmount(itemTotalAmount);

			calSubTotalAmount = calSubTotalAmount.add(itemTotalAmount);
		}

		// Service Charge
		for (OrderServiceCharge serviceCharge : Optional.ofNullable(order.getServiceCharges())
				.orElse(Collections.emptyList())) {
			Money chargeFixedAmount = serviceCharge.getChargeFixedAmount();
			BigDecimal chargeRate = serviceCharge.getChargeRate();
			Money calculatedAmount = chargeFixedAmount.add(calSubTotalAmount.multiply(chargeRate));
			serviceCharge.setAmount(calculatedAmount);
			calServiceChargeAmount = calServiceChargeAmount.add(calculatedAmount);
		}

		calTotalAmount = calTotalAmount.add(calSubTotalAmount.subtract(calDiscountAmount)
				.add(calServiceChargeAmount));

		// Tax include
		if (Objects.nonNull(order.getTaxInclude())) {
			if (Boolean.TRUE.equals(order.getTaxInclude())) {
				calTaxAmount = calTaxAmount.add(calTotalAmount.subtract(calTotalAmount.divide(BigDecimal.valueOf(1)
						.add(taxRate))));
				calGrandTotalAmount = calGrandTotalAmount.add(calTotalAmount);
			} else {
				calTaxAmount = calTaxAmount.add(calTotalAmount.multiply(taxRate));
				calGrandTotalAmount = calGrandTotalAmount.add(calTotalAmount.add(calTaxAmount));
			}

			order.setTaxAmount(calTaxAmount);
		} else {
			calGrandTotalAmount = calGrandTotalAmount.add(calTotalAmount.add(calTaxAmount));
		}

		order.setSubtotalAmount(calSubTotalAmount);
		order.setServiceChargeAmount(calServiceChargeAmount);
		order.setDiscountAmount(calDiscountAmount);
		order.setTotalAmount(calGrandTotalAmount);

		return new OrderCalculatedEvent(order);
	}
}
