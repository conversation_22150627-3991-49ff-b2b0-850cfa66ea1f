/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.shared.http.preorder.menu.request;

import com.styl.pacific.common.validator.timeformat.Time;
import com.styl.pacific.order.service.shared.http.preorder.menu.enums.PreOrderMenuStatus;
import com.styl.pacific.order.service.shared.http.preorder.menu.enums.PreOrderMenuType;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Builder;

/**
 * <AUTHOR>
 */
@Builder
public record UpdatePreOrderMenuRequest(
		@Size(max = 80, message = "name must not exceed 80 characters") @NotBlank(message = "name must not be empty or null") String name,
		@Size(max = 255, message = "description must not exceed 255 characters") String description,
		@NotNull(message = "cutOffTime must not be null") @Time String cutOffTime,
		@NotNull(message = "cutOffDays must not be null") @Min(value = 0, message = "cutOffDays must be greater than or equal to 0") @Max(value = 30, message = "cutOffDays must be less than or equal to 30") Integer cutOffDays,
		@Min(value = 1, message = "maxItemOrderPerDay must be greater than or equal to 1") Integer maxItemOrderPerDay,
		@NotNull(message = "types must not be null") PreOrderMenuType type,
		@NotNull(message = "statuses must not be null") PreOrderMenuStatus status) {
}
