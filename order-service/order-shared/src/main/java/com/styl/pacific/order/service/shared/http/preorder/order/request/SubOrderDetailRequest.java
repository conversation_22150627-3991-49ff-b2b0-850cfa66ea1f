/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.shared.http.preorder.order.request;

import com.styl.pacific.common.validator.rate.ValidRate;
import com.styl.pacific.order.service.shared.http.order.v2.request.place.OrderServiceChargeV2Request;
import com.styl.pacific.order.service.shared.http.preorder.menu.enums.PreOrderMenuType;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDate;
import java.util.List;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Builder
@Data
public class SubOrderDetailRequest {
	@NotNull(message = "storeId must not be null")
	private String storeId;
	@NotNull(message = "collectionDate must not be null")
	private LocalDate collectionDate;
	@NotNull(message = "mealTimeId must not be null")
	@Digits(integer = 21, fraction = 0, message = "mealTimeId must be a number")
	private String mealTimeId;
	@NotNull(message = "preOrderMenuType must not be null")
	private PreOrderMenuType preOrderMenuType;
	@Valid
	@NotNull(message = "lineItems must not be null")
	@Size(min = 1, message = "lineItems must have at least 1 item")
	private List<PreOrderLineItemRequest> lineItems;
	@Size(max = 255, message = "note must not exceed 255 characters")
	private String note;
	@Valid
	private List<OrderServiceChargeV2Request> serviceCharges;
	private String taxName;
	@ValidRate
	@Min(value = 0, message = "taxRate must be greater than or equal to 0")
	private BigDecimal taxRate;
	private Boolean taxInclude;
	@Min(value = 0, message = "taxAmount must be greater than or equal to 0")
	private BigInteger taxAmount;
	@Min(value = 0, message = "subtotalAmount must be greater than or equal to 0")
	@NotNull(message = "subtotalAmount must not be null")
	private BigInteger subtotalAmount;
	@Min(value = 0, message = "serviceChargeAmount must be greater than or equal to 0")
	@NotNull(message = "serviceChargeAmount must not be null")
	private BigInteger serviceChargeAmount;
	@Min(value = 0, message = "discountAmount must be greater than or equal to 0")
	@NotNull(message = "discountAmount must not be null")
	private BigInteger discountAmount;
	@Min(value = 0, message = "totalAmount must be greater than or equal to 0")
	@NotNull(message = "totalAmount must not be null")
	private BigInteger totalAmount;

}
