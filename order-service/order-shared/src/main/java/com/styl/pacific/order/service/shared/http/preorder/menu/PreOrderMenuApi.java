/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.shared.http.preorder.menu;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.permissions.PacificApiAuthorized;
import com.styl.pacific.domain.permissions.PacificApiPermissionKey;
import com.styl.pacific.domain.permissions.PlatformApiSecurityLevel;
import com.styl.pacific.order.service.shared.http.preorder.menu.request.CreatePreOrderMenuRequest;
import com.styl.pacific.order.service.shared.http.preorder.menu.request.UpdatePreOrderMenuRequest;
import com.styl.pacific.order.service.shared.http.preorder.menu.request.query.PreOrderMenuPagingQueryRequest;
import com.styl.pacific.order.service.shared.http.preorder.menu.response.PreOrderMenuResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Digits;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * <AUTHOR>
 */
public interface PreOrderMenuApi {
	@GetMapping("/api/order/pre-orders/menus/{id}")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	PreOrderMenuResponse findById(
			@PathVariable @Digits(integer = 21, fraction = 0, message = "id must be valid number") String id);

	@GetMapping("/api/order/pre-orders/menus")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	Paging<PreOrderMenuResponse> findAllPaging(
			@Valid @SpringQueryMap @ModelAttribute PreOrderMenuPagingQueryRequest query);

	@PostMapping("/api/order/pre-orders/menus")
	@ResponseStatus(HttpStatus.CREATED)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.PRE_ORDER_MENU_MGMT_ADD)
	PreOrderMenuResponse create(@Valid @RequestBody CreatePreOrderMenuRequest request);

	@PutMapping("/api/order/pre-orders/menus/{id}")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.PRE_ORDER_MENU_MGMT_UPDATE)
	PreOrderMenuResponse update(
			@PathVariable @Digits(integer = 21, fraction = 0, message = "id must be valid number") String id,
			@Valid @RequestBody UpdatePreOrderMenuRequest request);

	@DeleteMapping("/api/order/pre-orders/menus/{id}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.PRE_ORDER_MENU_MGMT_DELETE)
	void deleteById(@PathVariable @Digits(integer = 21, fraction = 0, message = "id must be valid number") String id);

	@PatchMapping("/api/order/pre-orders/menus/{id}/activate")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.PRE_ORDER_MENU_MGMT_UPDATE)
	void activeById(@PathVariable @Digits(integer = 21, fraction = 0, message = "id must be valid number") String id);

	@PatchMapping("/api/order/pre-orders/menus/{id}/deactivate")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.PRE_ORDER_MENU_MGMT_UPDATE)
	void deActiveById(@PathVariable @Digits(integer = 21, fraction = 0, message = "id must be valid number") String id);

}
