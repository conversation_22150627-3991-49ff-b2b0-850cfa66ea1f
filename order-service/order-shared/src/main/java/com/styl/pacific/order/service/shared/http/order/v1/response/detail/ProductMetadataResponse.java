/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.shared.http.order.v1.response.detail;

import java.math.BigInteger;
import java.util.List;
import lombok.Builder;

@Builder
public record ProductMetadataResponse(String name,
		HealthierChoiceMetadataResponse healthierChoice,
		CategoryMetadataResponse category,
		String sku,
		String barcode,
		String ingredients,
		BigInteger unitPrice,
		BigInteger listingPrice,
		List<ProductOptionMetadataResponse> options,
		List<ProductImageMetadataResponse> images) {
}
