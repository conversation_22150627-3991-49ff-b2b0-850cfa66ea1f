/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.shared.http.order.v1;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.permissions.PacificApiAuthorized;
import com.styl.pacific.domain.permissions.PacificApiPermissionKey;
import com.styl.pacific.domain.permissions.PlatformApiSecurityLevel;
import com.styl.pacific.order.service.shared.http.order.v1.request.GenerateOrderLabelsRequest;
import com.styl.pacific.order.service.shared.http.order.v1.request.OrderFilterRequest;
import com.styl.pacific.order.service.shared.http.order.v1.request.PaginationOrderQueryRequest;
import com.styl.pacific.order.service.shared.http.order.v1.request.cancel.CancelOrderRequest;
import com.styl.pacific.order.service.shared.http.order.v1.request.collect.CollectOrderRequest;
import com.styl.pacific.order.service.shared.http.order.v1.request.place.CreateOrderRequest;
import com.styl.pacific.order.service.shared.http.order.v1.response.PlaceOrderResponse;
import com.styl.pacific.order.service.shared.http.order.v1.response.detail.OrderDtoResponse;
import com.styl.pacific.order.service.shared.http.order.v1.response.detail.OrderLightDtoResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Digits;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;

public interface OrderApi {
	@PostMapping("/api/order/orders/place-order")
	@ResponseStatus(HttpStatus.CREATED)
	@PacificApiAuthorized
	PlaceOrderResponse create(@RequestBody @Valid CreateOrderRequest request);

	@GetMapping("/api/order/orders/{id}")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	OrderDtoResponse findById(
			@PathVariable @Digits(integer = 21, fraction = 0, message = "id must be valid number") String id);

	@GetMapping("/api/order/orders")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	Paging<OrderDtoResponse> findAllPaging(@Valid @SpringQueryMap @ModelAttribute PaginationOrderQueryRequest query);

	@GetMapping("/api/order/orders/light")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	Paging<OrderLightDtoResponse> findAllLightPaging(@SpringQueryMap @ModelAttribute PaginationOrderQueryRequest query);

	@PatchMapping("/api/order/orders/cancel")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.ORDER_MGMT_CANCEL, isAllowedCustomerAccess = true)
	void cancel(@RequestBody @Valid CancelOrderRequest request);

	@PatchMapping("/api/order/orders/{id}/collect")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	OrderDtoResponse collect(
			@PathVariable @Digits(integer = 21, fraction = 0, message = "id must be valid number") String id,
			@RequestBody @Valid CollectOrderRequest request);

	@PostMapping(path = "/api/order/orders/download-label", consumes = {
			MediaType.APPLICATION_JSON_VALUE }, produces = { MediaType.APPLICATION_PDF_VALUE })
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	byte[] downloadOrdersLabel(@Valid @SpringQueryMap @ModelAttribute OrderFilterRequest query,
			@Valid @RequestBody GenerateOrderLabelsRequest request);
}
