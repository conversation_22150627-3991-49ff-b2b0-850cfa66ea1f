/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.shared.http.servicecharge;

import com.styl.pacific.domain.dto.Content;
import com.styl.pacific.domain.permissions.PacificApiAuthorized;
import com.styl.pacific.domain.permissions.PacificApiPermissionKey;
import com.styl.pacific.order.service.shared.http.servicecharge.request.CreateServiceChargeRequest;
import com.styl.pacific.order.service.shared.http.servicecharge.request.ServiceChargeQueryRequest;
import com.styl.pacific.order.service.shared.http.servicecharge.request.UpdateServiceChargeRequest;
import com.styl.pacific.order.service.shared.http.servicecharge.response.ServiceChargeDetailDTOResponse;
import com.styl.pacific.order.service.shared.http.servicecharge.response.ServiceChargeResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.NotNull;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * <AUTHOR>
 */
public interface ServiceChargeApi {
	@GetMapping("/api/order/service-charges/details")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized(permissions = PacificApiPermissionKey.SERVICE_CHARGE_MGMT_VIEW)
	Content<ServiceChargeDetailDTOResponse> findDetailAll(
			@Valid @SpringQueryMap @ModelAttribute ServiceChargeQueryRequest request);

	@GetMapping("/api/order/service-charges")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized(permissions = PacificApiPermissionKey.SERVICE_CHARGE_MGMT_VIEW)
	Content<ServiceChargeResponse> findAll(@Valid @SpringQueryMap @ModelAttribute ServiceChargeQueryRequest request);

	@GetMapping("/api/order/service-charges/{id}")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized(permissions = PacificApiPermissionKey.SERVICE_CHARGE_MGMT_VIEW)
	ServiceChargeDetailDTOResponse findById(
			@NotNull(message = "id must not be null") @Digits(integer = 21, fraction = 0, message = "id must be valid number") @PathVariable String id);

	@PostMapping("/api/order/service-charges")
	@ResponseStatus(HttpStatus.CREATED)
	@PacificApiAuthorized(permissions = PacificApiPermissionKey.SERVICE_CHARGE_MGMT_ADD)
	ServiceChargeDetailDTOResponse create(
			@RequestBody @NotNull(message = "request must not be null") @Valid CreateServiceChargeRequest request);

	@PutMapping("/api/order/service-charges/{id}")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized(permissions = PacificApiPermissionKey.SERVICE_CHARGE_MGMT_UPDATE)
	ServiceChargeDetailDTOResponse update(
			@NotNull(message = "id must not be null") @Digits(integer = 21, fraction = 0, message = "id must be valid number") @PathVariable String id,
			@RequestBody @NotNull(message = "request must not be null") @Valid UpdateServiceChargeRequest request);

	@DeleteMapping("/api/order/service-charges/{id}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PacificApiAuthorized(permissions = PacificApiPermissionKey.SERVICE_CHARGE_MGMT_DELETE)
	void deleteById(
			@NotNull(message = "id must not be null") @Digits(integer = 21, fraction = 0, message = "id must be valid number") @PathVariable String id);
}
