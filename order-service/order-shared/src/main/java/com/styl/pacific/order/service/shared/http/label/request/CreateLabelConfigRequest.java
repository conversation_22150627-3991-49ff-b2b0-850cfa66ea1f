/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.shared.http.label.request;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;

/**
 * <AUTHOR>
 */
@Builder
public record CreateLabelConfigRequest(
		@Valid @NotNull(message = "Page setup property must not be null") PageSetupPropertiesRequest pageSetup,
		@Valid @NotNull(message = "Label layout property must not be null") LabelLayoutPropertiesRequest labelLayout,
		@Valid @NotNull(message = "Cell format property must not be null") CellFormatPropertiesRequest cellFormat) {
}
