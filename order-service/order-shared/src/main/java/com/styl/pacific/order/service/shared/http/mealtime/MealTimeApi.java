/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.shared.http.mealtime;

import com.styl.pacific.domain.dto.Content;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.permissions.PacificApiAuthorized;
import com.styl.pacific.domain.permissions.PacificApiPermissionKey;
import com.styl.pacific.domain.permissions.PlatformApiSecurityLevel;
import com.styl.pacific.order.service.shared.http.mealtime.request.CreateMealTimeRequest;
import com.styl.pacific.order.service.shared.http.mealtime.request.UpdateMealTimeRequest;
import com.styl.pacific.order.service.shared.http.mealtime.request.query.MealTimeQueryRequest;
import com.styl.pacific.order.service.shared.http.mealtime.request.query.PaginationMealTimeQueryRequest;
import com.styl.pacific.order.service.shared.http.mealtime.response.MealTimeResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Digits;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * <AUTHOR>
 */
public interface MealTimeApi {
	@GetMapping("/api/order/mealtimes/{id}")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	MealTimeResponse findById(
			@PathVariable @Digits(integer = 21, fraction = 0, message = "id must be a valid number") String id);

	@PostMapping("/api/order/mealtimes")
	@ResponseStatus(HttpStatus.CREATED)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.PRE_ORDER_MENU_MGMT_MEALTIME)
	MealTimeResponse create(@Valid @RequestBody CreateMealTimeRequest request);

	@PutMapping("/api/order/mealtimes/{id}")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.PRE_ORDER_MENU_MGMT_MEALTIME)
	MealTimeResponse update(
			@PathVariable @Digits(integer = 21, fraction = 0, message = "id must be a valid number") String id,
			@Valid @RequestBody UpdateMealTimeRequest request);

	@GetMapping("/api/order/mealtimes/page")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	Paging<MealTimeResponse> findAllPaging(@Valid @SpringQueryMap @ModelAttribute PaginationMealTimeQueryRequest query);

	@GetMapping("/api/order/mealtimes")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	Content<MealTimeResponse> findAll(@Valid @SpringQueryMap @ModelAttribute MealTimeQueryRequest query);

	@DeleteMapping("/api/order/mealtimes/{id}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PacificApiAuthorized(security = PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS, permissions = PacificApiPermissionKey.PRE_ORDER_MENU_MGMT_MEALTIME)
	void deleteById(@PathVariable @Digits(integer = 21, fraction = 0, message = "id must be a valid number") String id);
}
