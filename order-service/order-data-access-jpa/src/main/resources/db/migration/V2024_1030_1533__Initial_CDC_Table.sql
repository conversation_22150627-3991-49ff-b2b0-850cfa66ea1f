CREATE
    TABLE
        IF NOT EXISTS tb_category_view(
            id BIGINT NOT NULL,
            __dbz__physicaltableidentifier TEXT NOT NULL,
            tenant_id BIGINT NOT NULL,
            name TEXT NOT NULL,
            icon_path TEXT,
            description TEXT,
            parent_id BIGINT,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL,
            updated_at TIMESTAMP WITH TIME ZONE,
            deleted_at TIMESTAMP WITH TIME ZONE,
            PRIMARY KEY(
                id,
                __dbz__physicaltableidentifier
            )
        );

CREATE
    TABLE
        IF NOT EXISTS tb_healthier_choice_view(
            id BIGINT NOT NULL,
            __dbz__physicaltableidentifier TEXT NOT NULL,
            tenant_id BIGINT NOT NULL,
            name TEXT NOT NULL,
            symbol_path TEXT NOT NULL,
            description TEXT,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL,
            updated_at TIMESTAMP WITH TIME ZONE,
            deleted_at TIMESTAMP WITH TIME ZONE,
            PRIMARY KEY(
                id,
                __dbz__physicaltableidentifier
            )
        );

CREATE
    TABLE
        IF NOT EXISTS tb_product_view(
            id BIGINT NOT NULL,
            __dbz__physicaltableidentifier TEXT NOT NULL,
            tenant_id BIGINT NOT NULL,
            store_id BIGINT NOT NULL,
            category_id BIGINT NOT NULL,
            healthier_choice_id BIGINT,
            name TEXT NOT NULL,
            brief_information TEXT,
            description TEXT,
            sku TEXT NOT NULL,
            ingredients TEXT,
            barcode TEXT,
            status TEXT NOT NULL,
            preparation_time INTEGER NOT NULL,
            unit_price NUMERIC NOT NULL,
            listing_price NUMERIC,
            currency_code TEXT NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL,
            updated_at TIMESTAMP WITH TIME ZONE,
            deleted_at TIMESTAMP WITH TIME ZONE,
            PRIMARY KEY(
                id,
                __dbz__physicaltableidentifier
            )
        );

CREATE
    TABLE
        IF NOT EXISTS tb_product_image_view(
            id BIGINT NOT NULL,
            __dbz__physicaltableidentifier TEXT NOT NULL,
            product_id BIGINT NOT NULL,
            POSITION INTEGER NOT NULL,
            image_path TEXT NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL,
            updated_at TIMESTAMP WITH TIME ZONE,
            deleted_at TIMESTAMP WITH TIME ZONE,
            PRIMARY KEY(
                id,
                __dbz__physicaltableidentifier
            )
        );