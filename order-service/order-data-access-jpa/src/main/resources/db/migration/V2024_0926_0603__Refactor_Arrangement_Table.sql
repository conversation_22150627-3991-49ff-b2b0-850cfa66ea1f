CREATE
    TABLE
        IF NOT EXISTS tb_arrangement_temp(
            menu_item_id BIGINT NOT NULL,
            start_date DATE NOT NULL,
            end_date DATE NOT NULL,
            start_time TIME WITHOUT TIME ZONE NOT NULL,
            end_time TIME WITHOUT TIME ZONE NOT NULL,
            available_on JSONB NOT NULL,
            created_at TIMESTAMP(6) WITH TIME ZONE NOT NULL,
            updated_at TIMESTAMP(6) WITH TIME ZONE,
            deleted_at TIMESTAMP(6) WITH TIME ZONE,
            CONSTRAINT pk_tb_arrangement_temp PRIMARY KEY(menu_item_id),
            CONSTRAINT fk_tb_arrangement_temp_id_tb_menu_item FOREIGN KEY(menu_item_id) REFERENCES tb_menu_item(id)
        );

DROP
    TABLE
        IF EXISTS tb_arrangement CASCADE;

ALTER TABLE
    IF EXISTS tb_arrangement_temp RENAME TO tb_arrangement;

ALTER TABLE
    IF EXISTS tb_arrangement RENAME CONSTRAINT pk_tb_arrangement_temp TO pk_tb_arrangement;

ALTER TABLE
    IF EXISTS tb_arrangement RENAME CONSTRAINT fk_tb_arrangement_temp_id_tb_menu_item TO fk_tb_arrangement_id_tb_menu_item;
