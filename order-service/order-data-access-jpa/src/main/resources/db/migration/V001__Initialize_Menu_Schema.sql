/***********************************
 **  Process all materialized view for Catalog Schema
 ************************************/
CREATE
    TABLE
        IF NOT EXISTS tb_category(
            id BIGINT PRIMARY KEY NOT NULL,
            tenant_id BIGINT NOT NULL,
            name CHARACTER VARYING(50) NOT NULL,
            icon_path VARCHAR(256),
            description CHARACTER VARYING(256) NOT NULL,
            parent_id BIGINT,
            created_at TIMESTAMP(6) WITH TIME ZONE NOT NULL,
            updated_at TIMESTAMP(6) WITH TIME ZONE,
            deleted_at TIMESTAMP(6) WITH TIME ZONE,
            CONSTRAINT fk_tb_category_parent_id_tb_category FOREIGN KEY(parent_id) REFERENCES tb_category(id)
        );

ALTER TABLE
    tb_category ADD CONSTRAINT FK_TB_CATEGORY_ON_PARENT FOREIGN KEY(parent_id) REFERENCES tb_category(id);

CREATE
    TABLE
        IF NOT EXISTS tb_healthier_choice(
            id BIGINT PRIMARY KEY NOT NULL,
            tenant_id BIGINT NOT NULL,
            name CHARACTER VARYING(256) NOT NULL,
            symbol_path VARCHAR(2048) NOT NULL,
            description TEXT NOT NULL,
            created_at TIMESTAMP(6) WITH TIME ZONE NOT NULL,
            updated_at TIMESTAMP(6) WITH TIME ZONE,
            deleted_at TIMESTAMP(6) WITH TIME ZONE
        );

CREATE
    TABLE
        IF NOT EXISTS tb_nutrition(
            id BIGINT PRIMARY KEY NOT NULL,
            tenant_id BIGINT NOT NULL,
            name CHARACTER VARYING(256) NOT NULL,
            unit CHARACTER VARYING(256) NOT NULL,
            created_at TIMESTAMP(6) WITH TIME ZONE NOT NULL,
            updated_at TIMESTAMP(6) WITH TIME ZONE,
            deleted_at TIMESTAMP(6) WITH TIME ZONE
        );

CREATE
    TABLE
        IF NOT EXISTS tb_product(
            id BIGINT PRIMARY KEY NOT NULL,
            tenant_id BIGINT NOT NULL,
            store_id BIGINT NOT NULL,
            category_id BIGINT NOT NULL,
            healthier_choice_id BIGINT,
            name CHARACTER VARYING(100) NOT NULL,
            brief_information TEXT,
            description TEXT,
            sku VARCHAR(50) NOT NULL,
            ingredients TEXT,
            barcode VARCHAR(100),
            status VARCHAR(32) NOT NULL DEFAULT 'AVAILABLE',
            preparation_time INTEGER NOT NULL,
            unit_price NUMERIC(
                38,
                0
            ) NOT NULL,
            listing_price NUMERIC(
                38,
                0
            ),
            currency_code VARCHAR(3) NOT NULL,
            quantity BIGINT NOT NULL,
            created_at TIMESTAMP(6) WITH TIME ZONE NOT NULL,
            updated_at TIMESTAMP(6) WITH TIME ZONE,
            deleted_at TIMESTAMP(6) WITH TIME ZONE
        );

ALTER TABLE
    tb_product ADD CONSTRAINT FK_TB_PRODUCT_ON_CATEGORY FOREIGN KEY(category_id) REFERENCES tb_category(id);

ALTER TABLE
    tb_product ADD CONSTRAINT FK_TB_PRODUCT_ON_HEALTHIER_CHOICE FOREIGN KEY(healthier_choice_id) REFERENCES tb_healthier_choice(id);

CREATE
    TABLE
        IF NOT EXISTS tb_product_image(
            id BIGINT PRIMARY KEY NOT NULL,
            product_id BIGINT NOT NULL,
            POSITION INTEGER NOT NULL,
            image_path VARCHAR(2048) NOT NULL,
            created_at TIMESTAMP(6) WITH TIME ZONE NOT NULL,
            updated_at TIMESTAMP(6) WITH TIME ZONE,
            deleted_at TIMESTAMP(6) WITH TIME ZONE,
            CONSTRAINT fk_tb_product_image_product_id_tb_product FOREIGN KEY(product_id) REFERENCES tb_product(id)
        );

ALTER TABLE
    tb_product_image ADD CONSTRAINT FK_TB_PRODUCT_IMAGE_ON_PRODUCT FOREIGN KEY(product_id) REFERENCES tb_product(id);

CREATE
    TABLE
        IF NOT EXISTS tb_product_nutrition(
            created_at TIMESTAMP(6) WITH TIME ZONE NOT NULL,
            updated_at TIMESTAMP(6) WITH TIME ZONE,
            deleted_at TIMESTAMP(6) WITH TIME ZONE,
            value NUMERIC(
                38,
                2
            ) NOT NULL,
            product_id BIGINT NOT NULL,
            nutrition_id BIGINT NOT NULL,
            CONSTRAINT pk_tb_product_nutrition PRIMARY KEY(
                product_id,
                nutrition_id
            )
        );

ALTER TABLE
    tb_product_nutrition ADD CONSTRAINT FK_TB_PRODUCT_NUTRITION_ON_NUTRITION FOREIGN KEY(nutrition_id) REFERENCES tb_nutrition(id);

ALTER TABLE
    tb_product_nutrition ADD CONSTRAINT FK_TB_PRODUCT_NUTRITION_ON_PRODUCT FOREIGN KEY(product_id) REFERENCES tb_product(id);

/***********************************
 **  Menu Schema
 ************************************/
CREATE
    TABLE
        IF NOT EXISTS tb_menu(
            id BIGINT NOT NULL,
            created_at TIMESTAMP(6) WITH TIME ZONE NOT NULL,
            updated_at TIMESTAMP(6) WITH TIME ZONE,
            deleted_at TIMESTAMP(6) WITH TIME ZONE,
            tenant_id BIGINT NOT NULL,
            store_id BIGINT NOT NULL,
            name VARCHAR(255) NOT NULL,
            TYPE VARCHAR(255) NOT NULL,
            description VARCHAR(255) NOT NULL,
            CONSTRAINT pk_tb_menu PRIMARY KEY(id)
        );

CREATE
    TABLE
        IF NOT EXISTS tb_menu_item(
            id BIGINT NOT NULL,
            created_at TIMESTAMP(6) WITH TIME ZONE NOT NULL,
            updated_at TIMESTAMP(6) WITH TIME ZONE,
            deleted_at TIMESTAMP(6) WITH TIME ZONE,
            product_id BIGINT NOT NULL,
            POSITION INTEGER NOT NULL,
            menu_id BIGINT NOT NULL,
            CONSTRAINT pk_tb_menu_item PRIMARY KEY(id)
        );

ALTER TABLE
    tb_menu_item ADD CONSTRAINT FK_TB_MENU_ITEM_ON_MENU FOREIGN KEY(menu_id) REFERENCES tb_menu(id);

ALTER TABLE
    tb_menu_item ADD CONSTRAINT FK_TB_MENU_ITEM_ON_PRODUCT FOREIGN KEY(product_id) REFERENCES tb_product(id);

CREATE
    TABLE
        IF NOT EXISTS tb_arrangement(
            id BIGINT NOT NULL,
            created_at TIMESTAMP(6) WITH TIME ZONE NOT NULL,
            updated_at TIMESTAMP(6) WITH TIME ZONE,
            deleted_at TIMESTAMP(6) WITH TIME ZONE,
            menu_item_id BIGINT NOT NULL,
            start_time TIME WITHOUT TIME ZONE NOT NULL,
            end_time TIME WITHOUT TIME ZONE NOT NULL,
            TYPE VARCHAR(255) NOT NULL,
            date_of_week VARCHAR(255),
            DATE DATE,
            CONSTRAINT pk_tb_arrangement PRIMARY KEY(id)
        );

ALTER TABLE
    tb_arrangement ADD CONSTRAINT FK_TB_ARRANGEMENT_ON_MENU_ITEM FOREIGN KEY(menu_item_id) REFERENCES tb_menu_item(id);