/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.jpa.features.inventory.specification;

import com.styl.pacific.data.access.jpa.specification.BaseSpecification;
import com.styl.pacific.order.service.data.access.jpa.features.inventory.entity.InventoryEntity;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.lang.NonNull;

/**
 * <AUTHOR>
 */
public class InventorySpecifications {

	public static Specification<InventoryEntity> byTenantIdAndId(Long tenantId, Long id) {
		List<Specification<InventoryEntity>> specifications = new ArrayList<>();
		if (Objects.nonNull(tenantId)) {
			specifications.add(withTenantId(tenantId));
		}
		if (Objects.nonNull(id)) {
			specifications.add(withId(id));
		}
		specifications.add(withIsNotDeleted());
		return specifications.stream()
				.reduce(Specification::and)
				.orElse((root, query, criteriaBuilder) -> criteriaBuilder.conjunction());
	}

	public static Specification<InventoryEntity> byMultipleCriteria(Long tenantId, List<Long> ids, Long fromQuantity,
			Long toQuantity) {
		List<Specification<InventoryEntity>> specifications = new ArrayList<>();
		if (Objects.nonNull(tenantId)) {
			specifications.add(withTenantId(tenantId));
		}
		if (Objects.nonNull(ids) && !ids.isEmpty()) {
			specifications.add(withIds(ids));
		}
		if (Objects.nonNull(fromQuantity)) {
			specifications.add(fromQuantity(fromQuantity));
		}
		if (Objects.nonNull(toQuantity)) {
			specifications.add(toQuantity(toQuantity));
		}
		specifications.add(withIsNotDeleted());
		return specifications.stream()
				.reduce(Specification::and)
				.orElse((root, query, criteriaBuilder) -> criteriaBuilder.conjunction());
	}

	public static Specification<InventoryEntity> withTenantId(Long tenantId) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<InventoryEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return equals(criteriaBuilder, root.get(InventoryEntity.FIELD_TENANT_ID), tenantId);
			}
		};
	}

	public static Specification<InventoryEntity> withId(Long id) {
		return new BaseSpecification<InventoryEntity>() {
			@Override
			public Predicate toPredicate(@NonNull Root<InventoryEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return equals(criteriaBuilder, root.get(InventoryEntity.FIELD_PRODUCT_ID), id);
			}
		};
	}

	public static Specification<InventoryEntity> withIds(List<Long> ids) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<InventoryEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return in(root.get(InventoryEntity.FIELD_PRODUCT_ID), Arrays.asList(ids.toArray()));
			}
		};
	}

	public static Specification<InventoryEntity> fromQuantity(Long fromQuantity) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<InventoryEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return greaterThanOrEqualTo(criteriaBuilder, root.get(InventoryEntity.FIELD_QUANTITY), fromQuantity);
			}
		};
	}

	public static Specification<InventoryEntity> toQuantity(Long toQuantity) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<InventoryEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return lessThanOrEqualTo(criteriaBuilder, root.get(InventoryEntity.FIELD_QUANTITY), toQuantity);
			}
		};
	}

	public static Specification<InventoryEntity> withIsNotDeleted() {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<InventoryEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return root.get(InventoryEntity.FIELD_DELETED_AT)
						.isNull();
			}
		};
	}
}
