/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.jpa.features.mealtime.specification;

import com.styl.pacific.data.access.jpa.specification.BaseSpecification;
import com.styl.pacific.order.service.data.access.jpa.features.mealtime.entity.MealTimeEntity;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.lang.NonNull;

/**
 * <AUTHOR>
 */
public class MealTimeSpecifications {

	public static Specification<MealTimeEntity> byTenantIdAndId(Long tenantId, Long id) {
		List<Specification<MealTimeEntity>> specifications = new ArrayList<>();
		if (Objects.nonNull(tenantId)) {
			specifications.add(withTenantId(tenantId));
		}
		if (Objects.nonNull(id)) {
			specifications.add(withId(id));
		}
		specifications.add(withIdNotDeleted());
		return specifications.stream()
				.reduce(Specification::and)
				.orElse((root, query, criteriaBuilder) -> criteriaBuilder.conjunction());
	}

	public static Specification<MealTimeEntity> byMultipleCriteria(Long tenantId, List<Long> ids, String name) {
		List<Specification<MealTimeEntity>> specifications = new ArrayList<>();
		if (Objects.nonNull(tenantId)) {
			specifications.add(withTenantId(tenantId));
		}
		if (Objects.nonNull(ids) && !ids.isEmpty()) {
			specifications.add(withIds(ids));
		}
		if (StringUtils.isNotBlank(name)) {
			specifications.add(withLikeName(name));
		}
		specifications.add(withIdNotDeleted());
		return specifications.stream()
				.reduce(Specification::and)
				.orElse((root, query, criteriaBuilder) -> criteriaBuilder.conjunction());
	}

	public static Specification<MealTimeEntity> withTenantId(Long tenantId) {
		return new BaseSpecification<MealTimeEntity>() {
			@Override
			public Predicate toPredicate(@NonNull Root<MealTimeEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return equals(criteriaBuilder, root.get(MealTimeEntity.FIELD_TENANT_ID), tenantId);
			}
		};
	}

	public static Specification<MealTimeEntity> withId(Long id) {
		return new BaseSpecification<MealTimeEntity>() {
			@Override
			public Predicate toPredicate(@NonNull Root<MealTimeEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return equals(criteriaBuilder, root.get(MealTimeEntity.FIELD_ID), id);
			}
		};
	}

	public static Specification<MealTimeEntity> withIds(List<Long> ids) {
		return new BaseSpecification<MealTimeEntity>() {
			@Override
			public Predicate toPredicate(@NonNull Root<MealTimeEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return root.get(MealTimeEntity.FIELD_ID)
						.in(ids);
			}
		};
	}

	public static Specification<MealTimeEntity> withLikeName(String name) {
		return new BaseSpecification<MealTimeEntity>() {
			@Override
			public Predicate toPredicate(@NonNull Root<MealTimeEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return like(criteriaBuilder, root.get(MealTimeEntity.FIELD_NAME), name);
			}
		};
	}

	public static Specification<MealTimeEntity> withIdNotDeleted() {
		return new BaseSpecification<MealTimeEntity>() {
			@Override
			public Predicate toPredicate(@NonNull Root<MealTimeEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return root.get(MealTimeEntity.FIELD_DELETED_AT)
						.isNull();
			}
		};
	}
}
