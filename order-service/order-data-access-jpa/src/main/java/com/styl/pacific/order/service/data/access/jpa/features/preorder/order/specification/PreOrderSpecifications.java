/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.jpa.features.preorder.order.specification;

import com.styl.pacific.data.access.jpa.specification.BaseSpecification;
import com.styl.pacific.domain.enums.order.OrderCancellationType;
import com.styl.pacific.domain.enums.order.OrderPaymentStatus;
import com.styl.pacific.order.service.data.access.jpa.features.preorder.order.entity.PreOrderEntity;
import com.styl.pacific.order.service.domain.features.preorder.order.enums.PreOrderStatus;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.math.BigInteger;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.lang.NonNull;

/**
 * <AUTHOR>
 */
public class PreOrderSpecifications {

	public static Specification<PreOrderEntity> withTenantIdAndId(Long tenantId, Long id) {
		List<Specification<PreOrderEntity>> specifications = new ArrayList<>();
		if (Objects.nonNull(tenantId)) {
			specifications.add(withTenantId(tenantId));
		}
		if (Objects.nonNull(id)) {
			specifications.add(withId(id));
		}
		return specifications.stream()
				.reduce(Specification::and)
				.orElse((root, query, criteriaBuilder) -> criteriaBuilder.conjunction());
	}

	public static Specification<PreOrderEntity> withTenantIdAndIds(Long tenantId, List<Long> ids) {
		List<Specification<PreOrderEntity>> specifications = new ArrayList<>();
		if (Objects.nonNull(tenantId)) {
			specifications.add(withTenantId(tenantId));
		}
		if (Objects.nonNull(ids) && !ids.isEmpty()) {
			specifications.add(withIds(ids));
		}
		return specifications.stream()
				.reduce(Specification::and)
				.orElse((root, query, criteriaBuilder) -> criteriaBuilder.conjunction());
	}

	public static Specification<PreOrderEntity> withMultipleCriteria(String matchesId, List<Long> ids, Long tenantId,
			List<PreOrderStatus> statuses, List<OrderCancellationType> cancellationTypes, List<Long> paymentMethodIds,
			String customerName, Set<Long> customerIds, List<OrderPaymentStatus> paymentStatuses, Long storeId,
			BigInteger fromPrice, BigInteger toPrice, Instant fromTime, Instant toTime, Boolean isExpired) {
		List<Specification<PreOrderEntity>> specifications = new ArrayList<>();
		if (Objects.nonNull(matchesId)) {
			specifications.add(withMatchesId(matchesId));
		}
		if (Objects.nonNull(ids) && !ids.isEmpty()) {
			specifications.add(withIds(ids));
		}
		if (Objects.nonNull(tenantId)) {
			specifications.add(withTenantId(tenantId));
		}
		if (Objects.nonNull(statuses) && !statuses.isEmpty()) {
			specifications.add(withStatuses(statuses));
		}
		if (Objects.nonNull(cancellationTypes) && !cancellationTypes.isEmpty()) {
			specifications.add(withCancellationTypes(cancellationTypes));
		}
		if (Objects.nonNull(paymentMethodIds) && !paymentMethodIds.isEmpty()) {
			specifications.add(withPaymentMethodIds(paymentMethodIds));
		}
		if (Objects.nonNull(customerName)) {
			specifications.add(withCustomerName(customerName));
		}
		if (Objects.nonNull(customerIds) && !customerIds.isEmpty()) {
			specifications.add(withCustomerIds(customerIds));
		}

		if (Objects.nonNull(paymentStatuses) && !paymentStatuses.isEmpty()) {
			specifications.add(withPaymentStatuses(paymentStatuses));
		}
		if (Objects.nonNull(storeId)) {
			specifications.add(withStoreId(storeId));
		}
		if (Objects.nonNull(fromPrice)) {
			specifications.add(withFromPrice(fromPrice));
		}
		if (Objects.nonNull(toPrice)) {
			specifications.add(withToPrice(toPrice));
		}
		if (Objects.nonNull(fromTime)) {
			specifications.add(withFromTime(fromTime));
		}
		if (Objects.nonNull(toTime)) {
			specifications.add(withToTime(toTime));
		}
		if (Objects.nonNull(isExpired) && isExpired) {
			specifications.add(withExpired());
		}
		return specifications.stream()
				.reduce(Specification::and)
				.orElse((root, query, criteriaBuilder) -> criteriaBuilder.conjunction());
	}

	public static Specification<PreOrderEntity> withId(Long id) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<PreOrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return equals(criteriaBuilder, root.get(PreOrderEntity.FIELD_ID), id);
			}
		};
	}

	public static Specification<PreOrderEntity> withIds(List<Long> ids) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<PreOrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return in(root.get(PreOrderEntity.FIELD_ID), new ArrayList<>(ids));
			}
		};
	}

	public static Specification<PreOrderEntity> withTenantId(Long tenantId) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<PreOrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return equals(criteriaBuilder, root.get(PreOrderEntity.FIELD_TENANT_ID), tenantId);
			}
		};
	}

	public static Specification<PreOrderEntity> withStatuses(List<PreOrderStatus> statuses) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<PreOrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return in(root.get(PreOrderEntity.FIELD_STATUS), new ArrayList<>(statuses));
			}
		};
	}

	public static Specification<PreOrderEntity> withCancellationTypes(List<OrderCancellationType> cancellationTypes) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<PreOrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return in(root.get(PreOrderEntity.FIELD_CANCELLATION_TYPE), new ArrayList<>(cancellationTypes));
			}
		};
	}

	public static Specification<PreOrderEntity> withPaymentMethodIds(List<Long> paymentMethodIds) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<PreOrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return in(root.get(PreOrderEntity.FIELD_PAYMENT_METHOD_ID), new ArrayList<>(paymentMethodIds));
			}
		};
	}

	public static Specification<PreOrderEntity> withCustomerName(String customerName) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<PreOrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return like(criteriaBuilder, root.get(PreOrderEntity.FIELD_CUSTOMER_NAME), customerName);
			}
		};
	}

	public static Specification<PreOrderEntity> withCustomerIds(Set<Long> customerIds) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<PreOrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return and(criteriaBuilder, List.of(root.get(PreOrderEntity.FIELD_CUSTOMER_ID)
						.isNotNull(), in(root.get(PreOrderEntity.FIELD_CUSTOMER_ID), new ArrayList<>(customerIds))));
			}
		};
	}

	public static Specification<PreOrderEntity> withMatchesId(String matchesId) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<PreOrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return like(criteriaBuilder, criteriaBuilder.concat(root.get(PreOrderEntity.FIELD_ID)
						.as(String.class), ""), String.valueOf(matchesId));
			}
		};
	}

	public static Specification<PreOrderEntity> withPaymentStatuses(List<OrderPaymentStatus> paymentStatuses) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<PreOrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return in(root.get(PreOrderEntity.FIELD_PAYMENT_STATUS), new ArrayList<>(paymentStatuses));
			}
		};
	}

	public static Specification<PreOrderEntity> withStoreId(Long storeId) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<PreOrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return equals(criteriaBuilder, root.get(PreOrderEntity.FIELD_STORE_ID), storeId);
			}
		};
	}

	public static Specification<PreOrderEntity> withFromPrice(BigInteger fromPrice) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<PreOrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return greaterThanOrEqualTo(criteriaBuilder, root.get(PreOrderEntity.FIELD_TOTAL_AMOUNT), fromPrice);
			}
		};
	}

	public static Specification<PreOrderEntity> withToPrice(BigInteger toPrice) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<PreOrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return lessThanOrEqualTo(criteriaBuilder, root.get(PreOrderEntity.FIELD_TOTAL_AMOUNT), toPrice);
			}
		};
	}

	public static Specification<PreOrderEntity> withFromTime(Instant fromTime) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<PreOrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return greaterThanOrEqualTo(criteriaBuilder, root.get(PreOrderEntity.FIELD_CREATED_AT), fromTime);
			}
		};
	}

	public static Specification<PreOrderEntity> withToTime(Instant toTime) {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<PreOrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return lessThanOrEqualTo(criteriaBuilder, root.get(PreOrderEntity.FIELD_CREATED_AT), toTime);
			}
		};
	}

	public static Specification<PreOrderEntity> withExpired() {
		return new BaseSpecification<>() {
			@Override
			public Predicate toPredicate(@NonNull Root<PreOrderEntity> root, CriteriaQuery<?> query,
					@NonNull CriteriaBuilder criteriaBuilder) {
				return lessThanOrEqualTo(criteriaBuilder, root.get(PreOrderEntity.FIELD_EXPIRED_AT), Instant.now());
			}
		};
	}
}
