/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.jpa.features.healthierchoice.entity;

import com.styl.pacific.data.access.entity.AuditableEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "tb_healthier_choice_view")
public class HealthierChoiceEntity extends AuditableEntity {
	@Id
	private Long id;
	@Column(nullable = false)
	private Long tenantId;
	@Column(nullable = false)
	private String name;
	@Column(nullable = false)
	private String symbolPath;
	@Column(nullable = false)
	private String description;

	private HealthierChoiceEntity(Builder builder) {
		setDeletedAt(builder.deletedAt);
		setCreatedAt(builder.createdAt);
		setUpdatedAt(builder.updatedAt);
		setId(builder.id);
		setTenantId(builder.tenantId);
		setName(builder.name);
		setSymbolPath(builder.symbolPath);
		setDescription(builder.description);
	}

	public static Builder builder() {
		return new Builder();
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;

		if (o == null || getClass() != o.getClass())
			return false;

		HealthierChoiceEntity entity = (HealthierChoiceEntity) o;

		return new EqualsBuilder().appendSuper(super.equals(o))
				.append(id, entity.id)
				.append(tenantId, entity.tenantId)
				.append(name, entity.name)
				.append(symbolPath, entity.symbolPath)
				.append(description, entity.description)
				.isEquals();
	}

	@Override
	public int hashCode() {
		return new HashCodeBuilder(17, 37).appendSuper(super.hashCode())
				.append(id)
				.append(tenantId)
				.append(name)
				.append(symbolPath)
				.append(description)
				.toHashCode();
	}

	public static final class Builder {
		private Instant deletedAt;
		private Instant createdAt;
		private Instant updatedAt;
		private Long id;
		private Long tenantId;
		private String name;
		private String symbolPath;
		private String description;

		private Builder() {
		}

		public Builder deletedAt(Instant deletedAt) {
			this.deletedAt = deletedAt;
			return this;
		}

		public Builder createdAt(Instant createdAt) {
			this.createdAt = createdAt;
			return this;
		}

		public Builder updatedAt(Instant updatedAt) {
			this.updatedAt = updatedAt;
			return this;
		}

		public Builder id(Long id) {
			this.id = id;
			return this;
		}

		public Builder tenantId(Long tenantId) {
			this.tenantId = tenantId;
			return this;
		}

		public Builder name(String name) {
			this.name = name;
			return this;
		}

		public Builder symbolPath(String symbolPath) {
			this.symbolPath = symbolPath;
			return this;
		}

		public Builder description(String description) {
			this.description = description;
			return this;
		}

		public HealthierChoiceEntity build() {
			return new HealthierChoiceEntity(this);
		}
	}
}
