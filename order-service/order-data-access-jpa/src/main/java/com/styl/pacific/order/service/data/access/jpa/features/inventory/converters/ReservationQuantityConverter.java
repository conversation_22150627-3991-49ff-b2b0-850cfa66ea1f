/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.jpa.features.inventory.converters;

import com.fasterxml.jackson.core.type.TypeReference;
import com.styl.pacific.data.access.jpa.converter.BaseJsonToTextConverter;
import com.styl.pacific.order.service.data.access.jpa.features.inventory.entity.ReservationQuantityEntity;
import jakarta.persistence.Converter;
import java.util.List;

/**
 * <AUTHOR>
 */
@Converter
public class ReservationQuantityConverter extends BaseJsonToTextConverter<List<ReservationQuantityEntity>> {
	public ReservationQuantityConverter() {
		super(new TypeReference<>() {
		});
	}
}
