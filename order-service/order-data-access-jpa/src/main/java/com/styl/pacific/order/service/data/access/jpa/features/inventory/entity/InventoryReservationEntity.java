/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.order.service.data.access.jpa.features.inventory.entity;

import com.styl.pacific.order.service.data.access.jpa.features.inventory.converters.ReservationQuantityConverter;
import com.styl.pacific.order.service.domain.features.inventory.enums.ReservationStatus;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import java.time.Instant;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@Entity
@Table(name = "tb_inventory_reservation")
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
public class InventoryReservationEntity {

	public static final String FIELD_TENANT_ID = "tenantId";
	public static final String FIELD_ID = "id";

	@EmbeddedId
	private InventoryReservationId id;

	@Column(nullable = false)
	private Long tenantId;

	@Column(nullable = false)
	@Convert(converter = ReservationQuantityConverter.class)
	private List<ReservationQuantityEntity> reservations;

	@Column(nullable = false)
	@Enumerated(EnumType.STRING)
	private ReservationStatus status;

	@CreatedDate
	@Temporal(TemporalType.TIMESTAMP)
	@Column(nullable = false, updatable = false)
	protected Instant createdAt;

	@LastModifiedDate
	@Temporal(TemporalType.TIMESTAMP)
	protected Instant updatedAt;

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;

		if (!(o instanceof InventoryReservationEntity entity))
			return false;

		return new EqualsBuilder().append(id, entity.id)
				.append(tenantId, entity.tenantId)
				.append(reservations, entity.reservations)
				.append(status, entity.status)
				.append(createdAt, entity.createdAt)
				.append(updatedAt, entity.updatedAt)
				.isEquals();
	}

	@Override
	public int hashCode() {
		return new HashCodeBuilder(17, 37).append(id)
				.append(tenantId)
				.append(reservations)
				.append(status)
				.append(createdAt)
				.append(updatedAt)
				.toHashCode();
	}
}
