/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.rest.controller;

import com.styl.pacific.application.rest.context.RequestContext;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.notification.service.apis.TemplatesApi;
import com.styl.pacific.notification.service.constant.Action;
import com.styl.pacific.notification.service.constant.FileTemplate;
import com.styl.pacific.notification.service.domain.TemplateDomainService;
import com.styl.pacific.notification.service.domain.dto.FindTemplatesQuery;
import com.styl.pacific.notification.service.domain.dto.GetTemplateQuery;
import com.styl.pacific.notification.service.domain.dto.SaveTemplateCommand;
import com.styl.pacific.notification.service.domain.dto.TemplatesFilterQuery;
import com.styl.pacific.notification.service.domain.entity.Template;
import com.styl.pacific.notification.service.enums.NotificationChannel;
import com.styl.pacific.notification.service.requests.FindTemplatesRequest;
import com.styl.pacific.notification.service.requests.SaveTemplateRequest;
import com.styl.pacific.notification.service.response.ListTemplateResponse;
import com.styl.pacific.notification.service.response.MetaDataTemplateResponse;
import com.styl.pacific.notification.service.response.TemplateResponse;
import com.styl.pacific.notification.service.rest.mapper.TemplateControllerMapper;
import java.util.Arrays;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */

@RestController
@RequiredArgsConstructor
public class TemplateController implements TemplatesApi {

	private final TemplateDomainService templateDomainService;

	private final RequestContext requestContext;

	@Override
	public ListTemplateResponse findTemplates(FindTemplatesRequest request) {
		TemplatesFilterQuery filterQuery = TemplateControllerMapper.INSTANCE.toTemplateFilterQuery(request.getFilter(),
				requestContext.getTenantId());
		FindTemplatesQuery query = TemplateControllerMapper.INSTANCE.toFindTemplatesQuery(request, filterQuery);
		Paging<Template> templates = templateDomainService.findTemplates(query);
		return new ListTemplateResponse(templates.getContent()
				.stream()
				.map(TemplateControllerMapper.INSTANCE::toTemplateResponse)
				.toList(), templates.getTotalElements(), templates.getTotalPages(), templates.getPage(), templates
						.getSort());
	}

	@Override
	public TemplateResponse getTemplate(String action, String channel) {
		GetTemplateQuery query = GetTemplateQuery.builder()
				.tenantId(requestContext.getTenantId())
				.action(action)
				.channel(NotificationChannel.valueOf(channel))
				.build();
		Template template = templateDomainService.getTemplate(query);
		return TemplateControllerMapper.INSTANCE.toTemplateResponse(template);
	}

	@Override
	public void deleteTemplate(String action, String channel) {
		GetTemplateQuery query = GetTemplateQuery.builder()
				.tenantId(requestContext.getTenantId())
				.action(action)
				.channel(NotificationChannel.valueOf(channel))
				.build();
		templateDomainService.deleteTemplate(query);
	}

	@Override
	public TemplateResponse saveTemplate(SaveTemplateRequest request) {
		SaveTemplateCommand command = TemplateControllerMapper.INSTANCE.toSaveTemplateCommand(request, requestContext
				.getTenantId());
		Template template = templateDomainService.saveTemplate(command);
		return TemplateControllerMapper.INSTANCE.toTemplateResponse(template);
	}

	@Override
	public List<MetaDataTemplateResponse> getDefaultMetaDataTemplates() {
		return Arrays.stream(Action.TemplateScope.values())
				.map(action -> MetaDataTemplateResponse.builder()
						.action(action.name())
						.channels(FileTemplate.getChannels(action.name()))
						.build())
				.toList();
	}
}
