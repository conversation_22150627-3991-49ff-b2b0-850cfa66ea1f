/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.enums;

import com.styl.pacific.notification.service.constant.Action;
import lombok.Getter;

@Getter
public enum InAppNotificationTemplate {

	WELCOME_USER(Action.WELCOME_USER, "Welcome to TeraBite",
			"Your account has been successfully created. Begin setting up your profile to get the most from our platform."),

	INVITE_USER_TO_ANOTHER_TENANT(Action.INVITE_USER_TO_ANOTHER_TENANT, "You are invited to join a tenant",
			"You have been invited to join ${tenantName} tenant. Accept the invitation to access shared resources and collaborate with team members."),

	TENANT_SUBMIT_REVIEW(Action.TENANT_SUBMIT_REVIEW, "Tenant application awaiting review",
			"${name} has submitted their application for final approval."),

	IMPORT_SUCCESS(Action.IMPORT_SUCCESS, "Data imported successful",
			"Your import of ${type} data has completed successfully. ${successRecords} records were added/updated in the system."),

	IMPORT_FAIL(Action.IMPORT_FAIL, "Import completed with errors",
			"Your file ${fileName} was processed partially. ${successRecords} records were imported successfully, while ${failedRecords} records contained errors requiring attention."),

	WELCOME_TENANT(Action.WELCOME_TENANT, "Tenant activation completed - Next steps",
			"Your tenant has been successfully activated. Please review the operation guide in your administration portal to optimize your business experience."),

	ADD_SUB_ACCOUNT(Action.ADD_SUB_ACCOUNT, "Sub-account connection updated",
			"Your account has been successfully linked as a sub-account to ${sponsor}. Your sponsor now has access to your profile and may delegate their wallet to you."),

	ACCEPT_SUB_ACCOUNT_INVITATION(Action.ACCEPT_SUB_ACCOUNT_INVITATION, "Sub-account invitation accepted",
			"${subAccount} has accepted your invitation and is now linked to your account. You can manage their access and delegate wallet permissions from your wallet dashboard."),

	REJECT_SUB_ACCOUNT_INVITATION(Action.REJECT_SUB_ACCOUNT_INVITATION, "Sub-account invitation declined",
			"${subAccount} has declined your invitation to connect as a sub-account. You may send a new invitation or contact them directly for more information."),

	BALANCE_CHANGE(Action.BALANCE_CHANGE, "Wallet balance updated",
			"Your wallet balance has changed from ${currency}${oldBalance} to ${currency}${balance}. View the details in your Wallet's transaction section."),

	PAYMENT_SUCCESS(Action.PAYMENT_SUCCESS, "Payment confirmed",
			"Your payment of ${currency}${amount} for transaction ${transactionId} has been successfully processed. Receipt details are available in your account statement."),

	PAYMENT_REFUND(Action.PAYMENT_REFUND, "Payment refunded",
			"Your payment of ${currency}${amount} for transaction ${transactionId} has been successfully refunded."),

	UPDATE_ORDER_STATUS(Action.UPDATE_ORDER_STATUS, "Order status updated",
			"Your Order ${orderNumber} status has changed to ${status}."),

	DATA_SYNC_MONITOR(Action.DATA_SYNC_MONITOR, "Data sync failure alert",
			"The scheduled data sync job ${jobId} has completed with errors. Immediate investigation required in system logs.");

	private final String action;
	private final String title;
	private final String content;

	InAppNotificationTemplate(String action, String title, String content) {
		this.action = action;
		this.title = title;
		this.content = content;
	}

	public static InAppNotificationTemplate getInAppTemplate(String action) {
		return switch (action) {
		case Action.WELCOME_USER -> WELCOME_USER;
		case Action.INVITE_USER_TO_ANOTHER_TENANT -> INVITE_USER_TO_ANOTHER_TENANT;
		case Action.TENANT_SUBMIT_REVIEW -> TENANT_SUBMIT_REVIEW;
		case Action.IMPORT_SUCCESS -> IMPORT_SUCCESS;
		case Action.IMPORT_FAIL -> IMPORT_FAIL;
		case Action.WELCOME_TENANT -> WELCOME_TENANT;
		case Action.ADD_SUB_ACCOUNT -> ADD_SUB_ACCOUNT;
		case Action.ACCEPT_SUB_ACCOUNT_INVITATION -> ACCEPT_SUB_ACCOUNT_INVITATION;
		case Action.REJECT_SUB_ACCOUNT_INVITATION -> REJECT_SUB_ACCOUNT_INVITATION;
		case Action.BALANCE_CHANGE -> BALANCE_CHANGE;
		case Action.PAYMENT_SUCCESS -> PAYMENT_SUCCESS;
		case Action.PAYMENT_REFUND -> PAYMENT_REFUND;
		case Action.UPDATE_ORDER_STATUS -> UPDATE_ORDER_STATUS;
		case Action.DATA_SYNC_MONITOR -> DATA_SYNC_MONITOR;
		default -> null;
		};
	}
}