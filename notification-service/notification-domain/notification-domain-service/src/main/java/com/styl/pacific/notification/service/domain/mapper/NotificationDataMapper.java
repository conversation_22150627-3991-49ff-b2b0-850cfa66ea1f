/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.domain.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.domain.valueobject.InAppNotificationId;
import com.styl.pacific.domain.valueobject.InAppNotificationTrackingId;
import com.styl.pacific.notification.service.domain.dto.CreateEmailNotificationCommand;
import com.styl.pacific.notification.service.domain.dto.CreateInAppNotificationCommand;
import com.styl.pacific.notification.service.domain.dto.CreateNotificationCommand;
import com.styl.pacific.notification.service.domain.dto.HandleNotificationCommand;
import com.styl.pacific.notification.service.domain.dto.SaveInAppNotificationTrackingCommand;
import com.styl.pacific.notification.service.domain.entity.EmailNotification;
import com.styl.pacific.notification.service.domain.entity.InAppNotification;
import com.styl.pacific.notification.service.domain.entity.InAppNotificationTracking;
import com.styl.pacific.notification.service.domain.entity.Notification;
import com.styl.pacific.notification.service.domain.event.QueueNotificationCreatedEvent;
import com.styl.pacific.notification.service.enums.NotificationChannel;
import com.styl.pacific.notification.service.response.InAppNotificationResponse;
import java.time.Instant;
import java.util.UUID;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class })
public interface NotificationDataMapper {

	NotificationDataMapper INSTANCE = Mappers.getMapper(NotificationDataMapper.class);

	@Mapping(target = "id", source = "id")
	@Mapping(target = "createdAt", source = "createdAt")
	QueueNotificationCreatedEvent notificationHandleToNotificationCreatedEvent(HandleNotificationCommand command,
			NotificationChannel channel, UUID id, long createdAt);

	@Mapping(target = "createdAt", qualifiedByName = "longToInstant")
	@Mapping(target = "expiresAt", qualifiedByName = "longToInstant")
	Notification createNotificationCommandToNotification(CreateNotificationCommand command);

	@Mapping(target = "timeToLive", ignore = true)
	@Mapping(target = "updatedAt", ignore = true)
	@Mapping(target = "createdAt", ignore = true)
	@Mapping(target = "userId", source = "command.userId", qualifiedByName = "longToUserId")
	@Mapping(target = "tenantId", source = "command.tenantId", qualifiedByName = "longToTenantId")
	InAppNotification inAppNotificationToNewInAppNotification(CreateInAppNotificationCommand command,
			InAppNotificationId id, Boolean read);

	@Mapping(target = "id", source = "id")
	EmailNotification createEmailNotificationToEmailNotification(CreateEmailNotificationCommand command, UUID id);

	@Mapping(target = "id", qualifiedByName = "inAppNotificationIdToLong")
	@Mapping(target = "tenantId", qualifiedByName = "tenantIdToLong")
	@Mapping(target = "userId", qualifiedByName = "userIdToLong")
	@Mapping(target = "createdAt", qualifiedByName = "instantToLong")
	InAppNotificationResponse inAppNotificationToInAppNotificationResponse(InAppNotification inAppNotification);

	@Mapping(target = "updatedAt", source = "updatedAt")
	@Mapping(target = "read", source = "read")
	InAppNotification inAppNotificationToNewInAppNotification(InAppNotification notification, Boolean read,
			Instant updatedAt);

	@Mapping(target = "updatedAt", ignore = true)
	@Mapping(target = "userId", qualifiedByName = "longToUserId")
	@Mapping(target = "tenantId", qualifiedByName = "longToTenantId")
	@Mapping(target = "createdAt", ignore = true)
	InAppNotificationTracking saveInAppNotificationTrackingToInAppNotificationTracking(
			SaveInAppNotificationTrackingCommand command, InAppNotificationTrackingId id);

	@Mapping(target = "hasNewNotification", source = "hasNewNotification")
	InAppNotificationTracking saveInAppNotificationTrackingToInAppNotificationTracking(Boolean hasNewNotification,
			InAppNotificationTracking existing);
}
