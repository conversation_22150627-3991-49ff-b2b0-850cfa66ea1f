/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.domain.output.repository;

import com.styl.pacific.notification.service.domain.dto.FindInAppNotificationsQuery;
import com.styl.pacific.notification.service.domain.entity.InAppNotification;
import com.styl.pacific.notification.service.domain.entity.InAppNotificationTracking;
import java.util.Optional;
import software.amazon.awssdk.enhanced.dynamodb.model.Page;

/**
 * <AUTHOR>
 *
 */
public interface InAppNotificationRepository {

	void save(InAppNotification notification);

	Page<InAppNotification> findInAppNotifications(FindInAppNotificationsQuery query);

	void deleteInAppNotification(InAppNotification notification);

	Optional<InAppNotification> findInAppNotificationBy(Long tenantId, Long userId, Long id);

	void save(InAppNotificationTracking inAppNotificationTracking);

	Optional<InAppNotificationTracking> findInAppNotificationTrackingBy(Long userId);
}
