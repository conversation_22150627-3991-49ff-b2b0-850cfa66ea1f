/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.domain.hander.helper;

import com.styl.pacific.notification.service.constant.FileTemplate;
import com.styl.pacific.notification.service.constant.LoaderType;
import com.styl.pacific.notification.service.domain.entity.Template;
import com.styl.pacific.notification.service.domain.exception.NotificationDomainException;
import com.styl.pacific.notification.service.domain.output.dto.LoaderMetaData;
import com.styl.pacific.notification.service.enums.NotificationChannel;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Component
@AllArgsConstructor
public class TemplateHelper {

	private final LoaderHelper loaderHelper;

	private final FilePathHelper filePathHelper;

	public Template buildDefaultTemplate(String action, NotificationChannel channel) {
		return Template.builder()
				.action(action)
				.channel(channel)
				.body(getLayoutTemplateDefault(action, channel, FileTemplate.getContentFileName(action)))
				.subject(getLayoutTemplateDefault(action, channel, FileTemplate.getSubjectFileName(action)))
				.build();
	}

	public String getLayoutTemplateDefault(String action, NotificationChannel channel, String fileName) {
		try {
			InputStream inputStream = loaderHelper.loadInputStream(LoaderType.FILE, LoaderMetaData.builder()
					.path(filePathHelper.getPath(channel))
					.extension(FileTemplate.getFileExtension(action))
					.fileName(fileName)
					.build());
			return new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
		} catch (IOException e) {
			throw new NotificationDomainException("Load default template fail: " + e.getMessage());
		}
	}
}
