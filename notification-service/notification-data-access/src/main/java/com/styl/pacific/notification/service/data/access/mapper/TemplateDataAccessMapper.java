/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.data.access.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.notification.service.data.access.entity.TemplateEntity;
import com.styl.pacific.notification.service.data.access.specification.TemplateEntitySpecification;
import com.styl.pacific.notification.service.domain.dto.TemplatesFilterQuery;
import com.styl.pacific.notification.service.domain.entity.Template;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class, MapstructCommonDomainMapper.class })
public interface TemplateDataAccessMapper {

	TemplateDataAccessMapper INSTANCE = Mappers.getMapper(TemplateDataAccessMapper.class);

	@Mapping(target = "tenantId", qualifiedByName = "longToTenantId")
	@Mapping(target = "id", qualifiedByName = "longToTemplateId")
	Template templateEntityToTemplate(TemplateEntity entity);

	@Mapping(target = "deletedAt", ignore = true)
	@Mapping(target = "tenantId", qualifiedByName = "tenantIdToLong")
	@Mapping(target = "id", qualifiedByName = "templateIdToLong")
	TemplateEntity templateToTemplateEntity(Template template);

	TemplateEntitySpecification toTemplateEntitySpecification(TemplatesFilterQuery filterQuery);
}
