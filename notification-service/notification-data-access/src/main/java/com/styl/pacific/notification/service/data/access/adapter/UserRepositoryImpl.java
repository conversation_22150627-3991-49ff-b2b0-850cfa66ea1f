/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.notification.service.data.access.adapter;

import com.styl.pacific.notification.service.data.access.mapper.CustomerDataMapper;
import com.styl.pacific.notification.service.domain.entity.dto.CustomerDto;
import com.styl.pacific.notification.service.domain.output.repository.UserRepository;
import com.styl.pacific.user.shared.http.users.request.FetchRuleRequest;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Component
public class UserRepositoryImpl implements UserRepository {

	private final UserClient userClient;

	@Override
	public CustomerDto getCustomerById(Long id) {
		return CustomerDataMapper.INSTANCE.toCustomerDto(userClient.getUserProfile(id, FetchRuleRequest.builder()
				.build()));
	}
}
