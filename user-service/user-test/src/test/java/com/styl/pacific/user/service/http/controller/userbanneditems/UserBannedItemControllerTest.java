/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.http.controller.userbanneditems;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.aws.s3.config.PresignerConfiguration;
import com.styl.pacific.aws.s3.config.S3ConfigProperties;
import com.styl.pacific.aws.s3.mapper.PresignerContextProvider;
import com.styl.pacific.common.test.utils.GenerateHttpHeader;
import com.styl.pacific.common.test.utils.HeaderGenerator;
import com.styl.pacific.domain.enums.UserType;
import com.styl.pacific.domain.tokenclaims.UserTokenClaim;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserBannedItemId;
import com.styl.pacific.user.api.userbanneditems.UserBannedItemController;
import com.styl.pacific.user.service.config.MvcTestConfiguration;
import com.styl.pacific.user.service.config.UserInitializationIntegrationSupporter;
import com.styl.pacific.user.service.core.features.subaccounts.UserSubAccountQueryService;
import com.styl.pacific.user.service.core.features.userbanneditems.UserBannedItemService;
import com.styl.pacific.user.service.core.features.userbanneditems.entities.UserBannedItem;
import com.styl.pacific.user.service.core.features.userbanneditems.request.SaveUserBannedItemCommand;
import com.styl.pacific.user.service.core.features.users.UserCommandService;
import com.styl.pacific.user.service.core.features.users.entities.User;
import com.styl.pacific.user.service.data.access.clients.tenants.TenantClient;
import com.styl.pacific.user.service.data.access.clients.userroles.UserRoleClient;
import com.styl.pacific.user.shared.http.userbanneditems.request.BannedItemRequest;
import com.styl.pacific.user.shared.http.userbanneditems.request.SaveUserBannedItemRequest;
import com.styl.pacific.user.shared.http.userbanneditems.response.UserBannedItemResponse;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Set;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;

@DirtiesContext
@WebMvcTest(UserBannedItemController.class)
@Import(value = { UserBannedItemController.class, PresignerConfiguration.class, S3ConfigProperties.class,
		PresignerContextProvider.class })
@ContextConfiguration(classes = { MvcTestConfiguration.class })
class UserBannedItemControllerTest {

	@MockitoBean
	private UserRoleClient userRoleClient;

	@MockitoBean
	private TenantClient tenantClient;

	@Autowired
	private ObjectMapper objectMapper;

	@MockitoBean
	private UserCommandService userCommandService;

	private UserInitializationIntegrationSupporter userSupporter;

	private static User user;

	@Autowired
	private MockMvc mockMvc;

	@MockitoBean
	private UserBannedItemService userBannedItemService;

	@MockitoBean
	private UserSubAccountQueryService userSubAccountQueryService;

	@Test
	void testSaveUserBannedItemWhenValidRequestThenReturnSuccessfully() throws Exception {
		// Arrange
		final var now = Instant.now();
		final var userId = 1L;
		final var userTokenClaim = UserTokenClaim.builder()
				.acr("acr")
				.aud("aud")
				.azp("azp")
				.email("<EMAIL>")
				.userId("1")
				.permissions(Set.of(UserTokenClaim.TenantRolePermission.builder()
						.tenantId("1")
						.userRoleId("1")
						.build()))
				.userType(UserType.CUSTOMER)
				.name("Name")
				.exp(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iat(now.plus(1, ChronoUnit.MINUTES)
						.toEpochMilli())
				.iss("iss")
				.jti("jti")
				.sub("sub")
				.typ("typ")
				.scope("scope")
				.emailVerified(true)
				.familyName("Name")
				.givenName("Name")
				.build();

		Instant createdAt = Instant.now();

		final var request = new SaveUserBannedItemRequest(List.of(new BannedItemRequest(null, "name1")));

		final var userBannedItemResponse = UserBannedItemResponse.builder()
				.id("1")
				.tenantId("1")
				.productName("name1")
				.createdAt(createdAt)
				.build();

		final var userBannedItems = UserBannedItem.builder()
				.productName("name1")
				.id(new UserBannedItemId(1L))
				.createdAt(createdAt)
				.tenantId(new TenantId(1L))
				.build();
		when(userBannedItemService.saveUserBannedItems(any(SaveUserBannedItemCommand.class))).thenReturn(List.of(
				userBannedItems));
		// Act & Assert
		mockMvc.perform(post(String.format("/api/user/users/%s/banned-items", userId)).headers(HeaderGenerator
				.generateHttpHeaders(GenerateHttpHeader.builder()
						.tenantId(1L)
						.objectMapper(objectMapper)
						.userTokenClaim(userTokenClaim)
						.build()))
				.content(objectMapper.writeValueAsString(request))
				.contentType(MediaType.APPLICATION_JSON))
				.andExpect(status().is2xxSuccessful())
				.andExpect(content().json(objectMapper.writeValueAsString(List.of(userBannedItemResponse))));
		verify(userBannedItemService, times(1)).saveUserBannedItems(any());
	}

}
