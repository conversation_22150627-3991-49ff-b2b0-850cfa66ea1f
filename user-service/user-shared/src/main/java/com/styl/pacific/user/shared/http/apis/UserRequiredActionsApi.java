/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.shared.http.apis;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.permissions.PacificApiAuthorized;
import com.styl.pacific.user.shared.http.requiredactions.request.QueryUserRequiredActionPaginationRequest;
import com.styl.pacific.user.shared.http.requiredactions.request.ReplyUserRequiredActionRequest;
import com.styl.pacific.user.shared.http.requiredactions.response.UserRequiredActionResponse;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;

public interface UserRequiredActionsApi {

	@GetMapping(path = "/api/user/users/{userId}/required-actions")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	Paging<UserRequiredActionResponse> queryUserRequiredActions(@PathVariable("userId") Long userId,
			@ModelAttribute @Valid QueryUserRequiredActionPaginationRequest request);

	@GetMapping(path = "/api/user/users/{userId}/required-actions/{userRequiredActionId}")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	UserRequiredActionResponse getUserRequiredAction(@PathVariable("userId") Long userId,
			@PathVariable("userRequiredActionId") Long userRequiredActionId);

	@PutMapping(path = "/api/user/users/{userId}/required-actions/{userRequiredActionId}/reply")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	void replyUserRequiredAction(@PathVariable("userId") Long userId,
			@PathVariable("userRequiredActionId") Long userRequiredActionId,
			@RequestBody @Valid ReplyUserRequiredActionRequest request);

}
