/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.shared.http.users.request;

import com.styl.pacific.common.validator.phonenumber.PhoneNumber;
import com.styl.pacific.domain.enums.UserType;
import com.styl.pacific.domain.range.LongDateTimeRange;
import com.styl.pacific.user.shared.enums.UserStatus;
import jakarta.validation.constraints.Email;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryUserRequest {
	@Length(max = 150)
	private String bySsoId;

	private Long byUserId;
	private Long byTenantId;
	private Set<Long> byRoleIds;
	private Set<Long> byUserIds;
	private String byRealmId;
	private Set<UserType> byUserTypes;
	private Set<UserStatus> byUserStatuses;
	private Set<String> byMigrationIds;
	private String byFamilyCode;
	private Integer groupLevel;

	private LongDateTimeRange byCreatedRange;

	@Length(max = 50)
	private String byUserCardId;
	@Length(max = 50)
	private String byContainingUserCardId;

	@Length(max = 36)
	private String byExternalId;
	@Length(max = 36)
	private String byContainingExternalId;

	private String byUserGroupPath;

	@PhoneNumber
	@Length(max = 20)
	private String byPhoneNumber;

	@Email
	@Length(max = 50)
	private String byEmail;
	@Length(max = 50)
	private String byContainingEmail;

	@Length(max = 120)
	private String byName;

	private Boolean doesExcludeBackOfficeUser;

	private Boolean isAlternativeUser;
}
