/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.shared.http.preferences.response;

import com.styl.pacific.user.shared.enums.UserPreferenceGroup;
import com.styl.pacific.user.shared.enums.UserPreferenceKey;
import java.time.Instant;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.With;

@Getter
@Builder
@With
@RequiredArgsConstructor
public class UserPreferenceResponse {
	private final String id;
	private final String userId;
	private final String tenantId;
	private final UserPreferenceKey key;
	private final UserPreferenceGroup group;
	private final UserPreferenceDataResponse data;
	private final boolean isDefault;

	private final Instant createdAt;
	private final Instant updatedAt;
}
