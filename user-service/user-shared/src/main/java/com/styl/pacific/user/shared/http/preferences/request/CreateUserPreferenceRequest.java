/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.shared.http.preferences.request;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.styl.pacific.user.shared.constants.UserPreferenceKeyConstants;
import com.styl.pacific.user.shared.enums.UserPreferenceKey;
import lombok.Getter;

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = CreateUserPreferenceRequest.TYPE_FIELD_NAME, visible = true)
@JsonSubTypes({
		@JsonSubTypes.Type(value = CreateAccountDateTimeFormatRequest.class, name = UserPreferenceKeyConstants.ACCOUNT_DATE_TIME_FORMAT),
		@JsonSubTypes.Type(value = CreateSecurityMfaRequest.class, name = UserPreferenceKeyConstants.SECURITY_MFA),
		@JsonSubTypes.Type(value = CreateNotificationPreferenceRequest.class, name = UserPreferenceKeyConstants.NOTIFICATION_PAYMENT_TRANSACTIONS_SUCCEEDED),
		@JsonSubTypes.Type(value = CreateNotificationPreferenceRequest.class, name = UserPreferenceKeyConstants.NOTIFICATION_WALLET_BALANCE_CHANGED),
		@JsonSubTypes.Type(value = CreateNotificationPreferenceRequest.class, name = UserPreferenceKeyConstants.NOTIFICATION_ORDER_STATUS_UPDATED), })
@Getter
public abstract class CreateUserPreferenceRequest {

	public static final String TYPE_FIELD_NAME = "key";

	private final UserPreferenceKey key;

	protected CreateUserPreferenceRequest(UserPreferenceKey key) {
		this.key = key;
	}
}
