/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.shared.http.users.response;

import com.styl.pacific.domain.dto.FileResponse;
import com.styl.pacific.domain.enums.UserType;
import com.styl.pacific.user.shared.enums.UserNonCompletedAction;
import com.styl.pacific.user.shared.enums.UserStatus;
import com.styl.pacific.user.shared.http.groups.response.UserGroupResponse;
import java.time.Instant;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@AllArgsConstructor
public class UserLiteResponse {
	private final String id;
	private final String ssoId;
	private final String externalId;
	private final String firstName;
	private final String lastName;
	private final String fullName;
	private final String email;
	private final FileResponse avatar;
	private final String avatarHash;
	private final UserType userType;
	private final UserStatus userStatus;
	private final String realmId;
	private final String phoneNumber;
	private final Boolean isTrustedSourceAccess;
	private final String trustedSource;
	private final String migrationId;
	private final String familyCode;
	private final UserGroupResponse userGroup;
	private final List<UserNonCompletedAction> userNonCompletedActions;
	private final Instant schedulingDeletedAt;
	private final Boolean isAlternativeUser;

	private final Instant createdAt;
	private final Instant updatedAt;
}
