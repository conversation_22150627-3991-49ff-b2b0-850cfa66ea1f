/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.shared.http.preferences.request;

import com.styl.pacific.domain.dto.pagination.PaginationQuery;
import java.util.Collection;
import lombok.Builder;
import org.springdoc.core.annotations.ParameterObject;

@ParameterObject
public class QueryUserPreferencePaginationRequest extends PaginationQuery<FilterUserPreferenceRequest> {
	@Builder
	public QueryUserPreferencePaginationRequest(FilterUserPreferenceRequest filter, Integer size, Integer page,
			String sortDirection, Collection<String> sortFields) {
		super(filter, size, page, sortDirection, sortFields);
	}

	public QueryUserPreferencePaginationRequest withFilter(FilterUserPreferenceRequest newFilter) {
		return new QueryUserPreferencePaginationRequest(newFilter, size, page, sortDirection, sortFields);
	}
}
