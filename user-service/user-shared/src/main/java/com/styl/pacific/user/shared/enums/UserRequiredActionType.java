/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.shared.enums;

import lombok.Getter;

@Getter
public enum UserRequiredActionType {
	ADDED_NEW_TENANT(false),
	INVITED_USER_SUB_ACCOUNT(true);

	private final boolean isAllowDismiss;

	UserRequiredActionType(boolean isAllowDismiss) {
		this.isAllowDismiss = isAllowDismiss;
	}
}
