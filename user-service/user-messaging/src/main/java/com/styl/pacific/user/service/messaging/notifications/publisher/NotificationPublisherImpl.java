/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.messaging.notifications.publisher;

import com.styl.pacific.domain.enums.UserType;
import com.styl.pacific.kafka.notification.avro.model.NotificationAvroChannel;
import com.styl.pacific.kafka.notification.avro.model.NotificationCreatedAvroEvent;
import com.styl.pacific.notification.service.constant.Action;
import com.styl.pacific.notification.service.constant.EmailNotificationKey;
import com.styl.pacific.user.service.core.features.notifications.NotificationPublisher;
import com.styl.pacific.user.service.core.features.notifications.entities.JoinedUserWelcomeNotification;
import com.styl.pacific.user.service.core.features.notifications.entities.NewUserWelcomeNotification;
import com.styl.pacific.user.service.core.features.notifications.entities.UserInvitationNotification;
import com.styl.pacific.user.service.core.features.subaccounts.entities.UserSubAccount;
import com.styl.pacific.user.service.core.features.tenants.entities.Tenant;
import com.styl.pacific.user.service.core.features.tenants.entities.TenantSetting;
import com.styl.pacific.user.service.messaging.notifications.publisher.kafka.NotificationCreatedEventKafkaPublisher;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class NotificationPublisherImpl implements NotificationPublisher {

	@Value("${pacific.keycloak.default-back-office-redirect-url:}")
	private String defaultBackOfficeRedirectUrl;

	@Value("${pacific.platform.brand-name:}")
	private String brandName;

	private final NotificationCreatedEventKafkaPublisher kafkaPublisher;

	@Override
	public void publish(UserInvitationNotification notification) {
		final var user = notification.getUser();

		final var systemName = Optional.ofNullable(notification.getTenant())
				.map(Tenant::getName)
				.orElse(brandName);

		kafkaPublisher.publish(NotificationCreatedAvroEvent.newBuilder()
				.setId(UUID.randomUUID())
				.setUserId(user.getId()
						.getValue())
				.setAction(Action.INVITE_USER)
				.setTenantId(notification.getTenantId()
						.getValue())
				.setSource("user-service")
				.setChannels(List.of(NotificationAvroChannel.EMAIL))
				.setData(Map.of("systemName", systemName, "userName", Optional.ofNullable(user.getFirstName())
						.filter(StringUtils::isNotBlank)
						.orElseGet(user::getEmail), "invitationLink", notification.getKeycloakInvitationLink(),
						EmailNotificationKey.TO, user.getEmail()))
				.setCreatedAt(Instant.now()
						.toEpochMilli())
				.setExpiresAt(notification.getExpiredAt()
						.toEpochMilli())
				.build());
	}

	@Override
	public void publish(NewUserWelcomeNotification notification) {
		final var user = notification.getUser();
		final var tenant = notification.getTenant();
		final var systemName = Optional.of(tenant)
				.map(Tenant::getName)
				.orElse(brandName);

		final var loginURL = UserType.CUSTOMER.equals(user.getUserType())
				? Optional.of(tenant)
						.map(Tenant::getSettings)
						.map(TenantSetting::getDefaultDomain)
						.orElse("")
				: defaultBackOfficeRedirectUrl;

		kafkaPublisher.publish(NotificationCreatedAvroEvent.newBuilder()
				.setId(UUID.randomUUID())
				.setUserId(user.getId()
						.getValue())
				.setAction(Action.WELCOME_USER)
				.setTenantId(notification.getTenantId()
						.getValue())
				.setSource("user-service")
				.setChannels(List.of(NotificationAvroChannel.EMAIL, NotificationAvroChannel.IN_APP))
				.setData(Map.of("systemName", systemName, "userName", Optional.ofNullable(user.getFirstName())
						.filter(StringUtils::isNotBlank)
						.orElseGet(user::getEmail), "loginUrl", loginURL, EmailNotificationKey.TO, user.getEmail()))
				.setCreatedAt(Instant.now()
						.toEpochMilli())
				.build());
	}

	@Override
	public void publish(JoinedUserWelcomeNotification notification) {
		final var user = notification.getUser();
		final var tenant = notification.getTenant();

		kafkaPublisher.publish(NotificationCreatedAvroEvent.newBuilder()
				.setId(UUID.randomUUID())
				.setUserId(user.getId()
						.getValue())
				.setAction(Action.INVITE_USER_TO_ANOTHER_TENANT)
				.setTenantId(notification.getTenantId()
						.getValue())
				.setSource("user-service")
				.setChannels(List.of(NotificationAvroChannel.IN_APP))
				.setData(Map.of("userName", Optional.ofNullable(user.getFirstName())
						.filter(StringUtils::isNotBlank)
						.orElseGet(user::getEmail), "tenantId", String.valueOf(tenant.getId()
								.getValue()), "tenantName", Optional.ofNullable(tenant.getName())
										.filter(StringUtils::isNotBlank)
										.orElse(""), EmailNotificationKey.TO, user.getEmail()))
				.setCreatedAt(Instant.now()
						.toEpochMilli())
				.build());
	}

	@Override
	public void publish(UserSubAccount userSubAccount) {
		NotificationCreatedAvroEvent notificationCreatedAvroEvent = NotificationCreatedAvroEvent.newBuilder()
				.setId(UUID.randomUUID())
				.setUserId(userSubAccount.getSubUser()
						.getId()
						.getValue())
				.setTenantId(userSubAccount.getTenantId()
						.getValue())
				.setAction(Action.ADD_SUB_ACCOUNT)
				.setSource("user-service")
				.setChannels(List.of(NotificationAvroChannel.IN_APP))
				.setData(Map.of("sponsor", userSubAccount.getSponsorUser()
						.getFullName()))
				.setCreatedAt(Instant.now()
						.toEpochMilli())
				.build();
		kafkaPublisher.publish(notificationCreatedAvroEvent);
	}

	@Override
	public void publish(UserSubAccount userSubAccount, String action) {
		NotificationCreatedAvroEvent notificationCreatedAvroEvent = NotificationCreatedAvroEvent.newBuilder()
				.setId(UUID.randomUUID())
				.setUserId(userSubAccount.getSponsorUser()
						.getId()
						.getValue())
				.setTenantId(userSubAccount.getTenantId()
						.getValue())
				.setAction(action)
				.setSource("user-service")
				.setChannels(List.of(NotificationAvroChannel.IN_APP))
				.setData(Map.of("subAccount", userSubAccount.getSubUser()
						.getFullName()))
				.setCreatedAt(Instant.now()
						.toEpochMilli())
				.build();
		kafkaPublisher.publish(notificationCreatedAvroEvent);
	}
}
