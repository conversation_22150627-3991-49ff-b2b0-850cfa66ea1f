---
server:
  port: 9202
logging:
  level:
    com.styl.pacific: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.orm.jdbc.bind: DEBUG
    org.hibernate.stat: DEBUG
    org.hibernate.SQL_SLOW: DEBUG
    org.springframework.retry: DEBUG
spring:
  jpa:
    hibernate.ddl-auto: none
    open-in-view: false
    show-sql: false
  datasource:
    url: *******************************************************************
    username: postgres
    password: postgres
    driver-class-name: org.postgresql.Driver
  flyway:
    enabled: true
    baselineOnMigrate: true
    locations: classpath:db/migration
    outOfOrder: true
  sql:
    init:
      platform: postgres


kafka-config:
  bootstrap-servers: "localhost:11091"
#  schema-registry-url-key: schema.registry.url
  schema-registry-url: http://localhost:11081

pacific:
  users:
    invitations:
      expiry-duration: PT10M
  clients:
    tenant-service:
      url: http://localhost:9201
    catalog-service:
      url: http://localhost:9203
    authz-service:
      url: http://localhost:9209
