/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.alternativeaccess;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.user.service.core.features.alternativeaccess.entities.AlternativeUserAccess;
import com.styl.pacific.user.service.core.features.alternativeaccess.request.AlternativeUserAccessPaginationQuery;
import com.styl.pacific.user.service.core.features.alternativeaccess.request.FilterAlternativeUserAccess;
import java.util.List;
import java.util.Optional;

public interface AlternativeUserAccessRepository {
	AlternativeUserAccess save(AlternativeUserAccess alternativeUserAccess);

	Optional<AlternativeUserAccess> findAlternativeUserAccessByTenantIdAndUserIdAndAlternativeUserId(TenantId tenantId,
			UserId userId, UserId alternativeUserId);

	void deleteAlternativeUserAccess(AlternativeUserAccess alternativeUserAccess);

	void deleteAlternativeUserAccess(FilterAlternativeUserAccess filter);

	Paging<AlternativeUserAccess> queryAlternativeUserAccess(AlternativeUserAccessPaginationQuery pagingQuery);

	Optional<AlternativeUserAccess> findSingleAlternativeUserAccess(FilterAlternativeUserAccess query);

	List<AlternativeUserAccess> findAlternativeUserAccesses(FilterAlternativeUserAccess filter);
}
