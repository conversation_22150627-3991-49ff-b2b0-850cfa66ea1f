/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.customers.service;

import com.styl.pacific.domain.enums.UserType;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserGroupId;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.user.service.core.features.customers.CustomerCommandService;
import com.styl.pacific.user.service.core.features.customers.request.AssignCustomersToGroupCommand;
import com.styl.pacific.user.service.core.features.customers.request.UnAssignCustomersFromGroupCommand;
import com.styl.pacific.user.service.core.features.customers.valueobject.CustomerSelfRegistrationLink;
import com.styl.pacific.user.service.core.features.groups.UserGroupQueryService;
import com.styl.pacific.user.service.core.features.groups.request.GetUserGroupQuery;
import com.styl.pacific.user.service.core.features.invitations.UserInvitationCommandService;
import com.styl.pacific.user.service.core.features.invitations.entities.UserInvitationContentCode;
import com.styl.pacific.user.service.core.features.invitations.request.SendCustomerSelfRegistrationInvitationCommand;
import com.styl.pacific.user.service.core.features.keycloak.generator.KeyCloakLinkGenerator;
import com.styl.pacific.user.service.core.features.tenants.TenantQueryService;
import com.styl.pacific.user.service.core.features.users.UserRepository;
import com.styl.pacific.user.service.core.features.users.request.FilterUserQuery;
import com.styl.pacific.user.service.core.features.users.valueobject.UserFetchRule;
import com.styl.pacific.user.shared.exceptions.UserGroupException;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.IntStream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class CustomerCommandServiceImpl implements CustomerCommandService {

	private final UserInvitationCommandService userInvitationCommandService;
	private final TenantQueryService tenantQueryService;
	private final KeyCloakLinkGenerator keyCloakLinkGenerator;
	private final UserGroupQueryService userGroupQueryService;
	private final UserRepository userRepository;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public CustomerSelfRegistrationLink generateSelfRegistrationAccess(TenantId tenantId, String redirectUrl) {
		// TODO: Vinh Validate Tenant Business Feature Policy

		final var tenant = tenantQueryService.getTenantById(tenantId);

		final var userInvitation = userInvitationCommandService.createInvitation(
				SendCustomerSelfRegistrationInvitationCommand.builder()
						.tenantId(tenantId)
						.realmId(tenant.getRealmId())
						.build());

		return CustomerSelfRegistrationLink.builder()
				.link(keyCloakLinkGenerator.generateSelfRegistrationLink(tenant.getId(), tenant.getRealmId(),
						((UserInvitationContentCode) userInvitation.getContent()).getCode(), redirectUrl))
				.expiredAt(userInvitation.getExpiredAt())
				.build();
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void assignCustomersToGroup(TenantId tenantId, AssignCustomersToGroupCommand command) {
		final var userGroup = userGroupQueryService.getUserGroup(GetUserGroupQuery.builder()
				.tenantId(tenantId)
				.userGroupId(command.getUserGroupId())
				.build());

		final var chunkSize = 100;
		final var userIds = command.getUserIds()
				.stream()
				.toList();

		IntStream.range(0, command.getUserIds()
				.size())
				.filter(i -> i % chunkSize == 0)
				.mapToObj(i -> userIds.subList(i, Math.min(i + chunkSize, userIds.size())))
				.forEach(userIdsChunk -> userRepository.saveAll(userRepository.getUsersByCondition(FilterUserQuery
						.builder()
						.byUserTypes(Set.of(UserType.CUSTOMER))
						.byUserIds(new HashSet<>(userIdsChunk))
						.build(), UserFetchRule.builder()
								.isFetchPermissions(true)
								.isFetchUserGroup(true)
								.build())
						.stream()
						.map(user -> user.withUserGroup(userGroup))
						.toList()));
	}

	@Override
	public void unAssignCustomersFromGroup(UnAssignCustomersFromGroupCommand command) {
		validateCustomersSameGroup(command.getUserGroupId(), command.getUserIds());

		final var chunkSize = 100;
		final var userIds = command.getUserIds()
				.stream()
				.toList();

		IntStream.range(0, command.getUserIds()
				.size())
				.filter(i -> i % chunkSize == 0)
				.mapToObj(i -> userIds.subList(i, Math.min(i + chunkSize, userIds.size())))
				.forEach(userIdsChunk -> userRepository.saveAll(userRepository.getUsersByCondition(FilterUserQuery
						.builder()
						.byUserTypes(Set.of(UserType.CUSTOMER))
						.byUserIds(new HashSet<>(userIdsChunk))
						.build(), UserFetchRule.builder()
								.isFetchPermissions(true)
								.isFetchUserGroup(true)
								.build())
						.stream()
						.map(user -> user.withUserGroup(null))
						.toList()));
	}

	private void validateCustomersSameGroup(UserGroupId userGroupId, Set<UserId> userIds) {
		final var count = userRepository.countUserByCondition(FilterUserQuery.builder()
				.byUserIds(userIds)
				.byUserTypes(Set.of(UserType.CUSTOMER))
				.byUserGroupId(userGroupId)
				.build());

		if (count != userIds.size()) {
			throw new UserGroupException("Cannot unAssign all users from group. All users are required the same group");
		}

	}
}
