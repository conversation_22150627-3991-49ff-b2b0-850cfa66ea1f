/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.groups.service;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserGroupId;
import com.styl.pacific.user.service.core.features.groups.UserGroupQueryService;
import com.styl.pacific.user.service.core.features.groups.UserGroupRepository;
import com.styl.pacific.user.service.core.features.groups.entities.UserGroup;
import com.styl.pacific.user.service.core.features.groups.request.GetAllUserGroupFilter;
import com.styl.pacific.user.service.core.features.groups.request.GetUserGroupQuery;
import com.styl.pacific.user.service.core.features.groups.request.UserGroupPaginationQuery;
import com.styl.pacific.user.service.core.features.groups.utils.UserGroupTreeBuilder;
import com.styl.pacific.user.shared.exceptions.UserGroupException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class UserGroupQueryServiceImpl implements UserGroupQueryService {
	private final UserGroupRepository userGroupRepository;

	@Override
	public UserGroup getUserGroup(GetUserGroupQuery query) {
		return userGroupRepository.findByTenantIdAndId(query.tenantId(), query.userGroupId())
				.orElseThrow(() -> new UserGroupException("User Group not found"));
	}

	@Override
	public Paging<UserGroup> queryUserGroup(UserGroupPaginationQuery query) {
		return userGroupRepository.queryUserGroups(query.withFilter(Optional.ofNullable(query.getFilter())
				.orElseGet(() -> GetAllUserGroupFilter.builder()
						.build())));
	}

	@Override
	public List<UserGroup> getUserGroupTreeByTenantId(@Valid @NotNull TenantId tenantId) {
		final var userGroups = userGroupRepository.findAllUserGroupsByTenantId(tenantId);
		return UserGroupTreeBuilder.buildTree(userGroups, Comparator.comparing(UserGroup::getGroupName));
	}

	@Override
	public Optional<UserGroup> getUserGroupByUserGroupId(UserGroupId userGroupId) {
		return userGroupRepository.findById(userGroupId);
	}
}
