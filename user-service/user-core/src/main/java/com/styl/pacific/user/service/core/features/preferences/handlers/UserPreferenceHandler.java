/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.preferences.handlers;

import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.user.service.core.features.preferences.entities.UserPreference;
import com.styl.pacific.user.service.core.features.preferences.request.CreateUserPreferenceCommand;
import com.styl.pacific.user.service.core.features.preferences.request.UpdateUserPreferenceCommand;
import com.styl.pacific.user.shared.enums.UserPreferenceKey;

public interface UserPreferenceHandler {

	boolean isSupported(UserPreferenceKey key);

	UserPreference createUserPreference(CreateUserPreferenceCommand command);

	UserPreference updateUserPreference(UpdateUserPreferenceCommand command);

	UserPreference getDefaultPreference(TenantId tenantId, UserId userId, UserPreferenceKey key);
}
