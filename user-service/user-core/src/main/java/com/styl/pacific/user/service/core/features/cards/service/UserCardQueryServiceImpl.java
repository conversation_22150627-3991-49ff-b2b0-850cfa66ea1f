/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.cards.service;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserCardId;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.user.service.core.features.cards.UserCardQueryService;
import com.styl.pacific.user.service.core.features.cards.UserCardRepository;
import com.styl.pacific.user.service.core.features.cards.entities.UserCard;
import com.styl.pacific.user.service.core.features.cards.request.FilterUserCardPaginationQuery;
import com.styl.pacific.user.service.core.features.cards.request.UserCardPaginationQuery;
import com.styl.pacific.user.shared.exceptions.UserCardNotFoundException;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class UserCardQueryServiceImpl implements UserCardQueryService {
	private final UserCardRepository repository;

	@Override
	public UserCard getUserCardByUserIdAndTenantIdAndCardId(UserId userId, TenantId tenantId, String cardId) {
		return repository.getUserCardByUserIdAndTenantIdAndCardId(userId, tenantId, cardId)
				.orElseThrow(UserCardNotFoundException::new);
	}

	@Override
	public Paging<UserCard> queryUserCards(UserCardPaginationQuery query) {
		return repository.queryUserCards(query.withFilter(Optional.ofNullable(query.getFilter())
				.orElseGet(() -> FilterUserCardPaginationQuery.builder()
						.build())));
	}

	@Override
	public UserCard getUserCardByUserIdAndId(UserId userId, UserCardId userCardId) {
		return repository.getUserCardByUserIdAndId(userId, userCardId)
				.orElseThrow(UserCardNotFoundException::new);
	}

	@Override
	@Transactional(readOnly = true)
	public UserCard getUserCardByTenantIdAndCardId(TenantId tenantId, String cardId) {
		return repository.getUserCardByTenantIdAndId(tenantId, cardId)
				.orElseThrow(UserCardNotFoundException::new);
	}
}
