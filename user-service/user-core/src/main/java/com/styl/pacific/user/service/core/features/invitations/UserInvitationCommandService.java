/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.invitations;

import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.domain.valueobject.UserInvitationId;
import com.styl.pacific.domain.valueobject.UserSubAccountId;
import com.styl.pacific.user.service.core.features.invitations.entities.UserInvitation;
import com.styl.pacific.user.service.core.features.invitations.request.SendUserInvitationCommand;
import com.styl.pacific.user.shared.enums.UserInvitationType;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.Set;

public interface UserInvitationCommandService {
	UserInvitation createInvitation(@Valid SendUserInvitationCommand command);

	void deleteInvitationById(@NotNull UserInvitationId id);

	void deleteInvitationByUserIdAndType(@NotNull UserId id, @NotNull UserInvitationType userInvitationType);

	void deleteInvitationByUserIds(Set<UserId> userIds);

	void deleteInvitationByTenantIdAndUserId(UserId userId, TenantId tenantId);

	void deleteInvitationByContentSubAccountId(UserSubAccountId byContentSubAccountId);
}
