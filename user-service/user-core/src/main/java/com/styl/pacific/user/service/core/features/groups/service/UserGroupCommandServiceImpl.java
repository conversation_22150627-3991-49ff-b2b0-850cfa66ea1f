/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.core.features.groups.service;

import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserGroupId;
import com.styl.pacific.user.service.core.features.groups.UserGroupCommandService;
import com.styl.pacific.user.service.core.features.groups.UserGroupRepository;
import com.styl.pacific.user.service.core.features.groups.constants.UserGroupConstants;
import com.styl.pacific.user.service.core.features.groups.entities.UserGroup;
import com.styl.pacific.user.service.core.features.groups.generator.UserGroupIdGenerator;
import com.styl.pacific.user.service.core.features.groups.mapper.UserGroupMapper;
import com.styl.pacific.user.service.core.features.groups.request.UpsertUserGroupCommand;
import com.styl.pacific.user.service.core.features.groups.utils.UserGroupPathSupporter;
import com.styl.pacific.user.service.core.features.users.UserRepository;
import com.styl.pacific.user.shared.exceptions.UserGroupException;
import com.styl.pacific.user.shared.exceptions.UserGroupReachingLimitException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserGroupCommandServiceImpl implements UserGroupCommandService {

	private static final int MAX_TREE_LEVEL = 5;
	private final UserGroupRepository userGroupRepository;
	private final UserGroupIdGenerator idGenerator;
	private final UserRepository userRepository;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public UserGroup upsertUserGroup(UpsertUserGroupCommand command) {
		final var parent = userGroupRepository.findByTenantIdAndId(command.tenantId(), command.parentId())
				.orElse(null);
		if (command.parentId() != null) {
			if (parent == null) {
				throw new UserGroupException("Parent not found");
			}
			if (UserGroupPathSupporter.getLevel(parent.getPath()) == MAX_TREE_LEVEL) {
				throw new UserGroupReachingLimitException("Reached to the maximum tree level");
			}
			if (!parent.getTenantId()
					.equals(command.tenantId())) {
				throw new UserGroupException("Wrong parent's tenantId");
			}
			if (command.userGroupId() != null && parent.getPath()
					.contains(MapstructCommonDomainMapper.INSTANCE.userGroupIdToLong(command.userGroupId())
							.toString())) {
				throw new UserGroupException("Cycling relationship error");
			}
		}

		return userGroupRepository.save(UserGroupMapper.INSTANCE.toEntity(command, parent, idGenerator));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deleteUserGroupById(TenantId tenantId, UserGroupId userGroupId) {
		final var userGroup = userGroupRepository.findByTenantIdAndId(tenantId, userGroupId)
				.orElseThrow(() -> new UserGroupException("User group not found"));

		if (UserGroupConstants.MAIN_USER_GROUP_TEMPLATE.getGroupKey()
				.equals(userGroup.getGroupKey())) {
			throw new UserGroupException("Cannot delete default user groups");
		}

		if (userRepository.countUserByUserGroupId(userGroup.getId()) > 0) {
			throw new UserGroupException("Cannot delete user group due to linking to current users");
		}

		if (userGroupRepository.countUserGroupByParentId(userGroup.getId()) > 0) {
			throw new UserGroupException("User group is parent of other group");
		}
		userGroupRepository.deleteByUserGroupId(userGroupId);
	}

	@Override
	@Retryable(retryFor = DataIntegrityViolationException.class, backoff = @Backoff(delay = 250), maxAttempts = 8)
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public UserGroup loadDefaultUserGroup(TenantId tenantId) {
		return userGroupRepository.findByGroupKey(tenantId, UserGroupConstants.MAIN_USER_GROUP_TEMPLATE.getGroupKey())
				.orElseGet(() -> userGroupRepository.save(UserGroupMapper.INSTANCE.toEntityFromTemplate(
						UserGroupConstants.MAIN_USER_GROUP_TEMPLATE, tenantId, idGenerator)));
	}
}
