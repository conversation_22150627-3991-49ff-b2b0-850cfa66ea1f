CREATE
    TABLE
        IF NOT EXISTS user_cards(
            id BIGSERIAL NOT NULL CONSTRAINT user_cards_pk PRIMARY KEY,
            card_id VARCHAR(50) NOT NULL,
            tenant_id BIGINT NOT NULL,
            user_id BIGINT NOT NULL,
            card_alias VARCHAR(80) NULL,
            card_status VARCHAR(20) NOT NULL,
            created_at TIMESTAMP(6) DEFAULT NOW(),
            updated_at TIMESTAMP(6) DEFAULT NOW(),
            CONSTRAINT user_cards_user_id_fk FOREIGN KEY(user_id) REFERENCES users(id)
        );

CREATE
    UNIQUE INDEX IF NOT EXISTS user_cards_card_id_tenant_id_uidx ON
    user_cards(
        tenant_id,
        card_id
    );

CREATE
    INDEX IF NOT EXISTS user_cards_user_id_card_id_idx ON
    user_cards(
        user_id,
        card_id
    );