/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.data.access.relational.invitations.repository;

import com.styl.pacific.data.access.jpa.querydsl.BaseQuerydslRepository;
import com.styl.pacific.user.service.data.access.relational.invitations.entities.UserInvitationEntity;
import com.styl.pacific.user.shared.enums.UserInvitationType;
import java.time.Instant;
import java.util.Optional;
import java.util.Set;

public interface JpaUserInvitationRepository extends BaseQuerydslRepository<UserInvitationEntity, Long>,
		AdvanceQueryDslUserInvitationRepository {

	Optional<UserInvitationEntity> findTopByTypeAndUserEntityIdAndExpiredAtGreaterThanEqual(
			UserInvitationType userInvitationType, Long userId, Instant expiredTime);

	void deleteUserTenantInvitationByUserEntityIdIn(Set<Long> userIds);

	void deleteUserTenantInvitationByUserEntityIdAndType(Long userId, UserInvitationType type);

	void deleteUserTenantInvitationByTenantIdAndUserEntityId(Long tenantId, Long userId);
}
