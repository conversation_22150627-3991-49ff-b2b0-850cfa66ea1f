/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.data.access.relational.subaccounts.repository;

import com.styl.pacific.data.access.jpa.querydsl.WhereBuilder;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.BaseId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.domain.valueobject.UserSubAccountId;
import com.styl.pacific.user.service.core.features.subaccounts.UserSubAccountRepository;
import com.styl.pacific.user.service.core.features.subaccounts.entities.UserSubAccount;
import com.styl.pacific.user.service.core.features.subaccounts.request.UpsertUserSubAccountCommand;
import com.styl.pacific.user.service.core.features.subaccounts.request.UserSubAccountPaginationQuery;
import com.styl.pacific.user.service.data.access.relational.subaccounts.entities.QUserSubAccountEntity;
import com.styl.pacific.user.service.data.access.relational.subaccounts.entities.UserSubAccountEntity;
import com.styl.pacific.user.service.data.access.relational.subaccounts.mapper.UserSubAccountEntityMapper;
import com.styl.pacific.user.service.data.access.relational.users.entities.QUserEntity;
import com.styl.pacific.user.service.data.access.relational.users.entities.UserEntity;
import com.styl.pacific.user.service.data.access.relational.users.repository.JpaUserRepository;
import com.styl.pacific.user.shared.exceptions.UserNotFoundException;
import com.styl.pacific.user.shared.exceptions.UserSubAccountException;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Component
@RequiredArgsConstructor
public class UserSubAccountRepositoryImpl implements UserSubAccountRepository {

	private final JpaUserSubAccountRepository jpaUserSubAccountRepository;
	private final JpaUserRepository jpaUserRepository;

	@Override
	public boolean existsSubAccountRelationship(TenantId tenantId, UserId sponsorUserId, UserId subUserId) {
		return jpaUserSubAccountRepository.exists(QUserSubAccountEntity.userSubAccountEntity.tenantId.eq(tenantId
				.getValue())
				.and(QUserSubAccountEntity.userSubAccountEntity.sponsorUserEntity.id.eq(sponsorUserId.getValue())
						.and(QUserSubAccountEntity.userSubAccountEntity.subUserEntity.id.eq(subUserId.getValue()))));
	}

	@Override
	public boolean existsSubAccountInOtherSponsor(TenantId tenantId, UserId subUserId) {
		return jpaUserSubAccountRepository.exists(QUserSubAccountEntity.userSubAccountEntity.tenantId.eq(tenantId
				.getValue())
				.and(QUserSubAccountEntity.userSubAccountEntity.subUserEntity.id.eq(subUserId.getValue())));
	}

	@Override
	public boolean existsSponsorInOtherSubAccount(TenantId tenantId, UserId subUserId) {
		return jpaUserSubAccountRepository.exists(QUserSubAccountEntity.userSubAccountEntity.tenantId.eq(tenantId
				.getValue())
				.and(QUserSubAccountEntity.userSubAccountEntity.sponsorUserEntity.id.eq(subUserId.getValue())));
	}

	@Override
	public UserSubAccount save(UpsertUserSubAccountCommand command) {
		return UserSubAccountEntityMapper.INSTANCE.toModel(Optional.ofNullable(command.getId())
				.map(BaseId::getValue)
				.map(id -> {
					final var existingSubAccount = jpaUserSubAccountRepository.findById(id)
							.orElseThrow(() -> new UserSubAccountException("Sub account item has not found"));
					existingSubAccount.setSubAccountStatus(command.getSubAccountStatus());
					return jpaUserSubAccountRepository.save(existingSubAccount);
				})
				.orElseGet(() -> {
					final var userEntitiesMap = jpaUserRepository.findAllWithBase(QUserEntity.userEntity.id.in(List.of(
							command.getSponsorUser()
									.getId()
									.getValue(), command.getSubUser()
											.getId()
											.getValue())))
							.stream()
							.collect(Collectors.toMap(UserEntity::getId, Function.identity()));

					if (userEntitiesMap.size() != 2) {
						throw new UserNotFoundException("Sponsor or sub account user has not found");
					}

					return jpaUserSubAccountRepository.save(UserSubAccountEntity.builder()
							.sponsorUserEntity(userEntitiesMap.get(command.getSponsorUser()
									.getId()
									.getValue()))
							.subUserEntity(userEntitiesMap.get(command.getSubUser()
									.getId()
									.getValue()))
							.tenantId(command.getTenantId()
									.getValue())
							.subAccountStatus(command.getSubAccountStatus())
							.build());
				}));
	}

	@Override
	@Transactional(readOnly = true)
	public List<UserSubAccount> getSubAccountsBySponsorId(UserId userId) {
		return jpaUserSubAccountRepository.findBySponsorUserEntityId(userId.getValue())
				.stream()
				.map(UserSubAccountEntityMapper.INSTANCE::toModel)
				.toList();
	}

	@Override
	@Transactional(readOnly = true)
	public List<UserSubAccount> getSubAccountsBySubAccountUserId(UserId userId) {
		return jpaUserSubAccountRepository.findBySubUserEntityId(userId.getValue())
				.stream()
				.map(UserSubAccountEntityMapper.INSTANCE::toModel)
				.toList();

	}

	@Override
	public Optional<UserSubAccount> findBySubAccountId(UserSubAccountId subAccountId) {
		return jpaUserSubAccountRepository.findById(subAccountId.getValue())
				.map(UserSubAccountEntityMapper.INSTANCE::toModel);
	}

	@Override
	public boolean existsBySubAccountId(UserSubAccountId subAccountId) {
		return jpaUserSubAccountRepository.existsById(subAccountId.getValue());
	}

	@Override
	public Optional<UserSubAccount> findByTenantIdAndSubAccountId(TenantId tenantId,
			UserSubAccountId userSubAccountId) {
		return jpaUserSubAccountRepository.findByIdAndTenantId(userSubAccountId.getValue(), tenantId.getValue())
				.map(UserSubAccountEntityMapper.INSTANCE::toModel);

	}

	@Override
	public void deleteByTenantIdAndSubAccountId(TenantId tenantId, UserSubAccountId subAccountId) {
		jpaUserSubAccountRepository.deleteByIdAndTenantId(subAccountId.getValue(), tenantId.getValue());
	}

	@Override
	public Paging<UserSubAccount> querySubAccounts(UserSubAccountPaginationQuery query) {
		final var request = query.getFilter();
		final var pageable = PageRequest.of(query.getPage(), query.getSize(), Sort.Direction.valueOf(query
				.getSortDirection()), query.getSortFields()
						.toArray(String[]::new));

		final var predicate = WhereBuilder.build()
				.applyIf(Optional.ofNullable(request.getBySponsorId())
						.map(BaseId::getValue)
						.isPresent(), where -> where.and(QUserSubAccountEntity.userSubAccountEntity.sponsorUserEntity.id
								.eq(request.getBySponsorId()
										.getValue()))
								.and(jpaUserRepository.softDeletionPredicate(
										QUserSubAccountEntity.userSubAccountEntity.sponsorUserEntity)))
				.applyIf(Optional.ofNullable(request.getBySubUserId())
						.map(BaseId::getValue)
						.isPresent(), where -> where.and(QUserSubAccountEntity.userSubAccountEntity.subUserEntity.id.eq(
								request.getBySubUserId()
										.getValue()))
								.and(jpaUserRepository.softDeletionPredicate(
										QUserSubAccountEntity.userSubAccountEntity.subUserEntity)))
				.applyIf(!CollectionUtils.isEmpty(request.getByStatuses()), where -> where.and(
						QUserSubAccountEntity.userSubAccountEntity.subAccountStatus.in(request.getByStatuses()))
						.and(jpaUserRepository.softDeletionPredicate(
								QUserSubAccountEntity.userSubAccountEntity.subUserEntity)))
				.applyIf(!CollectionUtils.isEmpty(request.getBySubUserIds()), where -> where.and(
						QUserSubAccountEntity.userSubAccountEntity.subUserEntity.id.in(request.getBySubUserIds()
								.stream()
								.map(BaseId::getValue)
								.collect(Collectors.toSet())))
						.and(jpaUserRepository.softDeletionPredicate(
								QUserSubAccountEntity.userSubAccountEntity.subUserEntity)))
				.and(jpaUserRepository.softDeletionPredicate(QUserSubAccountEntity.userSubAccountEntity.subUserEntity));

		final var pageResult = jpaUserSubAccountRepository.findAllWithBase(predicate, pageable);
		return new Paging<>(pageResult.getContent()
				.stream()
				.map(UserSubAccountEntityMapper.INSTANCE::toModel)
				.toList(), pageResult.getTotalElements(), pageResult.getTotalPages(), pageResult.getPageable()
						.getPageNumber(), pageResult.getSort()
								.stream()
								.map(Sort.Order::toString)
								.toList());

	}

	@Override
	public Optional<UserSubAccount> findByTenantIdAndSubAccountIdAndSponsorId(TenantId tenantId,
			UserSubAccountId subAccountId, UserId sponsorUserId) {
		return jpaUserSubAccountRepository.findByIdAndTenantIdAndSponsorUserEntityId(subAccountId.getValue(), tenantId
				.getValue(), sponsorUserId.getValue())
				.map(UserSubAccountEntityMapper.INSTANCE::toModel);
	}
}
