/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.service.data.access.relational.requiredactions.entities;

import com.styl.pacific.data.access.identifiers.SnowflakeId;
import com.styl.pacific.user.service.core.features.requiredactions.entities.UserRequiredActionMetadata;
import com.styl.pacific.user.service.data.access.relational.users.entities.UserEntity;
import com.styl.pacific.user.shared.enums.UserRequiredActionType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import java.time.Instant;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@Setter
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@EntityListeners(AuditingEntityListener.class)
@DynamicUpdate
@Table(name = "user_required_actions")
public class UserRequiredActionEntity {
	@Id
	@SnowflakeId
	@GeneratedValue(generator = "snowflake_id_generator")
	private Long id;

	@Column(name = "action_type")
	@Enumerated(EnumType.STRING)
	@NotNull
	private UserRequiredActionType actionType;

	@ManyToOne
	@JoinColumn(name = "user_id")
	private UserEntity userEntity;

	@Column(name = "tenant_id")
	@NotNull
	private Long tenantId;

	@Column(name = "metadata")
	@JdbcTypeCode(SqlTypes.JSON)
	private UserRequiredActionMetadata metadata;

	@CreatedDate
	@Column(name = "created_at")
	private Instant createdAt;

	@LastModifiedDate
	@Column(name = "updated_at")
	private Instant updatedAt;

	@Override
	public boolean equals(Object o) {

		if (this == o)
			return true;
		if (o == null || getClass() != o.getClass())
			return false;
		UserRequiredActionEntity that = (UserRequiredActionEntity) o;
		return Objects.equals(id, that.id) && actionType == that.actionType && Objects.equals(tenantId, that.tenantId)
				&& Objects.equals(metadata, that.metadata) && Objects.equals(updatedAt, that.updatedAt) && Objects
						.equals(createdAt, that.createdAt);
	}

	@Override
	public int hashCode() {
		return Objects.hash(id, actionType, tenantId, metadata, updatedAt, createdAt);
	}
}
