/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.api.subaccounts.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.user.service.core.features.subaccounts.entities.UserSubAccount;
import com.styl.pacific.user.service.core.features.subaccounts.request.UserSubAccountPaginationQuery;
import com.styl.pacific.user.shared.http.subaccounts.request.QuerySubAccountPaginationRequest;
import com.styl.pacific.user.shared.http.subaccounts.response.SubAccountResponse;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class,
		MapstructCommonDomainMapper.class, UserSubAccountRequestMapper.class, UserSubAccountResponseMapper.class })
public interface UserSubAccountPaginationMapper {
	UserSubAccountPaginationMapper INSTANCE = Mappers.getMapper(UserSubAccountPaginationMapper.class);

	UserSubAccountPaginationQuery toPagingQuery(QuerySubAccountPaginationRequest source);

	Paging<SubAccountResponse> toPagingResponse(Paging<UserSubAccount> pageResult);
}
