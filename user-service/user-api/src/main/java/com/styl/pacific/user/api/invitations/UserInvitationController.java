/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.api.invitations;

import com.styl.pacific.application.rest.context.RequestContext;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.domain.valueobject.UserId;
import com.styl.pacific.user.service.core.features.invitations.UserInvitationCommandService;
import com.styl.pacific.user.service.core.features.invitations.request.SendUserActivationInvitationCommand;
import com.styl.pacific.user.shared.http.apis.UserInvitationApi;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class UserInvitationController implements UserInvitationApi {

	private final UserInvitationCommandService userInvitationCommandService;
	private final RequestContext requestContext;

	@Override
	public void sendActivationInvitation(Long userId) {
		userInvitationCommandService.createInvitation(SendUserActivationInvitationCommand.builder()
				.tenantId(MapstructCommonDomainMapper.INSTANCE.longToTenantId(requestContext.getTenantId()))
				.userId(new UserId(userId))
				.build());
	}

}
