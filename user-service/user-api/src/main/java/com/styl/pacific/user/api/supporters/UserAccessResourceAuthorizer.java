/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.user.api.supporters;

import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.domain.dto.TokenClaim;
import com.styl.pacific.domain.enums.UserType;
import com.styl.pacific.user.shared.exceptions.UserDomainException;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class UserAccessResourceAuthorizer {
	public static void permitCustomerResourceAccess(TokenClaim tokenClaim, Long checkingUserId,
			UserDomainException exception) {

		if (tokenClaim == null || checkingUserId == null) {
			// We expected gateway needs to authorize request first, internal communication doesn't need token required
			return;
		}

		if (tokenClaim.getUserType()
				.equals(UserType.CUSTOMER) && !MapstructCommonDomainMapper.INSTANCE.stringToUserId(tokenClaim
						.getUserId())
						.equals(MapstructCommonDomainMapper.INSTANCE.longToUserId(checkingUserId))) {
			throw exception;
		}
	}
}
