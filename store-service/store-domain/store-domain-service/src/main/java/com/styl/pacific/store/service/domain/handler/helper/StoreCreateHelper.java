/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.domain.handler.helper;

import com.styl.pacific.store.service.domain.StoreDomainCore;
import com.styl.pacific.store.service.domain.dto.store.ActiveStoreCommand;
import com.styl.pacific.store.service.domain.dto.store.CreateStoreCommand;
import com.styl.pacific.store.service.domain.dto.store.DeleteStoreCommand;
import com.styl.pacific.store.service.domain.dto.store.SuspendStoreCommand;
import com.styl.pacific.store.service.domain.dto.store.UpdateStoreCommand;
import com.styl.pacific.store.service.domain.entity.Store;
import com.styl.pacific.store.service.domain.exception.StoreDomainException;
import com.styl.pacific.store.service.domain.exception.StoreNotFoundException;
import com.styl.pacific.store.service.domain.mapper.StoreDataMapper;
import com.styl.pacific.store.service.domain.output.repository.StoreRepository;
import com.styl.pacific.store.shared.http.requests.store.GetStoreQuery;
import java.util.Objects;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Component
@AllArgsConstructor
public class StoreCreateHelper {

	private final Logger logger = LoggerFactory.getLogger(this.getClass());

	private final StoreRepository storeRepository;

	private final StoreDomainCore storeDomainCore;

	public Store persistNewStore(CreateStoreCommand command) {
		Store store = StoreDataMapper.INSTANCE.createStoreCommandToStore(command);
		storeDomainCore.initDataStore(store);
		storeDomainCore.pending(store);
		Store storeResult = storeRepository.save(store);
		logger.info("Store id created with {}", storeResult.getId());
		return storeResult;
	}

	public Store updateStore(UpdateStoreCommand command) {
		logger.info("Updating store with id: {}", command.getStoreId());
		Optional<Store> existingStore = storeRepository.getStore(new GetStoreQuery(command.getTenantId(), command
				.getStoreId()));
		if (existingStore.isEmpty()) {
			throw new StoreNotFoundException("Store not found with id: " + command.getStoreId());
		}
		Store store = StoreDataMapper.INSTANCE.updateStoreCommandToStore(command, existingStore.get());
		storeDomainCore.updateTime(store);
		Store result = storeRepository.save(store);
		if (Objects.isNull(result)) {
			throw new StoreDomainException("Failed to save store with id: " + store.getId());
		}
		return result;
	}

	public void deleteStore(DeleteStoreCommand command) {
		Optional<Store> existingStore = storeRepository.getStore(new GetStoreQuery(command.getTenantId(), command
				.getStoreId()));
		if (existingStore.isEmpty()) {
			throw new StoreNotFoundException("Store is not found with id: " + command.getStoreId());
		}
		Store store = existingStore.get();
		storeDomainCore.terminate(store);
		storeDomainCore.updateTime(store);
		storeRepository.save(store);
	}

	public Store suspendStore(SuspendStoreCommand command) {
		Optional<Store> existingStore = storeRepository.getStore(new GetStoreQuery(command.getTenantId(), command
				.getStoreId()));
		if (existingStore.isEmpty()) {
			throw new StoreNotFoundException("Suspend Store which not found with id: " + command.getStoreId());
		}
		Store store = existingStore.get();
		storeDomainCore.suspend(store);
		storeDomainCore.updateTime(store);
		return storeRepository.save(store);
	}

	public Store activeStore(ActiveStoreCommand command) {
		Optional<Store> existingStore = storeRepository.getStore(new GetStoreQuery(command.getTenantId(), command
				.getStoreId()));
		if (existingStore.isEmpty()) {
			throw new StoreNotFoundException("Activate Store which not found with id: " + command.getStoreId());
		}
		Store store = existingStore.get();
		storeDomainCore.active(store);
		storeDomainCore.updateTime(store);
		return storeRepository.save(store);
	}
}
