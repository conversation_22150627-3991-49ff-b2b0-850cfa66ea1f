/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.domain.output.repository;

import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.store.service.domain.dto.device.FindDevicesQuery;
import com.styl.pacific.store.service.domain.entity.Device;
import com.styl.pacific.store.service.domain.entity.DeviceSession;
import com.styl.pacific.store.service.domain.entity.DeviceSetting;
import com.styl.pacific.store.shared.http.requests.device.FindDeviceSessionQuery;
import com.styl.pacific.store.shared.http.requests.device.GetDeviceQuery;
import com.styl.pacific.store.shared.http.requests.device.GetDeviceSettingQuery;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 *
 */
public interface DeviceRepository {

	Device save(Device device);

	Optional<Device> getDevice(GetDeviceQuery query);

	Optional<Device> findById(String id);

	Paging<Device> findDevices(FindDevicesQuery query);

	DeviceSession save(DeviceSession deviceSession);

	List<DeviceSession> findActivedSessions(FindDeviceSessionQuery query);

	DeviceSetting save(DeviceSetting deviceSetting);

	Optional<DeviceSetting> getDeviceSetting(GetDeviceSettingQuery query);

	Optional<DeviceSession> getDeviceSession(String sessionId, Long tenantId);
}
