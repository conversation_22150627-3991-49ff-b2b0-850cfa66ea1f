/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.domain.dto.staff;

import com.styl.pacific.store.shared.http.enums.StaffStatus;
import com.styl.pacific.store.shared.http.enums.StaffType;
import java.util.List;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Builder
@Data
public class StaffsFilterQuery {
	private Long tenantId;
	private Long storeId;
	private Long staffId;
	private String name;
	private String cardId;
	private List<StaffStatus> statuses;
	private StaffType type;
	private String staffCode;
	private String staffCodeEq;
	private String email;
}
