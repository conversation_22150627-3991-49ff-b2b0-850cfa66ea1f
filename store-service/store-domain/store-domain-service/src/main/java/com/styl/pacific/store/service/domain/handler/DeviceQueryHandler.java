/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.domain.handler;

import com.styl.pacific.store.service.domain.dto.device.FindDevicesQuery;
import com.styl.pacific.store.service.domain.entity.Device;
import com.styl.pacific.store.service.domain.entity.DeviceSession;
import com.styl.pacific.store.service.domain.entity.DeviceSetting;
import com.styl.pacific.store.service.domain.entity.Store;
import com.styl.pacific.store.service.domain.handler.helper.DeviceQueryHelper;
import com.styl.pacific.store.service.domain.handler.helper.StoreQueryHelper;
import com.styl.pacific.store.service.domain.mapper.DeviceDataMapper;
import com.styl.pacific.store.shared.http.requests.device.GetDeviceQuery;
import com.styl.pacific.store.shared.http.requests.device.GetDeviceSettingQuery;
import com.styl.pacific.store.shared.http.requests.store.GetStoreQuery;
import com.styl.pacific.store.shared.http.responses.device.DeviceResponse;
import com.styl.pacific.store.shared.http.responses.device.DeviceSessionResponse;
import com.styl.pacific.store.shared.http.responses.device.DeviceSettingResponse;
import com.styl.pacific.store.shared.http.responses.device.ListDevicesResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 *
 */

@Component
@AllArgsConstructor
public class DeviceQueryHandler {

	private final DeviceQueryHelper deviceQueryHelper;

	private final StoreQueryHelper storeQueryHelper;

	public DeviceResponse getDevice(GetDeviceQuery query) {
		Device device = deviceQueryHelper.getDevice(query);
		return DeviceDataMapper.INSTANCE.deviceToDeviceResponse(device);
	}

	public ListDevicesResponse findDevices(FindDevicesQuery query) {
		return deviceQueryHelper.findDevices(query);
	}

	public DeviceSettingResponse getDeviceSetting(Long tenantId, Long storeId, String deviceId) {
		Store store = storeQueryHelper.getStore(GetStoreQuery.builder()
				.tenantId(tenantId)
				.storeId(storeId)
				.build());
		Device device = deviceQueryHelper.getDevice(GetDeviceQuery.builder()
				.tenantId(tenantId)
				.storeId(storeId)
				.deviceId(deviceId)
				.build());
		DeviceSetting deviceSetting = deviceQueryHelper.getDeviceSetting(GetDeviceSettingQuery.builder()
				.tenantId(tenantId)
				.storeId(storeId)
				.deviceId(deviceId)
				.build());
		return DeviceDataMapper.INSTANCE.toDeviceSettingResponse(device, store, deviceSetting);
	}

	public DeviceSessionResponse getDeviceSession(String sessionId, Long tenantId) {
		DeviceSession deviceSession = deviceQueryHelper.getDeviceSession(sessionId, tenantId);
		return DeviceDataMapper.INSTANCE.deviceSessionToDeviceSessionResponse(deviceSession);
	}
}
