/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.application;

import com.styl.pacific.domain.valueobject.CashFloatId;
import com.styl.pacific.store.service.domain.entity.CashFloatIdGenerator;
import com.styl.pacific.utils.snowflake.id.Snowflake;

/**
 * <AUTHOR>
 *
 */
public class CashFloatIdGeneratorImpl implements CashFloatIdGenerator {

	private final Snowflake snowflake;

	public CashFloatIdGeneratorImpl() {
		this.snowflake = new Snowflake();
	}

	@Override
	public CashFloatId nextId() {
		return new CashFloatId(snowflake.nextId());
	}
}
