/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.data.access.store.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.styl.pacific.domain.valueobject.StoreId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.store.service.domain.dto.device.SaveDeviceCommand;
import com.styl.pacific.store.service.domain.entity.Device;
import com.styl.pacific.store.service.domain.mapper.DeviceDataMapper;
import com.styl.pacific.store.shared.http.enums.DeviceStatus;
import com.styl.pacific.store.shared.http.enums.DeviceType;
import com.styl.pacific.store.shared.http.responses.device.DeviceResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 */
class DeviceDataAccessMapperTest {

	private DeviceDataMapper mapper;

	@BeforeEach
	public void setUp() {
		mapper = DeviceDataMapper.INSTANCE;
	}

	@Test
	void whenMapperFromSaveDeviceCommand_shouldReturnDevice() {
		// Arrange
		SaveDeviceCommand command = SaveDeviceCommand.builder()
				.deviceId("12345")
				.model("model1")
				.type(DeviceType.POS)
				.build();
		// Act
		Device device = mapper.saveDeviceCommandToDevice(command);
		// Assert
		assertEquals(command.getDeviceId(), device.getId());
		assertEquals(command.getModel(), device.getModel());
		assertEquals(command.getType(), device.getType());
	}

	@Test
	void whenMapperFromDevice_shouldReturnDeviceResponse() {
		// Arrange
		Device device = Device.builder()
				.id("123456")
				.storeId(new StoreId(123456L))
				.model("model1")
				.serialNumber("serialNumber123")
				.status(DeviceStatus.ACTIVE)
				.tenantId(new TenantId(12345L))
				.type(DeviceType.POS)
				.build();
		// Act
		DeviceResponse deviceResponse = mapper.deviceToDeviceResponse(device);
		// Assert
		assertEquals(device.getId(), deviceResponse.getDeviceId());
		assertEquals(device.getModel(), deviceResponse.getModel());
		assertEquals(device.getType(), deviceResponse.getType());
		assertEquals(device.getTenantId()
				.getValue(), deviceResponse.getTenantId());
		assertEquals(device.getStoreId()
				.getValue(), deviceResponse.getStoreId());
		assertEquals(device.getSerialNumber(), deviceResponse.getSerialNumber());
		assertEquals(device.getStatus(), deviceResponse.getStatus());
	}
}
