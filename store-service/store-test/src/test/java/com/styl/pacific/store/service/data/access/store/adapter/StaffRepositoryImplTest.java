/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.data.access.store.adapter;

import static org.junit.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.styl.pacific.common.test.BaseDataJpaTest;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.valueobject.StaffId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.kafka.consumer.config.KafkaConsumerConfig;
import com.styl.pacific.store.service.config.IntegrationTestConfiguration;
import com.styl.pacific.store.service.domain.dto.staff.FindStaffsQuery;
import com.styl.pacific.store.service.domain.dto.staff.StaffsFilterQuery;
import com.styl.pacific.store.service.domain.entity.Staff;
import com.styl.pacific.store.service.domain.output.repository.StaffRepository;
import com.styl.pacific.store.shared.http.enums.StaffStatus;
import com.styl.pacific.store.shared.http.enums.StaffType;
import com.styl.pacific.store.shared.http.requests.staff.GetStaffQuery;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.MockBeans;
import org.springframework.test.context.ContextConfiguration;

/**
 * <AUTHOR>
 */
@MockBeans({ @MockBean(KafkaConsumerConfig.class) })
@ContextConfiguration(classes = { IntegrationTestConfiguration.class })
class StaffRepositoryImplTest extends BaseDataJpaTest {

	@Autowired
	StaffRepository staffRepository;

	private static final Long tenantId = 3L;

	@Test
	void testSaveStoreShouldOK() {
		// Arrange
		Staff staff = Staff.builder()
				.id(new StaffId(1L))
				.name("name1")
				.tenantId(new TenantId(tenantId))
				.type(StaffType.CASHIER)
				.cardId("11111111")
				.status(StaffStatus.ACTIVE)
				.staffCode("1234")
				.pinCode("1234")
				.salt("salt123")
				.build();
		// Act
		Staff staffResponse = staffRepository.save(staff);
		// Assert
		assertEquals(staff.getName(), staffResponse.getName());
		assertEquals(staff.getStaffCode(), staffResponse.getStaffCode());
		assertEquals(staff.getTenantId(), staffResponse.getTenantId());
		assertEquals(staff.getSalt(), staffResponse.getSalt());
		assertEquals(staff.getPinCode(), staffResponse.getPinCode());
		assertEquals(staff.getType(), staffResponse.getType());
		assertEquals(staff.getId(), staffResponse.getId());
		assertEquals(staff.getStatus(), staffResponse.getStatus());
	}

	@Test
	void shouldReturnStaff_whenGetStaff() {
		// Arrange
		Staff staff = Staff.builder()
				.id(new StaffId(1L))
				.name("name1")
				.tenantId(new TenantId(tenantId))
				.type(StaffType.CASHIER)
				.cardId("11111111")
				.status(StaffStatus.ACTIVE)
				.staffCode("1234")
				.pinCode("1234")
				.salt("salt123")
				.build();
		staffRepository.save(staff);
		// Act
		Optional<Staff> staffResponse = staffRepository.getStaff(GetStaffQuery.builder()
				.tenantId(tenantId)
				.staffId(1L)
				.build());
		// Assert
		assertTrue(staffResponse.isPresent());

		assertEquals(staff.getId(), staffResponse.get()
				.getId());
		assertEquals(staff.getName(), staffResponse.get()
				.getName());
		assertEquals(staff.getTenantId(), staffResponse.get()
				.getTenantId());
		assertEquals(staff.getStatus(), staffResponse.get()
				.getStatus());
		assertEquals(staff.getStaffCode(), staffResponse.get()
				.getStaffCode());
		assertEquals(staff.getSalt(), staffResponse.get()
				.getSalt());
		assertEquals(staff.getPinCode(), staffResponse.get()
				.getPinCode());
		assertEquals(staff.getCardId(), staffResponse.get()
				.getCardId());
		assertEquals(staff.getType(), staffResponse.get()
				.getType());
	}

	@Test
	void shouldReturnEmpty_whenGetStaff() {
		// Arrange
		Staff staff = Staff.builder()
				.id(new StaffId(1L))
				.name("name1")
				.tenantId(new TenantId(tenantId))
				.type(StaffType.CASHIER)
				.cardId("11111111")
				.status(StaffStatus.ACTIVE)
				.staffCode("1234")
				.salt("salt123")
				.pinCode("1234")
				.build();
		staffRepository.save(staff);
		// Act
		Optional<Staff> staffResponse = staffRepository.getStaff(GetStaffQuery.builder()
				.tenantId(tenantId)
				.staffId(2L)
				.build());
		// Assert
		assertTrue(staffResponse.isEmpty());
	}

	@Test
	void testUpdateStaffShouldOK() {
		Staff staff = Staff.builder()
				.id(new StaffId(1L))
				.name("name1")
				.tenantId(new TenantId(tenantId))
				.type(StaffType.CASHIER)
				.cardId("11111111")
				.salt("salt123")
				.status(StaffStatus.ACTIVE)
				.staffCode("1234")
				.pinCode("1234")
				.build();
		staffRepository.save(staff);

		// act
		Staff newStaff = Staff.builder()
				.id(new StaffId(1L))
				.name("name1")
				.tenantId(new TenantId(tenantId))
				.pinCode("123")
				.salt("xyz123")
				.type(StaffType.SUPERVISOR)
				.cardId("22222222")
				.status(StaffStatus.ACTIVE)
				.staffCode("1234")
				.pinCode("1234")
				.build();
		Staff updated = staffRepository.save(newStaff);

		// assert
		assertNotNull(updated);

		assertEquals(newStaff.getId(), updated.getId());
		assertEquals(newStaff.getName(), updated.getName());
		assertEquals(newStaff.getTenantId(), updated.getTenantId());
		assertEquals(newStaff.getStatus(), updated.getStatus());
		assertEquals(newStaff.getStaffCode(), updated.getStaffCode());
		assertEquals(newStaff.getSalt(), updated.getSalt());
		assertEquals(newStaff.getPinCode(), updated.getPinCode());
		assertEquals(newStaff.getCardId(), updated.getCardId());
		assertEquals(newStaff.getType(), updated.getType());
	}

	@Test
	void testPagingStaffShouldOK() {
		// create 2 tenants
		Staff staff1 = Staff.builder()
				.id(new StaffId(1L))
				.name("name1")
				.tenantId(new TenantId(tenantId))
				.name("testName")
				.type(StaffType.SUPERVISOR)
				.cardId("11111111")
				.salt("salt123")
				.status(StaffStatus.ACTIVE)
				.staffCode("1234")
				.pinCode("1234")
				.build();
		staffRepository.save(staff1);
		Staff staff2 = Staff.builder()
				.id(new StaffId(2L))
				.tenantId(new TenantId(tenantId))
				.name("testName")
				.type(StaffType.SUPERVISOR)
				.cardId("11111222")
				.salt("salt123")
				.status(StaffStatus.ACTIVE)
				.staffCode("7891")
				.pinCode("1234")
				.build();
		staffRepository.save(staff2);

		// Create 100 staff
		for (int i = 0; i < 100; i++) {
			Staff staff = Staff.builder()
					.id(new StaffId((long) 100 + i))
					.tenantId(new TenantId(tenantId))
					.name("name" + i)
					.type(StaffType.CASHIER)
					.cardId("111" + i)
					.salt("salt123")
					.status(StaffStatus.ACTIVE)
					.staffCode("4567" + i)
					.pinCode("1234")
					.build();
			staffRepository.save(staff);
		}

		StaffsFilterQuery filterQuery1 = StaffsFilterQuery.builder()
				.tenantId(tenantId)
				.name("testName")
				.build();
		// act
		Paging<Staff> result1 = staffRepository.findStaff(new FindStaffsQuery(filterQuery1, 10, 0, "asc", List.of(
				"id")));

		// assert
		assertNotNull(result1);
		assertEquals(2, result1.getContent()
				.size());
		assertEquals(2, result1.getTotalElements());
		assertEquals(1, result1.getTotalPages());
		assertEquals(0, result1.getPage());
		assertEquals(1, result1.getSort()
				.size());

		StaffsFilterQuery filterQuery2 = StaffsFilterQuery.builder()
				.tenantId(3L)
				.statuses(List.of(StaffStatus.ACTIVE))
				.build();
		// act
		Paging<Staff> result2 = staffRepository.findStaff(new FindStaffsQuery(filterQuery2, 10, 1, "DESC", List.of(
				"id")));
		assertNotNull(result2);
		assertEquals(10, result2.getContent()
				.size());
		assertEquals(102, result2.getTotalElements());
		assertEquals(11, result2.getTotalPages());
		assertEquals(1, result2.getPage());
		assertEquals(1, result2.getSort()
				.size());
		for (int i = 0; i < 9; i++) {
			assertTrue(result2.getContent()
					.get(i)
					.getId()
					.getValue() > result2.getContent()
							.get(i + 1)
							.getId()
							.getValue());
		}

		StaffsFilterQuery filterQuery3 = StaffsFilterQuery.builder()
				.tenantId(3L)
				.type(StaffType.CASHIER)
				.build();
		// act
		Paging<Staff> result3 = staffRepository.findStaff(new FindStaffsQuery(filterQuery3, 10, 0, "DESC", List.of(
				"id")));
		assertNotNull(result3);
		assertEquals(10, result3.getContent()
				.size());
		assertEquals(100, result3.getTotalElements());
		assertEquals(10, result3.getTotalPages());
		for (int i = 0; i < 9; i++) {
			assertTrue(result3.getContent()
					.get(i)
					.getId()
					.getValue() > result3.getContent()
							.get(i + 1)
							.getId()
							.getValue());
		}

		StaffsFilterQuery filterQuery4 = StaffsFilterQuery.builder()
				.tenantId(tenantId)
				.statuses(List.of(StaffStatus.INACTIVE))
				.build();

		// act
		Paging<Staff> result4 = staffRepository.findStaff(new FindStaffsQuery(filterQuery4, 10, 0, "DESC", List.of(
				"id")));
		assertNotNull(result4);
		assertEquals(0, result4.getContent()
				.size());
		assertEquals(0, result4.getTotalElements());
		assertEquals(0, result4.getTotalPages());

		StaffsFilterQuery filterQuery5 = StaffsFilterQuery.builder()
				.tenantId(tenantId)
				.staffCode("1234")
				.build();
		// act
		Paging<Staff> result6 = staffRepository.findStaff(new FindStaffsQuery(filterQuery5, 10, 0, "DESC", List.of(
				"id")));
		assertNotNull(result6);
		assertEquals(1, result6.getContent()
				.size());
	}

}
