/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.data.access.store.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.styl.pacific.domain.valueobject.StaffId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.store.service.data.access.entity.StaffEntity;
import com.styl.pacific.store.service.data.access.mapper.StaffDataAccessMapper;
import com.styl.pacific.store.service.domain.entity.Staff;
import com.styl.pacific.store.shared.http.enums.StaffStatus;
import com.styl.pacific.store.shared.http.enums.StaffType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 */
class StaffDataAccessMapperTest {

	private StaffDataAccessMapper mapper;

	@BeforeEach
	public void setUp() {
		mapper = StaffDataAccessMapper.INSTANCE;
	}

	@Test
	void whenMapperFromStaffEntity_shouldReturnStaff() {
		// Arrange
		StaffEntity staffEntity = new StaffEntity();
		staffEntity.setId(1L);
		staffEntity.setStaffCode("1234");
		staffEntity.setCardId("111111");
		staffEntity.setName("staffName");
		staffEntity.setStatus(StaffStatus.ACTIVE);
		staffEntity.setType(StaffType.CASHIER);
		staffEntity.setTenantId(1L);
		staffEntity.setPinCode("xyz123");
		staffEntity.setSalt("mnp1234");

		// Act
		Staff staff = mapper.staffEntityToStaff(staffEntity);
		// Assert
		assertEquals(staffEntity.getName(), staff.getName());
		assertEquals(staffEntity.getTenantId(), staff.getTenantId()
				.getValue());
		assertEquals(staffEntity.getCardId(), staff.getCardId());
		assertEquals(staffEntity.getStaffCode(), staff.getStaffCode());
		assertEquals(staffEntity.getType(), staff.getType());
		assertEquals(staffEntity.getStatus(), staff.getStatus());
	}

	@Test
	void whenMapperFromStaff_shouldReturnStaffEntity() {
		// Arrange
		Staff staff = Staff.builder()
				.id(new StaffId(1L))
				.tenantId(new TenantId(1L))
				.name("testName")
				.type(StaffType.CASHIER)
				.cardId("11111111")
				.status(StaffStatus.ACTIVE)
				.staffCode("1234")
				.build();
		// Act
		StaffEntity entity = mapper.staffToStaffEntity(staff);
		// Assert
		assertEquals(staff.getName(), entity.getName());
		assertEquals(staff.getTenantId()
				.getValue(), entity.getTenantId());
		assertEquals(staff.getCardId(), entity.getCardId());
		assertEquals(staff.getStaffCode(), entity.getStaffCode());
		assertEquals(staff.getType(), entity.getType());
		assertEquals(staff.getStatus(), entity.getStatus());
	}
}
