/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.rest.controller;

import com.styl.pacific.application.rest.context.RequestContext;
import com.styl.pacific.store.service.domain.dto.cashfloat.CashFloatsFilterQuery;
import com.styl.pacific.store.service.domain.dto.cashfloat.CreateCashFloatCommand;
import com.styl.pacific.store.service.domain.dto.cashfloat.FindCashFloatsQuery;
import com.styl.pacific.store.service.domain.dto.cashfloat.UpdateCashFloatCommand;
import com.styl.pacific.store.service.domain.port.input.service.CashFloatDomainService;
import com.styl.pacific.store.service.rest.mapper.CashFloatControllerMapper;
import com.styl.pacific.store.shared.http.apis.CashFloatApi;
import com.styl.pacific.store.shared.http.requests.cashfloat.CreateCashFloatRequest;
import com.styl.pacific.store.shared.http.requests.cashfloat.FindCashFloatsRequest;
import com.styl.pacific.store.shared.http.requests.cashfloat.GetCashFloatQuery;
import com.styl.pacific.store.shared.http.requests.cashfloat.UpdateCashFloatRequest;
import com.styl.pacific.store.shared.http.responses.cashfloat.CashFloatResponse;
import com.styl.pacific.store.shared.http.responses.cashfloat.ListCashFloatResponse;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */

@RestController
@AllArgsConstructor
public class CashFloatController implements CashFloatApi {

	private final CashFloatDomainService cashFloatDomainService;

	private final RequestContext requestContext;

	public CashFloatResponse createCashFloat(CreateCashFloatRequest request) {
		CreateCashFloatCommand createCashFloatCommand = CashFloatControllerMapper.INSTANCE
				.createCashFloatRequestToCreateCashFloatCommand(request, requestContext.getTenantId());
		return cashFloatDomainService.createCashFloat(createCashFloatCommand);
	}

	public CashFloatResponse updateCashFloat(long id, UpdateCashFloatRequest request) {
		UpdateCashFloatCommand updateCashFloatCommand = CashFloatControllerMapper.INSTANCE
				.updateCashFloatRequestToUpdateCashFloatCommand(request, id, requestContext.getTenantId());
		return cashFloatDomainService.updateCashFloat(updateCashFloatCommand);
	}

	public ListCashFloatResponse findCashFloat(FindCashFloatsRequest request) {
		CashFloatsFilterQuery filterQuery = CashFloatControllerMapper.INSTANCE
				.cashFloatsFilterRequestToCashFloatsFilterQuery(request.getFilter(), requestContext.getTenantId());
		FindCashFloatsQuery query = CashFloatControllerMapper.INSTANCE.findDevicesRequestToFindDevicesQuery(request,
				filterQuery);
		return cashFloatDomainService.findCashFloat(query);
	}

	public CashFloatResponse getCashFloat(long id) {
		return cashFloatDomainService.getCashFloat(GetCashFloatQuery.builder()
				.cashFloatId(id)
				.tenantId(requestContext.getTenantId())
				.build());
	}
}
