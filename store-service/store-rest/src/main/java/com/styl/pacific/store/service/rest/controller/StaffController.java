/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.rest.controller;

import com.styl.pacific.application.rest.context.RequestContext;
import com.styl.pacific.store.service.domain.dto.staff.ActivateStaffCommand;
import com.styl.pacific.store.service.domain.dto.staff.ArchiveStaffCommand;
import com.styl.pacific.store.service.domain.dto.staff.CreateStaffCommand;
import com.styl.pacific.store.service.domain.dto.staff.FindStaffsQuery;
import com.styl.pacific.store.service.domain.dto.staff.InActivateStaffCommand;
import com.styl.pacific.store.service.domain.dto.staff.ResetPinCodeCommand;
import com.styl.pacific.store.service.domain.dto.staff.StaffsFilterQuery;
import com.styl.pacific.store.service.domain.dto.staff.UpdateStaffCommand;
import com.styl.pacific.store.service.domain.dto.staffassignment.StaffAssignmentQuery;
import com.styl.pacific.store.service.domain.port.input.service.StaffAssignmentDomainService;
import com.styl.pacific.store.service.domain.port.input.service.StaffDomainService;
import com.styl.pacific.store.service.rest.mapper.StaffAssignmentControllerMapper;
import com.styl.pacific.store.service.rest.mapper.StaffControllerMapper;
import com.styl.pacific.store.shared.http.apis.StaffApi;
import com.styl.pacific.store.shared.http.requests.staff.CreateStaffRequest;
import com.styl.pacific.store.shared.http.requests.staff.FindStaffsRequest;
import com.styl.pacific.store.shared.http.requests.staff.GetStaffQuery;
import com.styl.pacific.store.shared.http.requests.staff.ResetPinCodeRequest;
import com.styl.pacific.store.shared.http.requests.staff.UpdateStaffRequest;
import com.styl.pacific.store.shared.http.requests.staff.VerifyStaffCardRequest;
import com.styl.pacific.store.shared.http.requests.staff.VerifyStaffRequest;
import com.styl.pacific.store.shared.http.requests.staffassignment.StaffAssignmentRequest;
import com.styl.pacific.store.shared.http.responses.staff.ListStaffResponse;
import com.styl.pacific.store.shared.http.responses.staff.StaffResponse;
import com.styl.pacific.store.shared.http.responses.staffassignment.StaffAssignmentResponse;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */

@RestController
@AllArgsConstructor
public class StaffController implements StaffApi {

	private final StaffDomainService staffDomainService;

	private final StaffAssignmentDomainService staffAssignmentDomainService;

	private final RequestContext requestContext;

	public ResponseEntity<StaffResponse> createStaff(CreateStaffRequest request) {
		CreateStaffCommand createStaffCommand = StaffControllerMapper.INSTANCE.createStaffRequestToCreateStaffCommand(
				request, requestContext.getTenantId());
		StaffResponse response = staffDomainService.createStaff(createStaffCommand);
		return ResponseEntity.ok(response);
	}

	public ResponseEntity<StaffResponse> updateStaff(long id, UpdateStaffRequest request) {
		UpdateStaffCommand updateStaffCommand = StaffControllerMapper.INSTANCE.updateStaffRequestToUpdateStaffCommand(
				request, id, requestContext.getTenantId());
		StaffResponse response = staffDomainService.updateStaff(updateStaffCommand);
		return ResponseEntity.ok(response);
	}

	public ResponseEntity<ListStaffResponse> findStaff(FindStaffsRequest request) {
		StaffsFilterQuery filter = StaffControllerMapper.INSTANCE.staffsFilterRequestToStaffsFilterQuery(request
				.getFilter(), requestContext.getTenantId());
		FindStaffsQuery query = StaffControllerMapper.INSTANCE.findStaffsRequestToFindStaffsQuery(request, filter);
		ListStaffResponse response = staffDomainService.findStaff(query);
		return ResponseEntity.ok(response);
	}

	public ResponseEntity<StaffResponse> getStaff(long id) {
		StaffResponse response = staffDomainService.getStaff(GetStaffQuery.builder()
				.staffId(id)
				.tenantId(requestContext.getTenantId())
				.build());
		return ResponseEntity.ok(response);
	}

	public ResponseEntity<Void> archiveStaff(long id) {
		staffDomainService.archiveStaff(ArchiveStaffCommand.builder()
				.staffId(id)
				.tenantId(requestContext.getTenantId())
				.build());

		return ResponseEntity.noContent()
				.build();
	}

	public ResponseEntity<StaffResponse> inActivateStaff(long id) {
		StaffResponse response = staffDomainService.inActivateStaff(InActivateStaffCommand.builder()
				.staffId(id)
				.tenantId(requestContext.getTenantId())
				.build());
		return ResponseEntity.ok(response);
	}

	public ResponseEntity<StaffResponse> activateStaff(long id) {
		StaffResponse response = staffDomainService.activateStaff(ActivateStaffCommand.builder()
				.staffId(id)
				.tenantId(requestContext.getTenantId())
				.build());
		return ResponseEntity.ok(response);
	}

	public ResponseEntity<Void> resetPinCode(long staffId, ResetPinCodeRequest request) {
		ResetPinCodeCommand resetPinCodeCommand = StaffControllerMapper.INSTANCE
				.resetPinCodeRequestToResetPinCodeCommand(request, staffId, requestContext.getTenantId());
		staffDomainService.resetPinCode(resetPinCodeCommand);
		return ResponseEntity.noContent()
				.build();
	}

	public ResponseEntity<StaffResponse> verifyStaff(VerifyStaffRequest request) {
		StaffResponse response = staffDomainService.verifyStaff(requestContext.getTenantId(), request.getStoreId(),
				request.getStaffCode(), request.getPinCode());
		return ResponseEntity.ok(response);
	}

	public ResponseEntity<StaffResponse> verifyStaffCard(VerifyStaffCardRequest request) {
		StaffResponse response = staffDomainService.verifyStaffCard(requestContext.getTenantId(), request.getStoreId(),
				request.getCardId());
		return ResponseEntity.ok(response);
	}

	@Override
	public ResponseEntity<List<StaffAssignmentResponse>> getStaffAssignment(StaffAssignmentRequest request) {
		StaffAssignmentQuery query = StaffAssignmentControllerMapper.INSTANCE
				.staffAssignmentRequestToStaffAssignmentQuery(request, requestContext.getTenantId());
		List<StaffAssignmentResponse> response = staffAssignmentDomainService.getStaffAssignment(query);
		return ResponseEntity.ok(response);
	}
}
