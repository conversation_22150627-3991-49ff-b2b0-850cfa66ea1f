/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.rest.mapper;

import com.styl.pacific.common.mapstruct.AppMapStructConfiguration;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.store.service.domain.dto.cashfloat.CashFloatsFilterQuery;
import com.styl.pacific.store.service.domain.dto.cashfloat.CreateCashFloatCommand;
import com.styl.pacific.store.service.domain.dto.cashfloat.FindCashFloatsQuery;
import com.styl.pacific.store.service.domain.dto.cashfloat.UpdateCashFloatCommand;
import com.styl.pacific.store.shared.http.requests.cashfloat.CashFloatsFilterRequest;
import com.styl.pacific.store.shared.http.requests.cashfloat.CreateCashFloatRequest;
import com.styl.pacific.store.shared.http.requests.cashfloat.FindCashFloatsRequest;
import com.styl.pacific.store.shared.http.requests.cashfloat.UpdateCashFloatRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */

@Mapper(config = AppMapStructConfiguration.class, uses = { MapstructCommonMapper.class })
public interface CashFloatControllerMapper {

	CashFloatControllerMapper INSTANCE = Mappers.getMapper(CashFloatControllerMapper.class);

	@Mapping(target = "staffId", source = "request.staffId")
	@Mapping(target = "openingAmount", source = "request.openingAmount")
	@Mapping(target = "deviceId", source = "request.deviceId")
	@Mapping(target = "tenantId", source = "tenantId")
	@Mapping(target = "storeId", source = "request.storeId")
	@Mapping(target = "openingTime", source = "request.openingTime", qualifiedByName = "longToInstant")
	@Mapping(target = "closingTime", source = "request.closingTime", qualifiedByName = "longToInstant")
	CreateCashFloatCommand createCashFloatRequestToCreateCashFloatCommand(CreateCashFloatRequest request,
			Long tenantId);

	@Mapping(target = "closingTime", source = "request.closingTime", qualifiedByName = "longToInstant")
	@Mapping(target = "closingAmount", source = "request.closingAmount")
	@Mapping(target = "desiredAmount", source = "request.desiredAmount")
	@Mapping(target = "cashFloatId", source = "cashFloatId")
	@Mapping(target = "tenantId", source = "tenantId")
	@Mapping(target = "storeId", source = "request.storeId")
	@Mapping(target = "staffId", source = "request.staffId")
	UpdateCashFloatCommand updateCashFloatRequestToUpdateCashFloatCommand(UpdateCashFloatRequest request,
			Long cashFloatId, Long tenantId);

	@Mapping(target = "filter", source = "filter")
	FindCashFloatsQuery findDevicesRequestToFindDevicesQuery(FindCashFloatsRequest request,
			CashFloatsFilterQuery filter);

	@Mapping(target = "storeId", source = "filter.storeId")
	@Mapping(target = "tenantId", source = "tenantId")
	@Mapping(target = "staffId", source = "filter.staffId")
	@Mapping(target = "staffName", source = "filter.staffName")
	@Mapping(target = "staffCode", source = "filter.staffCode")
	@Mapping(target = "fromTime", source = "filter.fromTime", qualifiedByName = "longToInstant")
	@Mapping(target = "toTime", source = "filter.toTime", qualifiedByName = "longToInstant")
	CashFloatsFilterQuery cashFloatsFilterRequestToCashFloatsFilterQuery(CashFloatsFilterRequest filter, Long tenantId);
}
