/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.shared.http.apis;

import com.styl.pacific.domain.permissions.PacificApiAuthorized;
import com.styl.pacific.store.shared.http.requests.cashfloat.CreateCashFloatRequest;
import com.styl.pacific.store.shared.http.requests.cashfloat.FindCashFloatsRequest;
import com.styl.pacific.store.shared.http.requests.cashfloat.UpdateCashFloatRequest;
import com.styl.pacific.store.shared.http.responses.cashfloat.CashFloatResponse;
import com.styl.pacific.store.shared.http.responses.cashfloat.ListCashFloatResponse;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseStatus;

public interface CashFloatApi {

	@PostMapping(path = "/api/store/cash-floats")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	CashFloatResponse createCashFloat(@RequestBody @Valid CreateCashFloatRequest request);

	@PutMapping(path = "/api/store/cash-floats/{id}")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	CashFloatResponse updateCashFloat(@PathVariable long id, @RequestBody @Valid UpdateCashFloatRequest request);

	@GetMapping(path = "/api/store/cash-floats")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	ListCashFloatResponse findCashFloat(@ModelAttribute @Valid FindCashFloatsRequest query);

	@GetMapping(path = "/api/store/cash-floats/{id}")
	@ResponseStatus(HttpStatus.OK)
	@PacificApiAuthorized
	CashFloatResponse getCashFloat(@PathVariable long id);
}
