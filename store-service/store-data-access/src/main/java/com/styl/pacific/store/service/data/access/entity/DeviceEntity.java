/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.data.access.entity;

import com.styl.pacific.data.access.entity.AuditableEntity;
import com.styl.pacific.store.shared.http.enums.DeviceStatus;
import com.styl.pacific.store.shared.http.enums.DeviceType;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.time.Instant;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "tb_devices")
public class DeviceEntity extends AuditableEntity {

	@Id
	private String id;

	private String name;

	private Long storeId;

	private Long tenantId;

	private String serialNumber;

	private String model;

	private String firmwareVersion;

	private String osVersion;

	@Enumerated(EnumType.STRING)
	private DeviceType type;

	@Enumerated(EnumType.STRING)
	private DeviceStatus status;

	private Instant assignedDate;

	private Instant lastOnline;

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (!(o instanceof DeviceEntity that))
			return false;
		if (!super.equals(o))
			return false;
		return Objects.equals(id, that.id) && Objects.equals(name, that.name) && Objects.equals(storeId, that.storeId)
				&& Objects.equals(tenantId, that.tenantId) && Objects.equals(serialNumber, that.serialNumber) && Objects
						.equals(model, that.model) && Objects.equals(firmwareVersion, that.firmwareVersion) && Objects
								.equals(osVersion, that.osVersion) && type == that.type && status == that.status
				&& Objects.equals(assignedDate, that.assignedDate) && Objects.equals(lastOnline, that.lastOnline);
	}

	@Override
	public int hashCode() {
		return Objects.hash(super.hashCode(), id, name, storeId, tenantId, serialNumber, model, firmwareVersion,
				osVersion, type, status, assignedDate, lastOnline);
	}
}
