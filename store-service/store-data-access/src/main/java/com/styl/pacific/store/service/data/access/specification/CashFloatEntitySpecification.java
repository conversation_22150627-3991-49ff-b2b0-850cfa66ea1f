/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.store.service.data.access.specification;

import com.styl.pacific.data.access.jpa.specification.BaseSpecification;
import com.styl.pacific.store.service.data.access.entity.CashFloatEntity;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.io.Serial;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import lombok.Builder;

/**
 * <AUTHOR>
 */
@Builder
public class CashFloatEntitySpecification extends BaseSpecification<CashFloatEntity> {
	@Serial
	private static final long serialVersionUID = 1L;

	private Long cashFloatId;

	private Long staffId;

	private Long storeId;

	private Long tenantId;

	private String staffName;

	private String staffCode;

	private Instant fromTime;

	private Instant toTime;

	@Override
	public Predicate toPredicate(Root<CashFloatEntity> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
		List<Predicate> predicates = new ArrayList<>();

		if (isNotNull(cashFloatId)) {
			predicates.add(equals(criteriaBuilder, root.get("id"), cashFloatId));
		}

		if (isNotNull(staffId)) {
			predicates.add(equals(criteriaBuilder, root.get("staffId"), staffId));
		}

		if (isNotNull(tenantId)) {
			predicates.add(equals(criteriaBuilder, root.get("tenantId"), tenantId));
		}

		if (isNotNull(storeId)) {
			predicates.add(equals(criteriaBuilder, root.get("storeId"), storeId));
		}

		if (isNotBlank(staffName)) {
			predicates.add(like(criteriaBuilder, root.get("staffName"), staffName));
		}

		if (isNotBlank(staffCode)) {
			predicates.add(like(criteriaBuilder, root.get("staffCode"), staffCode));
		}

		if (isNotNull(fromTime)) {
			predicates.add(greaterThanOrEqualTo(criteriaBuilder, root.get("createdAt"), fromTime));
		}

		if (isNotNull(toTime)) {
			predicates.add(lessThanOrEqualTo(criteriaBuilder, root.get("createdAt"), toTime));
		}

		return and(criteriaBuilder, predicates);
	}
}
