/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.loadtest.commons.strategies;

import com.styl.pacific.loadtest.commons.LoadTestConfigProperties;
import com.styl.pacific.loadtest.commons.exceptions.ApiLoadTestStrategyException;
import com.styl.pacific.loadtest.commons.strategies.config.ConstantUserPerSecondConfigProperties;
import com.styl.pacific.loadtest.commons.strategies.config.ExplosiveUserConfigProperties;
import com.styl.pacific.loadtest.commons.strategies.config.RampUserPerSecondConfigProperties;
import io.gatling.javaapi.core.ClosedInjectionStep;
import io.gatling.javaapi.core.CoreDsl;
import io.gatling.javaapi.core.OpenInjectionStep;
import java.util.Arrays;
import java.util.Optional;
import java.util.function.Function;
import org.apache.commons.lang3.StringUtils;

public enum ApiLoadTestStrategy {
	CONSTANT_USERS_PER_SEC(strategyProperty -> {
		final var config = strategyProperty.parseConfig(ConstantUserPerSecondConfigProperties.class);
		return CoreDsl.constantUsersPerSec(config.getUserRate())
				.during(config.getDuration());
	}, null),
	CONSTANT_CONCURRENT_USERS(null, strategyProperty -> {
		final var config = strategyProperty.parseConfig(ConstantUserPerSecondConfigProperties.class);
		return CoreDsl.constantConcurrentUsers(config.getUserRate())
				.during(config.getDuration());
	}),
	RAMP_USERS_PER_SEC(strategyProperty -> {
		final var config = strategyProperty.parseConfig(RampUserPerSecondConfigProperties.class);
		return CoreDsl.rampUsersPerSec(config.getFromUserRate())
				.to(config.getToUserRate())
				.during(config.getDuration());
	}, null),
	EXPLOSION_USERS(strategyProperty -> {
		final var config = strategyProperty.parseConfig(ExplosiveUserConfigProperties.class);
		return CoreDsl.atOnceUsers(config.getUsersRate());
	}, null);

	private final Function<LoadTestConfigProperties.ApiLoadTestStrategyProperties, OpenInjectionStep> buildOpenStrategyFunc;
	private final Function<LoadTestConfigProperties.ApiLoadTestStrategyProperties, ClosedInjectionStep> buildClosedStrategyFunc;

	ApiLoadTestStrategy(
			Function<LoadTestConfigProperties.ApiLoadTestStrategyProperties, OpenInjectionStep> buildOpenStrategyFunc,
			Function<LoadTestConfigProperties.ApiLoadTestStrategyProperties, ClosedInjectionStep> buildClosedStrategyFunc) {
		this.buildOpenStrategyFunc = buildOpenStrategyFunc;
		this.buildClosedStrategyFunc = buildClosedStrategyFunc;

		if ((buildOpenStrategyFunc != null && buildClosedStrategyFunc != null) || (buildOpenStrategyFunc == null
				&& buildClosedStrategyFunc == null)) {
			throw new ApiLoadTestStrategyException("Invalid API Load Strategy Config");
		}
	}

	public static ApiLoadTestStrategy of(String strategy) {
		return Optional.ofNullable(strategy)
				.filter(StringUtils::isNotBlank)
				.map(String::toUpperCase)
				.map(String::trim)
				.map(str -> Arrays.stream(values())
						.filter(it -> it.name()
								.equals(str))
						.findFirst()
						.orElseThrow(() -> new ApiLoadTestStrategyException("Strategy [%s] not found".formatted(
								strategy))))
				.orElseThrow(() -> new ApiLoadTestStrategyException("Strategy [%s] not found".formatted(strategy)));
	}

	public OpenInjectionStep buildOpenStrategy(
			LoadTestConfigProperties.ApiLoadTestStrategyProperties strategyProperty) {
		return buildOpenStrategyFunc.apply(strategyProperty);
	}

	public boolean isOpenOrCloseInject() {
		return buildOpenStrategyFunc != null;
	}

	public ClosedInjectionStep buildClosedStrategy(
			LoadTestConfigProperties.ApiLoadTestStrategyProperties strategyProperty) {
		return buildClosedStrategyFunc.apply(strategyProperty);
	}
}
