/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.loadtest.order.apis.ewallet;

import static io.gatling.javaapi.core.CoreDsl.StringBody;
import static io.gatling.javaapi.core.CoreDsl.exec;
import static io.gatling.javaapi.core.CoreDsl.jsonPath;
import static io.gatling.javaapi.core.CoreDsl.pause;
import static io.gatling.javaapi.http.HttpDsl.http;
import static io.gatling.javaapi.http.HttpDsl.status;

import com.styl.pacific.common.constant.PacificRestConstants;
import com.styl.pacific.loadtest.commons.ApiConnectionsConfigProperties;
import com.styl.pacific.loadtest.commons.LoadTestApi;
import com.styl.pacific.loadtest.commons.objectmapper.LoadTestMapper;
import com.styl.pacific.loadtest.commons.utils.RandomUtils;
import com.styl.pacific.loadtest.order.apis.ewallet.mapper.CreateCoreGatewayOrderEwalletManifestRequestMapper;
import com.styl.pacific.payment.shared.enums.PaymentTransactionType;
import com.styl.pacific.payment.shared.http.sessions.request.CreatePaymentSessionRequest;
import com.styl.pacific.payment.shared.http.sessions.request.ewallet.EWalletPaymentSessionParamsRequest;
import com.styl.pacific.wallet.service.requests.wallet.CreditRequest;
import io.gatling.javaapi.core.ChainBuilder;
import java.math.BigInteger;
import java.time.Duration;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;

@RequiredArgsConstructor
public class CoreGatewayOrderEWalletPaymentFlowApi implements LoadTestApi {
	private final ApiConnectionsConfigProperties apiConnection;

	private final CoreGwOrderEWalletPaymentConfigProperties apiConfigProperties;

	private static final String RETRY_COUNT = "RETRY_COUNT";
	private static final String PAYMENT_REF = "PAYMENT_REF";
	private static final int MAX_RETRY = 20;
	private static final int BASE_RETRY_MILLISECONDS = 250;
	private static final String ORDER_STATUS = "ORDER_STATUS";
	private static final String PAYMENT_SESSION_ID = "PAYMENT_SESSION_ID";

	@SneakyThrows
	@Override
	public ChainBuilder buildChain() {
		final var orderRequest = CreateCoreGatewayOrderEwalletManifestRequestMapper.INSTANCE.toPlaceOrderRequest(
				apiConfigProperties);
		final var rates = RandomUtils.generateDiminishingReturns(0.3, MAX_RETRY);
		return exec(http("PlaceOrderAPI").post("/api/order/orders/place-order")
				.header("Authorization", apiConnection.getUserToken())
				.header(PacificRestConstants.PlatformHeader.HEADER_X_TENANT_ID, apiConfigProperties.getTenantId())
				.body(StringBody(LoadTestMapper.JSON_MAPPER.writeValueAsString(orderRequest)))
				.check(status().is(201))
				.check(jsonPath("$.orderId").saveAs("ORDER_ID"))).exitHereIfFailed()
				.exec(session -> session.set(ORDER_STATUS, "CREATED")
						.set(RETRY_COUNT, 0))
				.asLongAs(session -> Optional.ofNullable(session.get(RETRY_COUNT))
						.map(it -> (Integer) it)
						.orElse(0) < MAX_RETRY + 1 && Optional.ofNullable(session.get(ORDER_STATUS))
								.map(String::valueOf)
								.filter("CREATED"::equals)
								.isPresent())
				.on(pause(session -> Duration.ofMillis(BASE_RETRY_MILLISECONDS + (int) (BASE_RETRY_MILLISECONDS * rates
						.get(Optional.ofNullable(session.get(RETRY_COUNT))
								.map(it -> (int) it)
								.orElse(0))))), exec(http("GetOrderAPI").get("/api/order/orders/#{ORDER_ID}")
										.header("Authorization", apiConnection.getUserToken())
										.header(PacificRestConstants.PlatformHeader.HEADER_X_TENANT_ID,
												apiConfigProperties.getTenantId())
										.check(jsonPath("$.paymentRef").saveAs(PAYMENT_REF))
										.check(jsonPath("$.status").saveAs(ORDER_STATUS))).exec(session -> session.set(
												RETRY_COUNT, Optional.ofNullable(session.get(RETRY_COUNT))
														.map(it -> (Integer) it)
														.orElse(0) + 1)))
				.doIf(session -> StringUtils.isNotBlank(session.get(PAYMENT_REF)))
				.then(exec(http("CreatePaymentSessionApi").post("/api/payment/sessions")
						.header("Authorization", apiConnection.getUserToken())
						.header(PacificRestConstants.PlatformHeader.HEADER_X_TENANT_ID, apiConfigProperties
								.getTenantId())
						.body(StringBody(LoadTestMapper.JSON_MAPPER.writeValueAsString(CreatePaymentSessionRequest
								.builder()
								.paymentMethodId(Long.valueOf(apiConfigProperties.getEwalletPaymentMethodId()))
								.description("Load Test EWallet #Payment Method %s".formatted(apiConfigProperties
										.getWalletId()))
								.paymentReference("#{PAYMENT_REF}")
								.transactionType(PaymentTransactionType.PURCHASE)
								.amount(orderRequest.order()
										.getTotalAmount()
										.longValue())
								.netAmount(orderRequest.order()
										.getTotalAmount()
										.longValue())
								.fee(0L)
								.customerId(apiConfigProperties.getCustomerId())
								.currencyCode("SGD")
								.customerEmail(apiConfigProperties.getCustomerEmail())
								.customerName(apiConfigProperties.getCustomerName())
								.systemSource("LoadTest")
								.processorParams(EWalletPaymentSessionParamsRequest.builder()
										.build())
								.build())))
						.check(status().is(201))
						.check(jsonPath("$.paymentSessionId").saveAs(PAYMENT_SESSION_ID))).exitHereIfFailed()
						.doIf(session -> StringUtils.isNotBlank(session.get(PAYMENT_SESSION_ID)))
						.then(exec(http("EwalletPayApi").post("/api/wallet/wallets/%s/pay".formatted(apiConfigProperties
								.getWalletId()))
								.header("Authorization", apiConnection.getUserToken())
								.header(PacificRestConstants.PlatformHeader.HEADER_X_TENANT_ID, apiConfigProperties
										.getTenantId())
								.body(StringBody(LoadTestMapper.JSON_MAPPER.writeValueAsString(CreditRequest.builder()
										.paymentSessionId("#{PAYMENT_SESSION_ID}")
										.currency("SGD")
										.amount(BigInteger.valueOf(orderRequest.order()
												.getTotalAmount()
												.longValue()))
										.acceptDelegatedWallet(false)
										.description("LoadTest")
										.build()))))));

	}
}
