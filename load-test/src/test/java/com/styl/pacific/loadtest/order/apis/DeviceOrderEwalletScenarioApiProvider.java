/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.loadtest.order.apis;

import com.styl.pacific.loadtest.commons.ApiConnectionsConfigProperties;
import com.styl.pacific.loadtest.commons.LoadTestApi;
import com.styl.pacific.loadtest.commons.ScenarioApiLoadTestProvider;
import com.styl.pacific.loadtest.order.OrderApiLoadTestConfigProperties;
import com.styl.pacific.loadtest.order.apis.ewallet.DeviceOrderEwalletPaymentFlowLoadTestApi;
import io.gatling.javaapi.core.ChainBuilder;
import java.util.List;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class DeviceOrderEwalletScenarioApiProvider implements ScenarioApiLoadTestProvider {

	private final ApiConnectionsConfigProperties apiConnection;
	private final OrderApiLoadTestConfigProperties apiLoadTestConfigs;

	@Override
	public String getScenarioName() {
		return "DeviceOrderPaymentCashFlowApis";
	}

	@Override
	public List<ChainBuilder> buildChainApis() {
		return Stream.of(new DeviceOrderEwalletPaymentFlowLoadTestApi(apiConnection, apiLoadTestConfigs
				.getDeviceOrderPaymentEwalletFlow()))
				.map(LoadTestApi::buildChain)
				.toList();
	}

}