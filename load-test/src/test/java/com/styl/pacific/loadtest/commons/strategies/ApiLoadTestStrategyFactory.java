/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.loadtest.commons.strategies;

import com.styl.pacific.loadtest.commons.LoadTestConfigProperties;
import com.styl.pacific.loadtest.commons.exceptions.ApiLoadTestStrategyException;
import io.gatling.javaapi.core.ClosedInjectionStep;
import io.gatling.javaapi.core.OpenInjectionStep;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ApiLoadTestStrategyFactory {

	private final Set<LoadTestConfigProperties.ApiLoadTestStrategyProperties> strategyProperties = new HashSet<>();

	private static ApiLoadTestStrategyFactory instance;

	public static synchronized void init(
			Set<LoadTestConfigProperties.ApiLoadTestStrategyProperties> strategyProperties) {
		instance = Optional.ofNullable(instance)
				.orElseGet(ApiLoadTestStrategyFactory::new);
		instance.strategyProperties.addAll(strategyProperties);
	}

	public static ApiLoadTestStrategyFactory getInstance() {
		if (instance == null) {
			throw new ApiLoadTestStrategyException("Please init load test strategy first");
		}
		return instance;
	}

	public boolean isOpenOrCloseInjectStrategy() {
		final var strategyProperty = getEnvironmentStrategyConfig();
		return strategyProperty.getStrategy()
				.isOpenOrCloseInject();
	}

	private LoadTestConfigProperties.ApiLoadTestStrategyProperties getEnvironmentStrategyConfig() {
		return Optional.ofNullable(System.getenv("LOAD_TEST_STRATEGY"))
				.map(ApiLoadTestStrategy::of)
				.map(it -> strategyProperties.stream()
						.filter(st -> st.getStrategy()
								.equals(it))
						.findFirst()
						.orElseThrow(() -> new ApiLoadTestStrategyException("Not found default strategy")))
				.orElseGet(() -> strategyProperties.stream()
						.filter(it -> Boolean.TRUE.equals(it.getIsDefault()))
						.findFirst()
						.orElseThrow(() -> new ApiLoadTestStrategyException("Not found default strategy")));
	}

	public OpenInjectionStep buildOpenStrategy() {
		final var strategyProperty = getEnvironmentStrategyConfig();
		return strategyProperty.getStrategy()
				.buildOpenStrategy(strategyProperty);
	}

	public ClosedInjectionStep buildClosedStrategy() {
		final var strategyProperty = getEnvironmentStrategyConfig();
		return strategyProperty.getStrategy()
				.buildClosedStrategy(strategyProperty);

	}
}
