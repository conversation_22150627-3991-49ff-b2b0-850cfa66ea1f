
# API LOAD TESTING RUNNING STEPS

1. Select a profile. Example: UAT
2. Replace the user access token to simulate the customer access in [load-test-uat.yaml](src%2Ftest%2Fresources%2Fload-test-uat.yaml)
4. Prepare data load test properly in the target yaml config
5. Execute as below command with 2 environment properties LOAD_TEST_PROFILE, LOAD_TEST_STRATEGY.
6. Note: Please fill descriptions (No format rule) to make easy understanding, and write out logs to file for tracing.

## Windows:
```
set LOAD_TEST_PROFILE=UAT 
    && set LOAD_TEST_STRATEGY=CONSTANT_USERS_PER_SEC 
    && mvn -pl load-test -am gatling:test
    -Dgatling.simulationClass=com.styl.pacific.loadtest.order.DeviceLoadTestOrderAPIsSimulation 
    -Dgatling.runDescription="strategy: CONSTANT_USERS_PER_SEC; userRate: 5; duration: PT5M"
        > 20241231-uat-CONSTANT_USERS_PER_SEC.logs
```
###