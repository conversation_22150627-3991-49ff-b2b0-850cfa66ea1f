/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.common.constant;

public class PacificRestConstants {

	private PacificRestConstants() {
	}

	public static class PlatformHeader {

		public static final String HEADER_X_TENANT_ID = "X-Tenant-ID";
		public static final String HEADER_X_USER_ID = "X-User-ID";
		public static final String HEADER_X_REQUEST_ID = "X-Request-ID";
		public static final String HEADER_X_TRACE_ID = "X-Trace-ID";
		public static final String HEADER_X_SESSION_ID = "X-Session-ID";

		private PlatformHeader() {
		}
	}
}
