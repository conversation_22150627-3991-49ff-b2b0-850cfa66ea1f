/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.aws.s3.config;

import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.http.urlconnection.UrlConnectionHttpClient;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3ClientBuilder;
import software.amazon.awssdk.services.s3.S3Configuration;

/**
 * <AUTHOR>
 */

@AllArgsConstructor
@Configuration
public class S3ClientConfiguration {

	private final S3ConfigProperties s3ConfigProperties;

	@Bean
	public S3Client s3Client() {
		S3ClientBuilder builder = S3Client.builder()
				.region(s3ConfigProperties.getRegion());
		if (s3ConfigProperties.getEndpoint() != null) {
			builder.endpointOverride(s3ConfigProperties.getEndpoint());
			builder.serviceConfiguration(S3Configuration.builder()
					.pathStyleAccessEnabled(true)
					.build());
		}
		if (StringUtils.hasText(s3ConfigProperties.getAccessKey()) && StringUtils.hasText(s3ConfigProperties
				.getSecretKey())) {
			builder.credentialsProvider(() -> AwsBasicCredentials.create(s3ConfigProperties.getAccessKey(),
					s3ConfigProperties.getSecretKey()));
		} else {
			builder.credentialsProvider(DefaultCredentialsProvider.create());
		}
		builder.httpClient(UrlConnectionHttpClient.create());
		return builder.build();
	}

}
