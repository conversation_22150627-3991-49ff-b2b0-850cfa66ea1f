/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.kafka.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 *
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "kafka-consumer-config")
public class KafkaConsumerConfigProps {
	private String keyDeserializer;
	private String valueDeserializer;
	private String autoOffsetReset;
	private String specificAvroReaderKey;
	private String specificAvroReader;
	private Boolean batchListener;
	private Boolean autoStartup;
	private Integer concurrencyLevel;
	private Integer sessionTimeoutMs;
	private Integer heartbeatIntervalMs;
	private Integer maxPollIntervalMs;
	private Long pollTimeoutMs;
	private Integer maxPollRecords;
	private Integer maxPartitionFetchBytesDefault;
	private Integer maxPartitionFetchBytesBoostFactor;
}
