{"namespace": "com.styl.pacific.kafka.order.avro.model", "type": "record", "name": "OrderDataChangedAvroEvent", "fields": [{"name": "id", "type": {"type": "string", "logicalType": "uuid"}}, {"name": "data", "type": {"type": "record", "name": "OrderAvro", "fields": [{"name": "id", "type": {"type": "string"}}, {"name": "tenantId", "type": {"type": "string"}}, {"name": "version", "type": {"type": "int"}}, {"name": "orderNumber", "type": {"type": "string"}}, {"name": "storeId", "type": {"type": "string"}}, {"name": "preOrderId", "type": ["null", "string"]}, {"name": "mealTimeId", "type": ["null", "string"]}, {"name": "staffCode", "type": ["null", "string"]}, {"name": "staffName", "type": ["null", "string"]}, {"name": "issuerId", "type": ["null", "string"]}, {"name": "customerId", "type": ["null", "string"]}, {"name": "customerEmail", "type": ["null", "string"]}, {"name": "customerName", "type": ["null", "string"]}, {"name": "status", "type": {"type": "string"}}, {"name": "type", "type": {"type": "string"}}, {"name": "lineItems", "type": {"type": "array", "items": {"type": "record", "name": "OrderLineItem", "fields": [{"name": "id", "type": {"type": "string"}}, {"name": "orderId", "type": {"type": "string"}}, {"name": "productId", "type": {"type": "string"}}, {"name": "menuItemId", "type": ["null", "string"], "default": null}, {"name": "preOrderMenuItemId", "type": ["null", "string"], "default": null}, {"name": "productName", "type": {"type": "string"}}, {"name": "metadata", "type": {"type": "string"}}, {"name": "quantity", "type": {"type": "int"}}, {"name": "note", "type": ["null", "string"]}, {"name": "unitPrice", "type": {"type": "long"}}, {"name": "optionPrice", "type": {"type": "long"}}, {"name": "totalDiscount", "type": {"type": "long"}}, {"name": "totalAmount", "type": {"type": "long"}}]}}}, {"name": "note", "type": ["null", "string"]}, {"name": "taxName", "type": ["null", "string"]}, {"name": "taxRate", "type": ["null", {"type": "bytes", "logicalType": "decimal", "precision": 5, "scale": 4}]}, {"name": "taxInclude", "type": ["null", "boolean"]}, {"name": "taxAmount", "type": ["null", "long"]}, {"name": "subtotalAmount", "type": {"type": "long"}}, {"name": "discountAmount", "type": {"type": "long"}}, {"name": "totalAmount", "type": {"type": "long"}}, {"name": "currencyCode", "type": {"type": "string"}}, {"name": "paymentMethodId", "type": {"type": "string"}}, {"name": "paymentTransactionId", "type": ["null", "string"]}, {"name": "paymentStatus", "type": ["null", "string"]}, {"name": "paymentRef", "type": ["null", "string"]}, {"name": "nonce", "type": ["null", "string"]}, {"name": "transactionRef", "type": ["null", "string"]}, {"name": "terminalId", "type": ["null", "string"]}, {"name": "cancellationDueAt", "type": ["null", "long"]}, {"name": "cancelReason", "type": ["null", "string"]}, {"name": "canceledAt", "type": ["null", "long"]}, {"name": "expiredAt", "type": ["null", "long"]}, {"name": "createdAt", "type": {"type": "long"}}, {"name": "updatedAt", "type": {"type": "long"}}]}}, {"name": "eventTime", "type": {"type": "long"}}]}