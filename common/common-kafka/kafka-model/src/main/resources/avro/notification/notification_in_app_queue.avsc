{"namespace": "com.styl.pacific.kafka.notification.avro.model", "type": "record", "name": "InAppNotificationCreatedAvroEvent", "fields": [{"name": "id", "type": {"type": "string", "logicalType": "uuid"}}, {"name": "userId", "type": {"type": "long"}}, {"name": "tenantId", "type": ["null", "long"], "default": null}, {"name": "source", "type": {"type": "string"}}, {"name": "content", "type": ["null", "string"], "default": null}, {"name": "data", "type": ["null", {"type": "map", "values": "string"}], "default": null}, {"name": "title", "type": ["null", "string"], "default": null}, {"name": "action", "type": {"type": "string"}}, {"name": "createdAt", "type": {"type": "long"}}]}