/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.kafka.producer.service;

import java.io.Serializable;
import java.util.function.BiConsumer;
import org.apache.avro.specific.SpecificRecordBase;
import org.springframework.kafka.support.SendResult;

/**
 * <AUTHOR>
 */
public interface KafkaProducer<K extends Serializable, V extends SpecificRecordBase> {
	void send(String topicName, K key, V message, BiConsumer<SendResult<K, V>, Throwable> callback);

	void send(String topicName, Integer partitionKey, K key, V message,
			BiConsumer<SendResult<K, V>, Throwable> callback);
}
