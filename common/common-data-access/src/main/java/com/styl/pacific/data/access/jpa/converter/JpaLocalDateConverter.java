/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.data.access.jpa.converter;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import java.time.LocalDate;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Converter
public class JpaLocalDateConverter implements AttributeConverter<LocalDate, LocalDate> {

	@Override
	public LocalDate convertToDatabaseColumn(LocalDate localDate) {
		if (Objects.isNull(localDate)) {
			return null;
		}
		return localDate;
	}

	@Override
	public LocalDate convertToEntityAttribute(LocalDate date) {
		if (Objects.isNull(date)) {
			return null;
		}
		return date;
	}
}
