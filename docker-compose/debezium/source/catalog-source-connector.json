{"config": {"connector.class": "io.debezium.connector.postgresql.PostgresConnector", "database.dbname": "pacific", "database.history.kafka.bootstrap.servers": "kafka-broker-1:9092,kafka-broker-2:9092,kafka-broker-3:9092", "database.history.kafka.topic": "schema-changes.catalog", "database.hostname": "postgres", "database.password": "postgres", "database.port": "5432", "database.server.name": "postgres", "database.user": "postgres", "snapshot.mode": "always", "name": "catalog-source-connector", "plugin.name": "pgoutput", "schema.include.list": "catalog-service", "table.include.list": "catalog-service.tb_product,catalog-service.tb_category,catalog-service.tb_healthier_choice,catalog-service.tb_product_image,catalog-service.tb_product_allergen,catalog-service.tb_product_nutrition,catalog-service.tb_product_option,catalog-service.tb_product_option_item", "tasks.max": "1", "topic.creation.default.cleanup.policy": "delete", "topic.creation.default.partitions": "1", "topic.creation.default.replication.factor": "1", "topic.creation.default.retention.ms": "604800000", "topic.creation.enable": "true", "topic.prefix": "cdc.catalog"}, "name": "catalog-source-connector"}