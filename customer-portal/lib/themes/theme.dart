import "package:flutter/material.dart";
import "package:pacific_2_customer_portal/themes/colors.dart";
import "package:pacific_2_customer_portal/themes/typography.dart";

class MaterialTheme {
  ThemeData light() {
    return theme(lightScheme);
  }

  ThemeData theme(ColorScheme colorScheme) => ThemeData(
        useMaterial3: true,
        brightness: colorScheme.brightness,
        colorScheme: colorScheme,
        fontFamily: 'Onest',
        textTheme: textTheme.apply(
          bodyColor: colorScheme.onSurface,
          displayColor: colorScheme.onSurface,
        ),
        scaffoldBackgroundColor: colorScheme.surfaceContainer,
        canvasColor: colorScheme.surface,
      );
}
