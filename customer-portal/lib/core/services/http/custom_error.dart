// coverage:ignore-file
import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:pacific_2_customer_portal/routes/app.routes.dart';

enum ErrorCode {
  checkoutCutOff(code: 50043),
  checkoutInsufficient(code: 50044),
  checkoutInvalidPreorderItemInfo(code: 50054),
  checkoutUnavailableItem(code: 50055),
  checkoutInvalidPreorder(code: 50056);

  const ErrorCode({required this.code});

  final int code;
}

Map<int, String> customErrorMessage = {
  ErrorCode.checkoutCutOff.code: 'cutoffErrorMessage',
  ErrorCode.checkoutInsufficient.code: 'insufficientErrorMessage',
  ErrorCode.checkoutInvalidPreorderItemInfo.code:
      'invalidPreorderItemInfoMessage',
  ErrorCode.checkoutUnavailableItem.code: 'itemUnavailableMessage',
  ErrorCode.checkoutInvalidPreorder.code: 'invalidPreorderMessage',
};

String getErrorMessage(int errorCode, {BuildContext? context}) {
  String? errorKey = customErrorMessage[errorCode];
  BuildContext ctx = context ?? navigatorKey.currentContext!;

  return errorKey != null
      ? FlutterI18n.translate(ctx, errorKey)
      : FlutterI18n.translate(
          ctx,
          'commonErrorMessage',
          translationParams: {"errorCode": '$errorCode'},
        );
}
