import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:flutter_svg/svg.dart';

class SelectedProduct extends StatelessWidget {
  const SelectedProduct({
    super.key,
    required this.selectedProducts,
    required this.onRemoveProduct,
  });

  final List<String> selectedProducts;
  final Function(String item) onRemoveProduct;

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.only(top: 12),
      padding: const EdgeInsets.only(top: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: theme.colorScheme.surface,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: Text(
              FlutterI18n.translate(context, 'items'),
              style: theme.textTheme.titleMedium,
            ),
          ),
          Flexible(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              shrinkWrap: true,
              itemCount: selectedProducts.length,
              itemBuilder: (ctx, index) {
                final item = selectedProducts[index];

                return Container(
                  height: 48,
                  decoration: BoxDecoration(
                    border: Border(
                      top: index == 0 ? BorderSide.none : BorderSide(color: theme.colorScheme.outline),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(item, style: theme.textTheme.bodyMedium),
                      ),
                      const SizedBox(width: 8),
                      IconButton(
                        onPressed: () => onRemoveProduct(item),
                        icon: SvgPicture.asset(
                          'assets/images/ic-trash.svg',
                          width: 24,
                        ),
                        padding: EdgeInsets.zero,
                      ),
                    ],
                  ),
                );
              },
            ),
          )
        ],
      ),
    );
  }
}
