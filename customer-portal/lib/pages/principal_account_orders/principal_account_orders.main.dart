// coverage:ignore-file
import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:get/get.dart';
import 'package:pacific_2_customer_portal/common/responsive/responsive.dart';
import 'package:pacific_2_customer_portal/common/widgets/bread_crumbs/bread_crumbs.dart';
import 'package:pacific_2_customer_portal/common/widgets/title_row/title_row.dart';
import 'package:pacific_2_customer_portal/common/widgets/user_avatar/user_avatar.dart';
import 'package:pacific_2_customer_portal/core/constants/system_const.dart';
import 'package:pacific_2_customer_portal/models/user_profile.model.dart';
import 'package:pacific_2_customer_portal/pages/order_details/order_details.main.dart';
import 'package:pacific_2_customer_portal/pages/principal_account_orders/principal_account_orders.controller.dart';
import 'package:pacific_2_customer_portal/pages/principal_account_orders/widgets/empty_view.dart';
import 'package:pacific_2_customer_portal/pages/principal_account_orders/widgets/order_item_block.dart';
import 'package:pacific_2_customer_portal/pages/principal_account_orders/widgets/summary_date_list.dart';
import 'package:pacific_2_customer_portal/routes/app.routes.dart';
import 'package:pacific_2_customer_portal/services/catalog.service.dart';
import 'package:pacific_2_customer_portal/services/order.service.dart';

class PrincipalAccountOrders extends StatefulWidget {
  const PrincipalAccountOrders({super.key, required this.user});

  final UserProfile user;

  @override
  State<PrincipalAccountOrders> createState() => _PrincipalAccountOrdersState();
}

class _PrincipalAccountOrdersState extends State<PrincipalAccountOrders> {
  late final PrincipalAccountOrdersController _ordersController =
      Get.put(PrincipalAccountOrdersController(OrderService(), CatalogService()));

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _ordersController.setUser(widget.user);
    });
    super.initState();
  }

  @override
  void dispose() {
    Get.delete<PrincipalAccountOrdersController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Responsive(
        mobile: _renderMobile(context),
        desktop: _renderDesktop(context),
      ),
    );
  }

  Widget _renderDesktop(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ..._renderTitleRow(context),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(flex: 1, child: _renderHeader(context)),
                const SizedBox(width: 24),
                Expanded(flex: 2, child: _renderOrderList(context)),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _renderMobile(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ..._renderTitleRow(context),
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
          child: _renderHeader(context),
        ),
        Expanded(
          child: _renderOrderList(
            context,
            padding: const EdgeInsets.symmetric(horizontal: 16),
          ),
        ),
      ],
    );
  }

  List<Widget> _renderTitleRow(BuildContext context) {
    final double paddingHor = Responsive.isDesktop(context) ? 32 : 16;
    final double paddingBot = Responsive.isDesktop(context) ? 22 : 12;
    return [
      Padding(
        padding: EdgeInsets.fromLTRB(paddingHor, 16, paddingHor, 4),
        child: BreadCrumbs(path: [
          FlutterI18n.translate(context, 'principalAccount'),
          FlutterI18n.translate(context, 'viewPreorder'),
        ]),
      ),
      Padding(
        padding: EdgeInsets.fromLTRB(paddingHor, 0, paddingHor, paddingBot),
        child: TitleRow(
          title: FlutterI18n.translate(context, 'preorderDetails'),
          onBackPressed: () => NavigationService.pop(
            context,
            route: '/${SystemConst.ROUTE['SUB_ACCOUNT']!['MAIN']!}',
          ),
        ),
      ),
    ];
  }

  Widget _renderHeader(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          children: [
            UserAvatar(url: widget.user.avatar?.url, size: 40),
            const SizedBox(width: 8),
            Expanded(child: Text(widget.user.getFullName(), style: theme.textTheme.bodyMedium)),
          ],
        ),
        const SizedBox(height: 12),
        SummaryDateList(
          focusedDate: _ordersController.focusedDate.value,
          selectedDate: _ordersController.selectedDate.value,
          onSelectDate: _ordersController.onSelectDate,
          onPrevious: _ordersController.onPreviousWeek,
          onNext: _ordersController.onNextWeek,
          onToday: _ordersController.onToday,
        ),
      ],
    );
  }

  Widget _renderOrderList(BuildContext context, {EdgeInsets padding = EdgeInsets.zero}) {
    return ListView(
      physics: const AlwaysScrollableScrollPhysics(),
      padding: padding,
      children: _ordersController.orderList.isEmpty
          ? const [EmptyOrderView()]
          : _ordersController.orderList
              .map((item) => OrderItemBlock(
                    item: item,
                    onCancelOrder: () => handlePressCancelOrder(
                      context,
                      totalAmount: item.totalAmount!,
                      cancelOrder: () => _ordersController.cancelOrder(item.id!),
                    ),
                  ))
              .toList(),
    );
  }
}
