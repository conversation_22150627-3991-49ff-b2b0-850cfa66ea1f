import 'package:get/get.dart';
import 'package:pacific_2_customer_portal/controllers/loading.controller.dart';
import 'package:pacific_2_customer_portal/models/wallet_transaction.model.dart';
import 'package:pacific_2_customer_portal/services/wallet.service.dart';

class TransactionDetailsController extends GetxController {
  final WalletService _walletService;
  final LoadingController _loadingController = Get.find<LoadingController>();

  TransactionDetailsController(this._walletService);

  String transactionId = '';
  Rxn<WalletTransaction> transactionItem = Rxn();

  void setTransactionId(String id) {
    transactionId = id;
    if (transactionId.isNotEmpty) getTransactionDetails();
  }

  Future<void> getTransactionDetails() async {
    _loadingController.showLoading(true);
    try {
      final response =
          await _walletService.getTransactionDetails(transactionId);
      final Map<String, dynamic> data = Map.from(response);
      transactionItem.value = WalletTransaction.fromJson(data);
    } catch (e) {/** */}
    _loadingController.showLoading(false);
  }
}
