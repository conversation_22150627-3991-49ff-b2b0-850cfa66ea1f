import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/main_button.dart';
import 'package:pacific_2_customer_portal/common/widgets/user_avatar/user_avatar.dart';
import 'package:pacific_2_customer_portal/models/user_profile.model.dart';

class MainAccountDesktopItem extends StatefulWidget {
  const MainAccountDesktopItem({
    super.key,
    required this.item,
    required this.onPressOrder,
  });

  final UserProfile item;
  final Function() onPressOrder;

  @override
  State<MainAccountDesktopItem> createState() => _MainAccountDesktopItemState();
}

class _MainAccountDesktopItemState extends State<MainAccountDesktopItem> {
  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);
    double padding = 64 + 44 * 3 + 228;
    double gridItemWidth = MediaQuery.of(context).size.width - padding;
    return Row(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Container(
            padding: const EdgeInsets.only(left: 12, top: 20, bottom: 20),
            width: 293 * gridItemWidth / (1360 - padding),
            alignment: Alignment.centerLeft,
            child: Row(
              children: [
                const SizedBox(width: 12),
                UserAvatar(url: widget.item.avatar?.url, size: 40),
                const SizedBox(width: 8),
                Expanded(child: Text('${widget.item.getFullName()} (me)', style: theme.textTheme.bodyMedium)),
              ],
            )),
        Container(
          padding: const EdgeInsets.only(left: 12, top: 20, bottom: 20),
          width: 178 * gridItemWidth / (1360 - padding),
          height: 80,
          alignment: Alignment.centerLeft,
          child: Text(widget.item.externalId ?? "-", style: theme.textTheme.bodyMedium),
        ),
        Expanded(
          child: Container(
            padding: const EdgeInsets.only(left: 12, top: 20, bottom: 20),
            width: 435 * gridItemWidth / (1360 - padding),
            height: 80,
            alignment: Alignment.centerLeft,
            child: Text(widget.item.userGroup?.groupName ?? "N/A", style: theme.textTheme.bodyMedium),
          ),
        ),
        const SizedBox(width: 100),
        const SizedBox(width: 44),
        const SizedBox(width: 44, height: 80),
        const SizedBox(width: 44, height: 80),
        Container(
          padding: const EdgeInsets.only(left: 0, right: 10),
          width: 128,
          height: 80,
          alignment: Alignment.centerLeft,
          child: MainButton(
            height: 32,
            width: 118,
            color: theme.colorScheme.primary.withValues(alpha: 0.08),
            text: FlutterI18n.translate(context, 'viewPreorder'),
            textColor: theme.colorScheme.primary,
            textStyle: theme.textTheme.labelSmall,
            onPressed: widget.onPressOrder,
          ),
        ),
      ],
    );
  }
}
