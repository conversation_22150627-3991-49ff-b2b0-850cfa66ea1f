import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:flutter_svg/svg.dart';

class DepositWalletAction extends StatelessWidget {
  const DepositWalletAction({super.key, required this.onPressSpendingLimit});

  final Function() onPressSpendingLimit;

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 10, 16, 8),
          child: Text(
            FlutterI18n.translate(context, 'depositWallet'),
            style: theme.textTheme.headlineSmall,
          ),
        ),
        InkWell(
          onTap: onPressSpendingLimit,
          child: Ink(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                SvgPicture.asset(
                  'assets/images/ic-spending-limit.svg',
                  width: 32,
                  height: 32,
                ),
                const SizedBox(width: 16),
                Text(
                  FlutterI18n.translate(context, 'setSpendingLimit'),
                  style: theme.textTheme.bodyMedium,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
