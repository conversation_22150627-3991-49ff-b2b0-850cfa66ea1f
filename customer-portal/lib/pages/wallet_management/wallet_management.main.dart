// coverage:ignore-file
import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:flutter_portal/flutter_portal.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:pacific_2_customer_portal/common/responsive/responsive.dart';
import 'package:pacific_2_customer_portal/common/widgets/empty_view/empty_view.dart';
import 'package:pacific_2_customer_portal/common/widgets/my_bottom_sheet/my_bottom_sheet.dart';
import 'package:pacific_2_customer_portal/common/widgets/title_row/title_row.dart';
import 'package:pacific_2_customer_portal/controllers/app.controller.dart';
import 'package:pacific_2_customer_portal/controllers/loading.controller.dart';
import 'package:pacific_2_customer_portal/core/constants/system_const.dart';
import 'package:pacific_2_customer_portal/pages/wallet_management/wallet_management.controller.dart';
import 'package:pacific_2_customer_portal/pages/wallet_management/widgets/set_spending_limit.dart';
import 'package:pacific_2_customer_portal/pages/wallet_management/widgets/deposit_wallet_action.dart';
import 'package:pacific_2_customer_portal/pages/wallet_management/widgets/deposit_wallet_item.dart';
import 'package:pacific_2_customer_portal/pages/wallet_management/widgets/funded_wallet_item.dart';
import 'package:pacific_2_customer_portal/common/widgets/transaction_item/transaction_item.dart';
import 'package:pacific_2_customer_portal/pages/wallet_management/widgets/select_wallet_user.dart';
import 'package:pacific_2_customer_portal/pages/wallet_management/widgets/spending_limit.dart';
import 'package:pacific_2_customer_portal/pages/wallet_management/widgets/wallet_account_list.dart';
import 'package:pacific_2_customer_portal/pages/wallet_management/widgets/wallet_delegate_setting.dart';
import 'package:pacific_2_customer_portal/services/user.service.dart';
import 'package:pacific_2_customer_portal/services/wallet.service.dart';
import 'package:pacific_2_customer_portal/utils/general_utils.dart';

class WalletManagement extends StatefulWidget {
  const WalletManagement({super.key});

  @override
  State<WalletManagement> createState() => _WalletManagementState();
}

class _WalletManagementState extends State<WalletManagement> {
  late final WalletManagementController _walletManagementController =
      Get.put(WalletManagementController(UserService(), WalletService()));
  final AppController _appController = Get.find<AppController>();
  final LoadingController _loadingController = Get.find<LoadingController>();

  bool _isSelectAccountVisible = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      waitForAuth(_appController, () {
        _walletManagementController.onUserSelected(_appController.userProfile.value);
      });
    });
  }

  @override
  void dispose() {
    Get.delete<WalletManagementController>();
    super.dispose();
  }

  void showActionsBottomSheet() {
    showMyBottomSheet(
      context,
      () => DepositWalletAction(onPressSpendingLimit: () {
        Navigator.of(context).pop();
        showSpendingLimitBottomSheet();
      }),
      isFullHeight: false,
    );
  }

  void showSpendingLimitBottomSheet() {
    showMyBottomSheet(
      context,
      () => SetSpendingLimit(
        currentLimitDay: _walletManagementController.spendingLimitDay.value,
        currentLimitWeek: _walletManagementController.spendingLimitWeek.value,
        currentLimitMonth: _walletManagementController.spendingLimitMonth.value,
        onSave: (limitDay, limitWeek, limitMonth) {
          final errorKey = _walletManagementController.validateSpendingLimit(limitDay, limitWeek, limitMonth);
          if (errorKey == null) {
            _walletManagementController.setSpendingLimit(limitDay, limitWeek, limitMonth);
            Navigator.of(context).pop();
          } else {
            _loadingController.showAlertDialog(
              context,
              message: FlutterI18n.translate(context, errorKey),
            );
          }
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Responsive(mobile: _renderMobile(context), desktop: _renderDesktop(context)),
    );
  }

  Widget _renderDesktop(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.fromLTRB(32, 16, 32, 0),
      child: Column(
        children: [
          TitleRow(title: FlutterI18n.translate(context, 'wallet')),
          const SizedBox(height: 20),
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  flex: 1,
                  child: SingleChildScrollView(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(color: theme.colorScheme.outline),
                      ),
                      clipBehavior: Clip.hardEdge,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          PortalTarget(
                            visible: _isSelectAccountVisible,
                            portalFollower: GestureDetector(
                              onTap: () => setState(() {
                                _isSelectAccountVisible = false;
                              }),
                            ),
                            child: PortalTarget(
                              visible: _isSelectAccountVisible,
                              anchor: const Aligned(
                                follower: Alignment.topLeft,
                                target: Alignment.bottomLeft,
                              ),
                              portalFollower: WalletAccountList(
                                  isDesktop: true,
                                  selectedAccount: _walletManagementController.selectedUser.value,
                                  onSelectAccount: (user) {
                                    _walletManagementController.onUserSelected(user);
                                    setState(() {
                                      _isSelectAccountVisible = false;
                                    });
                                  }),
                              child: SelectWalletUser(
                                user: _walletManagementController.selectedUser.value,
                                isMainUser: _walletManagementController.isMainUser(),
                                onPress: () => setState(() {
                                  _isSelectAccountVisible = true;
                                }),
                                isFocused: _isSelectAccountVisible,
                              ),
                            ),
                          ),
                          ..._renderWalletList(context),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 24),
                Expanded(
                  flex: 2,
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(color: theme.colorScheme.outline),
                    ),
                    child: ListView(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
                      children: _renderTransactionHistory(context),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _renderMobile(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        TitleRow(title: FlutterI18n.translate(context, 'wallet')),
        const SizedBox(height: 16),
        Text(FlutterI18n.translate(context, 'selectAccount'), style: theme.textTheme.bodyMedium),
        const SizedBox(height: 4),
        SelectWalletUser(
          user: _walletManagementController.selectedUser.value,
          isMainUser: _walletManagementController.isMainUser(),
          onPress: () {
            showMyBottomSheet(
              context,
              () => WalletAccountList(
                selectedAccount: _walletManagementController.selectedUser.value,
                onSelectAccount: _walletManagementController.onUserSelected,
              ),
            );
          },
        ),
        ..._renderWalletList(context),
        const SizedBox(height: 16),
        ..._renderTransactionHistory(context),
      ],
    );
  }

  List<Widget> _renderWalletList(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return [
      if (_walletManagementController.depositWallet.value != null) ...[
        const SizedBox(height: 16),
        Text(
          FlutterI18n.translate(context, 'depositWallet').toUpperCase(),
          style: theme.textTheme.titleLarge!.copyWith(color: theme.colorScheme.onSurfaceVariant),
        ),
        const SizedBox(height: 8),
        DepositWalletItem(
          wallet: _walletManagementController.depositWallet.value!,
          onPressActions: showActionsBottomSheet,
          onPressTopUp: () {
            context.push(
              '/${SystemConst.ROUTE['WALLET']!['TOPUP']!}?walletId=${_walletManagementController.depositWallet.value!.walletId}',
            );
          },
          onPressSpendingLimit: showSpendingLimitBottomSheet,
        ),
        if (_walletManagementController.shouldShowSpendingLimit())
          SpendingLimit(
            currentLimitDay: _walletManagementController.spendingLimitDay.value,
            currentLimitWeek: _walletManagementController.spendingLimitWeek.value,
            currentLimitMonth: _walletManagementController.spendingLimitMonth.value,
            onRemove: () => _walletManagementController.setSpendingLimit(0, 0, 0),
          ),
        if (!_walletManagementController.isMainUser())
          WalletDelegateSetting(
            enabled: _walletManagementController.isDelegateEnabled.value,
            onSwitch: (enable) =>
                enable ? _walletManagementController.enableDelegate() : _walletManagementController.disableDelegate(),
          )
      ],
      if (_walletManagementController.fundedWalletList.isNotEmpty) ...[
        const SizedBox(height: 16),
        Text(
          FlutterI18n.translate(context, 'fundedWallet'),
          style: theme.textTheme.titleLarge!.copyWith(color: theme.colorScheme.onSurfaceVariant),
        ),
        ..._walletManagementController.fundedWalletList.asMap().entries.map((e) => FundedWalletItem(
              wallet: e.value,
              margin: EdgeInsets.only(top: e.key == 0 ? 8 : 12),
            ))
      ],
    ];
  }

  List<Widget> _renderTransactionHistory(BuildContext context) {
    ThemeData theme = Theme.of(context);
    return [
      Text(
        FlutterI18n.translate(context, 'transactionHistory').toUpperCase(),
        style: theme.textTheme.titleLarge!.copyWith(color: theme.colorScheme.onSurfaceVariant),
      ),
      if (_walletManagementController.transactionList.isEmpty)
        const Align(alignment: Alignment.center, child: EmptyView())
      else ...[
        ..._walletManagementController.transactionList.map((item) => TransactionItem(item: item)),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            MouseRegion(
              cursor: SystemMouseCursors.click,
              child: GestureDetector(
                onTap: () {
                  context.push(
                    '/${SystemConst.ROUTE['WALLET']!['HISTORY']!}',
                    extra: _walletManagementController.selectedUser.value,
                  );
                },
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Row(
                    children: [
                      Text(FlutterI18n.translate(context, 'allTransactions'), style: theme.textTheme.labelSmall),
                      const SizedBox(width: 4),
                      const Icon(Icons.arrow_forward_ios_rounded, size: 16),
                    ],
                  ),
                ),
              ),
            ),
          ],
        )
      ]
    ];
  }
}
