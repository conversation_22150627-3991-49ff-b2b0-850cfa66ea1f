// coverage:ignore-file
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:get/get.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/main_button.dart';
import 'package:pacific_2_customer_portal/common/widgets/button/main_outlined_button.dart';
import 'package:pacific_2_customer_portal/common/widgets/checkbox/my_checkbox.dart';
import 'package:pacific_2_customer_portal/common/widgets/my_bottom_sheet/my_bottom_sheet.dart';
import 'package:pacific_2_customer_portal/controllers/app.controller.dart';
import 'package:pacific_2_customer_portal/pages/principal_account_add_new/widgets/terms_conditions.dart';

void showTOSAllergenWarningDialog(
  BuildContext context, {
  required String allergens,
  required Function() onAccept,
}) {
  final AppController appController = Get.find<AppController>();
  ThemeData theme = Theme.of(context);
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (_) {
      bool checkedTOS = false;
      return StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(
            FlutterI18n.translate(context, 'allergenWarningDialogTitle'),
            textAlign: TextAlign.center,
            style: theme.textTheme.headlineSmall,
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                FlutterI18n.translate(
                  context,
                  'allergenWarningDialogMessage',
                  translationParams: {'allergens': allergens},
                ),
                textAlign: TextAlign.center,
                style: theme.textTheme.bodyLarge,
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  MyCheckBox(
                      checked: checkedTOS,
                      onChanged: (value) {
                        setState(() {
                          checkedTOS = !checkedTOS;
                        });
                      }),
                  Expanded(
                    child: RichText(
                      text: TextSpan(
                        style: theme.textTheme.titleSmall,
                        children: <TextSpan>[
                          TextSpan(
                              text:
                                  "${FlutterI18n.translate(context, 'confirmTermPolicy')} ${appController.tenant.value.name}'s "),
                          TextSpan(
                            text: FlutterI18n.translate(context, 'termPolicy'),
                            style: TextStyle(
                              color: theme.colorScheme.primary, // Link color
                              decoration: TextDecoration.underline, // Underline
                              decorationColor: theme.colorScheme.primary,
                            ),
                            recognizer: TapGestureRecognizer()
                              ..onTap = () {
                                showMyBottomSheet(context, () => const TermsConditions());
                              },
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
          actions: [
            Row(
              children: [
                Expanded(
                  child: MainOutlinedButton(
                    height: 40,
                    text: FlutterI18n.translate(context, 'cancel'),
                    textStyle: theme.textTheme.labelMedium,
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                  ),
                ),
                const SizedBox(width: 24),
                Expanded(
                  child: MainButton(
                    height: 40,
                    text: FlutterI18n.translate(context, 'yes'),
                    textStyle: theme.textTheme.labelMedium,
                    disabled: !checkedTOS,
                    onPressed: () {
                      Navigator.of(context).pop();
                      onAccept();
                    },
                  ),
                )
              ],
            ),
          ],
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          actionsPadding: const EdgeInsets.fromLTRB(16, 12, 16, 16),
          titlePadding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
          backgroundColor: theme.colorScheme.surfaceContainer,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        ),
      );
    },
  );
}
