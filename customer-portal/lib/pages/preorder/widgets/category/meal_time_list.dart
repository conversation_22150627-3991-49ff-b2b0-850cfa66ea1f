import 'package:flutter/material.dart';
import 'package:pacific_2_customer_portal/models/meal_times.model.dart';

class MealTimeList extends StatelessWidget {
  const MealTimeList({
    super.key,
    required this.data,
    required this.selectedMealTime,
    required this.onSelectMealTime,
  });

  final List<MealTime> data;
  final MealTime selectedMealTime;
  final Function(MealTime value) onSelectMealTime;

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    return Material(
      color: theme.colorScheme.surfaceContainer,
      child: SizedBox(
        width: double.infinity,
        height: 42,
        child: ListView.separated(
          itemBuilder: (context, index) {
            MealTime mealTime = data[index];
            bool isSelected = selectedMealTime.id == mealTime.id;
            return InkWell(
              onTap: () {
                onSelectMealTime(mealTime);
              },
              child: Ink(
                height: 42,
                decoration: BoxDecoration(
                  border: isSelected
                      ? Border(
                          bottom: BorderSide(
                            color: theme.colorScheme.primary,
                            width: 2,
                          ),
                        )
                      : null,
                ),
                child: Center(
                  child: Text(
                    '${mealTime.name} (${mealTime.count})',
                    style: theme.textTheme.titleSmall!.copyWith(
                      color: isSelected ? theme.colorScheme.onSurface : theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
              ),
            );
          },
          separatorBuilder: (_, __) => const SizedBox(width: 20),
          itemCount: data.length,
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsets.symmetric(horizontal: 16),
        ),
      ),
    );
  }
}
