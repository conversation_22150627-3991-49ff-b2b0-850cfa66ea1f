import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:pacific_2_customer_portal/pages/preorder/widgets/menu_date_picker/day_component.dart';

class Legend extends StatelessWidget {
  const Legend({super.key});

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    return Row(
      mainAxisSize: MainAxisSize.max,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 16,
                    height: 16,
                    margin: const EdgeInsets.only(right: 4, left: 10),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: theme.colorScheme.surfaceContainer,
                      border: Border.all(color: theme.colorScheme.onSurface),
                    ),
                  ),
                  Text(
                    FlutterI18n.translate(context, 'availableDay'),
                    style: theme.textTheme.bodySmall,
                  )
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Container(
                    width: 16,
                    height: 16,
                    margin: const EdgeInsets.only(right: 4, left: 10),
                    clipBehavior: Clip.antiAlias,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: theme.colorScheme.errorContainer,
                    ),
                    child: CustomPaint(
                      painter: LinePainter(color: theme.colorScheme.error.withValues(alpha: 0.24)),
                    ),
                  ),
                  Text(
                    FlutterI18n.translate(context, 'itemAdded'),
                    style: theme.textTheme.bodySmall,
                  )
                ],
              ),
            ],
          ),
        ),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 16,
                    height: 16,
                    margin: const EdgeInsets.only(right: 4, left: 10),
                    clipBehavior: Clip.antiAlias,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: theme.colorScheme.secondaryContainer,
                    ),
                    child: CustomPaint(
                      painter: LinePainter(color: theme.colorScheme.secondary.withValues(alpha: 0.24)),
                    ),
                  ),
                  Text(
                    FlutterI18n.translate(context, 'orderCreated'),
                    style: theme.textTheme.bodySmall,
                  )
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Container(
                    width: 16,
                    height: 16,
                    margin: const EdgeInsets.only(right: 4, left: 10),
                    clipBehavior: Clip.antiAlias,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: theme.colorScheme.outline,
                    ),
                    child: CustomPaint(
                      painter: LinePainter(color: theme.colorScheme.outline.withValues(alpha: 0.8)),
                    ),
                  ),
                  Text(
                    FlutterI18n.translate(context, 'cafeClosed'),
                    style: theme.textTheme.bodySmall,
                  )
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}
