// coverage:ignore-file
import 'package:flutter/material.dart';
import 'package:pacific_2_customer_portal/common/const/const.asset.dart';

enum NotificationType {
  order(iconDot: ConstAsset.icNotiOrderDot, icon: ConstAsset.icNotiOrder),
  payment(iconDot: ConstAsset.icNotiPaymentDot, icon: ConstAsset.icNotiPayment),
  wallet(iconDot: ConstAsset.icNotiWalletDot, icon: ConstAsset.icNotiWallet),
  other(iconDot: ConstAsset.icNotiOrderDot, icon: ConstAsset.icNotiOrder);

  const NotificationType({required this.iconDot, required this.icon});

  final String iconDot;
  final String icon;
}

enum NotificationActionType {
  welcomeUser(action: 'WELCOME_USER', key: 'notiWelcomeUser', type: NotificationType.other),
  addSubAccount(action: 'ADD_SUB_ACCOUNT', key: 'notiAddSubAccount', type: NotificationType.other),
  balanceChange(action: 'BALANCE_CHANGE', key: 'notiBalanceChange', type: NotificationType.wallet),
  paymentSuccess(action: 'PAYMENT_SUCCESS', key: 'notiPaymentSuccess', type: NotificationType.payment),
  acceptSubAccountInvitation(
      action: 'ACCEPT_SUB_ACCOUNT_INVITATION', key: 'notiSubAccountIvitation', type: NotificationType.other),
  rejectSubAccountInvitation(
      action: 'REJECT_SUB_ACCOUNT_INVITATION', key: 'notiSubAccountIvitation', type: NotificationType.other),
  updateOrderStatus(action: 'UPDATE_ORDER_STATUS', key: 'notiUpdateOrderStatus', type: NotificationType.order),
  other(action: '', key: 'notiOther', type: NotificationType.other);

  const NotificationActionType({required this.action, required this.key, required this.type});

  final String action;
  final String key;
  final NotificationType type;

  static NotificationActionType fromString(String? action) {
    return NotificationActionType.values
        .firstWhere((e) => e.action == action, orElse: () => NotificationActionType.other);
  }
}

enum NotificationItemActionType {
  read(titleKey: 'markAsRead', iconData: Icons.check),
  delete(titleKey: 'deleteNotification', iconData: Icons.delete_outlined);

  const NotificationItemActionType({required this.titleKey, required this.iconData});

  final String titleKey;
  final IconData iconData;
}

class NotificationList {
  List<NotificationItem>? content;
  LastEvaluatedKey? lastEvaluatedKey;

  NotificationList({
    this.content,
    this.lastEvaluatedKey,
  });
  NotificationList.fromJson(Map<String, dynamic> json) {
    if (json['content'] != null) {
      content = <NotificationItem>[];
      json['content'].forEach((v) {
        content!.add(NotificationItem.fromJson(v));
      });
    }
    if (json['lastEvaluatedKey'] != null && json['lastEvaluatedKey'] is Map && json['lastEvaluatedKey'].isNotEmpty) {
      lastEvaluatedKey = LastEvaluatedKey.fromJson(json['lastEvaluatedKey']);
    }
  }
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (content != null) {
      data['content'] = content!.map((v) => v.toJson()).toList();
    }
    if (lastEvaluatedKey != null) {
      data['lastEvaluatedKey'] = lastEvaluatedKey!.toJson();
    }
    return data;
  }
}

class NotificationItem {
  String? id;
  String? tenantId;
  String? userId;
  bool? read;
  String? action;
  NotificationActionType? actionType;

  String? content;
  NotificationData? data;
  String? source;
  String? title;
  int? createdAt;

  NotificationItem({
    this.id,
    this.tenantId,
    this.userId,
    this.read,
    this.action,
    this.content,
    this.data,
    this.source,
    this.title,
    this.createdAt,
  });

  NotificationItem.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    tenantId = json['tenantId'];
    userId = json['userId'];
    read = json['read'];
    action = json['action'];
    content = json['content'];

    if (json['data'] != null) {
      data = NotificationData.fromJson(json['data']);
    }
    source = json['source'];
    title = json['title'];
    createdAt = json['createdAt'];

    actionType = NotificationActionType.fromString(action);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['tenantId'] = tenantId;
    data['userId'] = userId;
    data['read'] = read;
    data['action'] = action;

    data['content'] = content;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['source'] = source;
    data['title'] = title;
    data['createdAt'] = createdAt;
    return data;
  }
}

class NotificationData {
  String? systemName;
  String? loginUrl;
  String? to;
  String? userName;
  String? title;
  String? content;

  NotificationData({
    this.systemName,
    this.loginUrl,
    this.to,
    this.userName,
    this.title,
    this.content,
  });

  NotificationData.fromJson(Map<String, dynamic> json) {
    systemName = json['systemName'];
    loginUrl = json['loginUrl'];
    to = json['to'];
    userName = json['userName'];
    title = json['title'];
    content = json['content'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['systemName'] = systemName;
    data['loginUrl'] = loginUrl;
    data['to'] = to;
    data['userName'] = userName;
    data['title'] = title;
    data['content'] = content;
    return data;
  }
}

class LastEvaluatedKey {
  AdditionalProp? id;
  AdditionalProp? partition;

  final String idKey = 'id';
  final String partitionKey = 'partition_key';

  LastEvaluatedKey({
    this.id,
    this.partition,
  });
  LastEvaluatedKey.fromJson(Map<String, dynamic> json) {
    id = json[idKey] != null ? AdditionalProp.fromJson(json[idKey]) : null;
    partition = json[partitionKey] != null ? AdditionalProp.fromJson(json[partitionKey]) : null;
  }
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (id != null) {
      data[idKey] = id!.toJson();
    }
    if (partition != null) {
      data[partitionKey] = partition!.toJson();
    }
    return data;
  }

  bool isEqual(LastEvaluatedKey? other) {
    if (other == null) return false;
    if (id?.s != other.id?.s || id?.n != other.id?.n) return false;
    if (partition?.s != other.partition?.s || partition?.n != other.partition?.n) return false;
    return true;
  }
}

class AdditionalProp {
  String? s;
  String? n;

  AdditionalProp({
    this.s,
    this.n,
  });

  AdditionalProp.fromJson(Map<String, dynamic> json) {
    s = json['s'];
    n = json['n'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['s'] = s;
    data['n'] = n;
    return data;
  }
}

class NotificationTracking {
  String? tenantId;
  String? userId;
  bool? hasNewNotification;

  NotificationTracking({
    this.tenantId,
    this.userId,
    this.hasNewNotification,
  });

  NotificationTracking.fromJson(Map<String, dynamic> json) {
    tenantId = json['tenantId'];
    userId = json['userId'];
    hasNewNotification = json['hasNewNotification'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['tenantId'] = tenantId;
    data['userId'] = userId;
    data['hasNewNotification'] = hasNewNotification;
    return data;
  }
}
