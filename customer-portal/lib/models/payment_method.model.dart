// coverage:ignore-file

import 'package:pacific_2_customer_portal/models/common.model.dart';

enum PaymentProcessorId {
  eWallet(id: 'E_WALLET_PAYMENT'),
  stripe(id: 'STRIPE_WEB_PAYMENT'),
  stripeConnect(id: 'STRIPE_CONNECT_STANDARD_WEB_PAYMENT');

  const PaymentProcessorId({required this.id});

  final String id;
}

class PaymentMethod {
  String? id;
  String? processorId;
  String? tenantId;
  String? displayName;
  Image? icon;
  String? description;
  bool? isActive;
  String? paymentInstruction;
  double? surchargeRate;
  int? fixedSurcharge;
  Currency? currency;
  String? surchargeTitle;
  ProcessorConfig? processorConfig;
  List<String>? acceptedApplications;
  int? createdAt;
  int? updatedAt;
  String? createdBy;
  String? updatedBy;

  PaymentMethod(
      {this.id,
      this.processorId,
      this.tenantId,
      this.displayName,
      this.icon,
      this.description,
      this.isActive,
      this.paymentInstruction,
      this.surchargeRate,
      this.fixedSurcharge,
      this.currency,
      this.surchargeTitle,
      this.processorConfig,
      this.acceptedApplications,
      this.createdAt,
      this.updatedAt,
      this.createdBy,
      this.updatedBy});

  PaymentMethod.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    processorId = json['processorId'];
    tenantId = json['tenantId'];
    displayName = json['displayName'];
    icon = json['icon'] != null ? Image.fromJson(json['icon']) : null;
    description = json['description'];
    isActive = json['isActive'];
    paymentInstruction = json['paymentInstruction'];
    try {
      surchargeRate = double.parse(json['surchargeRate']);
      if (surchargeRate!.isNaN) surchargeRate = 0;
    } catch (e) {
      surchargeRate = 0;
    }
    fixedSurcharge = json['fixedSurcharge'];
    currency =
        json['currency'] != null ? Currency.fromJson(json['currency']) : null;
    surchargeTitle = json['surchargeTitle'];
    processorConfig = json['processorConfig'] != null
        ? ProcessorConfig.fromJson(json['processorConfig'])
        : null;
    acceptedApplications = json['acceptedApplications'].cast<String>();
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
    createdBy = json['createdBy'];
    updatedBy = json['updatedBy'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['processorId'] = processorId;
    data['tenantId'] = tenantId;
    data['displayName'] = displayName;
    if (icon != null) {
      data['icon'] = icon!.toJson();
    }
    data['description'] = description;
    data['isActive'] = isActive;
    data['paymentInstruction'] = paymentInstruction;
    data['surchargeRate'] = surchargeRate;
    data['fixedSurcharge'] = fixedSurcharge;
    if (currency != null) {
      data['currency'] = currency!.toJson();
    }
    data['surchargeTitle'] = surchargeTitle;
    if (processorConfig != null) {
      data['processorConfig'] = processorConfig!.toJson();
    }
    data['acceptedApplications'] = acceptedApplications;
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    data['createdBy'] = createdBy;
    data['updatedBy'] = updatedBy;
    return data;
  }
}

class ProcessorConfig {
  String? processorId;
  String? netsFamilyCardType;
  String? apiKey;

  ProcessorConfig({this.processorId, this.netsFamilyCardType});

  ProcessorConfig.fromJson(Map<String, dynamic> json) {
    processorId = json['processorId'];
    netsFamilyCardType = json['netsFamilyCardType'];
    apiKey = json['apiKey'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['processorId'] = processorId;
    data['netsFamilyCardType'] = netsFamilyCardType;
    data['apiKey'] = apiKey;
    return data;
  }
}
