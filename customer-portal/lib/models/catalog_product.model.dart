// coverage:ignore-file
import 'package:pacific_2_customer_portal/models/common.model.dart';

class CatalogProduct {
  String? id;
  String? tenantId;
  Category? category;
  String? storeId;
  HealthierChoice? healthierChoice;
  String? name;
  String? sku;
  String? briefInformation;
  String? description;
  String? ingredients;
  String? barcode;
  String? status;
  int? unitPrice;
  int? listingPrice;
  Currency? currency;
  int? preparationTime;
  List<Images>? images;
  List<ProductOption>? options;
  List<Nutrition>? nutrition;
  List<Allergens>? allergens;
  int? createdAt;
  int? updatedAt;
  String? storeName;

  CatalogProduct(
      {this.id,
      this.tenantId,
      this.category,
      this.storeId,
      this.healthierChoice,
      this.name,
      this.sku,
      this.briefInformation,
      this.description,
      this.ingredients,
      this.barcode,
      this.status,
      this.unitPrice,
      this.listingPrice,
      this.currency,
      this.preparationTime,
      this.images,
      this.options,
      this.nutrition,
      this.allergens,
      this.createdAt,
      this.updatedAt,
      this.storeName});

  CatalogProduct.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    tenantId = json['tenantId'];
    category =
        json['category'] != null ? Category.fromJson(json['category']) : null;
    storeId = json['storeId'];
    healthierChoice = json['healthierChoice'] != null
        ? HealthierChoice.fromJson(json['healthierChoice'])
        : null;
    name = json['name'];
    sku = json['sku'];
    briefInformation = json['briefInformation'];
    description = json['description'];
    ingredients = json['ingredients'];
    barcode = json['barcode'];
    status = json['status'];
    unitPrice = json['unitPrice'];
    listingPrice = json['listingPrice'];
    currency =
        json['currency'] != null ? Currency.fromJson(json['currency']) : null;
    preparationTime = json['preparationTime'];
    if (json['images'] != null) {
      images = <Images>[];
      json['images'].forEach((v) {
        images!.add(Images.fromJson(v));
      });
    }
    if (json['options'] != null) {
      options = <ProductOption>[];
      json['options'].forEach((v) {
        options!.add(ProductOption.fromJson(v));
      });
    }
    if (json['nutrition'] != null) {
      nutrition = <Nutrition>[];
      json['nutrition'].forEach((v) {
        nutrition!.add(Nutrition.fromJson(v));
      });
    }
    if (json['allergens'] != null) {
      allergens = <Allergens>[];
      json['allergens'].forEach((v) {
        allergens!.add(Allergens.fromJson(v));
      });
    }
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['tenantId'] = tenantId;
    if (category != null) {
      data['category'] = category!.toJson();
    }
    data['storeId'] = storeId;
    if (healthierChoice != null) {
      data['healthierChoice'] = healthierChoice!.toJson();
    }
    data['name'] = name;
    data['sku'] = sku;
    data['briefInformation'] = briefInformation;
    data['description'] = description;
    data['ingredients'] = ingredients;
    data['barcode'] = barcode;
    data['status'] = status;
    data['unitPrice'] = unitPrice;
    data['listingPrice'] = listingPrice;
    if (currency != null) {
      data['currency'] = currency!.toJson();
    }
    data['preparationTime'] = preparationTime;
    if (images != null) {
      data['images'] = images!.map((v) => v.toJson()).toList();
    }
    if (options != null) {
      data['options'] = options!.map((v) => v.toJson()).toList();
    }
    if (nutrition != null) {
      data['nutrition'] = nutrition!.map((v) => v.toJson()).toList();
    }
    if (allergens != null) {
      data['allergens'] = allergens!.map((v) => v.toJson()).toList();
    }
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    return data;
  }
}

class Category {
  String? id;
  String? tenantId;
  String? name;
  Image? icon;
  String? description;
  String? parentId;
  int? createdAt;
  int? updatedAt;

  Category(
      {this.id,
      this.tenantId,
      this.name,
      this.icon,
      this.description,
      this.parentId,
      this.createdAt,
      this.updatedAt});

  Category.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    tenantId = json['tenantId'];
    name = json['name'];
    icon = json['icon'] != null ? Image.fromJson(json['icon']) : null;
    description = json['description'];
    parentId = json['parentId'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['tenantId'] = tenantId;
    data['name'] = name;
    if (icon != null) {
      data['icon'] = icon!.toJson();
    }
    data['description'] = description;
    data['parentId'] = parentId;
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    return data;
  }
}

class HealthierChoice {
  String? id;
  String? tenantId;
  String? name;
  Image? symbol;
  String? description;
  int? createdAt;
  int? updatedAt;

  HealthierChoice(
      {this.id,
      this.tenantId,
      this.name,
      this.symbol,
      this.description,
      this.createdAt,
      this.updatedAt});

  HealthierChoice.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    tenantId = json['tenantId'];
    name = json['name'];
    symbol = json['symbol'] != null ? Image.fromJson(json['symbol']) : null;
    description = json['description'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['tenantId'] = tenantId;
    data['name'] = name;
    if (symbol != null) {
      data['symbol'] = symbol!.toJson();
    }
    data['description'] = description;
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    return data;
  }
}

class ProductOption {
  String? id;
  String? title;
  String? productId;
  int? minimum;
  int? maximum;
  List<ProductOptionItem>? items;
  int? createdAt;
  int? updatedAt;

  ProductOption(
      {this.id,
      this.title,
      this.productId,
      this.minimum,
      this.maximum,
      this.items,
      this.createdAt,
      this.updatedAt});

  ProductOption.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    productId = json['productId'];
    minimum = json['minimum'];
    maximum = json['maximum'];
    if (json['items'] != null) {
      items = <ProductOptionItem>[];
      json['items'].forEach((v) {
        items!.add(ProductOptionItem.fromJson(v));
      });
    }
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['title'] = title;
    data['productId'] = productId;
    data['minimum'] = minimum;
    data['maximum'] = maximum;
    if (items != null) {
      data['items'] = items!.map((v) => v.toJson()).toList();
    }
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    return data;
  }
}

class ProductOptionItem {
  String? id;
  String? optionId;
  String? name;
  int? additionPrice;
  bool? active;
  int? createdAt;
  int? updatedAt;

  ProductOptionItem(
      {this.id,
      this.optionId,
      this.name,
      this.additionPrice,
      this.active,
      this.createdAt,
      this.updatedAt});

  ProductOptionItem.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    optionId = json['optionId'];
    name = json['name'];
    additionPrice = json['additionPrice'];
    active = json['active'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['optionId'] = optionId;
    data['name'] = name;
    data['additionPrice'] = additionPrice;
    data['active'] = active;
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    return data;
  }
}

class Nutrition {
  String? id;
  String? tenantId;
  String? name;
  String? unit;
  double? value;
  int? createdAt;
  int? updatedAt;

  Nutrition(
      {this.id,
      this.tenantId,
      this.name,
      this.unit,
      this.value,
      this.createdAt,
      this.updatedAt});

  Nutrition.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    tenantId = json['tenantId'];
    name = json['name'];
    unit = json['unit'];
    value = json['value'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['tenantId'] = tenantId;
    data['name'] = name;
    data['unit'] = unit;
    data['value'] = value;
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    return data;
  }
}
