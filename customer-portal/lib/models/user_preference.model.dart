// coverage:ignore-file
import 'package:pacific_2_customer_portal/models/common.model.dart';

enum UserPreferenceKey {
  dateTimeFormat(key: 'ACCOUNT_DATE_TIME_FORMAT'),
  notificationPayment(key: 'NOTIFICATION_PAYMENT_TRANSACTIONS_SUCCEEDED'),
  notificationOrder(key: 'NOTIFICATION_ORDER_STATUS_UPDATED'),
  notificationWallet(key: 'NOTIFICATION_WALLET_BALANCE_CHANGED'),
  mfa(key: 'SECURITY_MFA');

  const UserPreferenceKey({required this.key});

  final String key;

  static UserPreferenceKey fromString(String prefKey) {
    return UserPreferenceKey.values.firstWhere(
      (e) => e.key == prefKey,
      orElse: () => UserPreferenceKey.dateTimeFormat,
    );
  }
}

enum NotiChannel {
  email(value: 'EMAIL'),
  inApp(value: 'IN_APP'),
  push(value: 'PUSH_NOTIFICATION');

  const NotiChannel({required this.value});

  final String value;
}

class UserPreference {
  String? id;
  String? userId;
  String? tenantId;
  String? key;
  String? group;
  PreferenceData? data;
  int? createdAt;
  int? updatedAt;
  bool? isDefault;

  UserPreference(
      {this.id,
      this.userId,
      this.tenantId,
      this.key,
      this.group,
      this.data,
      this.createdAt,
      this.updatedAt,
      this.isDefault});

  UserPreference.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['userId'];
    tenantId = json['tenantId'];
    key = json['key'];
    group = json['group'];
    data = json['data'] != null ? PreferenceData.fromJson(json['data']) : null;
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
    isDefault = json['default'] ?? true;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['userId'] = userId;
    data['tenantId'] = tenantId;
    data['key'] = key;
    data['group'] = group;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    data['default'] = isDefault;
    return data;
  }
}

class PreferenceData {
  String? key;
  TimeZone? timeZone;
  String? dateFormat;
  String? timeFormat;
  List<String>? channels;
  bool? isEnabledEmailOtp;

  PreferenceData({
    this.key,
    this.timeZone,
    this.dateFormat,
    this.timeFormat,
    this.channels,
    this.isEnabledEmailOtp,
  });

  PreferenceData.fromJson(Map<String, dynamic> json) {
    key = json['key'];
    timeZone =
        json['timeZone'] != null ? TimeZone.fromJson(json['timeZone']) : null;
    dateFormat = json['dateFormat'];
    timeFormat = json['timeFormat'];
    channels = (json['channels'] ?? []).cast<String>();
    isEnabledEmailOtp = json['isEnabledEmailOtp'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['key'] = key;
    if (timeZone != null) {
      data['timeZone'] = timeZone!.toJson();
    }
    data['dateFormat'] = dateFormat;
    data['timeFormat'] = timeFormat;
    data['channels'] = channels;
    data['isEnabledEmailOtp'] = isEnabledEmailOtp;
    return data;
  }
}
