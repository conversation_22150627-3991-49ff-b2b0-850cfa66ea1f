import 'dart:async';
import 'dart:math';

import 'package:jiffy/jiffy.dart';
import 'package:pacific_2_customer_portal/models/tenant.model.dart';
import 'package:timezone/timezone.dart';
import "package:universal_html/html.dart" as html;
import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:pacific_2_customer_portal/controllers/app.controller.dart';
import 'package:pacific_2_customer_portal/models/common.model.dart';

const String defaultDateFormat = 'yyyy-MM-dd';

String formatPrice(num price, {bool showCurrency = true}) {
  Currency? currency = Get.find<AppController>().tenant.value.settings?.currency;
  int fractionDigits = currency?.fractionDigits ?? 2;
  NumberFormat numberFormat =
      fractionDigits == 0 ? NumberFormat('#,##0') : NumberFormat('#,##0.${'0' * fractionDigits}');
  return "${showCurrency ? currency?.symbol ?? '\$' : ''}${numberFormat.format(price / pow(10, fractionDigits))}";
}

TZDateTime getTZDateTime(DateTime date) {
  String timeZoneId = Get.find<AppController>().timeZoneId.value;
  return TZDateTime.from(date, getLocation(timeZoneId));
}

TZDateTime getTZDateTimeFromUtc(DateTime date) {
  String timeZoneId = Get.find<AppController>().timeZoneId.value;
  return TZDateTime(getLocation(timeZoneId), date.year, date.month, date.day, date.hour, date.minute, date.second,
      date.millisecond, date.microsecond);
}

TZDateTime getServerDateTimeFromUtc(DateTime date) {
  Settings? settings = Get.find<AppController>().tenant.value.settings;
  String timeZoneId = settings?.timeZone?.zoneId ?? 'Etc/UTC';
  return TZDateTime(getLocation(timeZoneId), date.year, date.month, date.day, date.hour, date.minute, date.second,
      date.millisecond, date.microsecond);
}

bool isSameWeek(DateTime date1, DateTime date2) {
  return Jiffy.parseFromDateTime(date1).isSame(Jiffy.parseFromDateTime(date2), unit: Unit.week);
}

String formatDate(DateTime date) {
  String dateFormat = Get.find<AppController>().dateFormat.value;
  return DateFormat(dateFormat).format(date.isUtc ? date : getTZDateTime(date));
}

String formatTime(DateTime date) {
  String timeFormat = Get.find<AppController>().timeFormat.value;
  return DateFormat(timeFormat).format(getTZDateTime(date));
}

String formatDateTime(DateTime date) {
  AppController controller = Get.find<AppController>();
  String dateFormat = controller.dateFormat.value;
  String timeFormat = controller.timeFormat.value;
  return DateFormat('$timeFormat $dateFormat').format(getTZDateTime(date));
}

DateFormat getDefaultDateFormat() {
  return DateFormat(defaultDateFormat);
}

Map<T, List<S>> groupBy<S, T>(Iterable<S> values, T Function(S) key) {
  var map = <T, List<S>>{};
  for (var element in values) {
    (map[key(element)] ??= []).add(element);
  }
  return map;
}

String capitalizeFirstLetter(String input) {
  if (input.isEmpty) return input;
  return input[0].toUpperCase() + input.substring(1).toLowerCase();
}

int roundHalfEven(double value, {double epsilon = 0.0000001}) {
  if (value < 0.0) return -roundHalfEven(-value, epsilon: epsilon);
  double ipart = value.toInt().toDouble();

  // If 'value' is exctly halfway between two integers
  if ((value - (ipart + 0.5)).abs() < epsilon) {
    // If 'ipart' is even then return 'ipart'
    if (ipart % 2.0 < epsilon) {
      return ipart.toInt();
    }

    // Else return the nearest even integer
    return (ipart + 0.5).ceilToDouble().toInt();
  }

  // Otherwise use the usual round to closest
  // (Either symmetric half-up or half-down will do0
  return value.roundToDouble().toInt();
}

// coverage:ignore-start
Uri getBaseApiUri() {
  if (kIsWeb && !kDebugMode) {
    Uri baseUri = Uri.parse(html.document.baseUri ?? Uri.base.toString());
    return Uri(
      scheme: baseUri.scheme,
      host: baseUri.host,
      port: baseUri.port,
    );
  }
  return Uri.parse(dotenv.env['BASE_API_URL']!);
}

Uri getRedirectUri({String path = '', Map<String, String>? queryParameters}) {
  if (kIsWeb) {
    Uri baseUri = Uri.parse(html.document.baseUri ?? Uri.base.toString());
    Iterable<String> pathSegments = baseUri.pathSegments.where((seg) => seg.isNotEmpty);
    return Uri(
      scheme: baseUri.scheme,
      host: baseUri.host,
      port: baseUri.port,
      pathSegments: [...pathSegments, ...path.split('/')],
      queryParameters: queryParameters,
    );
  }
  return Uri.parse(dotenv.env['REDIRECT_URL']!);
}

// String formatWalletId(String walletId) {
//   return '#${walletId.replaceAllMapped(RegExp(r".{4}"), (match) => "${match.group(0)} ")}';
// }

StreamSubscription? waitForAuth(AppController appController, Function() callback) {
  if (appController.userProfile.value.id != null) {
    callback();
    return null;
  } else {
    return appController.userProfile.listen((profile) {
      if (profile.id != null) {
        callback();
      }
    });
  }
}
// coverage:ignore-end
