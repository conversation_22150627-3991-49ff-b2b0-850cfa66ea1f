import 'package:flutter/material.dart';
import 'package:pacific_2_customer_portal/models/preorder_menu_item.model.dart';

class CartQuantity extends StatelessWidget {
  const CartQuantity({
    super.key,
    required this.quantity,
    required this.item,
    required this.onUpdateQuantity,
    this.margin = EdgeInsets.zero,
  });

  final int quantity;
  final PreOrderMenuItem item;
  final Function(bool isAdd) onUpdateQuantity;
  final EdgeInsetsGeometry margin;

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    int step = item.product?.inventory?.step ?? 1;
    bool canSubQuantity = quantity > step;
    bool canAddQuantity =
        item.capacity == 0 || quantity < item.capacity! - item.ordered!;

    return Container(
      width: 96,
      margin: margin,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.outline),
      ),
      child: Row(
        children: [
          SizedBox.fromSize(
            size: const Size(32, 32),
            child: IconButton(
              padding: const EdgeInsets.all(8),
              onPressed: canSubQuantity ? () => onUpdateQuantity(false) : null,
              iconSize: 16,
              icon: const Icon(Icons.remove_rounded),
            ),
          ),
          Expanded(
            child: Text(
              quantity.toString(),
              style: theme.textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ),
          SizedBox.fromSize(
            size: const Size(32, 32),
            child: IconButton(
              padding: const EdgeInsets.all(8),
              onPressed: canAddQuantity ? () => onUpdateQuantity(true) : null,
              iconSize: 16,
              icon: const Icon(Icons.add_rounded),
            ),
          ),
        ],
      ),
    );
  }
}
