// coverage:ignore-file
import 'package:flutter/material.dart';
import 'package:pacific_2_customer_portal/models/order_history.model.dart';
import 'package:pacific_2_customer_portal/models/payment_transaction.model.dart';
import 'package:pacific_2_customer_portal/models/sub_account.model.dart';
import 'package:pacific_2_customer_portal/themes/colors.dart';

enum UnimplementedStatus { unimplemented }

class StatusConfig {
  final String labelKey;
  final Color textColor;
  final Color bgColor;

  const StatusConfig({
    required this.labelKey,
    required this.textColor,
    required this.bgColor,
  });
}

const green = Color(0xff1B806A);
const greenLight = Color(0x2936B37E);
const yellow = Color(0xffB76E00);
const yellowLight = Color(0x29FFAB00);
const red = Color(0xff920113);
const redLight = Color(0x29D0021B);

Map<Enum, StatusConfig> statusConfig = {
  // sub account
  SubAccountStatus.active: StatusConfig(
    labelKey: SubAccountStatus.active.labelKey,
    textColor: green,
    bgColor: greenLight,
  ),
  SubAccountStatus.pending: StatusConfig(
    labelKey: SubAccountStatus.pending.labelKey,
    textColor: lightScheme.onSurfaceVariant,
    bgColor: lightScheme.surface,
  ),

  // order
  OrderStatus.paid: StatusConfig(
    labelKey: OrderStatus.paid.labelKey,
    textColor: green,
    bgColor: greenLight,
  ),
  OrderStatus.completed: StatusConfig(
    labelKey: OrderStatus.completed.labelKey,
    textColor: green,
    bgColor: greenLight,
  ),
  OrderStatus.confirmed: StatusConfig(
    labelKey: OrderStatus.confirmed.labelKey,
    textColor: green,
    bgColor: greenLight,
  ),
  OrderStatus.preparing: StatusConfig(
    labelKey: OrderStatus.preparing.labelKey,
    textColor: green,
    bgColor: greenLight,
  ),
  OrderStatus.collected: StatusConfig(
    labelKey: OrderStatus.collected.labelKey,
    textColor: lightScheme.primary,
    bgColor: lightScheme.primary.withValues(alpha: 0.16),
  ),
  OrderStatus.created: StatusConfig(
    labelKey: OrderStatus.created.labelKey,
    textColor: yellow,
    bgColor: yellowLight,
  ),
  OrderStatus.pending: StatusConfig(
    labelKey: OrderStatus.pending.labelKey,
    textColor: yellow,
    bgColor: yellowLight,
  ),
  OrderStatus.cancelling: StatusConfig(
    labelKey: OrderStatus.cancelling.labelKey,
    textColor: red,
    bgColor: redLight,
  ),
  OrderStatus.cancelled: StatusConfig(
    labelKey: OrderStatus.cancelled.labelKey,
    textColor: red,
    bgColor: redLight,
  ),

  // payment
  PaymentStatus.pending: StatusConfig(
    labelKey: OrderStatus.pending.labelKey,
    textColor: yellow,
    bgColor: yellowLight,
  ),
  PaymentStatus.paid: StatusConfig(
    labelKey: OrderStatus.paid.labelKey,
    textColor: green,
    bgColor: greenLight,
  ),
  PaymentStatus.cancelled: StatusConfig(
    labelKey: OrderStatus.cancelled.labelKey,
    textColor: red,
    bgColor: redLight,
  ),
  PaymentStatus.failed: StatusConfig(
    labelKey: PaymentStatus.failed.labelKey,
    textColor: red,
    bgColor: redLight,
  ),
  PaymentStatus.refunding: StatusConfig(
    labelKey: PaymentStatus.refunding.labelKey,
    textColor: red,
    bgColor: redLight,
  ),
  PaymentStatus.refunded: StatusConfig(
    labelKey: PaymentStatus.refunded.labelKey,
    textColor: red,
    bgColor: redLight,
  ),

  // payment transaction
  PaymentTransactionStatus.succeeded: StatusConfig(
    labelKey: PaymentTransactionStatus.succeeded.labelKey,
    textColor: green,
    bgColor: greenLight,
  ),
  PaymentTransactionStatus.failed: StatusConfig(
    labelKey: PaymentStatus.failed.labelKey,
    textColor: red,
    bgColor: redLight,
  ),

  // unimplemented
  UnimplementedStatus.unimplemented: StatusConfig(
    labelKey: 'unimplemented',
    textColor: lightScheme.onSurfaceVariant,
    bgColor: lightScheme.surface,
  ),
};
