import 'package:flutter/material.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:pacific_2_customer_portal/common/responsive/responsive.dart';

class NavigationItem extends StatelessWidget {
  final bool isSelected;
  final String labelKey;
  final Function() onPressed;

  const NavigationItem({super.key, required this.isSelected, required this.labelKey, required this.onPressed});

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    return Responsive(
      mobile: SizedBox(
        height: 52,
        width: double.infinity,
        child: TextButton(
          onPressed: onPressed,
          style: TextButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 0),
            foregroundColor: theme.colorScheme.surfaceContainer,
            backgroundColor: theme.primaryColor,
          ),
          child: Row(
            children: [
              Text(
                FlutterI18n.translate(context, labelKey),
                style: theme.textTheme.headlineMedium!.copyWith(
                  color: isSelected ? theme.colorScheme.tertiary : theme.colorScheme.onPrimary,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
        ),
      ),
      desktop: Container(
        height: 32,
        margin: const EdgeInsets.symmetric(horizontal: 6),
        child: TextButton(
          onPressed: onPressed,
          style: TextButton.styleFrom(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            foregroundColor: theme.colorScheme.onSurface,
            backgroundColor: theme.colorScheme.surface,
            padding: const EdgeInsets.symmetric(horizontal: 8),
          ),
          child: Text(
            FlutterI18n.translate(context, labelKey),
            style: theme.textTheme.labelMedium!
                .copyWith(color: isSelected ? theme.colorScheme.primary : theme.colorScheme.onSurface),
          ),
        ),
      ),
    );
  }
}
