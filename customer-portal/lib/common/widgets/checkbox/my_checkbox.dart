import 'package:flutter/material.dart';

class MyCheckBox extends StatelessWidget {
  final bool checked;
  final bool disabled;
  final Function(bool? checked)? onChanged;

  const MyCheckBox({super.key, required this.checked, required this.onChanged, this.disabled = false});

  @override
  Widget build(BuildContext context) {
    ThemeData theme = Theme.of(context);

    return Checkbox(
      value: checked,
      onChanged: disabled ? null : onChanged,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
      fillColor: WidgetStateProperty.resolveWith<Color>(
        (Set<WidgetState> states) {
          if (states.contains(WidgetState.selected)) {
            return disabled ? theme.colorScheme.outlineVariant : theme.colorScheme.primary;
          }
          return theme.colorScheme.surfaceContainer;
        },
      ),
      checkColor: theme.colorScheme.onPrimary,
      side: BorderSide(color: theme.colorScheme.outlineVariant),
    );
  }
}
