import 'package:flutter_svg/svg.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:pacific_2_customer_portal/common/widgets/sort_header/sort_header.dart';
import 'package:pacific_2_customer_portal/models/sort.model.dart';

import '../../../mocks/localized_widget.dart';

void main() {
  testWidgets("SortHeader render correctly", (tester) async {
    bool pressedHeader = false;

    await tester.pumpWidget(
      renderLocalizedWidget(
        SortHeader(
          header: SortHeaderData('test', 'test'),
          currentKey: 'test',
          sortOrder: SortOrder.ASC,
          onTap: (key) => pressedHeader = true,
        ),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.text('test'), findsOne);
    expect(find.byType(SvgPicture), findsExactly(2));
    await tester.tap(find.text('test'));
    expect(pressedHeader, true);
  });
}
