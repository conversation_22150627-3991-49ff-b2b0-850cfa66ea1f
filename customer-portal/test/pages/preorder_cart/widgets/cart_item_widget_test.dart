import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:pacific_2_customer_portal/models/cart.model.dart';
import 'package:pacific_2_customer_portal/models/preorder_menu_item.model.dart';
import 'package:pacific_2_customer_portal/pages/preorder_cart/widgets/cart_item_widget.dart';

import '../../../mocks/localized_widget.dart';
import '../../../mocks/responses/menu_item_list.response.mock.dart';
import '../../../setup.config.dart';

void main() {
  setupTestEnv();

  testWidgets("CartItemWidget render correctly", (tester) async {
    CartItem cItem = CartItem(
      item: PreOrderMenuItem.fromJson(mockMenuItemListResponse['content'][0]),
      groupCriteria: CartGroupCriteria(),
      quantity: 2,
    );
    cItem.addNotes('test note');

    bool pressedRemove = false;
    bool pressedOption = false;

    await tester.pumpWidget(
      renderLocalizedWidget(
        CartItemWidget(
          cartItem: cItem,
          isUnavailable: false,
          onRemoveItem: () => pressedRemove = true,
          onUpdateOptions: () => pressedOption = true,
        ),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.text(cItem.item.product!.name!), findsOne);
    expect(find.text('2'), findsOne);
    expect(find.text('test note'), findsOne);

    await tester.tap(find.byIcon(Icons.add_rounded));
    await tester.pumpAndSettle();

    expect(cItem.quantity, 3);

    await tester.tap(find.byIcon(Icons.remove_rounded));
    await tester.pumpAndSettle();

    expect(cItem.quantity, 2);

    await tester.tap(find.byType(SvgPicture).last);
    await tester.pumpAndSettle();

    expect(pressedRemove, isTrue);

    await tester.tap(find.text('test note'));
    await tester.pumpAndSettle();

    expect(pressedOption, isTrue);
  });

  testWidgets("CartItemWidget render correctly when unavailable",
      (tester) async {
    CartItem cItem = CartItem(
      item: PreOrderMenuItem.fromJson(mockMenuItemListResponse['content'][0]),
      groupCriteria: CartGroupCriteria(),
      quantity: 2,
    );

    await tester.pumpWidget(
      renderLocalizedWidget(
        CartItemWidget(
          cartItem: cItem,
          isUnavailable: true,
          onRemoveItem: () {},
          onUpdateOptions: () {},
        ),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.text('Not available'), findsOne);
    expect(find.byIcon(Icons.add_rounded), findsNothing);
    expect(find.byIcon(Icons.remove_rounded), findsNothing);
  });

  testWidgets("CartItemWidget render correctly desktop", (tester) async {
    CartItem cItem = CartItem(
      item: PreOrderMenuItem.fromJson(mockMenuItemListResponse['content'][0]),
      groupCriteria: CartGroupCriteria(),
      quantity: 2,
    );
    cItem.addNotes('test');

    bool pressedRemove = false;
    bool pressedOption = false;

    await tester.pumpWidget(
      renderLocalizedWidgetDesktop(
        CartItemWidget(
          cartItem: cItem,
          isUnavailable: false,
          onRemoveItem: () => pressedRemove = true,
          onUpdateOptions: () => pressedOption = true,
        ),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.text(cItem.item.product!.name!), findsOne);
    expect(find.text('2'), findsOne);
    expect(find.text('test'), findsOne);

    await tester.tap(find.byIcon(Icons.add_rounded));
    await tester.pumpAndSettle();

    expect(cItem.quantity, 3);

    await tester.tap(find.byIcon(Icons.remove_rounded));
    await tester.pumpAndSettle();

    expect(cItem.quantity, 2);

    await tester.tap(find.byType(SvgPicture).last);
    await tester.pumpAndSettle();

    expect(pressedRemove, isTrue);

    await tester.tap(find.text('test'));
    await tester.pumpAndSettle();

    expect(pressedOption, isTrue);
  });

  testWidgets("CartItemWidget render correctly when unavailable desktop",
      (tester) async {
    CartItem cItem = CartItem(
      item: PreOrderMenuItem.fromJson(mockMenuItemListResponse['content'][0]),
      groupCriteria: CartGroupCriteria(),
      quantity: 2,
    );

    await tester.pumpWidget(
      renderLocalizedWidgetDesktop(
        CartItemWidget(
          cartItem: cItem,
          isUnavailable: true,
          onRemoveItem: () {},
          onUpdateOptions: () {},
        ),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.text('Not available'), findsOne);
    expect(find.byIcon(Icons.add_rounded), findsNothing);
    expect(find.byIcon(Icons.remove_rounded), findsNothing);
  });
}
