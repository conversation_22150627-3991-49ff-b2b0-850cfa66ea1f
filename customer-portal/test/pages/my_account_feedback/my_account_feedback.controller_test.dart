import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:pacific_2_customer_portal/pages/my_account_feedback/my_account_feedback.controller.dart';

import '../../mocks/mocks.mocks.dart';
import '../../setup.config.dart';

void main() {
  setupTestEnv();
  final mockUserService = MockUserService();
  late MyAccountFeedbackController myAccountFeedbackController;

  setUp(() {
    myAccountFeedbackController = MyAccountFeedbackController(mockUserService);
  });

  tearDown(() {
    reset(mockUserService);
  });

  test('Initial values for the MyAccountFeedbackController', () {
    expect(myAccountFeedbackController.title.value, "");
    expect(myAccountFeedbackController.description.value, "");
  });

  test('MyAccountFeedbackController should send feedback correctly', () async {
    when(mockUserService.sendFeedback(any)).thenAnswer((_) async => {});
    final result1 = await myAccountFeedbackController.sendFeedback();
    expect(result1, true);

    when(mockUserService.sendFeedback(any))
        .thenAnswer((_) async => {'error': ''});
    final result2 = await myAccountFeedbackController.sendFeedback();
    expect(result2, false);
  });

  test(
    'MyAccountFeedbackController should set title and description correctly',
    () async {
      expect(myAccountFeedbackController.canSubmit(), false);

      myAccountFeedbackController.onTitleChange('title');
      expect(myAccountFeedbackController.title.value, 'title');
      expect(myAccountFeedbackController.canSubmit(), false);

      myAccountFeedbackController.onDescriptionChange('des');
      expect(myAccountFeedbackController.description.value, 'des');
      expect(myAccountFeedbackController.canSubmit(), true);
    },
  );
}
