import 'package:flutter_test/flutter_test.dart';
import 'package:pacific_2_customer_portal/pages/preorder/widgets/select_field.dart';

import '../../../mocks/localized_widget.dart';

void main() {
  testWidgets("SelectField render correctly when value is not null",
      (tester) async {
    bool pressed = false;
    await tester.pumpWidget(
      renderLocalizedWidget(
        SelectField(
          title: 'test title',
          value: 'test value',
          onPress: () => pressed = true,
        ),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.text('test title'), findsOne);
    expect(find.text('test value'), findsOne);
    await tester.tap(find.text('test value'));
    expect(pressed, isTrue);
  });

  testWidgets("SelectField render correctly when value is null",
      (tester) async {
    await tester.pumpWidget(
      renderLocalizedWidget(
        SelectField(
          title: 'test title',
          value: null,
          onPress: () {},
        ),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.text('--'), findsOne);
  });

  testWidgets("Select<PERSON>ield render correctly desktop 1", (tester) async {
    bool pressed = false;
    await tester.pumpWidget(
      renderLocalizedWidgetDesktop(
        SelectField(
          title: 'test title',
          value: 'test value',
          onPress: () => pressed = true,
          isFocused: true,
        ),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.text('test title'), findsOne);
    expect(find.text('test value'), findsOne);
    await tester.tap(find.text('test value'));
    expect(pressed, isTrue);
  });
}
