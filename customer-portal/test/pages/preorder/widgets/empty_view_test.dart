import 'package:flutter_test/flutter_test.dart';
import 'package:pacific_2_customer_portal/pages/preorder/widgets/empty_view.dart';

import '../../../mocks/localized_widget.dart';

void main() {
  testWidgets("EmptyMenuView render correctly", (tester) async {
    await tester.pumpWidget(
      renderLocalizedWidget(
        const EmptyMenuView(),
      ),
    );
    await tester.pumpAndSettle();

    expect(
        find.text(
            'Information not available, please select an Account and a Day to view the menu'),
        findsOne);
  });
}
