import 'package:flutter_test/flutter_test.dart';
import 'package:pacific_2_customer_portal/models/wallet_transaction.model.dart';
import 'package:pacific_2_customer_portal/pages/transaction_details/widgets/transaction_content.dart';
import 'package:pacific_2_customer_portal/utils/general_utils.dart';

import '../../../mocks/localized_widget.dart';
import '../../../mocks/responses/transaction_history.response.mock.dart';
import '../../../setup.config.dart';

void main() {
  setupTestEnv();
  testWidgets("TransactionContent render correctly", (tester) async {
    final data = WalletTransaction.fromJson(
        mockTransactionHistoryResponse['content'][0]);

    await tester.pumpWidget(
      renderLocalizedWidget(
        TransactionContent(
          data: data,
        ),
      ),
    );
    await tester.pumpAndSettle();

    expect(find.text('Incoming'), findsOne);
    expect(find.text(formatPrice(data.amount!.abs())), findsOne);
    expect(
        find.text(formatDateTime(
            DateTime.fromMillisecondsSinceEpoch(data.createdAt!))),
        findsOne);
    expect(find.text('#${data.destinationWalletId}'), findsOne);
    expect(find.text(formatPrice(data.oldBalance!)), findsOne);
    expect(find.text(formatPrice(data.balance!)), findsOne);
  });
}
