import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:pacific_2_customer_portal/pages/wallet_management/widgets/spending_limit.dart';
import 'package:pacific_2_customer_portal/utils/general_utils.dart';

import '../../../mocks/localized_widget.dart';
import '../../../setup.config.dart';

void main() {
  setupTestEnv();
  testWidgets("SpendingLimit render correctly", (tester) async {
    bool pressedRemove = false;

    await tester.pumpWidget(renderLocalizedWidget(
      SpendingLimit(
        currentLimitDay: 100,
        currentLimitWeek: 0,
        currentLimitMonth: 0,
        onRemove: () => pressedRemove = true,
      ),
    ));
    await tester.pumpAndSettle();

    expect(find.text(formatPrice(100)), findsOne);
    expect(find.text('No limit'), findsExactly(2));

    await tester.tap(find.byType(IconButton));
    expect(pressedRemove, true);
  });
}
