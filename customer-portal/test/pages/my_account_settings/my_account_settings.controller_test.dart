import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/mockito.dart';
import 'package:pacific_2_customer_portal/controllers/app.controller.dart';
import 'package:pacific_2_customer_portal/models/user_preference.model.dart';
import 'package:pacific_2_customer_portal/models/user_profile.model.dart';
import 'package:pacific_2_customer_portal/pages/my_account_settings/my_account_settings.controller.dart';

import '../../mocks/mocks.mocks.dart';
import '../../mocks/responses/metadata.response.mock.dart';
import '../../mocks/responses/user_preference.response.mock.dart';
import '../../setup.config.dart';

void main() {
  setupTestEnv();
  final appController = Get.find<AppController>();
  appController.userProfile.value = UserProfile(id: '1');

  final mockUserService = MockUserService();
  final mockUtilityService = MockUtilityService();
  late MyAccountSettingsController accountSettingsController;

  setUp(() {
    accountSettingsController =
        MyAccountSettingsController(mockUserService, mockUtilityService);
    when(mockUserService.getUserPreference(
            any, UserPreferenceKey.dateTimeFormat.key))
        .thenAnswer((_) async => mockDateTimePrefResponse);
    when(mockUserService.getUserPreference(
            any, UserPreferenceKey.notificationPayment.key))
        .thenAnswer((_) async => mockPaymentNotiPrefResponse);
    when(mockUserService.getUserPreference(
            any, UserPreferenceKey.notificationOrder.key))
        .thenAnswer((_) async => mockOrderNotiPrefResponse);
    when(mockUserService.getUserPreference(
            any, UserPreferenceKey.notificationWallet.key))
        .thenAnswer((_) async => mockWalletNotiPrefResponse);
    when(mockUserService.getUserPreference(any, UserPreferenceKey.mfa.key))
        .thenAnswer((_) async => mockMFAPrefResponse);
    when(mockUtilityService.getDateFormatMetaData())
        .thenAnswer((_) async => mockDateFormatResponse);
    when(mockUtilityService.getTimeFormatMetaData())
        .thenAnswer((_) async => mockTimeFormatResponse);
    when(mockUtilityService.getTimeZoneMetaData())
        .thenAnswer((_) async => mockTimeZoneResponse);
  });

  tearDown(() {
    reset(mockUserService);
    reset(mockUtilityService);
  });

  test('Initial values for the MyAccountSettingsController', () {
    expect(accountSettingsController.dateFormatList, []);
    expect(accountSettingsController.timeFormatList, []);
    expect(accountSettingsController.timeZonelist, []);
    expect(accountSettingsController.dateTimeFormatPreference.value, null);
    expect(accountSettingsController.paymentNotiPreference.value, null);
    expect(accountSettingsController.orderNotiPreference.value, null);
    expect(accountSettingsController.walletNotiPreference.value, null);
  });

  test('MyAccountSettingsController should get metadata correctly', () async {
    await accountSettingsController.getMetaData();
    expect(
      accountSettingsController.dateFormatList.length,
      mockDateFormatResponse.length,
    );
    expect(
      accountSettingsController.timeFormatList.length,
      mockTimeFormatResponse.length,
    );
    expect(
      accountSettingsController.timeZonelist.length,
      mockTimeZoneResponse.length,
    );
    verify(mockUtilityService.getDateFormatMetaData()).called(1);
    verify(mockUtilityService.getTimeFormatMetaData()).called(1);
    verify(mockUtilityService.getTimeZoneMetaData()).called(1);
  });

  test('MyAccountSettingsController should get user pref correctly', () async {
    await accountSettingsController.getUserPreference();
    expect(
      accountSettingsController.dateTimeFormatPreference.value?.id,
      mockDateTimePrefResponse['id'],
    );
    expect(
      accountSettingsController.paymentNotiPreference.value?.id,
      mockPaymentNotiPrefResponse['id'],
    );
    expect(
      accountSettingsController.orderNotiPreference.value?.id,
      mockOrderNotiPrefResponse['id'],
    );
    expect(
      accountSettingsController.walletNotiPreference.value?.id,
      mockWalletNotiPrefResponse['id'],
    );
    expect(
      accountSettingsController.mfaPreference.value?.id,
      mockMFAPrefResponse['id'],
    );
    verify(mockUserService.getUserPreference(any, any)).called(5);
  });

  test(
    'MyAccountSettingsController should save date time format pref correctly',
    () async {
      when(mockUserService.createUserPreference(any, any))
          .thenAnswer((_) async => mockDateTimePrefResponse);
      when(mockUserService.updateUserPreference(any, any, any))
          .thenAnswer((_) async => mockDateTimePrefResponse);

      accountSettingsController.dateTimeFormatPreference.value =
          UserPreference(isDefault: true);
      final resultCreate = await accountSettingsController.saveDateTimeFormat();

      accountSettingsController.dateTimeFormatPreference.value =
          UserPreference(id: '', isDefault: false);
      final resultUpdate = await accountSettingsController.saveDateTimeFormat();

      expect(resultCreate, true);
      expect(resultUpdate, true);

      verify(mockUserService.createUserPreference(any, any)).called(1);
      verify(mockUserService.updateUserPreference(any, any, any)).called(1);
    },
  );

  test(
    'MyAccountSettingsController should save noti pref correctly',
    () async {
      when(mockUserService.createUserPreference(any, any))
          .thenAnswer((_) async => mockPaymentNotiPrefResponse);
      when(mockUserService.updateUserPreference(any, any, any))
          .thenAnswer((_) async => mockWalletNotiPrefResponse);

      accountSettingsController.paymentNotiPreference.value =
          UserPreference(isDefault: true);
      await accountSettingsController
          .saveNotiSettings(UserPreferenceKey.notificationPayment);

      accountSettingsController.walletNotiPreference.value =
          UserPreference(id: '', isDefault: false);
      await accountSettingsController
          .saveNotiSettings(UserPreferenceKey.notificationWallet);

      expect(
        accountSettingsController.paymentNotiPreference.value?.id,
        mockPaymentNotiPrefResponse['id'],
      );
      expect(
        accountSettingsController.walletNotiPreference.value?.id,
        mockWalletNotiPrefResponse['id'],
      );

      verify(mockUserService.createUserPreference(any, any)).called(1);
      verify(mockUserService.updateUserPreference(any, any, any)).called(1);
    },
  );

  test(
    'MyAccountSettingsController should save mfa pref correctly',
    () async {
      when(mockUserService.createUserPreference(any, any))
          .thenAnswer((_) async => mockMFAPrefResponse);
      when(mockUserService.updateUserPreference(any, any, any))
          .thenAnswer((_) async => mockMFAPrefResponse);

      accountSettingsController.mfaPreference.value =
          UserPreference(isDefault: true);
      await accountSettingsController.saveMFASettings();

      accountSettingsController.mfaPreference.value =
          UserPreference(id: '', isDefault: false);
      await accountSettingsController.saveMFASettings();

      expect(
        accountSettingsController.mfaPreference.value?.id,
        mockMFAPrefResponse['id'],
      );

      verify(mockUserService.createUserPreference(any, any)).called(1);
      verify(mockUserService.updateUserPreference(any, any, any)).called(1);
    },
  );
}
