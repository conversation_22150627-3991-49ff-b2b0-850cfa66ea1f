final Map<String, dynamic> mockWalletListResponse = {
  "content": [
    {
      "walletId": "119777210019456000",
      "name": "Test Wallet",
      "tenantId": "115697404415799296",
      "customerId": "116039188868036608",
      "fundSourceId": null,
      "status": "ACTIVE",
      "type": "DEPOSIT",
      "currency": {
        "displayName": "US Dollar",
        "numericCode": 840,
        "currencyCode": "USD",
        "symbol": "\$",
        "fractionDigits": 2
      },
      "spendingLimit": 1000,
      "balance": 10000,
      "priority": 1,
      "walletEntryDtos": null,
      "createdAt": 1728557112271
    },
    {
      "walletId": "119777210019456001",
      "name": "Scholarship",
      "tenantId": "115697404415799296",
      "customerId": "116039188868036608",
      "fundSourceId": null,
      "status": "ACTIVE",
      "type": "FUNDED",
      "currency": {
        "displayName": "US Dollar",
        "numericCode": 840,
        "currencyCode": "USD",
        "symbol": "\$",
        "fractionDigits": 2
      },
      "balance": 500,
      "priority": 0,
      "walletEntryDtos": [
        {
          "remainingBalance": 0,
          "currency": {
            "displayName": "string",
            "numericCode": 0,
            "currencyCode": "string",
            "symbol": "string",
            "fractionDigits": 0
          },
          "balance": 200,
          "description": "string",
          "expiresOn": DateTime.now().add(const Duration(days: 2)).millisecondsSinceEpoch,
          "createdAt": 0
        },
        {
          "remainingBalance": 0,
          "currency": {
            "displayName": "string",
            "numericCode": 0,
            "currencyCode": "string",
            "symbol": "string",
            "fractionDigits": 0
          },
          "balance": 100,
          "description": "string",
          "expiresOn": DateTime.now().add(const Duration(days: 1)).millisecondsSinceEpoch,
          "createdAt": 0
        },
        {
          "remainingBalance": 0,
          "currency": {
            "displayName": "string",
            "numericCode": 0,
            "currencyCode": "string",
            "symbol": "string",
            "fractionDigits": 0
          },
          "balance": 300,
          "description": "string",
          "expiresOn": DateTime.now().add(const Duration(days: 3)).millisecondsSinceEpoch,
          "createdAt": 0
        }
      ],
      "createdAt": 1728557112271
    }
  ],
  "totalElements": 1,
  "totalPages": 1,
  "page": 0,
  "sort": ["id,ASC"]
};

final Map<String, dynamic> mockWalletExpirationResponse = {
  "walletId": "119777210019456001",
  "amount": 100,
  "time": DateTime.now().add(const Duration(days: 3)).millisecondsSinceEpoch,
};
