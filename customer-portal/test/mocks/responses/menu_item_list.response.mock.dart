final Map<String, dynamic> mockMenuItemListResponse = {
  "content": [
    {
      "id": "1",
      "preOrderMenuId": "string",
      "chainId": "string",
      "mealTimeId": "breakfast",
      "mealTime": {
        "id": "118624583874426880",
        "tenantId": "115697404415799296",
        "name": "Breakfast",
        "startTime": "10:24:54",
        "endTime": "13:24:54",
        "color": "#FFFFFF",
        "createdAt": 1728282304739,
        "updatedAt": 1729219286688
      },
      "product": {
        "id": "string",
        "tenantId": "string",
        "category": {
          "id": "1",
          "tenantId": "string",
          "name": "Hot food",
          "icon": {"path": "string", "url": "string"},
          "description": "string",
          "createdAt": 0,
          "updatedAt": 0
        },
        "storeId": "string",
        "healthierChoice": {
          "id": "string",
          "tenantId": "string",
          "name": "string",
          "symbol": {"path": "string", "url": "string"},
          "description": "string",
          "createdAt": 0,
          "updatedAt": 0
        },
        "name": "test item",
        "sku": "string",
        "briefInformation": "string",
        "description": "string",
        "ingredients": "Tofu, Pork",
        "barcode": "string",
        "status": "ACTIVE",
        "inventory": {
          "tracking": true,
          "quantity": 0,
          "minimumQuantityOrder": 0,
          "maximumQuantityOrder": 0,
          "step": 1,
          "createdAt": 0,
          "updatedAt": 0
        },
        "unitPrice": 1200,
        "listingPrice": 1300,
        "currency": {
          "displayName": "string",
          "numericCode": 0,
          "currencyCode": "string",
          "symbol": "string",
          "fractionDigits": 0
        },
        "preparationTime": 0,
        "images": [
          {
            "id": "string",
            "position": 0,
            "image": {"path": "string", "url": "string"},
            "createdAt": 0,
            "updatedAt": 0
          }
        ],
        "createdAt": 0,
        "updatedAt": 0
      },
      "menu": {
        "id": "132694526803119104",
        "tenantId": "2",
        "version": 0,
        "storeId": "131091070931377152",
        "name": "Menu name 2",
        "description": "Description",
        "type": "DELIVERY",
        "status": "ACTIVE",
        "cutOffTime": "23:59:59",
        "cutOffDays": 0
      },
      "date": "string",
      "capacity": 1000,
      "ordered": 800,
      "createdAt": 0,
      "updatedAt": 0
    },
    {
      "id": "2",
      "preOrderMenuId": "string",
      "chainId": "string",
      "mealTimeId": "breakfast",
      "mealTime": {
        "id": "118624583874426880",
        "tenantId": "115697404415799296",
        "name": "Breakfast",
        "startTime": "10:24:54",
        "endTime": "13:24:54",
        "color": "#FFFFFF",
        "createdAt": 1728282304739,
        "updatedAt": 1729219286688
      },
      "product": {
        "id": "string",
        "tenantId": "string",
        "category": {
          "id": "2",
          "tenantId": "string",
          "name": "Cold food",
          "icon": {"path": "string", "url": "string"},
          "description": "string",
          "createdAt": 0,
          "updatedAt": 0
        },
        "storeId": "string",
        "healthierChoice": {
          "id": "string",
          "tenantId": "string",
          "name": "string",
          "symbol": {"path": "string", "url": "string"},
          "description": "string",
          "createdAt": 0,
          "updatedAt": 0
        },
        "name": "string",
        "sku": "string",
        "briefInformation": "string",
        "description": "string",
        "ingredients": "string",
        "barcode": "string",
        "status": "ACTIVE",
        "inventory": {
          "tracking": true,
          "quantity": 0,
          "minimumQuantityOrder": 0,
          "maximumQuantityOrder": 0,
          "step": 2,
          "createdAt": 0,
          "updatedAt": 0
        },
        "unitPrice": 0,
        "listingPrice": null,
        "currency": {
          "displayName": "string",
          "numericCode": 0,
          "currencyCode": "string",
          "symbol": "string",
          "fractionDigits": 0
        },
        "preparationTime": 0,
        "images": [
          {
            "id": "string",
            "position": 0,
            "image": {"path": "string", "url": "string"},
            "createdAt": 0,
            "updatedAt": 0
          }
        ],
        "createdAt": 0,
        "updatedAt": 0
      },
      "date": "string",
      "capacity": 1000,
      "ordered": 1000,
      "createdAt": 0,
      "updatedAt": 0
    },
    {
      "id": "3",
      "preOrderMenuId": "string",
      "chainId": "string",
      "mealTimeId": "dinner",
      "mealTime": {
        "id": "118624583874426881",
        "tenantId": "115697404415799296",
        "name": "Dinner",
        "startTime": "10:24:54",
        "endTime": "13:24:54",
        "color": "#FFFFFF",
        "createdAt": 1728282304739,
        "updatedAt": 1729219286688
      },
      "product": {
        "id": "string",
        "tenantId": "string",
        "category": {
          "id": "1",
          "tenantId": "string",
          "name": "Hot food",
          "icon": {"path": "string", "url": "string"},
          "description": "string",
          "createdAt": 0,
          "updatedAt": 0
        },
        "storeId": "string",
        "healthierChoice": {
          "id": "string",
          "tenantId": "string",
          "name": "string",
          "symbol": {"path": "string", "url": "string"},
          "description": "string",
          "createdAt": 0,
          "updatedAt": 0
        },
        "name": "string",
        "sku": "string",
        "briefInformation": "string",
        "description": "string",
        "ingredients": "string",
        "barcode": "string",
        "status": "ACTIVE",
        "inventory": {
          "tracking": true,
          "quantity": 0,
          "minimumQuantityOrder": 0,
          "maximumQuantityOrder": 0,
          "step": 1,
          "createdAt": 0,
          "updatedAt": 0
        },
        "unitPrice": 0,
        "listingPrice": 0,
        "currency": {
          "displayName": "string",
          "numericCode": 0,
          "currencyCode": "string",
          "symbol": "string",
          "fractionDigits": 0
        },
        "preparationTime": 0,
        "images": [
          {
            "id": "string",
            "position": 0,
            "image": {"path": "string", "url": "string"},
            "createdAt": 0,
            "updatedAt": 0
          }
        ],
        "createdAt": 0,
        "updatedAt": 0
      },
      "date": "string",
      "capacity": 1000,
      "ordered": 999,
      "createdAt": 0,
      "updatedAt": 0
    }
  ],
  "totalElements": 0,
  "totalPages": 0,
  "page": 0,
  "sort": ["string"]
};
