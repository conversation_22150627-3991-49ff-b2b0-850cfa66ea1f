final Map<String, dynamic> mockSpendingLimitResponse = {
  "content": [
    {
      "tenantId": "115697404415799296",
      "customerId": "116039188868036608",
      "walletId": "119777210019456000",
      "currency": {
        "displayName": "US Dollar",
        "numericCode": 840,
        "currencyCode": "USD",
        "symbol": "\$",
        "fractionDigits": 2
      },
      "spendingLimit": 0,
      "amount": 0,
      "type": "WEEK"
    },
    {
      "tenantId": "115697404415799296",
      "customerId": "116039188868036608",
      "walletId": "119777210019456000",
      "currency": {
        "displayName": "US Dollar",
        "numericCode": 840,
        "currencyCode": "USD",
        "symbol": "\$",
        "fractionDigits": 2
      },
      "spendingLimit": 1000,
      "amount": 0,
      "type": "DAY"
    },
    {
      "tenantId": "115697404415799296",
      "customerId": "116039188868036608",
      "walletId": "119777210019456000",
      "currency": {
        "displayName": "US Dollar",
        "numericCode": 840,
        "currencyCode": "USD",
        "symbol": "\$",
        "fractionDigits": 2
      },
      "spendingLimit": 2000,
      "amount": 0,
      "type": "MONTH"
    }
  ]
};
