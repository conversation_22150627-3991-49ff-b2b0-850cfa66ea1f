events {
    worker_connections 1024;
}
http {
	include       mime.types;
	default_type  text/plain;
	server {
		listen 80 default_server;

		root /app;

		# normal routes
		# serve given url and default to index.html if not found
		# e.g. /, /user and /foo/bar will return index.html
		location / {
			add_header Cache-Control "no-store";
			try_files $uri $uri/index.html /portal/index.html;
		}

		location ~ (main.dart.js)$ {
			try_files $uri /portal/main.dart.js;
		}

        location ~ ^/portal/[^/]+/assets/ {
			# Capture the path after `/portal/` and before `/assets/`
			rewrite ^/portal/[^/]+/assets/(.*)$ /portal/assets/$1 break;
        
			# Check if the rewritten file exists and serve it
			try_files $uri $uri/ =404;
		}

		# files
		# for all routes matching a dot, check for files and return 404 if not found
		# e.g. /file.js returns a 404 if not found
		location ~ \.(?!html) {
			add_header Cache-Control "public, max-age=2678400";
			try_files $uri =404;
		}
	}
}
