/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.sfpt.adapter.commons.commandline;

import com.styl.pacific.sfpt.adapter.commons.SfptAdapterException;
import java.io.IOException;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.plexus.util.StringUtils;

@Slf4j
@UtilityClass
public class CLIRunner {
	/**
	 * Run a command line command
	 *
	 * @param command the command to run
	 */
	public static void run(String command) {
		if (StringUtils.isBlank(command)) {
			throw new SfptAdapterException("Command is required");
		}
		final var processBuilder = new ProcessBuilder("sh", "-c", command);
		processBuilder.redirectErrorStream(true);
		try {
			final var process = processBuilder.start();
			try (var reader = new java.io.BufferedReader(new java.io.InputStreamReader(process.getInputStream()))) {
				String line;
				while ((line = reader.readLine()) != null) {
					log.info(line);
				}
			}

			int exitCode = process.waitFor();

			if (exitCode != 0) {
				throw new SfptAdapterException("Failed to execute command. Exit code: " + exitCode
						+ " Command: "
						+ command);
			}

		} catch (IOException | InterruptedException e) {
			throw new SfptAdapterException(e.getMessage(), e);
		}
	}
}
