/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.tenant.service.domain.entity;

import static org.assertj.core.api.Assertions.assertThat;

import com.styl.pacific.domain.valueobject.TenantId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class BusinessFeatureTest {

	private final TenantId tenantId = new TenantId(1L);
	private final String name = "C-Portal";
	private final String icon = "icon1";
	private final Boolean enabled = true;
	private final String groupName = "group1";
	private final String code = "code1";
	private final String description = "business feature";
	private BusinessFeature businessFeature;

	@BeforeEach
	public void setUp() {
		businessFeature = BusinessFeature.builder()
				.name(name)
				.icon(icon)
				.enabled(enabled)
				.code(code)
				.groupName(groupName)
				.description(description)
				.build();
	}

	@Test
	void testGetNameWhenNameIsSetThenReturnCorrectName() {
		// Act
		String result = businessFeature.getName();

		// Assert
		assertThat(result).isEqualTo(name);
	}

	@Test
	void testGetIconWhenIconIsSetThenReturnCorrectIcon() {
		// Act
		String result = businessFeature.getIcon();

		// Assert
		assertThat(result).isEqualTo(icon);
	}

	@Test
	void testGetEnabledWhenEnabledIsSetThenReturnCorrectEnabled() {
		// Act
		Boolean result = businessFeature.getEnabled();

		// Assert
		assertThat(result).isEqualTo(enabled);
	}

	@Test
	void testGetGroupNameWhenGroupNameIsSetThenReturnCorrectGroupName() {
		// Act
		String result = businessFeature.getGroupName();

		// Assert
		assertThat(result).isEqualTo(groupName);
	}

	@Test
	void testGetCodeWhenCodeIsSetThenReturnCode() {
		// Act
		String result = businessFeature.getCode();

		// Assert
		assertThat(result).isEqualTo(code);
	}

	@Test
	void testGetDescriptionWhenDescriptionIsSetThenReturnCorrectDescription() {
		// Act
		String result = businessFeature.getDescription();

		// Assert
		assertThat(result).isEqualTo(description);
	}

	@Test
	void testEqualsWhenDifferentObjectThenReturnFalse() {
		// Arrange
		BusinessFeature another = BusinessFeature.builder()
				.name("C-Portal")
				.groupName("Group1")
				.code("code1")
				.enabled(false)
				.icon("icon1")
				.description("business feature")
				.tenantId(tenantId)
				.build();

		// Act & Assert
		assertThat(businessFeature).isNotEqualTo(another);
	}

	@Test
	void testEqualsWhenSameObjectThenReturnTrue() {
		// Arrange
		BusinessFeature sameBusinessFeature = BusinessFeature.builder()
				.name(name)
				.icon(icon)
				.enabled(enabled)
				.code(code)
				.groupName(groupName)
				.description(description)
				.build();
		// Act & Assert
		assertThat(businessFeature).isEqualTo(sameBusinessFeature);
	}
}
