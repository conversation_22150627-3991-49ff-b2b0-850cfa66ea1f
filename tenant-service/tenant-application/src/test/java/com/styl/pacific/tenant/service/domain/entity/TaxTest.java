/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.tenant.service.domain.entity;

import static org.assertj.core.api.Assertions.assertThat;

import com.styl.pacific.domain.valueobject.TaxId;
import com.styl.pacific.domain.valueobject.TenantId;
import java.math.BigDecimal;
import java.time.Instant;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class TaxTest {

	private final TenantId tenantId = new TenantId(1L);
	private final TaxId taxId = new TaxId(1L);
	private final String name = "VAT";
	private final String taxRegNo = "*********";
	private final BigDecimal rate = BigDecimal.valueOf(0.2);
	private final String description = "description123";
	private final Boolean includeInPrice = true;
	private final Boolean enabled = true;
	private final Instant effectiveDate = Instant.now();
	private Tax tax;

	@BeforeEach
	public void setUp() {
		tax = Tax.builder()
				.id(taxId)
				.tenantId(tenantId)
				.includeInPrice(includeInPrice)
				.name(name)
				.description(description)
				.enabled(enabled)
				.taxRegNo(taxRegNo)
				.rate(rate)
				.effectiveDate(effectiveDate)
				.build();
	}

	@Test
	void testGetNameWhenNameIsSetThenReturnCorrectName() {
		// Act
		String result = tax.getName();

		// Assert
		assertThat(result).isEqualTo(name);
	}

	@Test
	void testGetTaxRegNoWhenTaxRegNoIsSetThenReturnCorrectTaxRegNo() {
		// Act
		String result = tax.getTaxRegNo();

		// Assert
		assertThat(result).isEqualTo(taxRegNo);
	}

	@Test
	void testGetEnabledWhenEnabledIsSetThenReturnCorrectEnabled() {
		// Act
		Boolean result = tax.getEnabled();

		// Assert
		assertThat(result).isEqualTo(enabled);
	}

	@Test
	void testEffectiveDateWhenEffectiveDateIsSetThenReturnCorrectEffectiveDate() {
		// Act
		Instant result = tax.getEffectiveDate();

		// Assert
		assertThat(result).isEqualTo(effectiveDate);
	}

	@Test
	void testIncludeInPriceWhenIncludeInPriceIsSetThenReturnIncludeInPrice() {
		// Act
		Boolean result = tax.getIncludeInPrice();

		// Assert
		assertThat(result).isEqualTo(includeInPrice);
	}

	@Test
	void testGetRateWhenRateIsSetThenReturnCorrectRate() {
		// Act
		BigDecimal result = tax.getRate();

		// Assert
		assertThat(result).isEqualTo(rate);
	}

	@Test
	void testGetEnabledWhenEnableIsSetThenReturnCorrectEnable() {
		// Act
		Boolean result = tax.getEnabled();

		// Assert
		assertThat(result).isEqualTo(enabled);
	}

	@Test
	void testEqualsWhenDifferentObjectThenReturnFalse() {
		// Arrange
		Tax another = Tax.builder()
				.id(new TaxId(2L))
				.tenantId(tenantId)
				.includeInPrice(false)
				.name("TN")
				.description(description)
				.enabled(enabled)
				.taxRegNo("123456")
				.rate(rate)
				.effectiveDate(effectiveDate)
				.build();

		// Act & Assert
		assertThat(tax).isNotEqualTo(another);
	}

	@Test
	void testEqualsWhenSameObjectThenReturnTrue() {
		// Arrange
		Tax sameTax = Tax.builder()
				.id(taxId)
				.tenantId(tenantId)
				.includeInPrice(includeInPrice)
				.name(name)
				.description(description)
				.enabled(enabled)
				.taxRegNo(taxRegNo)
				.rate(rate)
				.effectiveDate(effectiveDate)
				.build();
		// Act & Assert
		assertThat(tax).isEqualTo(sameTax);
	}
}
