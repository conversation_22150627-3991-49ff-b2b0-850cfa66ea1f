/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.tenant.service.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.styl.pacific.domain.valueobject.BusinessFeatureId;
import com.styl.pacific.domain.valueobject.TaxId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.tenant.service.domain.dto.CreateTaxCommand;
import com.styl.pacific.tenant.service.domain.dto.CreateTenantCommand;
import com.styl.pacific.tenant.service.domain.dto.UpdateTaxCommand;
import com.styl.pacific.tenant.service.domain.dto.UpdateTenantCommand;
import com.styl.pacific.tenant.service.domain.entity.BusinessFeature;
import com.styl.pacific.tenant.service.domain.entity.Tax;
import com.styl.pacific.tenant.service.domain.entity.Tenant;
import com.styl.pacific.tenant.service.domain.mapper.TenantDataMapper;
import java.math.BigDecimal;
import java.time.Instant;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mapstruct.factory.Mappers;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class TenantDataMapperTest {

	@InjectMocks
	private TenantDataMapper tenantDataMapper = Mappers.getMapper(TenantDataMapper.class);

	private CreateTenantCommand createTenantCommand;
	private UpdateTenantCommand updateTenantCommand;
	private TenantId tenantId;

	@BeforeEach
	public void setUp() {
		createTenantCommand = CreateTenantCommand.builder()
				.name("Test Tenant")
				.email("<EMAIL>")
				.phoneNumber("1234567890")
				.addressLine1("123 Test St")
				.addressLine2("Apt 4")
				.city("Test City")
				.country("Test Country")
				.postalCode("12345")
				.build();

		updateTenantCommand = UpdateTenantCommand.builder()
				.tenantId(1L)
				.name("Updated Tenant")
				.email("<EMAIL>")
				.phoneNumber("0987654321")
				.addressLine1("456 Updated St")
				.addressLine2("Apt 5")
				.city("Updated City")
				.country("Updated Country")
				.postalCode("54321")
				.build();

		tenantId = new TenantId(1L);
	}

	@Test
	void testCreateTenantCommandToTenantWhenValidCommandThenReturnTenant() {
		// Act
		Tenant result = tenantDataMapper.createTenantCommandToTenant(createTenantCommand);

		// Assert
		assertNotNull(result);
		assertEquals(createTenantCommand.getName(), result.getName());
		assertEquals(createTenantCommand.getEmail(), result.getEmail());
		assertEquals(createTenantCommand.getPhoneNumber(), result.getPhoneNumber());
		assertEquals(createTenantCommand.getAddressLine1(), result.getAddress()
				.getAddressLine1());
		assertEquals(createTenantCommand.getAddressLine2(), result.getAddress()
				.getAddressLine2());
		assertEquals(createTenantCommand.getCity(), result.getAddress()
				.getCity());
		assertEquals(createTenantCommand.getCountry(), result.getAddress()
				.getCountry());
		assertEquals(createTenantCommand.getPostalCode(), result.getAddress()
				.getPostalCode());
	}

	@Test
	void testUpdateTenantCommandToTenantWhenValidCommandThenReturnTenant() {
		Instant now = Instant.now();
		// Arrange
		Tenant existingTenant = Tenant.builder()
				.id(new TenantId(updateTenantCommand.getTenantId()))
				.name("Existing Tenant")
				.email("<EMAIL>")
				.createdAt(now)
				.updatedAt(now)
				.build();

		// Act
		Tenant result = tenantDataMapper.updateTenantCommandToTenant(updateTenantCommand, existingTenant);

		// Assert
		assertNotNull(result);
		assertEquals(updateTenantCommand.getTenantId(), result.getId()
				.getValue());
		assertEquals(updateTenantCommand.getName(), result.getName());
		assertEquals(updateTenantCommand.getEmail(), result.getEmail());
		assertEquals(updateTenantCommand.getPhoneNumber(), result.getPhoneNumber());
		assertEquals(updateTenantCommand.getAddressLine1(), result.getAddress()
				.getAddressLine1());
		assertEquals(updateTenantCommand.getAddressLine2(), result.getAddress()
				.getAddressLine2());
		assertEquals(updateTenantCommand.getCity(), result.getAddress()
				.getCity());
		assertEquals(updateTenantCommand.getCountry(), result.getAddress()
				.getCountry());
		assertEquals(updateTenantCommand.getPostalCode(), result.getAddress()
				.getPostalCode());
		assertEquals(existingTenant.getCreatedAt(), result.getCreatedAt());
		assertNotEquals(existingTenant.getUpdatedAt(), result.getUpdatedAt());
	}

	@Test
	void testCreateTenantCommandToTenantWhenCommandNullThenReturnNull() {
		// Act
		Tenant result = tenantDataMapper.createTenantCommandToTenant(null);

		// Assert
		Assertions.assertNull(result);
	}

	@Test
	void testUpdateTenantCommandToTenantWhenCommandNullThenReturnNull() {
		// Act
		Tenant result = tenantDataMapper.updateTenantCommandToTenant(null, null);

		// Assert
		Assertions.assertNull(result);
	}

	@Test
	void testBusinessFeatureIdToLongWhenValidBusinessFeatureIdThenReturnLong() {
		// Arrange
		BusinessFeatureId businessFeatureId = new BusinessFeatureId(1L);

		// Act
		Long result = tenantDataMapper.businessFeatureIdToLong(businessFeatureId);

		// Assert
		assertEquals(businessFeatureId.getValue(), result);
	}

	@Test
	void testBusinessFeatureIdToLongWhenBusinessFeatureIdNullThenReturnNull() {
		// Act
		Long result = tenantDataMapper.businessFeatureIdToLong(null);

		// Assert
		Assertions.assertNull(result);
	}

	@Test
	void testUpdateBusinessFeatureToNewBusinessFeatureShouldOk() {
		// Arrange
		BusinessFeature businessFeatureExisting = BusinessFeature.builder()
				.tenantId(new TenantId(1L))
				.description("description1")
				.icon("icon1")
				.enabled(true)
				.code("code1")
				.name("C-Portal")
				.build();
		// Act
		BusinessFeature newBusinessFeature = tenantDataMapper.updateBusinessFeatureToNewBusinessFeature(
				businessFeatureExisting, true);

		// Assert
		assertEquals(businessFeatureExisting.getTenantId(), newBusinessFeature.getTenantId());
		assertEquals(businessFeatureExisting.getCode(), newBusinessFeature.getCode());
		assertEquals(businessFeatureExisting.getDescription(), newBusinessFeature.getDescription());
		assertEquals(businessFeatureExisting.getName(), newBusinessFeature.getName());
		assertEquals(true, newBusinessFeature.getEnabled());
	}

	//	@Test
	//	void testEnumBusinessFeatureToBusinessFeatureShouldOk() {
	//		// Arrange
	//		BusinessFeatureEnum businessFeatureEnum = BusinessFeatureEnum.CUSTOMER_SELF_REGISTRATION;
	//		BusinessFeatureId businessFeatureId = new BusinessFeatureId(1L);
	//		// Act
	//		BusinessFeature newBusinessFeature = tenantDataMapper.enumBusinessFeatureToBusinessFeature(businessFeatureEnum,
	//				tenantId, businessFeatureId, true);
	//		// Assert
	//		assertEquals(businessFeatureEnum.getCode(), newBusinessFeature.getCode());
	//		assertEquals(businessFeatureEnum.getDescription(), newBusinessFeature.getDescription());
	//		assertEquals(businessFeatureEnum.getIcon(), newBusinessFeature.getIcon());
	//		assertEquals(businessFeatureEnum.getGroupName(), newBusinessFeature.getGroupName());
	//		assertEquals(tenantId, newBusinessFeature.getTenantId());
	//		assertEquals(true, newBusinessFeature.getEnabled());
	//	}

	//	@Test
	//	void testEnumBusinessFeatureToBusinessFeatureMasterDataShouldOk() {
	//		// Arrange
	//		BusinessFeatureEnum businessFeatureEnum = BusinessFeatureEnum.CUSTOMER_SELF_REGISTRATION;
	//
	//		// Act
	//		BusinessFeature newBusinessFeature = tenantDataMapper.enumBusinessFeatureToBusinessFeature(businessFeatureEnum);
	//		// Assert
	//		assertEquals(businessFeatureEnum.getCode(), newBusinessFeature.getCode());
	//		assertEquals(businessFeatureEnum.getDescription(), newBusinessFeature.getDescription());
	//		assertEquals(businessFeatureEnum.getIcon(), newBusinessFeature.getIcon());
	//		assertEquals(businessFeatureEnum.getGroupName(), newBusinessFeature.getGroupName());
	//	}

	@Test
	void testCreateTaxCommandToTaxShouldOk() {
		// Arrange
		CreateTaxCommand command = CreateTaxCommand.builder()
				.name("VAT")
				.taxRegNo("123456")
				.tenantId(1L)
				.taxRegNo("*********")
				.rate(BigDecimal.valueOf(0.3))
				.effectiveDate(Instant.now())
				.enabled(true)
				.includeInPrice(true)
				.build();

		// Act
		Tax tax = tenantDataMapper.createTaxCommandToTax(command, new TaxId(1L));
		// Assert
		assertEquals(new TaxId(1L), tax.getId());
		assertEquals(command.getTaxRegNo(), tax.getTaxRegNo());
		assertEquals(command.getDescription(), tax.getDescription());
		assertEquals(command.getName(), tax.getName());
		assertEquals(command.getEnabled(), tax.getEnabled());
		assertEquals(command.getIncludeInPrice(), tax.getIncludeInPrice());
		assertEquals(command.getEffectiveDate(), tax.getEffectiveDate());
	}

	@Test
	void testUpdateTaxCommandToTaxShouldOk() {
		// Arrange
		UpdateTaxCommand command = UpdateTaxCommand.builder()
				.name("VAT")
				.taxRegNo("123456")
				.taxRegNo("*********")
				.rate(BigDecimal.valueOf(0.3))
				.effectiveDate(Instant.now())
				.enabled(true)
				.includeInPrice(true)
				.build();

		Tax taxExisting = Tax.builder()
				.id(new TaxId(1L))
				.tenantId(tenantId)
				.includeInPrice(true)
				.name("name123")
				.description("des1234")
				.enabled(true)
				.taxRegNo("*********")
				.rate(BigDecimal.valueOf(0.2))
				.effectiveDate(Instant.now())
				.build();

		// Act
		Tax tax = tenantDataMapper.updateTaxCommandToTax(command, taxExisting);
		// Assert
		assertEquals(new TaxId(1L), tax.getId());
		assertEquals(command.getTaxRegNo(), tax.getTaxRegNo());
		assertEquals(command.getDescription(), tax.getDescription());
		assertEquals(command.getName(), tax.getName());
		assertEquals(command.getEnabled(), tax.getEnabled());
		assertEquals(command.getIncludeInPrice(), tax.getIncludeInPrice());
		assertEquals(command.getEffectiveDate(), tax.getEffectiveDate());
	}
}
