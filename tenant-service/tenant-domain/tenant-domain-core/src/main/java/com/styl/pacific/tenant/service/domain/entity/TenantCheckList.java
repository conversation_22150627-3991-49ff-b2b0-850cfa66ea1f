/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.tenant.service.domain.entity;

import com.styl.pacific.domain.valueobject.TenantId;
import java.time.Instant;

/**
 * <AUTHOR>
 */

public class TenantCheckList {

	private final Long itemId;
	private final TenantId tenantId;
	private final String name;
	private final String description;
	private final boolean allowedManualUpdate;
	private final Instant updatedAt;
	private boolean completed;

	public TenantCheckList(Builder builder) {
		this.itemId = builder.itemId;
		this.tenantId = builder.tenantId;
		this.name = builder.name;
		this.description = builder.description;
		this.allowedManualUpdate = builder.allowedManualUpdate;
		this.completed = builder.completed;
		this.updatedAt = builder.updatedAt;
	}

	public static Builder builder() {
		return new Builder();
	}

	public Long getItemId() {
		return itemId;
	}

	public TenantId getTenantId() {
		return tenantId;
	}

	public String getName() {
		return name;
	}

	public String getDescription() {
		return description;
	}

	public boolean isAllowedManualUpdate() {
		return allowedManualUpdate;
	}

	public boolean isCompleted() {
		return completed;
	}

	public void setCompleted(boolean completed) {
		this.completed = completed;
	}

	public Instant getUpdatedAt() {
		return updatedAt;
	}

	public static final class Builder {
		private Long itemId;
		private TenantId tenantId;
		private String name;
		private String description;
		private boolean allowedManualUpdate;
		private boolean completed;
		private Instant updatedAt;

		private Builder() {
		}

		public static Builder aTenantCheckList() {
			return new Builder();
		}

		public Builder itemId(Long itemId) {
			this.itemId = itemId;
			return this;
		}

		public Builder tenantId(TenantId tenantId) {
			this.tenantId = tenantId;
			return this;
		}

		public Builder name(String name) {
			this.name = name;
			return this;
		}

		public Builder description(String description) {
			this.description = description;
			return this;
		}

		public Builder allowedManualUpdate(boolean allowedManualUpdate) {
			this.allowedManualUpdate = allowedManualUpdate;
			return this;
		}

		public Builder completed(boolean completed) {
			this.completed = completed;
			return this;
		}

		public Builder updatedAt(Instant updatedAt) {
			this.updatedAt = updatedAt;
			return this;
		}

		public TenantCheckList build() {
			return new TenantCheckList(this);
		}
	}
}
