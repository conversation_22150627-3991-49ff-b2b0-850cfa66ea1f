/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.tenant.service.domain.dto;

import com.styl.pacific.tenant.service.domain.entity.BusinessType;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 */

@Getter
@EqualsAndHashCode(callSuper = true)
public class CreateTenantCommand extends BaseTenantCommand {

	@Builder
	public CreateTenantCommand(@NotBlank @Length(max = 100) String name, @Length(max = 100) String businessRegNo,
			@NotBlank BusinessType businessType, @Email @NotBlank @Length(max = 255) String email,
			@NotBlank String phoneNumber, String contactRemarks, @Email @Length(max = 255) String email2,
			String phoneNumber2, String contactRemarks2, @Email @Length(max = 255) String email3, String phoneNumber3,
			String contactRemarks3, @Length(max = 255) String website, @Length(max = 255) String logoPath,
			@NotBlank @Length(max = 255) String addressLine1, @Length(max = 255) String addressLine2,
			@NotBlank @Length(max = 100) String city, @NotBlank String country, @Length(max = 10) String postalCode) {
		super(name, businessRegNo, businessType, email, phoneNumber, contactRemarks, email2, phoneNumber2,
				contactRemarks2, email3, phoneNumber3, contactRemarks3, website, logoPath, addressLine1, addressLine2,
				city, country, postalCode);
	}
}
