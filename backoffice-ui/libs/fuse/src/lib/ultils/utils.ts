import { DateTime } from 'luxon';

interface AnyObject {
  [key: string]: string | AnyObject | undefined;
}

export class FuseUltils {
  /**
   * Convert object to query string
   *
   * @param params
   * @param prefix
   */
  static objectToQueryString(params: any, prefix?: string): string {
    const query = Object.keys(params)
      .filter(
        (key) =>
          params[key] !== null &&
          params[key] !== undefined &&
          params[key] !== '',
      )
      .map((k) => {
        const key = k;
        const value = params[key];
        if (typeof value === 'object' && !Array.isArray(value)) {
          return this.objectToQueryString(
            value,
            prefix ? `${prefix}.${key}` : key,
          );
        } else if (Array.isArray(value)) {
          return value
            .map(
              (v) =>
                `${prefix ? `${prefix}.${key}` : key}=${encodeURIComponent(v)}`,
            )
            .join('&');
        } else {
          return `${prefix ? `${prefix}.${key}` : key}=${encodeURIComponent(value)}`;
        }
      });
    return query.join('&');
  }

  /**
   * Check real mime type file
   * @param reader
   * @param type 1: only bin ext, default => image ext
   * @returns string
   */
  public static getRealMimeType(reader: any, type?: number): string {
    const arr = new Uint8Array(reader.result).subarray(0, 4);
    let header = '';
    let realMimeType;
    for (let i = 0; i < arr.length; i++) {
      header += arr[i].toString(16);
    }

    if (type && type === 1) {
      switch (header) {
        case 'e96220':
          realMimeType = 'bin';
          break;
        default:
          realMimeType = 'unknown';
          break;
      }
    } else {
      switch (header) {
        case '89504e47':
          realMimeType = 'png';
          break;
        case '47494638':
          realMimeType = 'gif';
          break;
        case 'ffd8ffDB':
        case 'ffd8ffe0':
        case 'ffd8ffe1':
        case 'ffd8ffe2':
        case 'ffd8ffe3':
        case 'ffd8ffe8':
          realMimeType = 'jpeg';
          break;
        case '3c737667':
        case '3c3f786d':
          realMimeType = 'svg';
          break;
        default:
          realMimeType = 'unknown';
          break;
      }
    }

    return realMimeType;
  }

  /**
   * Convert timestamp to local datetime
   * Convert Time use luxon-js
   * @param timestamp
   * @param format
   * @returns string
   */
  public static tsToLocalTime(timestamp: any, format?: any): any {
    const timestampInMillis =
      String(timestamp).length === 10
        ? Number(timestamp) * 1000
        : Number(timestamp);
    return DateTime.fromMillis(timestampInMillis).toFormat(
      format ? format : 'dd/LL/yyyy HH:mm:ss',
    );
  }

  /**
   * Convert timestamp to utc datetime
   * Convert Time use luxon-js
   * @param timestamp
   * @returns string
   */
  public static tsToUTC(timestamp: number): any {
    return DateTime.fromMillis(Number(timestamp)).toUTC();
    // return moment.utc(moment(timestamp)).format();
  }

  /**
   * Convert utc to timestamp millisecond
   * Convert Time use luxon-js
   * @param dateUTC
   * @returns string
   */
  public static utcToTs(dateUTC: string): any {
    return DateTime.fromISO(dateUTC, { zone: 'utc' }).toMillis();
    // return moment(dateUTC).format('x');
  }

  /**
   * Convert local datetime to utc
   * Convert Time use luxon-js
   * @param localTime
   * @returns string
   */
  public static localTimeToUTC(localTime: any): any {
    return DateTime.fromJSDate(localTime).toUTC();
    // return moment.utc(moment(localTime)).format();
  }

  /**
   * Convert time of day to utc timestamp
   * Convert Time use luxon-js
   * @param date
   * @returns number
   */
  public static timeOfDayToUTCTs(date: Date | number): number {
    const newDate = new Date(date);

    if (typeof date === 'number' || date === null || date === undefined) {
      return 0;
    }

    return DateTime.utc(
      1970,
      1,
      1,
      newDate.getHours(),
      newDate.getMinutes(),
      newDate.getSeconds(),
      0,
    ).valueOf();
  }

  /**
   * Convert utc timestamp to time of day
   * Convert Time use luxon-js
   * @param number
   * @returns date
   */
  public static utcTimestampToTimeOfDay(timestamp = 0): Date {
    return new Date(
      timestamp + new Date(timestamp).getTimezoneOffset() * 60 * 1000,
    );
  }

  /**
   * Get list timezone
   * @param
   * @returns array
   */
  public static getTimezones(): Array<any> {
    const timezones = (Intl as any).supportedValuesOf('timeZone');
    const listTimezones = Object.entries(timezones).map((zoneName: any) => {
      const timezone = DateTime.local({ zone: zoneName[1] }).toFormat('ZZ');
      const offset = DateTime.local({ zone: zoneName[1] }).offset;

      return {
        title: zoneName[1] + ` (UTC${timezone})`,
        key: zoneName[1],
        timezone: timezone,
        offset: offset,
      };
    });

    return listTimezones.sort((a, b) => a.offset - b.offset);
  }

  /**
   * Trim string value in the object
   * @param
   * @returns object
   */
  public static trimObjectValues(obj: AnyObject): AnyObject {
    const trimmedObj: AnyObject = {};
    for (const key in obj) {
      // eslint-disable-next-line no-prototype-builtins
      if (obj.hasOwnProperty(key)) {
        const value = obj[key];
        if (typeof value === 'string') {
          trimmedObj[key] = value.trim();
        } else {
          trimmedObj[key] = value;
        }
      }
    }
    return trimmedObj;
  }

  /**
   * Check value is null or empty
   * @param value
   * @returns boolean
   */
  public static isNullOrEmpty(value: any): boolean {
    if (value == null || value == undefined) {
      return true;
    } else if (typeof value === 'string' && value.trim() === '') {
      return true;
    }

    return false;
  }

  /**
   * Format a number as a currency string with specified decimal places.
   * @param price - The numeric value to be formatted.
   * @param fractionDigits - The number of decimal places to include in the formatted string.
   * @returns The formatted currency string.
   */
  public static formatCurrency(
    price: number,
    fractionDigits: number,
  ): number | null {
    if (!this.isNullOrEmpty(price)) {
      const priceStr = price.toString();

      if (fractionDigits == 0) {
        return Number(`${price}`);
      } else if (priceStr.length > fractionDigits) {
        return Number(
          `${priceStr.substring(0, priceStr.length - fractionDigits)}.${priceStr.substring(priceStr.length - fractionDigits)}`,
        );
      } else if (priceStr.length == fractionDigits) {
        return Number(`0.${priceStr}`);
      } else {
        return Number(
          `0.${'0'.repeat(fractionDigits - priceStr.length)}${priceStr}`,
        );
      }
    } else {
      return null;
    }
  }

  /**
   * Truncate the decimal part of a number to a specified number of digits.
   * @param price - The numeric value to be truncated.
   * @param fractionDigits - The number of decimal places to keep (default is 2).
   * @returns The truncated decimal string.
   */
  public static truncateDecimalCurrency(
    price: number,
    fractionDigits = 2,
  ): number | null {
    if (!this.isNullOrEmpty(price)) {
      const priceStr = price.toString();
      const integerPart = priceStr.split('.')[0];
      const decimalPart = priceStr.split('.')[1];

      if (decimalPart) {
        if (decimalPart.length > fractionDigits) {
          return Number(
            `${integerPart}${decimalPart.slice(0, fractionDigits)}`,
          );
        } else {
          return Number(
            `${integerPart}${decimalPart}${'0'.repeat(fractionDigits - decimalPart.length)}`,
          );
        }
      } else {
        return Number(`${integerPart}${'0'.repeat(fractionDigits)}`);
      }
    } else {
      return null;
    }
  }

  /**
   * Format a numeric price as a string with optional currency symbol and positioning.
   * @param price - The numeric value to be formatted as a price.
   * @param currency - An object containing currency details.
   * @param currency.fractionDigits - The number of decimal places to include in the formatted price.
   * @param currency.symbol - The symbol of the currency (e.g., "$", "€").
   * @param currency.code - The code of the currency (e.g., "USD", "EUR").
   * @param position - An optional string to specify the position of the currency symbol ('left' or 'right').
   * @returns The formatted price string with the specified currency symbol in the desired position.
   */
  public static formatPrice(
    price: number,
    currency: any,
    position = 'right',
  ): string | null {
    if (!this.isNullOrEmpty(price)) {
      const priceStr =
        this.formatCurrency(Math.abs(price), currency.fractionDigits) ?? '';
      const priceFormat = priceStr.toLocaleString('en-US', {
        minimumFractionDigits: currency.fractionDigits,
        maximumFractionDigits: currency.fractionDigits,
      });

      if (position == 'left') {
        return `${price < 0 ? '-' : ''}${priceFormat}${this.getCurrencyDisplay(currency)}`;
      } else {
        return `${price < 0 ? '-' : ''}${this.getCurrencyDisplay(currency)}${priceFormat}`;
      }
    } else {
      return null;
    }
  }

  /**
   * Returns a string representation of a currency based on its symbol or code.
   * @param currency - An object containing currency details.
   * @param currency.symbol - The symbol of the currency (e.g., "$", "€").
   * @param currency.currencyCode - The code of the currency (e.g., "USD", "EUR").
   * @returns The currency symbol, the code prefixed with a space, or an empty string if neither is provided.
   */
  public static getCurrencyDisplay(currency: any) {
    return currency.symbol || currency.currencyCode
      ? currency.symbol || ` ${currency.currencyCode}`
      : '';
  }

  /**
   * Recursively searches for a node by its unique identifier in a hierarchical tree structure.
   * @param tree - An array of nodes, where each node may contain an `id` property and a `children` property (an array of child nodes).
   * @param id - The unique identifier of the node to be found.
   * @returns  The first node that matches the given `id`, or `null` if no matching node is found.
   */
  public static findNodeById(tree: Array<any> = [], id: string): any {
    for (const node of tree) {
      if (node.id === id) {
        return node;
      }
      const childNode = this.findNodeById(node.children, id);
      if (childNode) {
        return childNode;
      }
    }
    return null;
  }

  /**
   * Open the specified URL in a new browser tab
   * @param url - The URL to open in a new tab
   */
  public static openNewTab(url: string): void {
    window.open(url, '_blank');
  }

  /**
   * Remove duplicate item in the array
   * @param array
   */
  public static removeDuplicateItems(array: any[], key: string): any[] {
    return array.filter(
      (item, index, self) =>
        index === self.findIndex((t) => t[key] === item[key]),
    );
  }

  /**
   * Check if the value is a valid number
   * @param value - The value to check
   * @return boolean - Returns true if the value is a valid number, false otherwise
   */
  public static isValidNumber(value: any) {
    if (this.isNullOrEmpty(value)) return false;

    const num = Number(value);
    return typeof num === 'number' && !isNaN(num);
  }
}
