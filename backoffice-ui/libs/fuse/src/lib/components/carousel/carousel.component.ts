import { <PERSON><PERSON><PERSON>, Ng<PERSON>tyle } from '@angular/common';
import { Component, Input, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { MatIcon } from '@angular/material/icon';
import { interval, Subject, takeUntil, timer } from 'rxjs';

@Component({
  selector: 'fuse-carousel',
  standalone: true,
  imports: [NgClass, NgStyle, MatIcon],
  templateUrl: './carousel.component.html',
})
export class FuseCarouselComponent implements OnInit, OnDestroy {
  @Input() slides: string[] = [];
  @Input() autoPlay = true;
  @Input() autoPlaySpeed = 10000;
  @Input() animationSpeed = 500;
  @Input() indicatorsVisible = false;
  @Input() navigateButtonVisible = false;

  currentSlide = 0;
  hidden = false;

  private _destroy$: Subject<void> = new Subject<void>();

  ngOnInit(): void {
    if (this.autoPlay) {
      interval(this.autoPlaySpeed)
        .pipe(takeUntil(this._destroy$))
        .subscribe(() => {
          this.gotoNextSlide();
        });
    }
  }

  ngOnDestroy(): void {
    this._destroy$.next();
    this._destroy$.complete();
  }

  gotoNextSlide(): void {
    const currentSlide = (this.currentSlide + 1) % this.slides.length;
    this.onJumpSelectedSlide(currentSlide);
  }

  gotoPreviousSlide(): void {
    const currentSlide =
      (this.currentSlide - 1 + this.slides.length) % this.slides.length;
    this.onJumpSelectedSlide(currentSlide);
  }

  onJumpSelectedSlide(index: number): void {
    this.hidden = true;
    timer(this.animationSpeed)
      .pipe(takeUntil(this._destroy$))
      .subscribe(() => {
        this.currentSlide = index;
        this.hidden = false;
      });
  }
}
