/* eslint-disable @typescript-eslint/no-empty-function */
import { Component, forwardRef, Input } from '@angular/core';
import {
  ControlValueAccessor,
  FormGroup,
  FormsModule,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { FuseErrorMessageComponent } from '../error-message/error-message.component';
import { TranslocoPipe } from '@jsverse/transloco';

@Component({
  selector: 'fuse-input',
  standalone: true,
  imports: [
    FormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatTooltipModule,
    ReactiveFormsModule,
    TranslocoPipe,
    FuseErrorMessageComponent,
  ],
  templateUrl: './input.component.html',
  styleUrl: './input.component.scss',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FuseInputComponent),
      multi: true,
    },
  ],
})
export class FuseInputComponent implements ControlValueAccessor {
  @Input() public form!: FormGroup;
  @Input() label = '';
  @Input() name = '';
  @Input() type = 'text';
  @Input() placeholder = '';
  @Input() disabled!: boolean;
  @Input() required = false;
  @Input() description = '';
  @Input() prefixText = '';
  @Input() prefixIcon = '';
  @Input() prefixIconColor = '#868FA0';
  @Input() suffixText = '';
  @Input() suffixIcon = '';
  @Input() suffixIconColor = '#868FA0';
  @Input() errorMessages: any;
  @Input() autocomplete: string | null = '';

  value = '';
  showPassword = false;

  onChange = (value: string) => {};
  onTouched = () => {};

  writeValue(obj: any): void {
    this.value = obj;
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState?(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  onValueChange(event: any): void {
    this.onChange(event);
  }

  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }
}
