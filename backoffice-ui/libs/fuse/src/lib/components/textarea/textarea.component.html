<div [formGroup]="form">
  <mat-form-field
    class="textarea-field"
    floatLabel="always"
    [subscriptSizing]="'dynamic'"
  >
    <mat-label>
      {{ label | transloco }}
      @if (required) {
        <span> * </span>
      }
      @if (description) {
        <mat-icon
          matPrefix
          [svgIcon]="'heroicons_outline:question-mark-circle'"
          class="w-3.5 h-3.5 cursor-pointer"
          #tooltip="matTooltip"
          [matTooltip]="description | transloco"
          [matTooltipPosition]="'above'"
        ></mat-icon>
      }
    </mat-label>
    <textarea
      matInput
      [id]="name"
      [rows]="rows"
      [placeholder]="placeholder | transloco"
      [disabled]="disabled"
      (change)="onValueChange($event)"
      [formControlName]="name"
    ></textarea>
  </mat-form-field>

  @if (errorMessages) {
    <fuse-error-message
      [control]="form.get(name)"
      [messages]="errorMessages"
    ></fuse-error-message>
  }
</div>
