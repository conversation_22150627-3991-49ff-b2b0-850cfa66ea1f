import {
  AfterViewInit,
  Component,
  Input,
  OnChanges,
  ViewChild,
} from '@angular/core';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { FuseCustomPaginatorDirective } from '@fuse/components/table/custom-paginator.directive';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'fuse-paginator',
  templateUrl: 'paginator.component.html',
  standalone: true,
  imports: [CommonModule, MatPaginatorModule, FuseCustomPaginatorDirective],
})
export class FusePaginatorComponent implements AfterViewInit, OnChanges {
  @Input() total: any = 0;

  @ViewChild(MatPaginator) paginator!: MatPaginator;

  constructor(
    private _route: ActivatedRoute,
    private _router: Router,
  ) {}

  ngOnChanges(changes: any) {
    if (changes['total'] && this.paginator) {
      this.paginator.length = this.total;
      this.paginator.page.next(null as any);
    }
  }

  ngAfterViewInit() {
    this.paginator.page.subscribe(() => {
      this.updateQueryParams();
    });

    this._route.queryParams.subscribe((params: any) => {
      const pageIndex = params['page'] ? +params['page'] : 0;
      const pageSize = params['size'] ? +params['size'] : 10;

      if (this.paginator) {
        this.paginator.pageIndex = pageIndex;
        this.paginator.pageSize = pageSize;
      }
    });
  }

  private updateQueryParams() {
    this._router.navigate([], {
      relativeTo: this._route,
      queryParams: {
        page: this.paginator.pageIndex,
        size: this.paginator.pageSize,
      },
      queryParamsHandling: 'merge',
    });
  }
}
