import { Component, Input } from '@angular/core';
import { MatIcon } from '@angular/material/icon';
import { MatTooltip } from '@angular/material/tooltip';

@Component({
  selector: 'fuse-help-link',
  standalone: true,
  imports: [MatTooltip, MatIcon],
  templateUrl: './help-link.component.html',
})
export class FuseHelpLinkComponent {
  @Input() baseUrl = 'https://operator-manual.terabite.sg/back-office';
  @Input() url = '';
  @Input() icon = 'heroicons_outline:question-mark-circle';
  @Input() tooltip = 'Help';
}
