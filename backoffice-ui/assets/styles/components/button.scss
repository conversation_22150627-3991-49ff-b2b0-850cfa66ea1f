button {
  span {
    @apply font-onest #{!important};
  }
}

.btn-label {
  &__medium {
    @apply font-onest text-base font-bold leading-6;
  }
}

.btn {
  &__medium {
    @extend .btn-label__medium;
    @apply inline-flex justify-center items-center gap-2 h-10 px-4 py-2 rounded-[4px] text-center;
  }
}

.btn-type {
  &__contained {
    &:disabled {
      @apply bg-grey-500/24 text-grey-500/80 hover:bg-grey-500/24;
    }
  }

  &__outlined {
    @apply border-[1px] border-solid;

    &:disabled {
      @apply bg-grey-500/24 text-grey-500/80 hover:bg-grey-500/24 border-grey-500/35;
    }
  }

  &__text {
    &:disabled {
      @apply text-grey-500/80 hover:bg-transparent;
    }
  }

  &__soft {
    &:disabled {
      @apply text-grey-500/80 bg-grey-500/24;
    }
  }
}

.btn-contained {
  &__primary {
    &__medium {
      @extend .btn__medium;
      @extend .btn-type__contained;
      @apply bg-primary text-white hover:bg-primary-700;
    }
  }

  &__success {
    &__medium {
      @extend .btn__medium;
      @extend .btn-type__contained;
      @apply bg-success-main text-white hover:bg-success-dark;
    }
  }

  &__warning {
    &__medium {
      @extend .btn__medium;
      @extend .btn-type__contained;
      @apply bg-warning-main text-white hover:bg-warning-dark;
    }
  }
}

.btn-outlined {
  &__primary {
    &__medium {
      @extend .btn__medium;
      @extend .btn-type__outlined;
      @apply bg-white text-primary border-primary hover:bg-primary/8;
    }
  }

  &__error {
    &__medium {
      @extend .btn__medium;
      @extend .btn-type__outlined;
      @apply bg-white text-error-main border-error-main hover:bg-error-main/8;
    }
  }

  &__success {
    &__medium {
      @extend .btn__medium;
      @extend .btn-type__outlined;
      @apply bg-white text-success-main border-success-main hover:bg-success-main/8;
    }
  }
}

.btn-text {
  &__inherit {
    &__medium {
      @extend .btn__medium;
      @extend .btn-type__text;
      @apply bg-transparent text-primary hover:bg-grey-500/8;
    }
  }

  &__error {
    &__medium {
      @extend .btn__medium;
      @extend .btn-type__text;
      @apply bg-transparent text-error-main hover:bg-error-main/8;
    }
  }
}

.btn-soft {
  &__primary {
    &__medium {
      @extend .btn__medium;
      @extend .btn-type__soft;
      @apply bg-primary/24 text-primary hover:bg-primary/35;
    }
  }
}

.mat-success-button {
  background-color: #36b37e !important;
  color: #ffffff !important;
}

.btn-refund {
  @extend .btn__medium;
  @extend .btn-type__contained;
  @apply bg-[#718596] text-white font-semibold hover:bg-[#637381];
}

.mdc-button {
  @apply h-10 #{!important};

  .mat-mdc-button-touch-target {
    @apply hidden #{!important};
  }
}

.btn-icon {
  @apply flex items-center justify-center h-10 border border-gray-300 shadow w-9 rounded-3;
}
