const path = require('path');
const colors = require('tailwindcss/colors');
const defaultTheme = require('tailwindcss/defaultTheme');
const generatePalette = require(
  path.resolve(__dirname, './assets/vendor/tailwind/utils/generate-palette'),
);

/**
 * Custom palettes
 *
 * Uses the generatePalette helper method to generate
 * Tailwind-like color palettes automatically
 */
const customPalettes = {
  brand: generatePalette('#165adc'),
  secondary: generatePalette('#7a57dd'),
  info: generatePalette('#21b5d5'),
  success: generatePalette('#36b37e'),
  warning: generatePalette('#ffab00'),
  error: generatePalette('#d0021b'),
  grey: generatePalette('#161c24'),
};

/**
 * Themes
 */
const themes = {
  // Default theme is required for theming system to work correctly!
  default: {
    primary: {
      ...colors.indigo,
      DEFAULT: colors.indigo[600],
    },
    accent: {
      ...colors.slate,
      DEFAULT: colors.slate[800],
    },
    warn: {
      ...colors.red,
      DEFAULT: colors.red[600],
    },
    'on-warn': {
      500: colors.red['50'],
    },
  },
  // Rest of the themes will use the 'default' as the base
  // theme and will extend it with their given configuration.
  brand: {
    primary: customPalettes.brand,
    accent: customPalettes.secondary,
    warn: customPalettes.warning,
    'on-warn': customPalettes.error,
  },
};

/**
 * Tailwind configuration
 */
const config = {
  darkMode: 'class',
  content: [
    './apps/**/*.{html,scss,ts}',
    './libs/**/*.{html,scss,ts}',
    './assets/**/*.{html,scss,ts}',
  ],
  important: true,
  theme: {
    fontSize: {
      xs: '0.75rem',
      sm: '0.8125rem',
      base: '0.875rem',
      lg: '0.9375rem',
      xl: '1rem',
      '2xl': '1.125rem',
      '3xl': '1.25rem',
      '4xl': '1.5rem',
      '5xl': '2rem',
      '6xl': '2.5rem',
      '7xl': '3rem',
      '8xl': '4rem',
      '9xl': '5rem',
      '10xl': '7.5rem',
      '11xl': '15rem',
    },
    screens: {
      sm: '600px',
      md: '960px',
      lg: '1280px',
      xl: '1440px',
    },
    extend: {
      animation: {
        'spin-slow': 'spin 3s linear infinite',
      },
      colors: {
        grey: {
          50: '#f9fafb',
          100: '#f9fafb',
          200: '#f4f6f8',
          300: '#dfe3e8',
          400: '#c4cdd5',
          500: '#919eab',
          600: '#637381',
          700: '#454f5b',
          800: '#212b36',
          900: '#161c24',
        },
        info: {
          lighter: '#b9f3ff',
          light: '#6ee5ff',
          main: '#21b5d5',
          dark: '#1a93ad',
          darker: '#136c80',
        },
        success: {
          lighter: '#d8fbde',
          light: '#86e8ab',
          main: '#36b37e',
          dark: '#1b806a',
          darker: '#0a5554',
        },
        warning: {
          lighter: '#fff5cc',
          light: '#ffd666',
          main: '#ffab00',
          dark: '#b76e00',
          darker: '#7a4100',
        },
        error: {
          lighter: '#',
          light: '#',
          main: '#d0021b',
          dark: '#',
          darker: '#',
        },
        gray: colors.slate,
        lighter: '#C6DAFF',
        light: '#76A5FF',
        main: '#165ADC',
        dark: '#1046AD',
        darker: '#0D327B',
        content: '#F1F3F5',
        placeholderEl: '#A1A9B8',
      },
      flex: {
        0: '0 0 auto',
      },
      fontFamily: {
        onest: `"Onest", ${defaultTheme.fontFamily.sans.join(',')}`,
        barlow: `"Barlow", ${defaultTheme.fontFamily.mono.join(',')}`,
      },
      opacity: {
        8: '0.08',
        12: '0.12',
        16: '0.16',
        24: '0.24',
        38: '0.38',
        48: '0.48',
        84: '0.84',
      },
      rotate: {
        '-270': '270deg',
        15: '15deg',
        30: '30deg',
        60: '60deg',
        270: '270deg',
      },
      scale: {
        '-1': '-1',
      },
      zIndex: {
        '-1': -1,
        49: 49,
        60: 60,
        70: 70,
        80: 80,
        90: 90,
        99: 99,
        999: 999,
        9999: 9999,
        99999: 99999,
      },
      spacing: {
        13: '3.25rem',
        15: '3.75rem',
        18: '4.5rem',
        22: '5.5rem',
        26: '6.5rem',
        30: '7.5rem',
        50: '12.5rem',
        90: '22.5rem',

        // Bigger values
        100: '25rem',
        120: '30rem',
        128: '32rem',
        140: '35rem',
        160: '40rem',
        180: '45rem',
        192: '48rem',
        200: '50rem',
        240: '60rem',
        256: '64rem',
        280: '70rem',
        320: '80rem',
        360: '90rem',
        400: '100rem',
        480: '120rem',

        // Fractional values
        '1/2': '50%',
        '1/3': '33.333333%',
        '2/3': '66.666667%',
        '1/4': '25%',
        '2/4': '50%',
        '3/4': '75%',
      },
      minHeight: ({ theme }) => ({
        ...theme('spacing'),
      }),
      maxHeight: {
        none: 'none',
      },
      minWidth: ({ theme }) => ({
        ...theme('spacing'),
        screen: '100vw',
      }),
      maxWidth: ({ theme }) => ({
        ...theme('spacing'),
        screen: '100vw',
      }),
      transitionDuration: {
        400: '400ms',
      },
      transitionTimingFunction: {
        drawer: 'cubic-bezier(0.25, 0.8, 0.25, 1)',
      },

      // @tailwindcss/typography
      typography: ({ theme }) => ({
        DEFAULT: {
          css: {
            color: 'var(--fuse-text-default)',
            '[class~="lead"]': {
              color: 'var(--fuse-text-secondary)',
            },
            a: {
              color: 'var(--fuse-primary-500)',
            },
            strong: {
              color: 'var(--fuse-text-default)',
            },
            'ol > li::before': {
              color: 'var(--fuse-text-secondary)',
            },
            'ul > li::before': {
              backgroundColor: 'var(--fuse-text-hint)',
            },
            hr: {
              borderColor: 'var(--fuse-border)',
            },
            blockquote: {
              color: 'var(--fuse-text-default)',
              borderLeftColor: 'var(--fuse-border)',
            },
            h1: {
              color: 'var(--fuse-text-default)',
            },
            h2: {
              color: 'var(--fuse-text-default)',
            },
            h3: {
              color: 'var(--fuse-text-default)',
            },
            h4: {
              color: 'var(--fuse-text-default)',
            },
            'figure figcaption': {
              color: 'var(--fuse-text-secondary)',
            },
            code: {
              color: 'var(--fuse-text-default)',
              fontWeight: '500',
            },
            'a code': {
              color: 'var(--fuse-primary)',
            },
            pre: {
              color: theme('colors.white'),
              backgroundColor: theme('colors.gray.800'),
            },
            thead: {
              color: 'var(--fuse-text-default)',
              borderBottomColor: 'var(--fuse-border)',
            },
            'tbody tr': {
              borderBottomColor: 'var(--fuse-border)',
            },
            'ol[type="A" s]': false,
            'ol[type="a" s]': false,
            'ol[type="I" s]': false,
            'ol[type="i" s]': false,
          },
        },
        sm: {
          css: {
            code: {
              fontSize: '1em',
            },
            pre: {
              fontSize: '1em',
            },
            table: {
              fontSize: '1em',
            },
          },
        },
      }),

      //   backgroundImage
      backgroundImage: {
        default: "url('/portal/assets/images/backgrounds/default.png')",
        avatar: "url('/portal/assets/images/backgrounds/avatar.svg')",
      },

      // borderRadius
      borderRadius: {
        0: '0rem',
        1: '0.14285714285714285rem',
        2: '0.2857142857142857rem',
        3: '0.35714285714285715rem',
        4: '0.42857142857142855rem',
        5: '0.5714285714285714rem',
        6: '0.7142857142857143rem',
        7: '0.8571428571428571rem',
        8: '1.1428571428571428rem',
        9: '3.5714285714285716rem',
        10: '5.714285714285714rem',
        11: '71.35714285714286rem',
      },

      //boxShadow
      boxShadow: {
        z1: '0px 1px 2px 0px #919EAB29',
        xs: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
        xl: '0px 12px 16px -4px rgba(36, 36, 36, 0.08), 0px 4px 6px -2px rgba(36, 36, 36, 0.03)',
        '3xl': '0px 2px 5px 0px #5960781A, 0px 0px 0px 1px #464F6029, 0px 1px 1px 0px #0000001A'
      },
    },
  },
  corePlugins: {
    appearance: false,
    container: false,
    float: false,
    clear: false,
    placeholderColor: false,
    placeholderOpacity: false,
    verticalAlign: false,
  },
  plugins: [
    // Fuse - Tailwind plugins
    require(
      path.resolve(__dirname, './assets/vendor/tailwind/plugins/utilities'),
    ),
    require(
      path.resolve(__dirname, './assets/vendor/tailwind/plugins/icon-size'),
    ),
    require(
      path.resolve(__dirname, './assets/vendor/tailwind/plugins/theming'),
    )({
      themes,
    }),

    // Other third party and/or custom plugins
    require('@tailwindcss/typography')({ modifiers: ['sm', 'lg'] }),
  ],
};

module.exports = config;
