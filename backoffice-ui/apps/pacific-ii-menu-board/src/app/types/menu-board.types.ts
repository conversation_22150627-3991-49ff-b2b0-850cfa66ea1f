export interface MenuBoardVerification {
  uuid: string;
  clientName: string;
}

export interface MenuBoardVerificationResponse {
  uuid: string;
  name: string;
  code: string;
  expiredAt: number;
  createdAt: number;
  updatedAt: number;
}

export interface MenuBoardSessionDetail {
  id: string;
  tenantId: string;
  name: string;
  menuBoard: MenuBoardDetail;
  createdAt: number;
  updatedAt: number;
}

export interface MenuBoardDetail {
  id: string;
  tenantId: string;
  name: string;
  images: MenuBoardImage[];
  displayMode: string;
  delayBetweenImages: number;
  createdAt: number;
  updatedAt: number;
}

export interface MenuBoardImage {
  id: string;
  menuBoardId: string;
  image: {
    path: string;
    url: string;
  };
  createdAt: number;
  updatedAt: number;
}
