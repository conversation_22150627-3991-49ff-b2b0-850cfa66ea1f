import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { interval, Subject, Subscription, takeUntil, timer } from 'rxjs';
import { MenuBoardService } from '../../services/menu-board.service';
import {
  MenuBoardVerification,
  MenuBoardVerificationResponse,
} from '../../types/menu-board.types';
import { StorageService } from '../../core/services/storage.service';
import { Router } from '@angular/router';
import { v4 as uuidv4 } from 'uuid';

@Component({
  selector: 'app-authorize-menu-board',
  standalone: true,
  imports: [],
  templateUrl: './authorize-menu-board.component.html',
})
export class AuthorizeMenuBoardComponent implements OnInit, OnDestroy {
  uuid!: string;
  clientName!: string;
  code!: string;

  guideSteps = [
    `Log in to your Back Office account and navigate to the "Menu Board" section under "Menu Management".`,
    `Select the Menu Board you wish to authorize and click the "three dots" icon in the Action column.`,
    `Click the "Authorize" button and enter the code provided below.`,
  ];

  private _destroy$: Subject<any> = new Subject<any>();
  private intervalSubscription!: Subscription;
  private timerSubscription!: Subscription;

  constructor(
    private _router: Router,
    private _storageService: StorageService,
    private _menuBoardService: MenuBoardService,
  ) {
    this.getUuid();
    this.getClientName();
    this._menuBoardService.menuBoardSessionDetail = null;
  }

  ngOnInit(): void {
    this.handleGetAuthorizeCode();
  }

  ngOnDestroy(): void {
    if (this.intervalSubscription) {
      this.intervalSubscription.unsubscribe();
    }

    if (this.timerSubscription) {
      this.timerSubscription.unsubscribe();
    }

    this._destroy$.next(null);
    this._destroy$.complete();
  }

  handleGetAuthorizeCode(): void {
    if (this.intervalSubscription) {
      this.intervalSubscription.unsubscribe();
    }

    if (this.timerSubscription) {
      this.timerSubscription.unsubscribe();
    }

    const data: MenuBoardVerification = {
      uuid: this.uuid,
      clientName: this.clientName,
    };

    this._menuBoardService
      .verifyMenuBoardSession(data)
      .pipe(takeUntil(this._destroy$))
      .subscribe({
        next: (response) => {
          this.handleVerifyMenuBoardSessionResponse(response);
        },
        error: () => {
          this.getUuid();
          this.getClientName();

          this.timerSubscription = timer(10000)
            .pipe(takeUntil(this._destroy$))
            .subscribe(() => {
              this.handleGetAuthorizeCode();
            });
        },
      });
  }

  handleVerifyMenuBoardSessionResponse(
    response: MenuBoardVerificationResponse,
  ): void {
    this.code = response.code;

    this.intervalSubscription = interval(5000)
      .pipe(takeUntil(this._destroy$))
      .subscribe(() => {
        this.handleCheckMenuBoardSessionStatus(response.uuid);
      });

    const expireTime = response.expiredAt - new Date().getTime() + 1;
    this.timerSubscription = timer(expireTime)
      .pipe(takeUntil(this._destroy$))
      .subscribe(() => {
        this.handleGetAuthorizeCode();
      });
  }

  handleCheckMenuBoardSessionStatus(uuid: string): void {
    this._menuBoardService
      .getMenuBoardSessionDetail(uuid)
      .pipe(takeUntil(this._destroy$))
      .subscribe({
        next: () => {
          this._router.navigate(['/']);
        },
      });
  }

  getUuid(): void {
    this.uuid = this._storageService.getUuid();
    if (!this.uuid) {
      this.uuid = this.generatorUuid();
      this._storageService.setUuid(this.uuid);
    }
  }

  getClientName(): void {
    this.clientName = this._storageService.getClientName();
    if (!this.clientName) {
      this.clientName = this.generatorClientName();
      this._storageService.setClientName(this.clientName);
    }
  }

  private generatorUuid(): string {
    return typeof crypto !== 'undefined' && crypto.randomUUID
      ? crypto.randomUUID()
      : uuidv4();
  }

  private generatorClientName(): string {
    let clientName = '';

    while (clientName.length < 8) {
      const randomChar = String.fromCharCode(
        Math.floor(Math.random() * 26) + 65,
      );
      clientName += randomChar;
    }

    return clientName;
  }
}
