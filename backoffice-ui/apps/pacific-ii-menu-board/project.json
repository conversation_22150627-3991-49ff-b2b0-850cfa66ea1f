{"name": "pacific-ii-menu-board", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "app", "sourceRoot": "apps/pacific-ii-menu-board/src", "tags": [], "targets": {"build": {"executor": "@angular-devkit/build-angular:application", "outputs": ["{options.outputPath}"], "options": {"baseHref": "/menu/", "outputPath": "target/apps/pacific-ii-menu-board", "index": "apps/pacific-ii-menu-board/src/index.html", "browser": "apps/pacific-ii-menu-board/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "apps/pacific-ii-menu-board/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "apps/pacific-ii-menu-board/public"}, {"glob": "**/heroicons-solid.svg", "input": "./assets/icons", "output": "./assets/icons"}, {"glob": "**/background.svg", "input": "./assets/images/backgrounds", "output": "./assets/images/backgrounds"}, {"glob": "**/logo.svg", "input": "./assets/images/logo", "output": "./assets/images/logo"}], "stylePreprocessorOptions": {"includePaths": ["assets/styles"]}, "styles": ["assets/styles/tailwind.scss", "assets/styles/user-themes.scss", "assets/styles/themes.scss", "assets/styles/vendors.scss", "assets/styles/menu-board.scss", "apps/pacific-ii-menu-board/src/styles.scss"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "500kb", "maximumError": "5mb"}], "outputHashing": "all"}, "development": {"optimization": true, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"executor": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "pacific-ii-menu-board:build:production"}, "development": {"port": 4400, "buildTarget": "pacific-ii-menu-board:build:development", "proxyConfig": "apps/pacific-ii-menu-board/proxy.conf.json"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "pacific-ii-menu-board:build"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/pacific-ii-menu-board/jest.config.ts"}}}}