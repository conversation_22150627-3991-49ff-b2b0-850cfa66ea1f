<div class="parent-page">
  <!--  Breadcrumb -->
  <div class="parent-page__header">
    <div class="flex gap-2 items-center">
      Store Management
      <fuse-help-link
        [url]="'/store-operation/storemange/#view-store-list-and-change-status'"
      ></fuse-help-link>
    </div>
    <div class="parent-page__header__actions">
      @if (permissionList.create) {
        <button
          [color]="'primary'"
          type="button"
          mat-flat-button
          (click)="handleAddNewStore()"
        >
          Add Store
        </button>
      }
    </div>
  </div>
  <!-- Main -->
  <div class="parent-page__body">
    <!-- CONTENT GOES HERE -->
    <div class="parent-page__body__table">
      <fuse-table-component
        [dataSource]="dataSource"
        [displayedColumns]="displayedColumns"
        [actions]="actions"
        [total]="total"
        [sortDefault]="sortDefault"
        [rowClick]="handleViewStoreDetail"
        [searchForm]="searchForm"
      ></fuse-table-component>
    </div>
  </div>
</div>
