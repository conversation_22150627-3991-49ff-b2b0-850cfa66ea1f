import {
  Component,
  ElementRef,
  Inject,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  ReactiveFormsModule,
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import {
  MAT_DIALOG_DATA,
  MatDialog,
  MatDialogRef,
} from '@angular/material/dialog';
import { FuseInputComponent } from '@fuse/components/input';
import { FuseTextareaComponent } from '@fuse/components/textarea';
import { FuseUltils } from '@fuse/ultils';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { ToastrService } from 'ngx-toastr';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { UtilityService } from '../../../../services/utility.service';
import { concatMap } from 'rxjs';
import { HealthierChoiceManagementService } from '../healthier-choice-management.service';
import { UploadService } from '../../../../services/upload.service';
import { Utils } from 'apps/pacific-ii-cms/src/app/core/utils/utils';
import { FuseHelpLinkComponent } from '@fuse/components/help-link';

@Component({
  selector: 'app-add-healthier-choice',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    ReactiveFormsModule,
    MatProgressSpinnerModule,
    FuseInputComponent,
    FuseTextareaComponent,
    FuseHelpLinkComponent,
  ],
  templateUrl: './add-healthier-choice.component.html',
})
export class AddHealthierChoiceComponent {
  @ViewChild('imageInput') imageInput!: ElementRef<HTMLInputElement>;
  @ViewChild('viewSymbolTemplate') viewSymbolTemplate!: TemplateRef<any>;

  healthierChoiceForm!: UntypedFormGroup;
  loading = false;

  errorMessages = {
    name: {
      required: 'Please enter name!',
      maxlength: 'Name cannot exceed 80 characters!',
    },
    description: {
      maxlength: 'Description cannot exceed 250 characters!',
    },
  };

  constructor(
    private _dialog: MatDialog,
    private _toast: ToastrService,
    private _dialogRef: MatDialogRef<any>,
    private _uploadService: UploadService,
    private _formBuilder: UntypedFormBuilder,
    private _utilityService: UtilityService,
    private _healthierChoicesService: HealthierChoiceManagementService,
    @Inject(MAT_DIALOG_DATA) public data: any,
  ) {
    this.initForm();
  }

  submitForm(): void {
    this.healthierChoiceForm.patchValue({
      ...FuseUltils.trimObjectValues({ ...this.healthierChoiceForm.value }),
    });

    for (const i in this.healthierChoiceForm.controls) {
      this.healthierChoiceForm.controls[i].markAsTouched();
      this.healthierChoiceForm.controls[i].updateValueAndValidity();
    }

    if (this.healthierChoiceForm.invalid) {
      Utils.scrollToInvalid();
      return;
    }
    if (
      !this.healthierChoiceForm.value.imageFile &&
      !this.healthierChoiceForm.value.symbolPath
    ) {
      this._toast.error('Please upload symbol!');
      return;
    }

    this.loading = true;
    if (this.healthierChoiceForm.value.imageFile) {
      this.handleGetUploadSymbolUrl();
    } else {
      const healthierChoiceData = this.prepareHealthierChoiceDetailData(
        this.healthierChoiceForm.value,
      );
      this.handleAddHealthierChoice(healthierChoiceData);
    }
  }

  handleAddHealthierChoice(data: any): void {
    this._healthierChoicesService.addHealthierChoice(data).subscribe({
      next: () => {
        this.loading = false;
        this._toast.success('Add healthier choice success!');
        this.onClose('save');
      },
      error: () => {
        this.loading = false;
      },
    });
  }

  prepareHealthierChoiceDetailData(data: any): any {
    const detail = {
      ...data,
    };

    delete detail.imageFile;
    delete detail.image;
    delete detail.symbolUrl;

    return detail;
  }

  onClose(message?: string): void {
    this._dialogRef.close(message);
  }

  onUploadSymbolFile($event: any): void {
    const file = $event.target.files[0];
    const reader = new FileReader();
    const imageFileType = ['image/png', 'image/jpeg', 'image/jpg'];

    if (file.size > 5242880) {
      this._toast.error(
        'The size limit for images is 5.0 MB. Reduce the file size and try again.',
      );
      this.resetUploadedSymbolField($event);
      return;
    }

    if (imageFileType.indexOf(file.type) == -1) {
      this._toast.error(
        'Please upload the following file types: .jpeg, jpg, .png.',
      );
      this.resetUploadedSymbolField($event);
      return;
    }

    if (file) {
      reader.onload = () => {
        this.healthierChoiceForm.patchValue({
          imageFile: file,
          image: reader.result,
        });
      };
      reader.readAsDataURL(file);
    }

    this.imageInput.nativeElement.value = '';
  }

  handleGetUploadSymbolUrl(): void {
    const data = { ...this.healthierChoiceForm.value };
    const params = {
      fileName: data.imageFile.name,
    };

    this._utilityService
      .getUploadUrl(params)
      .pipe(
        concatMap((urlData: any) => {
          data.symbolPath = urlData.filePath;
          return this._uploadService.uploadFile(
            urlData.presignedUrl,
            data.imageFile,
          );
        }),
      )
      .subscribe({
        next: () => {
          this.loading = false;
          const healthierChoiceData =
            this.prepareHealthierChoiceDetailData(data);
          this.handleAddHealthierChoice(healthierChoiceData);
        },
        error: () => {
          this.loading = false;
        },
      });
  }

  resetUploadedSymbolField($event: any): void {
    $event.target.value = null;
    this.healthierChoiceForm.patchValue({
      imageFile: null,
      image: null,
    });
  }

  viewSymbol(): void {
    this._dialog.open(this.viewSymbolTemplate, {
      width: '472px',
    });
  }

  removeUploadedSymbol() {
    this.healthierChoiceForm.patchValue({
      symbolPath: null,
      symbolUrl: null,
      imageFile: null,
      image: null,
    });
  }

  private initForm(): void {
    this.healthierChoiceForm = this._formBuilder.group({
      name: [null, [Validators.required, Validators.maxLength(80)]],
      description: [null, [Validators.maxLength(250)]],
      image: [null],
      imageFile: [null],
      symbolPath: [null],
      symbolUrl: [null],
    });
  }
}
