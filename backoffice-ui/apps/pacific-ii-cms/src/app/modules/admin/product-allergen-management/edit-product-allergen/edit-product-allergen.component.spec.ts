import { ComponentFixture, TestBed } from '@angular/core/testing';
import { EditProductAllergenComponent } from './edit-product-allergen.component';
import { MatDialogModule } from '@angular/material/dialog';
import { ReactiveFormsModule } from '@angular/forms';
import { ToastrModule } from 'ngx-toastr';
import { provideIcons } from '../../../../core/icons/icons.provider';
import { provideHttpClient } from '@angular/common/http';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { ActivatedRoute } from '@angular/router';
import { of } from 'rxjs';
import { provideTranslate } from '../../../../core/transloco/transloco.provider';

describe('EditProductAllergenComponent', () => {
  let component: EditProductAllergenComponent;
  let fixture: ComponentFixture<EditProductAllergenComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        EditProductAllergenComponent,
        MatDialogModule,
        ReactiveFormsModule,
        ToastrModule.forRoot(),
      ],
      providers: [
        provideIcons(),
        provideTranslate(),
        provideHttpClient(),
        provideAnimationsAsync(),
        {
          provide: ActivatedRoute,
          useValue: {
            paramMap: of({
              params: {},
            }),
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(EditProductAllergenComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
