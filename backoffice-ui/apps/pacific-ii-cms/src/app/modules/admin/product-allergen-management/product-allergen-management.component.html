<div class="parent-page">
  <!--  Breadcrumb -->
  <div class="parent-page__header">
    <div class="flex gap-2 items-center">
      Allergen Management
      <fuse-help-link
        [url]="
          '/product-management/healthier-choice/#allergen-management-procedure'
        "
      ></fuse-help-link>
    </div>
    <div class="parent-page__header__actions">
      @if (permissionList.action) {
        <button
          class="btn-contained__primary__medium"
          (click)="onOpenAddAllergenDialog()"
        >
          Add Allergen
        </button>
      }
    </div>
  </div>
  <!-- Main -->
  <div class="parent-page__body">
    <!-- CONTENT GOES HERE -->
    <div class="parent-page__body__table">
      <fuse-table-component
        [dataSource]="dataSource"
        [displayedColumns]="displayedColumns"
        [total]="total"
        [pagination]="false"
        [sortDefault]="sortDefault"
        [selectElement]="permissionList.action"
        [rowClick]="handleViewAllergen"
        [actions]="actions"
        [bulkActions]="bulkActions"
        (selectedElementChanged)="onChangeSelectedElement($event)"
        [searchForm]="searchForm"
      ></fuse-table-component>
    </div>
  </div>
</div>
