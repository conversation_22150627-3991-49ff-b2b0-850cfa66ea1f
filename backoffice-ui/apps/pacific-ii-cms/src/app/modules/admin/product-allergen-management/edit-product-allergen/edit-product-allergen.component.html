<div class="flex flex-col w-full">
  <div class="page-header">
    <div class="page-header__wrapper">
      <div class="page-header__breadcrumb">
        <div class="page-header__breadcrumb__parent">Allergen Management</div>
        <div class="page-header__breadcrumb__dot"></div>
        <div class="page-header__breadcrumb__child">Allergen Information</div>
      </div>

      <div class="page-header__title">
        <button type="button" (click)="gotoAllergenDetail()">
          <mat-icon [svgIcon]="'heroicons_outline:chevron-left'"></mat-icon>
        </button>
        <div>Allergen Information</div>
        <fuse-help-link
          [url]="
            '/product-management/healthier-choice/#allergen-management-procedure'
          "
        ></fuse-help-link>
      </div>
    </div>

    <div class="flex gap-4">
      <button
        class="btn-outlined__primary__medium"
        (click)="gotoAllergenDetail()"
      >
        <mat-icon
          class="w-5 h-5"
          [svgIcon]="'heroicons_outline:x-mark'"
        ></mat-icon>
        <span>Cancel</span>
      </button>
      <button
        class="btn-contained__success__medium"
        (click)="submitForm()"
        [disabled]="loading || allergenForm.invalid || !allergenForm.dirty"
      >
        @if (loading) {
          <mat-spinner diameter="18" class="mr-0"></mat-spinner>
        } @else {
          <mat-icon
            class="w-5 h-5"
            [svgIcon]="'heroicons_outline:check'"
          ></mat-icon>
        }
        <span>Save</span>
      </button>
    </div>
  </div>

  <div class="flex flex-col w-full gap-4 p-4">
    <!-- GENERAL INFORMATION -->
    <div class="box">
      <div class="box__header__title">General Information</div>

      <form
        class="flex flex-col gap-4"
        [formGroup]="allergenForm"
        autocomplete="off"
      >
        <div class="grid grid-cols-2 gap-4">
          <fuse-input
            class="w-full"
            [form]="allergenForm"
            [label]="'Name'"
            [name]="'name'"
            [placeholder]="'Enter name'"
            [errorMessages]="errorMessages.name"
          />
          <fuse-textarea
            class="w-full"
            [form]="allergenForm"
            [label]="'Description'"
            [name]="'description'"
            [placeholder]="'Enter description'"
            [errorMessages]="errorMessages.description"
          />
        </div>
      </form>
    </div>
  </div>
</div>
