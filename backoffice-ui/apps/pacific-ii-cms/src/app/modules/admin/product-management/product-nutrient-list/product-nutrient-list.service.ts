import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { API } from '../../../../core/const';

@Injectable({ providedIn: 'root' })
export class ProductNutrientListService {
  private _httpClient = inject(HttpClient);

  // -----------------------------------------------------------------------------------------------------
  // @ Public methods
  // -----------------------------------------------------------------------------------------------------

  /**
   * Get all product nutrient
   */
  getProductNutrients(id: string, queryParams: any = {}): Observable<any> {
    return this._httpClient.get<any>(
      `${API.PRODUCT.NUTRIENT.LIST.replace('{id}', id)}?${queryParams}`,
    );
  }

  /**
   * Update product nutrient
   */
  updateProductNutrients(id: string, data: any): Observable<any> {
    return this._httpClient.post<any>(
      API.PRODUCT.NUTRIENT.UPDATE.replace('{id}', id),
      data,
    );
  }
}
