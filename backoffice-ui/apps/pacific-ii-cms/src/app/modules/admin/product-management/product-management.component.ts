import { CommonModule } from '@angular/common';
import {
  AfterContentChecked,
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  ViewEncapsulation,
} from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { ActivatedRoute, Router } from '@angular/router';
import { FuseTableComponent, IAction } from '@fuse/components/table';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { FuseUltils } from '@fuse/ultils';
import { ToastrService } from 'ngx-toastr';
import { forkJoin, Subject, takeUntil, tap } from 'rxjs';
import {
  DATE_TIME_FORMAT,
  PRODUCT_STATUS,
  ProductStatusList,
  ROLE_MODULES,
  ROLE_SCOPES,
  ROUTE,
} from '../../../core/const';
import { StorageService } from '../../../core/services/storage.service';
import { UserPermissionService } from '../../../core/user/user-permission.service';
import { Utils } from '../../../core/utils/utils';
import { HealthierChoiceManagementService } from '../healthier-choice-management/healthier-choice-management.service';
import { ProductCategoryManagementService } from '../product-category-management/product-category-management.service';
import { StoreManagementService } from '../store-management/store-management.service';
import { Currency } from '../tenant-management/tenant.types';
import { ProductManagementService } from './product-management.service';
import { IProduct, IProductList } from './product.types';
import { FuseHelpLinkComponent } from '@fuse/components/help-link';

@Component({
  selector: 'app-product-management',
  standalone: true,
  templateUrl: './product-management.component.html',
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule,
    ReactiveFormsModule,
    FuseTableComponent,
    FuseHelpLinkComponent,
  ],
  encapsulation: ViewEncapsulation.None,
})
export class ProductManagementComponent
  implements OnInit, OnDestroy, AfterContentChecked, AfterViewInit
{
  queryParams!: any;
  selectedProducts: Array<string> = [];
  tenantCurrency!: Currency;

  dataSource: Array<any> = [];
  displayedColumns: any = [];
  total = 0;

  searchForm: any;
  actions: Array<IAction> = [];
  bulkActions: Array<IAction> = [];
  sortDefault: any = {
    field: 'name',
    direction: 'asc',
  };

  optionListStore: Array<any> = [];
  storeParams: any = {
    filter: {},
    page: 0,
    size: 10,
  };
  storeAppend = false;

  optionListCategory: Array<any> = [];
  optionListHealthierSymbol: Array<any> = [];

  permissionList = {
    create: false,
    update: false,
    delete: false,
  };

  private _unsubscribeAll: Subject<any> = new Subject<any>();

  /**
   * Constructor
   */
  constructor(
    private _router: Router,
    private _toast: ToastrService,
    private _storageService: StorageService,
    private _activatedRoute: ActivatedRoute,
    private _changeDetectorRef: ChangeDetectorRef,
    private _storesService: StoreManagementService,
    private _productsService: ProductManagementService,
    private _confirmationService: FuseConfirmationService,
    private _categoriesService: ProductCategoryManagementService,
    private _healthierChoicesService: HealthierChoiceManagementService,
    private _userPermissionService: UserPermissionService,
  ) {
    this.getPermission();
    this.tenantCurrency = this._storageService.getTenantCurrency();
    this.handleGetStore();
    this.handleGetCategories();
    this.handleGetHealthierChoice();
    this.initTableColumn();
    this.searchForm = this.initSearchForm();
    this.initProductsSubscription();
  }

  ngOnInit(): void {
    this._activatedRoute.queryParams
      .pipe(
        takeUntil(this._unsubscribeAll),
        tap((queryParams) => {
          this.queryParams = queryParams;
        }),
      )
      .subscribe(() => {
        this.handleGetProducts();
      });
  }

  ngAfterViewInit(): void {
    this.initTableAction();
    this._changeDetectorRef.detectChanges();
  }

  /**
   * On destroy
   */
  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this._unsubscribeAll.next(null);
    this._unsubscribeAll.complete();
  }

  ngAfterContentChecked() {
    this._changeDetectorRef.detectChanges();
  }

  handleLazyLoadSelect(event: any) {
    if (event.controlName === 'storeId') {
      this.storeAppend = event.value.page > this.storeParams.page;
      this.storeParams.filter.name = event.value.search;
      this.storeParams.page = event.value.page;
      this.handleGetStore();
    }
  }

  handleGetCategories() {
    this._categoriesService
      .getProductCategories()
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((categoryData) => {
        const categoriesServer = categoryData.content.map((item: any) => {
          return {
            ...item,
            label: item.name,
            value: item.id,
          };
        });

        this.optionListCategory = [...categoriesServer];

        this.searchForm.basic[1].options = [...this.optionListCategory];
        this.searchForm = { ...this.searchForm };
        this._changeDetectorRef.markForCheck();
      });
  }

  handleGetStore() {
    this._storesService
      .getStores(FuseUltils.objectToQueryString(this.storeParams))
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((storeData) => {
        const storesServer = storeData.content.map((item: any) => {
          return {
            ...item,
            label: item.name,
            value: item.storeId,
          };
        });

        this.optionListStore = this.storeAppend
          ? [...this.optionListStore, ...storesServer]
          : [{ value: '', label: 'All' }, ...storesServer];

        this.searchForm.basic[2].options = [...this.optionListStore];
        this.searchForm = { ...this.searchForm };
        this._changeDetectorRef.markForCheck();
      });
  }

  handleGetDisplayedStores(storeIds: Array<any>) {
    const params = {
      filter: {
        storeIds: storeIds,
      },
      page: 0,
      size: this.queryParams?.size || 10,
    };
    this._storesService
      .getStores(FuseUltils.objectToQueryString(params))
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((res: any) => {
        if (this.displayedColumns) {
          this.displayedColumns[4] = {
            key: 'storeId',
            name: 'Store Name',
            selected: true,
            render: (value: string) => {
              const store = res.content.find(
                (item: any) => item.storeId == value,
              );
              return store ? store.name : `Unknown Store (${value})`;
            },
          };
          this.displayedColumns = [...this.displayedColumns];
          this._changeDetectorRef.detectChanges();
        }
      });
  }

  handleGetHealthierChoice(): void {
    this._healthierChoicesService
      .getHealthierChoices()
      .subscribe((healthierChoiceData) => {
        const healthierChoicesServer = healthierChoiceData.content.map(
          (item: any) => {
            return {
              ...item,
              label: item.name,
              value: item.id,
            };
          },
        );

        this.optionListHealthierSymbol = [...healthierChoicesServer];

        this.searchForm.advanced[2].options = this.optionListHealthierSymbol;
        this.searchForm = { ...this.searchForm };
        this._changeDetectorRef.markForCheck();
      });
  }

  handleGetProducts(): void {
    const mappedData = this.getProductParams(this.queryParams);
    this._productsService
      .getProducts(FuseUltils.objectToQueryString(mappedData))
      .subscribe();
  }

  getProductParams(queryParams: any): any {
    const basicSearch = this.searchForm.basic;
    const advancedSearch = this.searchForm.advanced;

    const filter = {
      name: queryParams['name'] ?? basicSearch[0].defaultValue,
      categoryIds: queryParams['categoryIds'] ?? basicSearch[1].defaultValue,
      healthierChoiceIds:
        queryParams['healthierChoiceIds'] ?? advancedSearch[2].defaultValue,
      storeId: queryParams['storeId'] ?? basicSearch[2].defaultValue,
      barcode: queryParams['barcode'] ?? advancedSearch[3].defaultValue,
      sku: queryParams['sku'] ?? advancedSearch[4].defaultValue,
      statuses: queryParams['statuses'] ?? basicSearch[3].defaultValue,
      fromUnitPrice:
        FuseUltils.truncateDecimalCurrency(
          queryParams['fromUnitPrice'],
          this.tenantCurrency.fractionDigits,
        ) ?? advancedSearch[0].defaultValue,
      toUnitPrice:
        FuseUltils.truncateDecimalCurrency(
          queryParams['toUnitPrice'],
          this.tenantCurrency.fractionDigits,
        ) ?? advancedSearch[1].defaultValue,
    };

    const mappedData = {
      filter,
      size: queryParams['size'] || 10,
      page: queryParams['page'] || 0,
      sortDirection: queryParams['sortDirection'] || this.sortDefault.direction,
      sortFields: queryParams['sortFields'] || [this.sortDefault.field],
    };

    return mappedData;
  }

  gotoImportProducts(): void {
    this._router.navigate([
      `${ROUTE.PRODUCT.MAIN}/${ROUTE.PRODUCT.IMPORT.MAIN}`,
    ]);
  }

  onChangeSelectedElement(event: Array<any>): void {
    this.selectedProducts = event.map((item) => item.id);
  }

  handleAddNewProduct() {
    this._router.navigate([`${ROUTE.PRODUCT.MAIN}/${ROUTE.PRODUCT.ADD}`]);
  }

  handleViewProductDetail(element: any): void {
    this._router.navigate([
      `${ROUTE.PRODUCT.MAIN}/${ROUTE.PRODUCT.DETAIL}/${element.id}`,
    ]);
  }

  handleEditProductDetail(element: any): void {
    this._router.navigate([
      `${ROUTE.PRODUCT.MAIN}/${ROUTE.PRODUCT.EDIT}/${element.id}`,
    ]);
  }

  onOpenActivateProductDialog(element: any): void {
    const dialogRef = this._confirmationService.open({
      title: 'Activate Product',
      message: 'Please confirm to activate this product.',
      icon: {
        show: false,
      },
      actions: {
        confirm: {
          label: 'Activate',
        },
        cancel: {
          label: 'Close',
        },
      },
      dismissible: false,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        this.handleActivateProduct(element);
      }
    });
  }

  handleActivateProduct(product: any): void {
    this._productsService.activateProduct(product.id).subscribe(() => {
      this._toast.success('Activate product success!');
      this.handleGetProducts();
    });
  }

  onOpenUnavailableProductDialog(element: any): void {
    const dialogRef = this._confirmationService.open({
      title: 'Mark Unavailable',
      message: 'Please confirm to mark this product as unavailable.',
      icon: {
        show: false,
      },
      actions: {
        confirm: {
          label: 'Confirm',
        },
        cancel: {
          label: 'Close',
        },
      },
      dismissible: false,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        this.handleUnavailableProduct(element);
      }
    });
  }

  handleUnavailableProduct(product: any): void {
    this._productsService.unavailableProduct(product.id).subscribe(() => {
      this._toast.success('Mark product as unavailable success!');
      this.handleGetProducts();
    });
  }

  onOpenArchiveProductDialog(element: any): void {
    const dialogRef = this._confirmationService.open({
      title: 'Archive Product',
      message: 'Please confirm to archive this product.',
      icon: {
        show: false,
      },
      actions: {
        confirm: {
          label: 'Archive',
        },
        cancel: {
          label: 'Close',
        },
      },
      dismissible: false,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        this.handleArchiveProduct(element);
      }
    });
  }

  handleArchiveProduct(product: any): void {
    this._productsService.archiveProduct(product.id).subscribe(() => {
      this._toast.success('Archive product success!');
      this.handleGetProducts();
    });
  }

  onOpenDeleteProductDialog(element: any): void {
    const dialogRef = this._confirmationService.open({
      title: 'Delete Product',
      message:
        'Please confirm to delete the selected product. <br/> Please be careful for this action. When this is removed, you can not recover it.',
      icon: {
        show: false,
      },
      actions: {
        confirm: {
          label: 'Delete',
        },
        cancel: {
          label: 'Close',
        },
      },
      dismissible: false,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        this.handleDeleteProduct(element);
      }
    });
  }

  handleDeleteProduct(product: any): void {
    this._productsService.deleteProduct(product.id).subscribe(() => {
      this._toast.success('Delete product success!');
      this.handleGetProducts();
    });
  }

  onOpenDeleteSelectedProductsDialog(): void {
    const dialogRef = this._confirmationService.open({
      title: 'Delete',
      message: 'Please confirm to the selected products.',
      icon: {
        show: false,
      },
      actions: {
        confirm: {
          label: 'Delete',
        },
        cancel: {
          label: 'Close',
        },
      },
      dismissible: false,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        this.handleDeleteSelectedProducts();
      }
    });
  }

  handleDeleteSelectedProducts(): void {
    forkJoin(
      this.selectedProducts.map((id) =>
        this._productsService.deleteProduct(id),
      ),
    ).subscribe({
      next: () => {
        this.handleGetProducts();
        this._toast.success(
          'Selected product(s) have been deleted successfully!',
        );
      },
      error: () => {
        this.handleGetProducts();
      },
    });
  }

  private initProductsSubscription(): void {
    this._productsService.products$
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((products: IProductList) => {
        this.dataSource = products.content.map((item: IProduct) => {
          return {
            ...item,
          };
        });
        this.total = products.totalElements;

        const storeIds = [
          ...new Set(products.content.map((value) => value.storeId)),
        ];
        this.handleGetDisplayedStores(storeIds);
      });
  }

  /**
   * Initializes the table column configuration.
   */
  private initTableColumn(): void {
    this.displayedColumns = [
      {
        key: 'sku',
        name: 'Product SKU',
        sort: true,
        selected: true,
        renderHtml: (value: string) => {
          return `<p class="font-semibold">${value}</p>`;
        },
      },
      {
        key: 'name',
        name: 'Name',
        sort: true,
        selected: true,
        renderHtml: (value: string) => {
          return `<p class="font-semibold">${value}</p>`;
        },
      },
      {
        key: 'briefInformation',
        name: 'Brief Description',
        // sort: true,
        selected: false,
      },
      {
        key: 'category',
        name: 'Category',
        sort: true,
        selected: true,
        render: (value: any) => {
          return value?.name ?? '';
        },
      },
      {
        key: 'storeId',
        name: 'Store',
        selected: true,
        render: (value: any) => {
          return `Unknown Store (${value})`;
        },
      },
      {
        key: 'unitPrice',
        name: 'Selling Price',
        sort: true,
        selected: true,
        headerAlign: 'right',
        renderHtml: (value: any, data: any) => {
          const currency = data?.currency;
          return `<div class="text-nowrap flex justify-end">${FuseUltils.formatPrice(value, currency) ?? '_'}</div>`;
        },
      },
      {
        key: 'description',
        name: 'Description',
        // sort: true,
        selected: false,
      },
      {
        key: 'allergens',
        name: 'Allergen',
        // sort: true,
        selected: false,
        renderHtml: (value: any) => {
          const allergens = value.map((data: any) => data.name).join(', ');
          return `<div class="text-nowrap flex justify-end">${allergens}</div>`;
        },
      },
      {
        key: 'healthierChoice',
        name: 'Healthier Choice',
        // sort: true,
        selected: true,
        renderHtml: (value: any) => {
          return value?.name ?? '';
        },
      },
      {
        key: 'barcode',
        name: 'Barcode',
        // sort: true,
        selected: false,
      },
      {
        key: 'listingPrice',
        name: 'Listing Price',
        // sort: true,
        selected: false,
        headerAlign: 'right',
        renderHtml: (value: any, data: any) => {
          const currency = data?.currency;
          return `<div class="text-nowrap flex justify-end">${FuseUltils.formatPrice(value, currency) ?? '_'}</div>`;
        },
      },
      {
        key: 'preparationTime',
        name: 'Preparation Time',
        // sort: true,
        selected: false,
        renderHtml: (value: number) => {
          return `<span class="whitespace-nowrap"> ${value} minutes </span>`;
        },
      },
      {
        key: 'createdAt',
        name: 'Creation Date',
        // sort: true,
        selected: false,
        render: (date: number) => {
          return FuseUltils.tsToLocalTime(date, DATE_TIME_FORMAT.DATE_DEFAULT);
        },
      },
      {
        key: 'updatedAt',
        name: 'Last Updated Date',
        // sort: true,
        selected: false,
        render: (date: number) => {
          return FuseUltils.tsToLocalTime(date, DATE_TIME_FORMAT.DATE_DEFAULT);
        },
      },
      {
        key: 'status',
        name: 'Status',
        selected: true,
        renderHtml: (value: string) => {
          const chipColor = Utils.getStatusColor(value);

          return `<mat-chip class="px-2 py-1 rounded-3 capitalize ${chipColor}">${value}</mat-chip>`;
        },
      },
    ];
  }

  /**
   * Initializes the actions array for the table.
   *
   * @private
   * @returns {void}
   */
  private initTableAction(): void {
    this.actions = [
      {
        name: 'View',
        type: 'event',
        callback: (element) => this.handleViewProductDetail(element),
      },
      {
        name: 'Edit',
        type: 'event',
        hidden: () => !this.permissionList.update,
        callback: (element) => this.handleEditProductDetail(element),
      },
      {
        name: 'Activate',
        type: 'event',
        hidden: (element) =>
          (element.status != PRODUCT_STATUS.UNAVAILABLE &&
            element.status != PRODUCT_STATUS.ARCHIVED) ||
          !this.permissionList.update,
        callback: (element) => this.onOpenActivateProductDialog(element),
      },
      {
        name: 'Mark as Unavailable',
        type: 'event',
        hidden: (element) =>
          element.status != PRODUCT_STATUS.ACTIVE ||
          !this.permissionList.update,
        callback: (element) => this.onOpenUnavailableProductDialog(element),
      },
      {
        name: 'Archive',
        type: 'event',
        hidden: (element) =>
          element.status == PRODUCT_STATUS.ARCHIVED ||
          !this.permissionList.update,
        callback: (element) => this.onOpenArchiveProductDialog(element),
      },
      {
        name: 'Delete',
        type: 'event',
        hidden: () => !this.permissionList.delete,
        callback: (element) => this.onOpenDeleteProductDialog(element),
      },
    ];

    this.bulkActions = [
      {
        name: 'Delete',
        type: 'event',
        hidden: () => !this.permissionList.delete,
        callback: () => this.onOpenDeleteSelectedProductsDialog(),
      },
    ];
  }

  /**
   * Initializes the search form configuration object.
   *
   * @returns {any} The search form configuration object.
   */
  private initSearchForm(): any {
    return {
      basic: [
        {
          label: 'Search by Name',
          name: 'name',
          placeholder: 'Enter name',
          type: 'text',
          prefixIcon: 'styl:MagnifyingGlassOutlineBold',
          defaultValue: '',
        },
        {
          label: 'Category',
          name: 'categoryIds',
          placeholder: 'All',
          type: 'select',
          defaultValue: [],
          isMultiple: true,
          options: [],
        },
        {
          label: 'Store Name',
          name: 'storeId',
          type: 'lazy-load-select',
          showSearch: true,
          defaultValue: '',
          options: [],
        },
        {
          label: 'Status',
          name: 'statuses',
          type: 'select',
          defaultValue: [PRODUCT_STATUS.ACTIVE, PRODUCT_STATUS.UNAVAILABLE],
          isMultiple: true,
          options: [...ProductStatusList],
        },
      ],
      advanced: [
        {
          label: 'From Unit Price',
          name: 'fromUnitPrice',
          placeholder: 'Enter unit price',
          type: 'text',
          defaultValue: '',
        },
        {
          label: 'To Unit Price',
          name: 'toUnitPrice',
          placeholder: 'Enter unit price',
          type: 'text',
          defaultValue: '',
        },
        {
          label: 'Healthier Choice',
          name: 'healthierChoiceIds',
          placeholder: 'All',
          type: 'select',
          defaultValue: [],
          isMultiple: true,
          options: [],
        },
        {
          label: 'Barcode',
          name: 'barcode',
          placeholder: 'Enter barcode',
          type: 'text',
          defaultValue: '',
        },
        {
          label: 'SKU',
          name: 'sku',
          placeholder: 'Enter sku',
          type: 'text',
          defaultValue: '',
        },
        // {
        //   label: 'Creation Date',
        //   name: 'createdAt',
        //   placeholder: 'datetime',
        //   type: 'datetime',
        //   defaultValue: '',
        // },
      ],
    };
  }

  private getPermission(): void {
    this._userPermissionService.permissions$
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe(() => {
        this.permissionList = {
          create: this._userPermissionService.hasPermissions([
            { module: ROLE_MODULES.PRODUCT_MGMT, scope: ROLE_SCOPES.ADD },
          ]),
          update: this._userPermissionService.hasPermissions([
            { module: ROLE_MODULES.PRODUCT_MGMT, scope: ROLE_SCOPES.UPDATE },
          ]),
          delete: this._userPermissionService.hasPermissions([
            { module: ROLE_MODULES.PRODUCT_MGMT, scope: ROLE_SCOPES.DELETE },
          ]),
        };

        this.displayedColumns = [...this.displayedColumns];
      });
  }
}
