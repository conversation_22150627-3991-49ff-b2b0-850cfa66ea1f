import {
  Component,
  Input,
  OnD<PERSON>roy,
  OnInit,
  AfterViewInit,
  ChangeDetectorRef,
} from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { forkJoin, Subject, takeUntil } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import { ReactiveFormsModule } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import {
  FuseTableComponent,
  IAction,
} from '../../../../../../../../libs/fuse/src/lib/components/table';
import { AddProductOptionComponent } from './add-product-option/add-product-option.component';
import { ProductOptionsService } from './product-options.service';
import { ProductOptionDetailComponent } from './product-option-detail/product-option-detail.component';
import { FuseConfirmationService } from '../../../../../../../../libs/fuse/src/lib/services/confirmation';

@Component({
  selector: 'app-product-options',
  standalone: true,
  imports: [ReactiveFormsModule, MatIconModule, FuseTableComponent],
  templateUrl: './product-options.component.html',
})
export class ProductOptionsComponent
  implements OnInit, OnDestroy, AfterViewInit
{
  @Input() id!: string;
  @Input() currency!: any;
  @Input() mode = 'view';
  @Input() dataSource: Array<any> = [];
  displayedColumns: any = [];
  total = 0;

  actions: Array<IAction> = [];
  sortDefault: any = {
    field: 'name',
    direction: 'asc',
  };

  selectedOptions: Array<string> = [];

  private _unsubscribeAll: Subject<void> = new Subject<void>();

  /**
   * Constructor
   */
  constructor(
    private _dialog: MatDialog,
    private _toast: ToastrService,
    private _changeDetectorRef: ChangeDetectorRef,
    private _optionsService: ProductOptionsService,
    private _confirmationService: FuseConfirmationService,
  ) {
    this.initOptionsSubscription();
  }

  ngOnInit(): void {
    this.handleGetProductOptions();
  }

  ngAfterViewInit(): void {
    this.initTableColumn();
    this.initTableAction();
    this._changeDetectorRef.detectChanges();
  }

  /**
   * On destroy
   */
  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this._unsubscribeAll.next();
    this._unsubscribeAll.complete();
  }

  handleGetProductOptions(): void {
    this._optionsService.getProductOptions(this.id, '').subscribe();
  }

  onChangeSelectedElement(event: Array<any>): void {
    this.selectedOptions = event.map((item) => item.id);
  }

  onOpenAddOptionDialog() {
    const dialogRef = this._dialog.open(AddProductOptionComponent, {
      width: '80%',
      autoFocus: false,
      disableClose: true,
      data: {
        productId: this.id,
      },
    });
    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.handleGetProductOptions();
      }
    });
  }

  onOpenEditOptionDialog(element: any): void {
    const dialogRef = this._dialog.open(AddProductOptionComponent, {
      width: '80%',
      autoFocus: false,
      disableClose: true,
      data: {
        productId: this.id,
        detail: element,
      },
    });
    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.handleGetProductOptions();
      }
    });
  }

  onOpenViewOptionDetailDialog(element: any): void {
    this._dialog.open(ProductOptionDetailComponent, {
      width: '80%',
      data: {
        detail: element,
        currency: this.currency,
      },
    });
  }

  onOpenDeleteOptionDialog(element: any): void {
    const dialogRef = this._confirmationService.open({
      title: 'Delete Product Option',
      message: 'Please confirm to delete this product option.',
      icon: {
        show: false,
      },
      actions: {
        confirm: {
          label: 'Delete',
        },
        cancel: {
          label: 'Close',
        },
      },
      dismissible: false,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        this.handleDeleteProductOption(element);
      }
    });
  }

  handleDeleteProductOption(option: any): void {
    this._optionsService.deleteOption(this.id, option.id).subscribe(() => {
      this._toast.success('Delete product option success!');
      this.handleGetProductOptions();
    });
  }

  onOpenDeleteSelectedOptionsDialog(): void {
    const dialogRef = this._confirmationService.open({
      title: 'Delete Product Options',
      message: 'Please confirm to delete the selected product options.',
      icon: {
        show: false,
      },
      actions: {
        confirm: {
          label: 'Delete',
        },
        cancel: {
          label: 'Close',
        },
      },
      dismissible: false,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        this.handleDeleteSelectedProductOption();
      }
    });
  }

  handleDeleteSelectedProductOption(): void {
    forkJoin(
      this.selectedOptions.map((item: any) =>
        this._optionsService.deleteOption(this.id, item),
      ),
    ).subscribe({
      next: () => {
        this._toast.success(
          'Selected product option(s) have been deleted successfully!',
        );
        this.handleGetProductOptions();
      },
      error: () => {
        this.handleGetProductOptions();
      },
    });
  }

  onRowClick = (row: any) => {
    if (this.mode !== 'view') {
      this.onOpenViewOptionDetailDialog(row);
    }
  };

  private initOptionsSubscription(): void {
    this._optionsService.options$
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((options: any) => {
        this.dataSource = options.content;
        this.total = options.totalElements;
      });
  }

  /**
   * Initializes the table column configuration.
   */
  private initTableColumn(): void {
    this.displayedColumns = [
      {
        key: 'title',
        name: 'Option Group',
        selected: true,
        renderHtml: (value: string) => {
          return `<p class="font-semibold">${value}</p>`;
        },
      },
      {
        key: 'minimum',
        name: 'Minimum Selection',
        selected: true,
        description:
          'Minimum is 0: Product option is optional. Minimum > 0: Product option is required user to choose.',
      },
      {
        key: 'maximum',
        name: 'Maximum Selection',
        selected: true,
        description:
          'Maximum selection is 1: Customer can select single option when order. Maximum selection more than 1: Customer can select multiple option when order.',
      },
    ];
  }

  /**
   * Initializes the actions array for the table.
   *
   * @private
   * @returns {void}
   */
  private initTableAction(): void {
    this.actions = [
      {
        name: 'View',
        type: 'event',
        callback: (element) => this.onOpenViewOptionDetailDialog(element),
      },
      {
        name: 'Edit',
        type: 'event',
        callback: (element) => this.onOpenEditOptionDialog(element),
      },
      {
        name: 'Delete',
        type: 'event',
        callback: (element) => this.onOpenDeleteOptionDialog(element),
      },
    ];
  }
}
