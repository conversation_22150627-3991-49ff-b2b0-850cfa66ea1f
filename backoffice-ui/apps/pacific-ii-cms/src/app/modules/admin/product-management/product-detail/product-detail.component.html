<div class="flex flex-col w-full">
  <div class="page-header">
    <div class="page-header__wrapper">
      <div class="page-header__breadcrumb">
        <div class="page-header__breadcrumb__parent">Product List</div>
        <div class="page-header__breadcrumb__dot"></div>
        <div class="page-header__breadcrumb__child">Product Information</div>
      </div>

      <div class="page-header__title">
        <button type="button" (click)="gotoProductManagement()">
          <mat-icon [svgIcon]="'heroicons_outline:chevron-left'"></mat-icon>
        </button>
        <div>Product Information</div>
        <fuse-help-link
          [url]="
            '/product-management/productmanage/#view-product-list-and-change-status'
          "
        ></fuse-help-link>
      </div>
    </div>

    <div>
      @if (permissionList.update) {
        <button class="btn-soft__primary__medium" (click)="gotoEditProduct()">
          <mat-icon class="w-5 h-5" [svgIcon]="'styl:Pencil'"></mat-icon>
          <span>Edit</span>
        </button>
      }
    </div>
  </div>

  <div class="flex flex-col w-full gap-4 p-4">
    <!-- GENERAL INFORMATION -->
    <div class="box">
      <div class="box__header__title">General Information</div>

      <div class="flex flex-col gap-4">
        <div class="grid grid-cols-2 gap-4">
          <div class="card">
            <span class="card__label"> Product Name </span>
            <div>
              {{
                productDetail && productDetail.name ? productDetail.name : 'N/A'
              }}
            </div>
          </div>

          <div class="card">
            <span class="card__label"> Selling Price </span>
            <div>
              {{
                productDetail && !isNullOrEmpty(productDetail.unitPrice)
                  ? formatPriceValue(productDetail.unitPrice)
                  : 'N/A'
              }}
            </div>
          </div>

          <div class="card">
            <span class="card__label"> Listing Price </span>
            <div>
              {{
                productDetail && !isNullOrEmpty(productDetail.listingPrice)
                  ? formatPriceValue(productDetail.listingPrice)
                  : 'N/A'
              }}
            </div>
          </div>

          <div class="card">
            <span class="card__label"> Store Name </span>
            <div>
              {{ storeName }}
            </div>
          </div>

          <div class="card">
            <span class="card__label"> Category </span>
            <div>
              {{
                productDetail &&
                productDetail.category &&
                productDetail.category.name
                  ? productDetail.category.name
                  : 'N/A'
              }}
            </div>
          </div>

          <div class="card">
            <span class="card__label"> Healthier Choice </span>
            <div>
              {{
                productDetail &&
                productDetail.healthierChoice &&
                productDetail.healthierChoice.name
                  ? productDetail.healthierChoice.name
                  : 'No'
              }}
            </div>
          </div>

          <div class="card">
            <span class="card__label"> Preparation Time </span>
            <div>
              {{
                productDetail && productDetail.preparationTime
                  ? productDetail.preparationTime + ' minutes'
                  : 'N/A'
              }}
            </div>
          </div>

          <div class="card">
            <span class="card__label"> Status </span>
            <div
              class="px-2 py-0.5 web__caption w-fit rounded-3"
              [ngClass]="
                getStatusColor(
                  productDetail && productDetail.status
                    ? productDetail.status
                    : ''
                )
              "
            >
              {{
                productDetail && productDetail.status
                  ? productDetail.status
                  : 'N/A'
              }}
            </div>
          </div>

          <div class="card">
            <span class="card__label"> SKU </span>
            <div>
              {{
                productDetail && productDetail.sku ? productDetail.sku : 'N/A'
              }}
            </div>
          </div>

          <div class="card">
            <span class="card__label"> Barcode </span>
            <div>
              {{
                productDetail && productDetail.barcode
                  ? productDetail.barcode
                  : 'N/A'
              }}
            </div>
          </div>
        </div>

        <div class="grid grid-cols-2 gap-4">
          <div class="card">
            <span class="card__label"> Ingredients </span>
            <div>
              {{
                productDetail && productDetail.ingredients
                  ? productDetail.ingredients
                  : 'N/A'
              }}
            </div>
          </div>

          <div class="card">
            <span class="card__label"> Brief Description </span>
            <div class="break-all whitespace-pre-wrap">
              {{
                productDetail && productDetail.briefInformation
                  ? productDetail.briefInformation
                  : 'N/A'
              }}
            </div>
          </div>
        </div>

        <div class="grid grid-cols-1 gap-4">
          <div class="card">
            <span class="card__label"> Description </span>
            <div class="break-all whitespace-pre-wrap">
              {{
                productDetail && productDetail.description
                  ? productDetail.description
                  : 'N/A'
              }}
            </div>
          </div>
        </div>

        <!-- KEYWORD -->
        <div class="grid grid-cols-1 gap-4">
          <div class="card">
            <span class="card__label"> Keywords </span>

            @if (
              productDetail &&
              productDetail.keywords &&
              productDetail.keywords.length
            ) {
              <div class="flex flex-wrap items-center gap-2 mt-1 mb-2">
                @for (key of productDetail.keywords; track key) {
                  <div class="keyword-card">
                    {{ key }}
                  </div>
                }
              </div>
            } @else {
              <div>N/A</div>
            }
          </div>
        </div>

        <!-- IMAGES -->
        <div class="card">
          <span class="card__label"> Images </span>
          @if (productDetail) {
            <div class="flex gap-4 mt-2">
              @for (
                item of productDetail.images;
                track item;
                let idx = $index
              ) {
                <div class="upload-image-box__image">
                  <img [src]="item.image.url" alt="product" />
                </div>
              }
            </div>
          } @else {
            <div class="flex justify-center">
              <mat-icon
                class="h-auto w-26"
                [svgIcon]="'styl:NoData'"
              ></mat-icon>
            </div>
          }
        </div>
      </div>
    </div>

    <!-- TRACK STOCK QUANTITY FOR THIS PRODUCT -->
    <div class="box">
      <div class="box__header__title">Quantity Tracking</div>

      <div class="grid grid-cols-2 gap-4">
        <div class="card">
          <span class="card__label"> Quantity </span>
          <div>
            {{
              productQuantityDetail &&
              !isNullOrEmpty(productQuantityDetail.quantity)
                ? productQuantityDetail.quantity
                : 'N/A'
            }}
          </div>
        </div>

        <div class="card">
          <span class="card__label"> Step </span>
          <div>
            {{
              productQuantityDetail &&
              !isNullOrEmpty(productQuantityDetail.step)
                ? productQuantityDetail.step
                : 'N/A'
            }}
          </div>
        </div>

        <div class="card">
          <span class="card__label"> Maximum Number for each Order </span>
          <div>
            {{
              productQuantityDetail &&
              !isNullOrEmpty(productQuantityDetail.maximumQuantityOrder)
                ? productQuantityDetail.maximumQuantityOrder
                : 'N/A'
            }}
          </div>
        </div>

        <div class="card">
          <span class="card__label"> Minimum Number for each Order </span>
          <div>
            {{
              productQuantityDetail &&
              !isNullOrEmpty(productQuantityDetail.minimumQuantityOrder)
                ? productQuantityDetail.minimumQuantityOrder
                : 'N/A'
            }}
          </div>
        </div>
      </div>
    </div>

    <!-- PRODUCT OPTIONS & NUTRITION & ALLERGEN -->
    <div class="box !gap-4 h-120">
      <mat-tab-group
        class="h-full"
        mat-stretch-tabs="false"
        mat-align-tabs="start"
      >
        <mat-tab [label]="'Product Options'">
          <app-product-options
            [id]="id"
            [currency]="productDetail ? productDetail.currency : null"
            [mode]="'view'"
          ></app-product-options>
        </mat-tab>
        <mat-tab [label]="'Nutrition'">
          <app-product-nutrient-list
            [id]="id"
            [mode]="'view'"
          ></app-product-nutrient-list>
        </mat-tab>
        <mat-tab [label]="'Allergen'">
          <app-product-allergen-list
            [id]="id"
            [mode]="'view'"
          ></app-product-allergen-list>
        </mat-tab>
      </mat-tab-group>
    </div>
  </div>
</div>
