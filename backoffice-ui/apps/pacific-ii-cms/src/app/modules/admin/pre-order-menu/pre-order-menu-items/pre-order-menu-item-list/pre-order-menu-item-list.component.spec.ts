import { ComponentFixture, TestBed } from '@angular/core/testing';
import { PreOrderMenuItemListComponent } from './pre-order-menu-item-list.component';
import { provideIcons } from '../../../../../core/icons/icons.provider';
import { provideHttpClient } from '@angular/common/http';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { ToastrModule } from 'ngx-toastr';
import { ActivatedRoute } from '@angular/router';
import { of } from 'rxjs';
import { provideTranslate } from '../../../../../core/transloco/transloco.provider';

describe('PreOrderMenuItemListComponent', () => {
  let component: PreOrderMenuItemListComponent;
  let fixture: ComponentFixture<PreOrderMenuItemListComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [PreOrderMenuItemListComponent, ToastrModule.forRoot()],
      providers: [
        provideIcons(),
        provideTranslate(),
        provideHttpClient(),
        provideAnimationsAsync(),
        {
          provide: ActivatedRoute,
          useValue: {
            paramMap: of({}),
            queryParams: of({}),
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(PreOrderMenuItemListComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
