<div class="flex flex-col w-full">
  <div class="page-header">
    <div class="page-header__wrapper">
      <div class="page-header__breadcrumb">
        <div class="page-header__breadcrumb__parent">Pre-order Menu</div>
        <div class="page-header__breadcrumb__dot"></div>
        <div class="page-header__breadcrumb__child">
          {{ preOrderMenuDetail ? preOrderMenuDetail.name : '' }}
        </div>
        <div class="page-header__breadcrumb__dot"></div>
        <div class="page-header__breadcrumb__child">Pre-order Menu Items</div>
      </div>

      <div class="page-header__title">
        <button type="button" (click)="gotoPreOrderMenuDetail()">
          <mat-icon [svgIcon]="'heroicons_outline:chevron-left'"></mat-icon>
        </button>
        <div>Pre-order Menu Items</div>
        <fuse-help-link
          [url]="
            '/pre-order-menu/pre-order-management/#view-and-edit-pre-order-menu'
          "
        ></fuse-help-link>
      </div>
    </div>

    <div class="flex justify-end gap-2.5">
      <button
        type="button"
        class="flex items-center justify-center gap-1 px-3 py-2 h-fit rounded-3 shadow-3xl"
        (click)="gotoPreOrderMenuItemCalendar()"
      >
        <mat-icon
          class="w-3 h-3"
          [svgIcon]="'heroicons_outline:calendar'"
        ></mat-icon>
        <span class="web__body2">Calendar</span>
      </button>

      <button
        type="button"
        class="flex items-center justify-center gap-1 px-3 py-2 h-fit rounded-3 shadow-3xl bg-[#C6DAFF] border-[1px] border-solid border-primary"
      >
        <mat-icon
          class="w-3 h-3"
          [svgIcon]="'heroicons_outline:queue-list'"
        ></mat-icon>
        <span class="web__body2 text-nowrap">List Items</span>
      </button>

      @if (permissionList.update) {
        <button
          class="btn-contained__primary__medium"
          (click)="onOpenAddPreOrderMenuItemDialog()"
        >
          Add Item
        </button>
      }
    </div>
  </div>

  <div class="m-4 box !gap-4 flex-auto w-auto h-[calc(100vh-234px)]">
    <div class="web__subtitle1">Order Items</div>

    <div class="flex-auto w-auto overflow-auto">
      <fuse-table-component
        [dataSource]="dataSource"
        [displayedColumns]="displayedColumns"
        [actions]="actionShow ? actions : []"
        [bulkActions]="actionShow ? bulkActions : []"
        [total]="total"
        [template]="template"
        [hideColBtn]="true"
        [rowClick]="actionShow ? onRowClick : undefined"
        [selectElement]="permissionList.update"
        (selectedElementChanged)="onChangeSelectedElement($event)"
        [searchForm]="searchForm"
      ></fuse-table-component>

      <ng-template #template let-column="header" let-element="data">
        @if (column.name === 'Product Name') {
          <div class="flex items-center gap-1.5">
            @if (
              element.product &&
              element.product.status &&
              element.product.status === productStatus.ARCHIVED
            ) {
              <div
                class="flex-shrink-0 w-3 h-3 cursor-pointer rounded-xl bg-warning-main"
                [matTooltip]="productWarning"
              ></div>
            }
            <div>{{ element.name }}</div>
          </div>
        }
      </ng-template>
    </div>
  </div>
</div>
