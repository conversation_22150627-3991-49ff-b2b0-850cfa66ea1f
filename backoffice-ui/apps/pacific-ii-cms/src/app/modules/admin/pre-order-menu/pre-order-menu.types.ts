export interface IPreOrderMenuList {
  content: Array<IPreOrderMenuInfo>;
  totalElements: number;
  totalPages?: number;
  page?: number;
  sort?: Array<string>;
}

export interface IPreOrderMenuInfo {
  id: string;
  version: number;
  tenantId: string;
  storeId: string;
  name: string;
  description: string;
  type: string;
  status: string;
  cutOffTime: string;
  cutOffDays: number;
  maxItemOrderPerDay?: number | null;
  createdAt: number;
  updatedAt: number;
}

export interface IPreOrderMenuFilter {
  filter?: {
    name?: string;
    status?: string;
    type?: string;
    storeId?: string;
  };
  size?: number;
  page?: number;
  sortDirection?: string;
  sortFields?: string;
}

export interface IPreOrderMenu {
  storeId: string;
  name: string;
  description: string;
  cutOffTime: string;
  cutOffDays: number;
  type: string;
  status: string;
}

export interface IPreOrderMenuAssignmentList {
  content: Array<IPreOrderMenuAssignmentInfo>;
  totalElements: number;
  totalPages?: number;
  page?: number;
  sort?: Array<string>;
}

export interface IPreOrderMenuAssignmentInfo {
  userGroupId: string;
  menu: IPreOrderMenuInfo;
  createdAt: number;
  updatedAt: number;
}

export interface IPreOrderMenuAssignment {
  preOrderMenuId: string;
  groupIds: Array<string>;
}
