import {
  ChangeDetector<PERSON><PERSON>,
  Component,
  OnInit,
  AfterViewInit,
  OnDestroy,
} from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { IAction } from '@fuse/components/table';
import { forkJoin, Subject, takeUntil } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { PreOrderMenuItemsService } from '../pre-order-menu-items.service';
import { ProductCategoryManagementService } from '../../../product-category-management/product-category-management.service';
import { FuseUltils } from '@fuse/ultils';
import {
  DATE_TIME_FORMAT,
  ROLE_MODULES,
  ROLE_SCOPES,
  ROUTE,
} from '../../../../../core/const';
import { AddPreOrderMenuItemComponent } from '../add-pre-order-menu-item/add-pre-order-menu-item.component';
import {
  IPreOrderMenuItem,
  IPreOrderMenuItemFilter,
} from '../pre-order-menu-items.types';
import { FuseCalendarComponent } from '@fuse/components/calendar';
import { PreOrderMenuItemCardComponent } from '../pre-order-menu-item-card/pre-order-menu-item-card.component';
import { EditRecurrentPreOrderMenuItemComponent } from '../edit-recurrent-pre-order-menu-item/edit-recurrent-pre-order-menu-item.component';
import { IPreOrderMenuInfo } from '../../pre-order-menu.types';
import { PreOrderMenuService } from '../../pre-order-menu.service';
import { MealTimeManagementService } from '../../../meal-time-management/meal-time-management.service';
import { IMealTimeInfo } from '../../../meal-time-management/meal-time.types';
import { FuseErrorNoDataComponent } from '@fuse/components/error-no-data/error-no-data.component';
import { UserPermissionService } from 'apps/pacific-ii-cms/src/app/core/user/user-permission.service';
import { FuseHelpLinkComponent } from '@fuse/components/help-link';

@Component({
  selector: 'app-order-item-calendar',
  standalone: true,
  imports: [
    MatIconModule,
    FuseCalendarComponent,
    PreOrderMenuItemCardComponent,
    FuseErrorNoDataComponent,
    FuseHelpLinkComponent,
  ],
  templateUrl: './pre-order-menu-item-calendar.component.html',
})
export class PreOrderMenuItemCalendarComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  id!: string;
  preOrderMenuDetail!: IPreOrderMenuInfo;

  selectDate!: string;
  actions: Array<IAction> = [];

  optionListCategory: Array<any> = [];
  optionListMealTime: Array<any> = [];

  mealTimeItems: Array<any> = [];

  permissionList = {
    update: false,
    productView: false,
  };
  actionShow = false;

  private _unsubscribeAll: Subject<void> = new Subject<void>();

  /**
   * Constructor
   */
  constructor(
    private _router: Router,
    private _dialog: MatDialog,
    private _toast: ToastrService,
    private _activatedRoute: ActivatedRoute,
    private _changeDetectorRef: ChangeDetectorRef,
    private _preOrderMenusService: PreOrderMenuService,
    private _mealTimesService: MealTimeManagementService,
    private _preOrderMenuItemsService: PreOrderMenuItemsService,
    private _categoriesService: ProductCategoryManagementService,
    private _userPermissionService: UserPermissionService,
  ) {
    this.getPermission();
    this.initItemAction();
    this.getPreOrderMenuId();
    this.handleGetData();
    this.initMenuDetailSubscription();
  }

  ngOnInit(): void {
    this.handleGetPreOrderMenuDetail();
  }

  ngAfterViewInit(): void {
    this._changeDetectorRef.detectChanges();
  }

  /**
   * On destroy
   */
  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this._unsubscribeAll.next();
    this._unsubscribeAll.complete();
  }

  handleGetData(): void {
    const getCategories = this._categoriesService.getProductCategories();
    const getMealTimes = this._mealTimesService.getMealTimes();

    forkJoin([getCategories, getMealTimes]).subscribe(
      ([categoriesData, mealTimesData]) => {
        this.optionListCategory = categoriesData.content.map((item: any) => {
          return {
            ...item,
            label: item.name,
            value: item.id,
          };
        });

        this.optionListMealTime = mealTimesData.content;
        this.initPreOrderMenuItemsSubscription();
      },
    );
  }

  handleGetPreOrderMenuDetail(): void {
    this._preOrderMenusService.getPreOrderMenuDetail(this.id).subscribe();
  }

  handleGetAllPreOrderMenuItems(): void {
    const params: IPreOrderMenuItemFilter = {
      filter: {
        startDate: this.selectDate,
        endDate: this.selectDate,
      },
      page: 0,
      size: 20,
    };
    this._preOrderMenuItemsService
      .getAllPreOrderMenuItems(this.id, params)
      .subscribe();
  }

  gotoPreOrderMenuDetail(): void {
    this._router.navigate([
      `${ROUTE.PRE_ORDER_MENU.MAIN}/${ROUTE.PRE_ORDER_MENU.DETAIL}/${this.id}`,
    ]);
  }

  gotoPreOrderMenuItemList(): void {
    this._router.navigate([
      `${ROUTE.PRE_ORDER_MENU.MAIN}/${ROUTE.PRE_ORDER_MENU.DETAIL}/${this.id}/${ROUTE.PRE_ORDER_MENU.ITEMS.MAIN}/${ROUTE.PRE_ORDER_MENU.ITEMS.LIST}`,
    ]);
  }

  handleViewProductDetail(element: IPreOrderMenuItem): void {
    this._router.navigate([
      `${ROUTE.PRODUCT.MAIN}/${ROUTE.PRODUCT.DETAIL}/${element.product.id}`,
    ]);
  }

  onOpenAddPreOrderMenuItemDialog() {
    const dialogRef = this._dialog.open(AddPreOrderMenuItemComponent, {
      width: '80%',
      autoFocus: false,
      disableClose: true,
      data: {
        menuId: this.id,
        storeId: this.preOrderMenuDetail.storeId,
        mealTimes: this.optionListMealTime,
      },
    });
    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.handleGetAllPreOrderMenuItems();
      }
    });
  }

  onOpenEditPreOrderMenuItemDialog(item: any): void {
    const dialogRef = this._dialog.open(AddPreOrderMenuItemComponent, {
      width: '80%',
      autoFocus: false,
      disableClose: true,
      data: {
        menuId: this.id,
        storeId: this.preOrderMenuDetail.storeId,
        mealTimes: this.optionListMealTime,
        detail: item,
      },
    });
    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.onOpenEditRecurrentMenuItemDialog({
          ...item,
          ...result,
        });
      }
    });
  }

  onOpenEditRecurrentMenuItemDialog(item: any): void {
    const dialogRef = this._dialog.open(
      EditRecurrentPreOrderMenuItemComponent,
      {
        width: '60%',
        autoFocus: false,
        disableClose: true,
        data: {
          type: 'edit',
          menuId: this.id,
          detail: item,
        },
      },
    );

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.handleGetAllPreOrderMenuItems();
      }
    });
  }

  onOpenDeletePreOrderMenuItemDialog(item: any): void {
    const dialogRef = this._dialog.open(
      EditRecurrentPreOrderMenuItemComponent,
      {
        width: '60%',
        autoFocus: false,
        disableClose: true,
        data: {
          type: 'delete',
          menuId: this.id,
          detail: item,
        },
      },
    );

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.handleGetAllPreOrderMenuItems();
      }
    });
  }

  handleDeletePreOrderMenuItem(item: any): void {
    this._preOrderMenuItemsService
      .deletePreOrderMenuItem(this.id, item.id, false)
      .subscribe(() => {
        this._toast.success('Delete pre-order menu item success!');
        this.handleGetAllPreOrderMenuItems();
      });
  }

  onSelectedDateChange(event: any): void {
    this.selectDate = FuseUltils.tsToLocalTime(
      event,
      DATE_TIME_FORMAT.DATE_ISO,
    );
    this.handleGetAllPreOrderMenuItems();
  }

  /**
   * Initializes the actions array for the table.
   *
   * @private
   * @returns {void}
   */
  private initItemAction(): void {
    this.actions = [
      {
        name: 'View Product Detail',
        type: 'event',
        hidden: () => !this.permissionList.productView,
        callback: (element) => this.handleViewProductDetail(element),
      },
      {
        name: 'Edit',
        type: 'event',
        hidden: () => !this.permissionList.update,
        callback: (element) => this.onOpenEditPreOrderMenuItemDialog(element),
      },
      {
        name: 'Delete',
        type: 'event',
        hidden: () => !this.permissionList.update,
        callback: (element) => this.onOpenDeletePreOrderMenuItemDialog(element),
      },
    ];
  }

  private initPreOrderMenuItemsSubscription(): void {
    this._preOrderMenuItemsService.items$
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((items: IPreOrderMenuItem[]) => {
        const mealTimeIds = items.map((item: any) => item.mealTimeId);
        const mealTimeIdsSet = new Set(mealTimeIds);
        const mealTimeList = this.optionListMealTime.filter((mealTime) =>
          mealTimeIdsSet.has(mealTime.id),
        );

        this.mealTimeItems = mealTimeList.map((mealTime: IMealTimeInfo) => {
          return {
            title: mealTime.name,
            items: items.filter(
              (item: IPreOrderMenuItem) => item.mealTimeId == mealTime.id,
            ),
          };
        });
      });
  }

  private initMenuDetailSubscription(): void {
    this._preOrderMenusService.preOrderMenuDetail$
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((detail: IPreOrderMenuInfo) => {
        this.preOrderMenuDetail = { ...detail };
      });
  }

  private getPreOrderMenuId(): void {
    this._activatedRoute.paramMap.subscribe((params: any) => {
      if (params.params.id) {
        this.id = params.params.id;
      }
    });
  }

  private getPermission(): void {
    this._userPermissionService.permissions$
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe(() => {
        this.permissionList = {
          update: this._userPermissionService.hasPermissions([
            {
              module: ROLE_MODULES.PRE_ORDER_MENU_MGMT,
              scope: ROLE_SCOPES.UPDATE,
            },
          ]),
          productView: this._userPermissionService.hasPermissions([
            {
              module: ROLE_MODULES.PRODUCT_MGMT,
              scope: ROLE_SCOPES.VIEW,
            },
          ]),
        };

        this.actionShow =
          this.permissionList.productView || this.permissionList.update;
      });
  }
}
