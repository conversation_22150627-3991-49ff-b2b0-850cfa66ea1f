import { Routes } from '@angular/router';
import { UserManagementComponent } from './user-management.component';
import { ROUTE } from '../../../core/const';
import { EditUserComponent } from './edit-user/edit-user.component';
import { UserDetailComponent } from './user-detail/user-detail.component';

export default [
  {
    path: ROUTE.USER.LIST,
    component: UserManagementComponent,
  },
  {
    path: ROUTE.USER.EDIT + '/:id',
    component: EditUserComponent,
  },
  {
    path: ROUTE.USER.DETAIL + '/:id',
    component: UserDetailComponent,
  },
  { path: '', pathMatch: 'full', redirectTo: ROUTE.USER.LIST },
] as Routes;
