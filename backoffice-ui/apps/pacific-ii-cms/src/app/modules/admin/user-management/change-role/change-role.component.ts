import {
  ChangeDetectorRef,
  Component,
  Inject,
  OnInit,
  AfterViewInit,
  OnDestroy,
} from '@angular/core';
import {
  ReactiveFormsModule,
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { USER_ROLE, USER_TYPE } from 'apps/pacific-ii-cms/src/app/core/const';
import { MatButtonModule } from '@angular/material/button';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { StorageService } from 'apps/pacific-ii-cms/src/app/core/services/storage.service';
import { TenantManagementService } from '../../tenant-management/tenant-management.service';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { FuseLazyLoadSelectComponent } from '@fuse/components/lazy-load-select/lazy-load-select.component';
import { Utils } from 'apps/pacific-ii-cms/src/app/core/utils/utils';
import { FuseHelpLinkComponent } from '@fuse/components/help-link';
import { UserManagementService } from '../user-management.service';
import { FuseUltils } from '../../../../../../../../libs/fuse/src/lib/ultils';
import { combineLatest, of, Subject, switchMap, takeUntil, tap } from 'rxjs';
import { IUser } from '../../../../core/user/user.types';
import { findTenantOwnerRole } from '../user.types';

@Component({
  selector: 'app-change-role',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    MatIconModule,
    MatButtonModule,
    MatDialogModule,
    MatProgressSpinnerModule,
    FuseLazyLoadSelectComponent,
    FuseHelpLinkComponent,
  ],
  templateUrl: './change-role.component.html',
})
export class ChangeRoleComponent implements OnInit, AfterViewInit, OnDestroy {
  changeRoleForm!: UntypedFormGroup;
  optionListUserRole: Array<any> = [];
  loading = false;

  userTenantOwner!: IUser;

  errorMessages = {
    email: {
      required: 'Please enter email!',
      unavailable: 'This email is not available.',
    },
    userRoleId: {
      required: 'Please select user role!',
    },
  };

  roleParams: any = {
    'filter.excludeExternalIds': USER_ROLE.CUSTOMER,
    page: 0,
    size: 10,
  };
  roleAppend = false;

  userMode = this._storageService.getUserMode();
  tenantId = this._storageService.getTenantId();

  private _unsubscribeAll: Subject<any> = new Subject<any>();

  constructor(
    private _dialogRef: MatDialogRef<any>,
    private _storageService: StorageService,
    private _formBuilder: UntypedFormBuilder,
    private _changeDetectorRef: ChangeDetectorRef,
    private _userService: UserManagementService,
    private _tenantService: TenantManagementService,
    @Inject(MAT_DIALOG_DATA) public data: any,
  ) {
    this.initForm();
  }

  ngOnInit(): void {
    this.roleParams['filter.byTenantId'] = this.tenantId;
    this.roleParams.size = this.userMode === 'tenant' ? 11 : 10;
    this.handleGetUserRoles();
  }

  ngAfterViewInit(): void {
    if (this.data?.user) {
      this.updateUserRoleData();
    }
    this._changeDetectorRef.detectChanges();
  }

  /**
   * On destroy
   */
  ngOnDestroy(): void {
    this._unsubscribeAll.next(null);
    this._unsubscribeAll.complete();
  }

  handleLazyLoadSelect(event: any) {
    this.roleAppend = event.page > this.roleParams.page;
    this.roleParams['filter.byRoleTitle'] = event.search;
    this.roleParams.page = event.page;
    this.roleParams.size =
      event.page === 0 && this.userMode === 'tenant' ? 11 : 10;

    this.handleGetUserRoles();
  }

  handleGetUserRoles(): void {
    this._tenantService
      .getUserRole(this.roleParams)
      .pipe(
        takeUntil(this._unsubscribeAll),
        switchMap((userRoleListData) => {
          const roleTenantOwner = findTenantOwnerRole(userRoleListData.content);

          return combineLatest([
            of(userRoleListData.content),
            roleTenantOwner && !this.userTenantOwner
              ? this._userService.getUsers(
                  FuseUltils.objectToQueryString({
                    filter: { byRoleIds: roleTenantOwner?.id },
                  }),
                )
              : of(null),
          ]);
        }),
        tap(([userRoleList, userOwnerListData]) => {
          this.updateOptionListUserRole(userRoleList, userOwnerListData);
        }),
      )
      .subscribe();
  }

  updateOptionListUserRole(userRoleList: any, userOwnerListData: any): void {
    if (userOwnerListData && userOwnerListData.content.length > 0) {
      this.userTenantOwner = userOwnerListData.content[0];
    }

    // Process roles based on tenant owner conditions
    const processedRoles = this.processRolesList(userRoleList);

    // Combine with existing roles if appending
    let combinedRoles = this.roleAppend
      ? [...this.optionListUserRole, ...processedRoles]
      : [...processedRoles];

    // Handle user's current permissions
    combinedRoles = this.includeUserPermissions(combinedRoles);

    this.optionListUserRole = FuseUltils.removeDuplicateItems(
      combinedRoles,
      'id',
    );
    this._changeDetectorRef.markForCheck();
  }

  processRolesList(roles: any[]): any[] {
    const shouldHideTenantOwner =
      (this.userTenantOwner && this.userTenantOwner.id != this.data.user.id) ||
      this.data?.account?.userType !== USER_TYPE.SYSTEM_ADMIN;

    if (shouldHideTenantOwner) {
      // Mark tenant owner role as hidden
      roles.forEach((item: any) => {
        item.hidden = item.externalId === USER_ROLE.TENANT_OWNER;
      });

      // Sort to put tenant owner roles at the end
      return [
        ...roles.filter(
          (item: any) => item.externalId !== USER_ROLE.TENANT_OWNER,
        ),
        ...roles.filter(
          (item: any) => item.externalId === USER_ROLE.TENANT_OWNER,
        ),
      ];
    }

    return roles;
  }

  includeUserPermissions(roles: any[]): any[] {
    const permissions = this.data.user.permissions;
    const shouldIncludeCurrentPermission =
      !this.roleParams['filter.byRoleTitle'] &&
      permissions?.length &&
      !permissions[0].userRole?.includes(permissions[0].userRoleId);

    if (shouldIncludeCurrentPermission) {
      return [permissions[0], ...roles];
    }

    return roles;
  }

  submitForm(): void {
    for (const i in this.changeRoleForm.controls) {
      this.changeRoleForm.controls[i].markAsTouched();
      this.changeRoleForm.controls[i].updateValueAndValidity();
    }

    if (this.changeRoleForm.invalid) {
      Utils.scrollToInvalid();
      return;
    }

    this.loading = true;
    this.handleUpdateUserRole();
  }

  handleUpdateUserRole(): void {
    this.loading = false;
    this.onClose(this.changeRoleForm.controls['userRoleId'].value);
  }

  onClose(userRoleId?: string): void {
    this._dialogRef.close(userRoleId ?? null);
  }

  updateUserRoleData() {
    this.data?.user?.permissions?.forEach((item: any) => {
      item.id = item.userRoleId;
      item.roleTitle = item.userRole;
    });

    this.changeRoleForm.patchValue({
      ...this.data?.user,
      userRoleId:
        this.data?.user?.permissions && this.data.user.permissions.length
          ? this.data.user.permissions[0].userRoleId
          : null,
    });
  }

  private initForm() {
    this.changeRoleForm = this._formBuilder.group({
      userRoleId: [null, [Validators.required]],
    });
  }
}
