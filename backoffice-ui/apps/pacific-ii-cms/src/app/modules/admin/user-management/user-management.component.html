<div class="parent-page">
  <!--  Breadcrumb -->
  <div class="parent-page__header">
    <div class="flex gap-2 items-center">
      User Management
      <fuse-help-link
        [url]="'/user-management/usermanagement/#view-user-list'"
      ></fuse-help-link>
    </div>
    <div class="parent-page__header__actions">
      @if (permissionList.create) {
        <button
          [color]="'primary'"
          type="button"
          mat-flat-button
          (click)="onInviteUserDialog()"
        >
          Invite User
        </button>
      }
    </div>
  </div>
  <!-- Main -->
  <div class="parent-page__body">
    <!-- CONTENT GOES HERE -->
    <div class="parent-page__body__table">
      <fuse-table-component
        [dataSource]="dataSource"
        [displayedColumns]="displayedColumns"
        [actions]="actions"
        [total]="total"
        [sortDefault]="sortDefault"
        [rowClick]="handleViewUserDetail"
        [searchForm]="searchForm"
        (searchSelectChanged)="handleLazyLoadSelect($event)"
      ></fuse-table-component>
    </div>
  </div>
</div>

<ng-template #inviteUserTpl>
  <app-invite-user
    class="h-full"
    (submitEmitter)="handleGetUsers()"
  ></app-invite-user>
</ng-template>
