import { provideHttpClient } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatDialog } from '@angular/material/dialog';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { ActivatedRoute } from '@angular/router';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { FuseUltils } from '@fuse/ultils';
import {
  ROLE_MODULES,
  ROLE_SCOPES,
  ROUTE,
} from 'apps/pacific-ii-cms/src/app/core/const';
import { provideIcons } from 'apps/pacific-ii-cms/src/app/core/icons/icons.provider';
import { StorageService } from 'apps/pacific-ii-cms/src/app/core/services/storage.service';
import { UserService } from 'apps/pacific-ii-cms/src/app/core/user/user.service';
import { Utils } from 'apps/pacific-ii-cms/src/app/core/utils/utils';
import { AuthorizationService } from 'apps/pacific-ii-cms/src/app/services/authorization.service';
import { ToastrModule } from 'ngx-toastr';
import { of, ReplaySubject } from 'rxjs';
import { ChangeRoleComponent } from '../change-role/change-role.component';
import { UserManagementService } from '../user-management.service';
import { UserInfo } from '../user.types';
import { UserDetailComponent } from './user-detail.component';
import { provideTranslate } from '../../../../core/transloco/transloco.provider';

describe('UserDetailComponent', () => {
  let component: UserDetailComponent;
  let fixture: ComponentFixture<UserDetailComponent>;

  const mockTenantId = '116024918514279424';

  const mockUserRoles = {
    content: [
      {
        id: '1',
        roleTitle: 'System Admin',
        externalId: 'SYSTEM_ADMIN',
        tenantId: '0',
        createdAt: 1729834695757,
        updatedAt: 1729834695757,
        permissions: [],
      },
      {
        id: '2',
        roleTitle: 'Service Operator',
        externalId: 'SERVICE_TEAM',
        tenantId: '0',
        createdAt: 1729834695804,
        updatedAt: 1729834695804,
        permissions: [],
      },
      {
        id: '126545645041453056',
        roleTitle: 'Role 1',
        externalId: null,
        tenantId: '0',
        createdAt: 1730170832946,
        updatedAt: 1730170832946,
        permissions: [],
      },
      {
        id: '131668116276788224',
        roleTitle: 'Duc',
        externalId: null,
        tenantId: '0',
        createdAt: 1731392125197,
        updatedAt: 1731392125197,
        permissions: [],
      },
    ],
    totalElements: 4,
    totalPages: 1,
    page: 0,
    sort: ['id: ASC'],
  };

  const mockUser: UserInfo = {
    id: '132728749089101824',
    ssoId: '855ea070-cfef-45ce-971b-6e0fa018b94a',
    externalId: '***********',
    uniqueExternalId: undefined,
    firstName: 'D',
    lastName: 'D',
    email: '<EMAIL>',
    userType: 'SYSTEM_ADMIN',
    realmId: 'pacific-backoffice',
    avatar: {
      url: 'url.test.com',
      path: 'path.test.com',
    },
    userStatus: 'ACTIVE',
    phoneNumber: '**********',
    totalSubAccounts: '0',
    totalSponsors: '0',
    userGroup: {
      id: '121520479628366848',
      tenantId: mockTenantId,
      path: '121520479628366848',
      groupName: 'Default',
      description:
        'By default, all customer of tenant would be added into this group. ',
      parent: '',
    },
    userNonCompletedActions: [],
    permissions: [
      {
        tenantId: mockTenantId,
        userId: '132728749089101824',
        userRoleId: mockUserRoles.content[0].id,
        permissionStatus: 'ACTIVE',
      },
      {
        tenantId: 0,
        userId: '132728749089101824',
        userRoleId: mockUserRoles.content[1].id,
        permissionStatus: 'ACTIVE',
      },
    ],
    createdAt: *************,
    updatedAt: *************,
  };

  const mockFormattedUser = {
    ...mockUser,
    userStatusColor: Utils.getStatusColor(mockUser.userStatus),
    permissions: [
      {
        tenantId: mockTenantId,
        userId: '132728749089101824',
        userRoleId: mockUserRoles.content[0].id,
        permissionStatus: 'ACTIVE',
        userRole: mockUserRoles.content[0].roleTitle,
        permissionStatusColor: Utils.getStatusColor('ACTIVE'),
        externalId: mockUserRoles.content[0].externalId,
      },
    ],
  };

  const mockPermissionList = [
    {
      externalId: `${ROLE_MODULES.USER_MGMT}_${ROLE_SCOPES.ADD}`,
    },
    {
      externalId: `${ROLE_MODULES.USER_MGMT}_${ROLE_SCOPES.UPDATE}`,
    },
    {
      externalId: `${ROLE_MODULES.USER_MGMT}_${ROLE_SCOPES.DELETE}`,
    },
  ];

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [UserDetailComponent, ToastrModule.forRoot()],
      providers: [
        provideIcons(),
        provideTranslate(),
        provideHttpClient(),
        provideAnimationsAsync(),
        {
          provide: ActivatedRoute,
          useValue: {
            paramMap: of({
              params: {
                id: mockUser.id,
              },
            }),
          },
        },
        {
          provide: UserManagementService,
          useValue: {
            userDetail$: new ReplaySubject<UserInfo>(1),
            getUserDetail: jest.fn().mockReturnValue(of(mockUser)),
            changeUserRole: jest.fn().mockReturnValue(of(null)),
            resendInvitation: jest.fn().mockReturnValue(of(null)),
          },
        },
        {
          provide: AuthorizationService,
          useValue: {
            getUserRole: jest.fn().mockReturnValue(of(mockUserRoles)),
          },
        },
        {
          provide: UserService,
          useValue: {
            user$: of(mockUser),
          },
        },
        {
          provide: StorageService,
          useValue: {
            getUserMode: jest.fn().mockReturnValue('tenant'),
            getTenantId: () => mockTenantId,
          },
        },
        {
          provide: MatDialog,
          useValue: {
            open: jest.fn().mockReturnValue({
              afterClosed: () => of('confirmed'),
            }),
          },
        },
        {
          provide: FuseConfirmationService,
          useValue: {
            open: jest.fn().mockReturnValue({
              afterClosed: () => of('confirmed'),
            }),
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(UserDetailComponent);
    component = fixture.componentInstance;

    component['_userPermissionService']['_permissions'].next(
      mockPermissionList,
    );
    component['_userPermissionService']['permissionList'] = mockPermissionList;

    jest.spyOn(component['_toast'], 'error');
    jest.spyOn(component['_toast'], 'success');
    jest.spyOn(component['_router'], 'navigate');
    jest.spyOn(component['_userService'], 'getUserDetail');

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
    expect(component.id).toBe(mockUser.id);
  });

  describe('Lifecycle Hook', () => {
    it('should get the account detail and call handleGetData when run ngOnInit', () => {
      jest.spyOn(component, 'handleGetData');

      component.ngOnInit();

      expect(component.accountDetail).toEqual(mockUser);
      expect(component.handleGetData).toHaveBeenCalled();
    });

    it('should unsubscribe all service when run ngOnDestroy', () => {
      const unsubscribeNextSpy = jest.spyOn(
        component['_unsubscribeAll'],
        'next',
      );
      const unsubscribeCompleteSpy = jest.spyOn(
        component['_unsubscribeAll'],
        'complete',
      );

      component.ngOnDestroy();

      expect(unsubscribeNextSpy).toHaveBeenCalled();
      expect(unsubscribeCompleteSpy).toHaveBeenCalled();
    });
  });

  describe('Data Fetching', () => {
    it('should get the role list and then call handleGetUserDetail when run handleGetData', () => {
      const params = {
        'filter.byTenantId': mockTenantId,
        page: 0,
        size: 500,
      };
      component.userRoleList = [];
      jest.spyOn(component, 'handleGetUserDetail');

      component.handleGetData();

      expect(
        component['_authorizationService'].getUserRole,
      ).toHaveBeenCalledWith(FuseUltils.objectToQueryString(params));
      expect(component.handleGetUserDetail).toHaveBeenCalled();
      expect(component.userRoleList).toEqual(mockUserRoles.content);
    });

    it('should send request to get user detail when run handleGetUserDetail', () => {
      component.id = mockUser.id;

      component.handleGetUserDetail();

      expect(component['_userService'].getUserDetail).toHaveBeenCalledWith(
        mockUser.id,
      );
      expect(component.userDetail).toEqual(mockFormattedUser);
    });

    it('should get the user permission when run getUserPermission', () => {
      expect(component.permissionList).toEqual({
        update: false,
      });

      component['getPermission']();

      expect(component.permissionList).toEqual({
        update: true,
      });
    });
  });

  describe('Server Communication Function', () => {
    it('should send request to resend the onResendInvitation to user when run on', () => {
      component.userDetail = mockUser;

      component.onResendInvitation();

      expect(component['_userService'].resendInvitation).toHaveBeenCalledWith(
        mockUser.id,
      );
      expect(component['_toast'].success).toHaveBeenCalledWith(
        'Resend invitation success!',
      );
    });

    it('should update the user role and call the handleGetUserDetail when run handleChangeUserRole', () => {
      jest.spyOn(component, 'handleGetUserDetail');

      component.handleChangeUserRole(mockUserRoles.content[1].id);

      expect(component['_userService'].changeUserRole).toHaveBeenCalledWith(
        mockUser.id,
        mockUserRoles.content[1].id,
      );
      expect(component.handleGetUserDetail).toHaveBeenCalled();
      expect(component['_toast'].success).toHaveBeenCalledWith(
        'Change role of the user success!',
      );
    });
  });

  describe('Utility Function', () => {
    it('should navigate to User Management page when run gotoUserManagement', () => {
      component.gotoUserManagement();

      expect(component['_router'].navigate).toHaveBeenCalledWith([
        ROUTE.USER.MAIN,
      ]);
    });

    it('should navigate to Edit page when run gotoEditPage', () => {
      component.gotoEditUser();

      expect(component['_router'].navigate).toHaveBeenCalledWith([
        `${ROUTE.USER.MAIN}/${ROUTE.USER.EDIT}/${mockUser.id}`,
      ]);
    });

    it('should open the change role dialog and call the onOpenChangeRoleConfirmationDialog when run onOpenChangeRoleDialog', () => {
      jest.spyOn(component, 'onOpenChangeRoleConfirmationDialog');

      component.onOpenChangeRoleDialog();

      expect(component['_dialog'].open).toHaveBeenCalledWith(
        ChangeRoleComponent,
        {
          width: '60%',
          data: {
            user: mockFormattedUser,
            account: mockFormattedUser,
          },
          autoFocus: false,
          disableClose: true,
        },
      );

      expect(component.onOpenChangeRoleConfirmationDialog).toHaveBeenCalledWith(
        'confirmed',
      );
    });

    it('should open the change role confirmation dialog and call the handleChangeRole when run onOpenChangeRoleConfirmationDialog', () => {
      jest.spyOn(component, 'handleChangeUserRole');

      component.onOpenChangeRoleConfirmationDialog(mockUser.id);

      expect(component['_confirmationService'].open).toHaveBeenCalledWith({
        title: 'Change Role',
        message: 'Please confirm to change role of this user.',
        icon: {
          show: false,
        },
        actions: {
          confirm: {
            label: 'Confirm',
            class: 'btn-contained__primary__medium',
          },
          cancel: {
            label: 'Cancel',
            class: 'btn-text__inherit__medium',
          },
        },
        dismissible: false,
      });

      expect(component.handleChangeUserRole).toHaveBeenCalledWith(mockUser.id);
    });
  });
});
