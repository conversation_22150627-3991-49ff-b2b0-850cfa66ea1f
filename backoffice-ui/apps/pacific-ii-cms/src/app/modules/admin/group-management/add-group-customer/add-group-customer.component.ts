import { ChangeDetectorRef, Component, Inject, OnInit } from '@angular/core';
import {
  ReactiveFormsModule,
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { FuseLazyLoadSelectComponent } from '@fuse/components/lazy-load-select/lazy-load-select.component';
import { FuseUltils } from '@fuse/ultils';
import { ToastrService } from 'ngx-toastr';
import { GroupManagementService } from '../group-management.service';
import { Utils } from 'apps/pacific-ii-cms/src/app/core/utils/utils';
import { FuseHelpLinkComponent } from '@fuse/components/help-link';

@Component({
  selector: 'app-add-group-customer',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    MatProgressSpinnerModule,
    FuseLazyLoadSelectComponent,
    MatIconModule,
    FuseHelpLinkComponent,
  ],
  templateUrl: './add-group-customer.component.html',
})
export class AddGroupCustomerComponent implements OnInit {
  customersForm!: UntypedFormGroup;
  loading = false;

  optionListCustomer: Array<any> = [];
  customerList: Array<any> = [];
  selectedCustomerList: Array<any> = [];

  customerAppend = false;

  errorMessages = {
    customers: {
      required: 'Please select customer!',
      maxlength: 'Maximum add 100 customers!',
    },
  };

  constructor(
    private _toast: ToastrService,
    private _dialogRef: MatDialogRef<any>,
    private _formBuilder: UntypedFormBuilder,
    private _groupsService: GroupManagementService,
    private _changeDetectorRef: ChangeDetectorRef,
    @Inject(MAT_DIALOG_DATA) public data: any,
  ) {
    this.initForm();
  }

  ngOnInit(): void {
    this.handleGetListCustomer();
  }

  handleGetListCustomer(): void {
    const mappedData = {
      filter: {
        byName: this.customersForm.value.searchValue,
      },
      size: 10,
      page: this.customersForm.value.page,
    };

    this._groupsService
      .getCustomers(FuseUltils.objectToQueryString(mappedData))
      .subscribe((customers: any) => {
        const customersServer = customers?.content?.map((item: any) => {
          return {
            ...item,
            name: `${item.firstName} ${item.lastName} - (${item.email ? item.email : 'no email'})${item?.userGroup?.groupName ? ' - (' + item.userGroup.groupName + ')' : ''}`,
          };
        });

        this.optionListCustomer = this.customerAppend
          ? [...this.optionListCustomer, ...customersServer]
          : [...customersServer];

        this.optionListCustomer = [
          ...this.optionListCustomer.map((item) => {
            return {
              ...item,
              hidden: item?.userGroup?.id == this.data?.groupId,
            };
          }),
        ];

        if (
          !this.customersForm.value.searchValue &&
          !this.customersForm.value.page
        ) {
          this.customerList = [...this.optionListCustomer];
        }

        this._changeDetectorRef.markForCheck();
      });
  }

  submitForm(): void {
    for (const i in this.customersForm.controls) {
      this.customersForm.controls[i].markAsTouched();
      this.customersForm.controls[i].updateValueAndValidity();
    }

    if (this.customersForm.invalid) {
      Utils.scrollToInvalid();
      return;
    }

    this.loading = true;
    const data = { userIds: this.customersForm.value.customers };
    this.handleAddCustomer(data);
  }

  handleAddCustomer(data: any): void {
    this._groupsService.assignCustomer(this.data.groupId, data).subscribe({
      next: () => {
        this.loading = false;
        this._toast.success('Add customer success!');
        this.onClose('save');
      },
      error: () => {
        this.loading = false;
      },
    });
  }

  onClose(message?: string): void {
    this._dialogRef.close(message);
  }

  getCustomerDetailList(customerIds: any): any {
    const customerIdsSet = new Set(customerIds);
    const customerDetailList = this.optionListCustomer.filter((customer) =>
      customerIdsSet.has(customer.id),
    );

    this.selectedCustomerList = [...customerDetailList];
  }

  changeSelectedData(data: any) {
    this.selectedCustomerList = [...data];
  }

  onChangeSearchValue(event: any) {
    this.customerAppend = event.page > this.customersForm.value.page;

    this.customersForm.patchValue({
      searchValue: event.search,
      page: event.page,
    });
    this.handleGetListCustomer();
  }

  private initForm(): void {
    this.customersForm = this._formBuilder.group({
      customers: [[], [Validators.required, Validators.maxLength(100)]],
      searchValue: [null],
      page: [0],
    });

    // this.customersForm.controls['customers'].valueChanges.subscribe((value) => {
    //   this.getCustomerDetailList(value);
    // });
  }
}
