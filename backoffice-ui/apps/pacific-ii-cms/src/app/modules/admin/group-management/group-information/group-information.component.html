<div class="flex flex-col h-full overflow-auto">
  <div
    class="flex items-end justify-between border-b-[1px] border-dashed border-grey-300 mx-4"
  >
    <div class="flex flex-col items-start justify-end flex-1 gap-2 py-4 pr-4">
      @if (groupDetail) {
        <div class="flex flex-col items-start gap-1">
          <div class="web__subtitle1 text-primary-700">
            {{ groupDetail?.groupName }}
          </div>
          <div class="break-all web__body2 text-grey-900">
            {{ groupDetail?.description }}
          </div>
        </div>

        <div>
          <div class="web__body2 text-[#212b36]">Creation Date</div>
          <div class="web__body2 text-grey-600">
            {{
              groupDetail && groupDetail.createdAt
                ? groupDetail.createdDate
                : 'N/A'
            }}
          </div>
        </div>

        @if (permissionList.update || permissionList.delete) {
          <div>
            @if (permissionList.update) {
              <button
                class="btn-text__inherit__medium"
                (click)="onOpenEditGroupDialog()"
              >
                Edit
              </button>
            }

            @if (permissionList.delete) {
              <button
                class="btn-text__error__medium"
                [disabled]="!leafNode"
                (click)="onOpenDeleteGroupDialog()"
              >
                Delete
              </button>
            }
          </div>
        }
      }
    </div>

    <div class="flex pl-4 py-4 justify-center items-end gap-2.5">
      @if (permissionList.update) {
        <button
          class="btn-contained__primary__medium"
          [disabled]="!groupId"
          (click)="onOpenAddSubGroupDialog()"
        >
          Add Sub-group
        </button>
      }
    </div>
  </div>

  <div class="flex-1">
    <fuse-table-component
      [dataSource]="dataSource"
      [displayedColumns]="displayedColumns"
      [bulkActions]="bulkActions"
      [total]="total"
      [sortDefault]="sortDefault"
      [selectElement]="permissionList.delete ? true : false"
      (selectedElementChanged)="onChangeSelectedElement($event)"
      [searchForm]="searchForm"
    >
      <ng-container fuseTableSubtitle>
        <div class="flex justify-between p-3">
          <div class="web__subtitle2">Sub-group(s)</div>
        </div>
      </ng-container></fuse-table-component
    >
  </div>
</div>
