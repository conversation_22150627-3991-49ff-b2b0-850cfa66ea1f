import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  ElementRef,
  Input,
  OnDestroy,
  OnInit,
} from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { filter, forkJoin, Subject, takeUntil, tap } from 'rxjs';
import { FuseTableComponent, IAction } from '@fuse/components/table';
import { ActivatedRoute } from '@angular/router';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { ToastrService } from 'ngx-toastr';
import { MatIconModule } from '@angular/material/icon';
import { CardManagementService } from '../card-management.service';
import { FuseUltils } from '@fuse/ultils';
import { AddCardComponent } from '../add-card/add-card.component';
import {
  MatSlideToggle,
  MatSlideToggleChange,
  MatSlideToggleModule,
} from '@angular/material/slide-toggle';
import {
  DATE_TIME_FORMAT,
  ROLE_MODULES,
  ROLE_SCOPES,
} from '../../../../core/const';
import { UserPermissionService } from '../../../../core/user/user-permission.service';

@Component({
  selector: 'app-card-list',
  standalone: true,
  imports: [MatIconModule, MatSlideToggleModule, FuseTableComponent],
  templateUrl: './card-list.component.html',
})
export class CardListComponent implements OnInit, OnDestroy, AfterViewInit {
  @Input() id!: string;
  queryParams!: any;
  selectedCards: Array<any> = [];

  dataSource: Array<any> = [];
  displayedColumns: any = [];
  actions: Array<IAction> = [];
  total = 0;
  sortDefault: any = {
    field: 'cardId',
    direction: 'asc',
  };

  permissionList = {
    update: false,
  };

  private _unsubscribeAll: Subject<any> = new Subject<any>();

  constructor(
    private _dialog: MatDialog,
    private _toast: ToastrService,
    private _activatedRoute: ActivatedRoute,
    private _cardsService: CardManagementService,
    private _confirmationService: FuseConfirmationService,
    private _changeDetectorRef: ChangeDetectorRef,
    private _userPermissionService: UserPermissionService,
  ) {
    this.getPermission();
    this.initCardsSubscription();
  }

  ngOnInit(): void {
    this._activatedRoute.queryParams
      .pipe(
        takeUntil(this._unsubscribeAll),
        filter((queryParams) => queryParams['tab'] != 0),
        tap((queryParams) => {
          this.queryParams = queryParams;
        }),
      )
      .subscribe(() => {
        this.handleGetCards();
      });
  }

  ngAfterViewInit(): void {
    this.initTableColumn();
    this.initTableAction();
    this._changeDetectorRef.detectChanges();
  }

  /**
   * On destroy
   */
  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this._unsubscribeAll.next(null);
    this._unsubscribeAll.complete();
  }

  handleGetCards(): void {
    const mappedData = this.getCardParams(this.queryParams);
    this._cardsService
      .getCards(this.id, FuseUltils.objectToQueryString(mappedData))
      .subscribe();
  }

  getCardParams(queryParams: any): any {
    const mappedData = {
      size: queryParams['size'] || 10,
      page: queryParams['page'] || 0,
      sortDirection: queryParams['sortDirection'] || this.sortDefault.direction,
      sortFields: queryParams['sortFields'] || this.sortDefault.field,
    };

    return mappedData;
  }

  onRowClick = (row: any) => {
    if (this.permissionList.update) {
      this.onOpenEditCardDialog(row);
    }
  };

  detectToggleChange(event: MatSlideToggleChange, element: any) {
    event.source.checked = element.status === 'ACTIVE' ? true : false;

    this.onOpenChangeStatusDialog(
      element,
      element.status === 'ACTIVE' ? false : true,
    );
  }

  stopPropagation(event: MouseEvent): void {
    event.stopPropagation();
  }

  onChangeSelectedElement(event: Array<any>): void {
    this.selectedCards = event.map((item) => item.id);
  }

  onOpenAddCardDialog(): void {
    const dialogRef = this._dialog.open(AddCardComponent, {
      width: '60%',
      data: {
        userId: this.id,
      },
      autoFocus: false,
      disableClose: true,
    });
    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.handleGetCards();
      }
    });
  }

  onOpenEditCardDialog(element: any): void {
    const dialogRef = this._dialog.open(AddCardComponent, {
      width: '60%',
      data: {
        userId: this.id,
        card: element,
      },
      autoFocus: false,
      disableClose: true,
    });
    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.handleGetCards();
      }
    });
  }

  onOpenDeleteCardDialog(element: any): void {
    const dialogRef = this._confirmationService.open({
      title: 'Delete Card',
      message: 'Please confirm to delete this card.',
      icon: {
        show: false,
      },
      actions: {
        confirm: {
          label: 'Delete',
        },
        cancel: {
          label: 'Close',
        },
      },
      dismissible: false,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        this.handleDeleteCard(element);
      }
    });
  }

  handleDeleteCard(card: any): void {
    this._cardsService.deleteCard(this.id, card.id).subscribe(() => {
      this._toast.success('Delete card success!');
      this.handleGetCards();
    });
  }

  onOpenDeleteSelectedCardsDialog(): void {
    const dialogRef = this._confirmationService.open({
      title: 'Delete Card',
      message: 'Please confirm to delete the selected cards.',
      icon: {
        show: false,
      },
      actions: {
        confirm: {
          label: 'Delete',
        },
        cancel: {
          label: 'Close',
        },
      },
      dismissible: false,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        this.handleDeleteSelectedCard();
      }
    });
  }

  handleDeleteSelectedCard(): void {
    forkJoin(
      this.selectedCards.map((item: any) =>
        this._cardsService.deleteCard(this.id, item),
      ),
    ).subscribe({
      next: () => {
        this._toast.success('Selected card(s) have been deleted successfully!');
        this.handleGetCards();
      },
      error: () => {
        this.handleGetCards();
      },
    });
  }

  onOpenChangeStatusDialog(element: any, checked: boolean): void {
    const dialogRef = this._confirmationService.open({
      title: `${checked ? 'Activate' : 'Deactivate'}`,
      message: `Please confirm to ${checked ? 'activate' : 'deactivate'} this card.`,
      icon: {
        show: false,
      },
      actions: {
        confirm: {
          label: 'OK',
          class: 'btn-contained__warning__medium',
        },
        cancel: {
          label: 'Cancel',
          class: 'btn-text__inherit__medium',
        },
      },
      dismissible: false,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        this.handleUpdateStatus(element, checked);
      }
    });
  }

  handleUpdateStatus(card: any, checked: boolean): void {
    const data = {
      ...card,
      status: checked ? 'ACTIVE' : 'IN_ACTIVE',
    };
    this._cardsService.updateCard(this.id, card.id, data).subscribe(() => {
      this._toast.success('You have changed the status successfully!');
      this.handleGetCards();
    });
  }

  private initCardsSubscription(): void {
    this._cardsService.cards$
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((data: any) => {
        this.dataSource = data.content;
        this.total = data.totalElements;
      });
  }

  /**
   * Initializes the table column configuration.
   */
  private initTableColumn(): void {
    this.displayedColumns = [
      {
        key: 'cardId',
        name: 'Card ID',
        // sort: true,
        selected: true,
        renderHtml: (value: string) => {
          return `<p class="font-semibold">${value}</p>`;
        },
      },
      {
        key: 'cardAlias',
        name: 'Card Alias',
        selected: true,
        renderHtml: (value: string) => {
          return `<p class="font-semibold">${value}</p>`;
        },
      },
      {
        key: 'createdAt',
        name: 'Creation Date',
        selected: true,
        render: (date: number) => {
          return FuseUltils.tsToLocalTime(date, DATE_TIME_FORMAT.DATE_DEFAULT);
        },
      },
      {
        key: 'status',
        name: 'Status',
        selected: true,
        custom: true,
      },
    ];
  }

  /**
   * Initializes the actions array for the table.
   *
   * @private
   * @returns {void}
   */
  private initTableAction(): void {
    this.actions = [
      {
        name: 'Edit',
        type: 'event',
        hidden: () => !this.permissionList.update,
        callback: (element) => this.onOpenEditCardDialog(element),
      },
      {
        name: 'Delete',
        type: 'event',
        hidden: () => !this.permissionList.update,
        callback: (element) => this.onOpenDeleteCardDialog(element),
      },
    ];
  }

  private getPermission(): void {
    this._userPermissionService.permissions$
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe(() => {
        this.permissionList = {
          update: this._userPermissionService.hasPermissions([
            {
              module: ROLE_MODULES.CUSTOMER_USER_CARD_MGMT,
              scope: ROLE_SCOPES.UPDATE,
            },
          ]),
        };

        if (this.displayedColumns) {
          this.displayedColumns = [...this.displayedColumns];
        }
      });
  }
}
