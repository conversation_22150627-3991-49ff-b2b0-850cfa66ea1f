import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable, ReplaySubject, Subject, tap } from 'rxjs';
import {
  PaymentTransaction,
  PaymentTransactionResponse,
} from './payment-transaction.types';
import { API } from '../../../core/const';

@Injectable({
  providedIn: 'root',
})
export class PaymentTransactionService {
  private _httpClient = inject(HttpClient);
  private _paymentTransactionResponse: ReplaySubject<PaymentTransactionResponse> =
    new ReplaySubject<PaymentTransactionResponse>(1);
  private _paymentTransactionDetail: Subject<PaymentTransaction> =
    new Subject<PaymentTransaction>();

  /**
   * Getter for payment transaction response
   */
  get paymentTransactionResponse$(): Observable<PaymentTransactionResponse> {
    return this._paymentTransactionResponse.asObservable();
  }

  /**
   * Getter for payment transaction detail
   */
  get paymentTransactionDetail$(): Observable<PaymentTransaction> {
    return this._paymentTransactionDetail.asObservable();
  }

  // -----------------------------------------------------------------------------------------------------
  // @ Public methods
  // -----------------------------------------------------------------------------------------------------

  /**
   * Get all payment transactions
   */
  getPaymentTransactions(queryParams?: any): Observable<any> {
    return this._httpClient
      .get<any>(`${API.PAYMENT_TRANSACTION.LIST}?${queryParams ?? ''}`)
      .pipe(
        tap((response) => {
          this._paymentTransactionResponse.next(response);
        }),
      );
  }

  /**
   * Get payment transaction detail
   */
  getPaymentTransactionDetail(id: string): Observable<any> {
    return this._httpClient
      .get<any>(API.PAYMENT_TRANSACTION.DETAIL.replace('{id}', id))
      .pipe(
        tap((detail) => {
          this._paymentTransactionDetail.next(detail);
        }),
      );
  }
}
