import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute } from '@angular/router';
import { FuseTableComponent, IAction } from '@fuse/components/table';
import { FuseUltils } from '@fuse/ultils';
import { Subject, switchMap, takeUntil, tap } from 'rxjs';
import { PaymentTransactionDetailComponent } from './payment-transaction-detail/payment-transaction-detail.component';
import { PaymentTransactionService } from './payment-transaction.service';
import { PaymentTransaction } from './payment-transaction.types';
import { PaymentMethodService } from '../payment-method/payment-method.service';
import {
  PaymentMethod,
  PaymentMethodResponse,
} from '../payment-method/payment.types';
import { StorageService } from '../../../core/services/storage.service';
import { Currency } from '../tenant-management/tenant.types';
import { DeviceManagementService } from '../device-management/device-management.service';
import { MatTooltip } from '@angular/material/tooltip';
import { OFFLINE_OPTIONS } from '../../../core/const';
import { NgClass } from '@angular/common';

@Component({
  selector: 'app-payment-transaction',
  standalone: true,
  imports: [NgClass, MatTooltip, FuseTableComponent],
  templateUrl: './payment-transaction.component.html',
})
export class PaymentTransactionComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  queryParams!: any;
  dataSource: Array<PaymentTransaction> = [];
  displayedColumns: any = [];
  total = 0;
  sortDefault: any = {
    field: 'paidAt',
    direction: 'desc',
  };
  actions: Array<IAction> = [];

  searchForm: any = {};

  paymentMethodSearchConfig: {
    page: number;
    size: number;
    filter: {
      byMethodName: string;
    };
    sortDirection: string;
    sortFields: string;
  } = {
    sortDirection: 'asc',
    sortFields: 'displayName',
    page: 0,
    size: 10,
    filter: {
      byMethodName: '',
    },
  };
  paymentMethodAppend = false;

  deviceParams = {
    filter: {
      name: '',
    },
    page: 0,
    size: 10,
  };
  deviceAppend = false;
  deviceOptions: any = [];

  tenantCurrency!: Currency;

  mapTransactionTypes: any = {
    TOP_UP: 'TOP UP',
    PURCHASE: 'PURCHASE',
  };

  displayDevices: any = [];

  private _unsubscribeAll: Subject<any> = new Subject<any>();

  constructor(
    private _paymentTransService: PaymentTransactionService,
    private _dialog: MatDialog,
    private _activatedRoute: ActivatedRoute,
    private _changeDetectorRef: ChangeDetectorRef,
    private _paymentMethodService: PaymentMethodService,
    private _storageService: StorageService,
    private _deviceService: DeviceManagementService,
  ) {
    this.tenantCurrency = this._storageService.getTenantCurrency();
    this.searchForm = this.initSearchForm();
    this.paymentTransactionSubscription();
  }

  ngOnInit(): void {
    this.getPaymentMethods();
    this.handleGetDevices();
    this._activatedRoute.queryParams
      .pipe(
        takeUntil(this._unsubscribeAll),
        tap((queryParams) => {
          this.queryParams = queryParams;
        }),
      )
      .subscribe(() => {
        this.getPaymentTransactions();
      });
  }

  ngAfterViewInit(): void {
    this.initTableColumn();
    this.initActions();
    this._changeDetectorRef.detectChanges();
  }

  ngOnDestroy(): void {
    this._unsubscribeAll.next(null);
    this._unsubscribeAll.complete();
  }

  /**
   * Get data functions
   */
  paymentTransactionSubscription() {
    this._paymentTransService.paymentTransactionResponse$
      .pipe(
        takeUntil(this._unsubscribeAll),
        tap((value) => {
          this.dataSource = value.content.map((value) => {
            value.customerEmail = value.customerEmail || '_';
            value.customerName = value.customerName || '_';

            return value;
          });
          this.total = value.totalElements;
        }),
        switchMap((value) => {
          const deviceIds = [
            ...new Set(value.content.map((data) => data.deviceId)),
          ];

          return this.handleGetDisplayedDevices(deviceIds);
        }),
      )
      .subscribe();
  }

  getPaymentTransactions() {
    const mappedData: any = this.mapParams(this.queryParams);
    this._paymentTransService
      .getPaymentTransactions(FuseUltils.objectToQueryString(mappedData))
      .subscribe((data: any) => {
        this.dataSource = data.content;
        this.total = data.totalElements;
      });
  }

  getPaymentMethods() {
    this._paymentMethodService
      .getPaymentMethods(
        FuseUltils.objectToQueryString(this.paymentMethodSearchConfig),
      )
      .subscribe((data: PaymentMethodResponse) => {
        const serverDataMap = [
          ...data.content.map((value: PaymentMethod) => ({
            label: value.displayName,
            value: value.id,
          })),
        ];

        const updatedSearchForm = { ...this.searchForm };

        updatedSearchForm.advanced[1].options = this.paymentMethodAppend
          ? [...updatedSearchForm.advanced[1].options, ...serverDataMap]
          : [
              {
                label: 'All',
                value: '',
              },
              ...serverDataMap,
            ];

        // Trigger the changes in Advanced Search Component
        this.searchForm = { ...updatedSearchForm };
        this._changeDetectorRef.markForCheck();
      });
  }

  mapParams(queryParams: any) {
    const filter = {
      byCustomerEmail: queryParams['customerEmail'] || '',
      byTransactionType:
        queryParams['transactionType'] ?? this.searchForm.basic[1].defaultValue,
      byStatus:
        queryParams['transactionStatus'] ??
        this.searchForm.basic[2].defaultValue,
      byPaymentMethodId: queryParams['paymentMethodId'] || '',
      byTransactionIds: queryParams['id'] ? [queryParams['id']] : [],
      byCreatedRange: {
        from: queryParams['createdAtStart'],
        to: queryParams['createdAtEnd'],
      },
      byDeviceId: queryParams['deviceId'],
      byDeviceTransactionNumber: queryParams['deviceTransactionNumber'],
      byTransactionNumber: queryParams['transactionNumber'],
      isOffline: queryParams['isOffline'] || '',
    };

    const mappedData = {
      filter,
      size: queryParams['size'] || 10,
      page: queryParams['page'] || 0,
      sortDirection: queryParams['sortDirection'] || this.sortDefault.direction,
      sortFields: queryParams['sortFields'] || [this.sortDefault.field],
    };

    if (queryParams['sortFields']) {
      mappedData.sortFields = mappedData.sortFields.replace('storeId', 'id');
    }

    return mappedData;
  }

  handleLazyLoadSelect(event: any) {
    if (event.controlName === 'paymentMethodId') {
      this.paymentMethodAppend =
        event.value.page > this.paymentMethodSearchConfig.page;

      this.paymentMethodSearchConfig = {
        ...this.paymentMethodSearchConfig,
        page: event.value.page,
        size: 10,
        filter: {
          byMethodName: event.value.search,
        },
      };

      this.getPaymentMethods();
    } else {
      this.deviceAppend = event.value.page > this.deviceParams.page;
      this.deviceParams.filter.name = event.value.search;
      this.deviceParams.page = event.value.page;

      this.handleGetDevices();
    }
  }

  handleGetDevices() {
    this._deviceService
      .getDevices(FuseUltils.objectToQueryString(this.deviceParams))
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((data: any) => {
        const serverDataMap = [
          ...data.content.map((value: any) => ({
            label: value.name || value.deviceId,
            value: value.deviceId,
          })),
        ];

        this.deviceOptions = this.deviceAppend
          ? [...this.deviceOptions, ...serverDataMap]
          : [
              {
                label: 'All',
                value: '',
              },
              ...serverDataMap,
            ];

        const updatedSearchForm = { ...this.searchForm };

        updatedSearchForm.advanced[3].options = [...this.deviceOptions];

        // Trigger the changes in Advanced Search Component
        this.searchForm = { ...updatedSearchForm };
        this._changeDetectorRef.markForCheck();
      });
  }

  handleGetDisplayedDevices(deviceIds: Array<string>) {
    const params = {
      filter: {
        deviceId: deviceIds,
      },
      page: 0,
      size: this.queryParams?.size || 10,
    };
    return this._deviceService
      .getDevices(FuseUltils.objectToQueryString(params))
      .pipe(
        takeUntil(this._unsubscribeAll),
        tap((res: any) => {
          this.displayDevices = [...res.content];
          if (this.displayedColumns) {
            const updatedColumns = [...this.displayedColumns];
            updatedColumns[6] = {
              key: 'deviceId',
              name: 'Device Name',
              selected: false,
              render: (value: string) => {
                const method = this.displayDevices.find(
                  (item: any) => item.deviceId == value,
                );

                return method ? method.name : `Unknown Device (${value})`;
              },
            };
            this.displayedColumns = [...updatedColumns];
            this._changeDetectorRef.detectChanges();
          }
        }),
      );
  }

  onOpenViewTransactionDetailDialog(element: any): void {
    this._dialog.open(PaymentTransactionDetailComponent, {
      width: '60%',
      height: '90vh',
      data: { id: element.id },
    });
  }

  onRowClick = (row: any) => {
    this.onOpenViewTransactionDetailDialog(row);
  };

  /**
   * Initializes the table column configuration.
   */
  private initTableColumn(): void {
    this.displayedColumns = [
      {
        key: 'paidAt',
        name: 'Payment Time',
        selected: true,
        sort: true,
        renderHtml: (date: number) => {
          return FuseUltils.tsToLocalTime(date);
        },
      },
      {
        key: 'id',
        name: 'Transaction ID',
        sort: true,
        selected: true,
        renderHtml: (value: string) => {
          return `<p class="font-semibold">${value}</p>`;
        },
      },
      {
        key: 'paymentMethodId',
        name: 'Payment Method ID',
        sort: true,
        selected: false,
        renderHtml: (value: string) => {
          return `<p class="font-semibold">${value}</p>`;
        },
      },
      {
        key: 'paymentProcessorId',
        name: 'Payment Processor ID',
        sort: true,
        selected: false,
        renderHtml: (value: string) => {
          return `<p class="font-semibold">${value}</p>`;
        },
      },
      {
        key: 'paymentMethodDisplayName',
        name: 'Method Name',
        sort: true,
        selected: true,
      },
      {
        key: 'transactionType',
        name: 'Transaction Type',
        selected: true,
        sort: true,
        renderHtml: (value: string) => {
          return this.mapTransactionTypes[value];
        },
      },
      {
        key: 'deviceId',
        name: 'Device Name',
        selected: false,
        render: (value: string) => {
          return `Unknown Device (${value})`;
        },
      },
      {
        key: 'deviceTransactionNumber',
        name: 'Device Transaction Number',
        sort: true,
        selected: false,
      },
      {
        key: 'transactionNumber',
        name: 'Transaction Number',
        sort: true,
        selected: false,
      },
      {
        key: 'customerName',
        name: 'Customer Name',
        selected: true,
        sort: true,
        renderHtml: (value: string) => {
          return `<div class="w-full h-full">${value}</div>`;
        },
      },
      {
        key: 'description',
        name: 'Description',
        selected: false,
        sort: true,
      },
      {
        key: 'paymentReference',
        name: 'Reference',
        selected: false,
        sort: true,
      },
      {
        key: 'customerId',
        name: 'Customer ID',
        selected: false,
        sort: true,
        renderHtml: (value: string) => {
          if (value !== 'null') {
            return value;
          }

          return '_';
        },
      },
      {
        key: 'customerEmail',
        name: 'Customer Email',
        selected: false,
        sort: true,
        renderHtml: (value: string) => {
          return `<div class="w-full h-full">${value}</div>`;
        },
      },
      {
        key: 'amount',
        name: 'Amount',
        selected: true,
        sort: true,
        headerAlign: 'right',
        renderHtml: (value: number) => {
          return `<div class="flex justify-end">${FuseUltils.formatPrice(value, this.tenantCurrency)}</div>`;
        },
      },
      {
        key: 'refundedAmount',
        name: 'Refunded Amount',
        selected: true,
        sort: true,
        headerAlign: 'right',
        renderHtml: (value: number) => {
          return `<div class="flex justify-end">${FuseUltils.formatPrice(value, this.tenantCurrency)}</div>`;
        },
      },
      {
        key: 'fee',
        name: 'Fee',
        selected: false,
        sort: true,
        headerAlign: 'right',
        renderHtml: (value: number) => {
          return `<div class="flex justify-end">${FuseUltils.formatPrice(value, this.tenantCurrency)}</div>`;
        },
      },
      {
        key: 'netAmount',
        name: 'Net Amount',
        selected: false,
        sort: true,
        headerAlign: 'right',
        renderHtml: (value: number) => {
          return `<div class="flex justify-end">${FuseUltils.formatPrice(value, this.tenantCurrency)}</div>`;
        },
      },
      {
        key: 'systemSource',
        name: 'System Source',
        selected: false,
        sort: true,
      },
      {
        key: 'merchantName',
        name: 'Merchant Name',
        selected: false,
        sort: true,
      },
      {
        key: 'appliedSurchargeRate',
        name: 'Applied Surcharge Rate',
        selected: false,
        sort: true,
        renderHtml: (value: number) => {
          return `<p>${(Number(value) * 100).toFixed(2)}%</p>`;
        },
      },
      {
        key: 'appliedFixedSurcharge',
        name: 'Applied Fixed Surcharge',
        selected: false,
        sort: true,
        headerAlign: 'right',
        renderHtml: (value: number, element: PaymentTransaction) => {
          return `<div class="flex justify-end">${FuseUltils.formatPrice(value, this.tenantCurrency)}</div>`;
        },
      },
      {
        key: 'offline',
        name: 'Online',
        sort: false,
        selected: true,
        custom: true,
      },
      {
        key: 'transactionStatus',
        name: 'Status',
        selected: true,
        renderHtml: (value: string) => {
          const chipColor =
            value === 'SUCCEEDED'
              ? 'bg-[#36B37E] text-white'
              : 'bg-[#D0021B] text-white';
          return `<mat-chip class="px-2 py-1 rounded-3 capitalize ${chipColor}">${value}</mat-chip>`;
        },
      },
    ];
  }

  /**
   * Initialize the action on each row of table
   */
  initActions() {
    this.actions = [
      {
        name: 'View',
        type: 'event',
        callback: (element) => this.onOpenViewTransactionDetailDialog(element),
      },
    ];
  }

  /**
   * Initializes the search form configuration object.
   *
   * @returns {any} The search form configuration object.
   */
  private initSearchForm(): any {
    return {
      basic: [
        {
          label: 'Transaction ID',
          name: 'id',
          placeholder: 'Enter transaction id',
          type: 'text',
          defaultValue: '',
        },
        {
          label: 'Transaction Type',
          name: 'transactionType',
          placeholder: 'Choose the transaction type',
          type: 'select',
          defaultValue: '',
          options: [
            {
              label: 'ALL',
              value: '',
            },
            {
              label: 'PURCHASE',
              value: 'PURCHASE',
            },
            {
              label: 'TOP UP',
              value: 'TOP_UP',
            },
          ],
        },
        {
          label: 'Status',
          name: 'transactionStatus',
          placeholder: 'Choose the transaction status',
          type: 'select',
          defaultValue: '',
          options: [
            {
              label: 'ALL',
              value: '',
            },
            {
              label: 'SUCCEEDED',
              value: 'SUCCEEDED',
            },
            {
              label: 'FAILED',
              value: 'FAILED',
            },
          ],
        },
      ],
      advanced: [
        {
          label: 'Customer Email',
          name: 'customerEmail',
          placeholder: 'Enter customer email',
          type: 'text',
          defaultValue: '',
        },
        {
          label: 'Payment Method',
          name: 'paymentMethodId',
          placeholder: 'Select payment method',
          type: 'lazy-load-select',
          defaultValue: '',
          showSearch: true,
          options: [],
        },
        {
          label: 'Creation Date',
          name: 'createdAt',
          placeholders: ['Enter start date', 'Enter end date'],
          type: 'date',
          defaultValue: '',
        },
        {
          label: 'Device Name',
          name: 'deviceId',
          placeholder: 'Select device name',
          type: 'lazy-load-select',
          defaultValue: '',
          showSearch: true,
          options: [],
        },
        {
          label: 'Device Transaction Number',
          name: 'deviceTransactionNumber',
          placeholder: 'Enter device transaction number',
          type: 'text',
          defaultValue: '',
        },
        {
          label: 'Transaction Number',
          name: 'transactionNumber',
          placeholder: 'Enter transaction number',
          type: 'text',
          defaultValue: '',
        },
        {
          label: 'Mode',
          name: 'isOffline',
          type: 'select',
          defaultValue: '',
          options: [{ label: 'All', value: '' }, ...OFFLINE_OPTIONS],
        },
      ],
    };
  }
}
