import { HttpClient, HttpHeaders } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable, ReplaySubject, tap } from 'rxjs';
import { API, TENANT_ADMIN_ID } from '../../../core/const';
import { StorageService } from '../../../core/services/storage.service';
import { ReqAddNewRole, RoleInfo } from './role-permission.types';

@Injectable({ providedIn: 'root' })
export class RolePermissionService {
  private _httpClient = inject(HttpClient);
  private _storageService = inject(StorageService);
  private _roleDetail: ReplaySubject<RoleInfo> = new ReplaySubject<RoleInfo>(1);
  /**
   * Getter for role detail
   */
  get roleDetail$(): Observable<RoleInfo> {
    return this._roleDetail.asObservable();
  }

  getRoleList(params?: any): Observable<any> {
    return this._httpClient.get<any>(API.AUTHORIZATION.ROLES_LIST, {
      params: params,
    });
  }

  getUserRole(params?: any): Observable<any> {
    return this._httpClient.get<any>(
      `${API.AUTHORIZATION.ROLES_QUERY}?${params}`,
    );
  }

  getRoleTree(params?: any): Observable<any> {
    return this._httpClient.get<any>(API.AUTHORIZATION.ROLES_TREE, {
      params: params,
    });
  }

  addNewRole(data: ReqAddNewRole): Observable<any> {
    let headers = new HttpHeaders();
    const tenantId = this._storageService.getTenantId();
    if (tenantId === TENANT_ADMIN_ID) {
      headers = headers.set('X-Tenant-ID', TENANT_ADMIN_ID);
    }

    return this._httpClient.post<any>(API.AUTHORIZATION.ADD_ROLE, data, {
      headers,
    });
  }

  editRole(data: ReqAddNewRole): Observable<any> {
    let headers = new HttpHeaders();
    const tenantId = this._storageService.getTenantId();
    if (tenantId === TENANT_ADMIN_ID) {
      headers = headers.set('X-Tenant-ID', TENANT_ADMIN_ID);
    }

    return this._httpClient.put<any>(API.AUTHORIZATION.EDIT_ROLE, data, {
      headers,
    });
  }

  getRoleById(id: any): Observable<RoleInfo> {
    return this._httpClient
      .get<RoleInfo>(API.AUTHORIZATION.DETAIL.replace('{id}', `${id}`))
      .pipe(
        tap((roleDetail) => {
          this._roleDetail.next(roleDetail);
        }),
      );
  }

  deleteRole(id: any): Observable<any> {
    return this._httpClient.delete<any>(
      API.AUTHORIZATION.DELETE_ROLE.replace('{id}', `${id}`),
    );
  }
}
