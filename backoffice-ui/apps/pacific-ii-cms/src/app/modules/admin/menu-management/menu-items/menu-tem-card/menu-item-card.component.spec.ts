import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MenuItemCardComponent } from './menu-item-card.component';
import { provideIcons } from '../../../../../core/icons/icons.provider';
import { provideHttpClient } from '@angular/common/http';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { provideTranslate } from '../../../../../core/transloco/transloco.provider';

describe('MenuItemCardComponent', () => {
  let component: MenuItemCardComponent;
  let fixture: ComponentFixture<MenuItemCardComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [MenuItemCardComponent],
      providers: [
        provideIcons(),
        provideTranslate(),
        provideHttpClient(),
        provideAnimationsAsync(),
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(MenuItemCardComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
