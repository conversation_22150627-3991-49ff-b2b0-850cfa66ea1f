import {
  After<PERSON>iew<PERSON>nit,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  ViewEncapsulation,
} from '@angular/core';
import { FuseTableComponent, IAction } from '@fuse/components/table';
import { ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { forkJoin, Subject, switchMap, takeUntil, tap } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { ToastrService } from 'ngx-toastr';
import {
  DATE_TIME_FORMAT,
  MENU_STATUS,
  MenuStatusList,
  MenuTypeList,
  ROLE_MODULES,
  ROLE_SCOPES,
  ROUTE,
} from '../../../core/const';
import { FuseUltils } from '@fuse/ultils';
import { MenuManagementService } from './menu-management.service';
import { IMenuInfo, IMenuList } from './menu.types';
import { Utils } from '../../../core/utils/utils';
import { StoreManagementService } from '../store-management/store-management.service';
import { UserPermissionService } from '../../../core/user/user-permission.service';
import { IStore } from '../store-management/store.types';
import { FuseHelpLinkComponent } from '@fuse/components/help-link';

@Component({
  selector: 'app-menu-management',
  standalone: true,
  templateUrl: './menu-management.component.html',
  imports: [
    MatIconModule,
    ReactiveFormsModule,
    FuseTableComponent,
    FuseHelpLinkComponent,
  ],
  encapsulation: ViewEncapsulation.None,
})
export class MenuManagementComponent
  implements OnInit, OnDestroy, AfterViewInit
{
  queryParams!: any;

  dataSource: Array<any> = [];
  displayedColumns: any = [];
  total = 0;
  sortDefault: any = {
    field: 'name',
    direction: 'asc',
  };
  selectedMenus: Array<string> = [];

  searchForm: any;
  actions: Array<IAction> = [];
  bulkActions: Array<IAction> = [];

  optionListStore: Array<any> = [];

  permissionList = {
    create: false,
    update: false,
    delete: false,
  };
  storeParams: any = {
    filter: {},
    page: 0,
    size: 10,
  };
  storeAppend = false;
  displayedStores: Array<IStore> = [];

  private _unsubscribeAll: Subject<any> = new Subject<any>();

  /**
   * Constructor
   */
  constructor(
    private _router: Router,
    private _toast: ToastrService,
    private _activatedRoute: ActivatedRoute,
    private _menusService: MenuManagementService,
    private _storesService: StoreManagementService,
    private _changeDetectorRef: ChangeDetectorRef,
    private _confirmationService: FuseConfirmationService,
    private _userPermissionService: UserPermissionService,
  ) {
    this.getPermission();
    this.initMenusSubscription();
    this.searchForm = this.initSearchForm();
    this.handleGetStores();
  }

  ngOnInit(): void {
    this._activatedRoute.queryParams
      .pipe(
        takeUntil(this._unsubscribeAll),
        tap((queryParams) => {
          this.queryParams = queryParams;
        }),
      )
      .subscribe(() => {
        this.handleGetMenus();
      });
  }

  ngAfterViewInit(): void {
    this.initTableColumn();
    this.initTableAction();
    this._changeDetectorRef.detectChanges();
  }

  /**
   * On destroy
   */
  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this._unsubscribeAll.next(null);
    this._unsubscribeAll.complete();
  }

  handleLazyLoadSelect(event: any) {
    this.storeAppend = event.value.page > this.storeParams.page;
    this.storeParams.filter.name = event.value.search;
    this.storeParams.page = event.value.page;

    this.handleGetStores();
  }

  handleGetStores(): void {
    this._storesService
      .getStores(FuseUltils.objectToQueryString(this.storeParams))
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((res: any) => {
        const storesServer = res.content.map((item: any) => {
          return {
            ...item,
            label: item.name,
            value: item.storeId,
          };
        });

        this.optionListStore = this.storeAppend
          ? [...this.optionListStore, ...storesServer]
          : [{ label: 'All', value: '' }, ...storesServer];

        this.searchForm.basic[1].options = [...this.optionListStore];
        this.searchForm = { ...this.searchForm };

        this._changeDetectorRef.markForCheck();
      });
  }

  handleGetDisplayedStores(storeIds: Array<string>) {
    const params = {
      filter: {
        storeIds: storeIds,
      },
      page: 0,
      size: this.queryParams?.size || 10,
    };
    return this._storesService
      .getStores(FuseUltils.objectToQueryString(params))
      .pipe(
        takeUntil(this._unsubscribeAll),
        tap((res: any) => {
          this.displayedStores = [...res.content];
          if (this.displayedColumns) {
            const updatedColumns = [...this.displayedColumns];
            updatedColumns[2] = {
              key: 'storeId',
              name: 'Store',
              selected: true,
              render: (value: string) => {
                const store = this.displayedStores.find(
                  (item: any) => item.storeId == value,
                );

                return store ? store.name : `Unknown Store (${value})`;
              },
            };
            this.displayedColumns = [...updatedColumns];
            this._changeDetectorRef.detectChanges();
          }
        }),
      );
  }

  handleGetMenus(): void {
    const mappedData = this.getMenuParams(this.queryParams);
    this._menusService
      .getMenus(FuseUltils.objectToQueryString(mappedData))
      .subscribe();
  }

  getMenuParams(queryParams: any): any {
    const basicSearch = this.searchForm.basic;
    const filter = {
      name: queryParams['name'] ?? basicSearch[0].defaultValue,
      storeIds: queryParams['storeId'] ?? basicSearch[1].defaultValue,
      type: queryParams['type'] ?? basicSearch[2].defaultValue,
      statuses: queryParams['statuses'] ?? basicSearch[3].defaultValue,
    };

    const mappedData = {
      filter,
      size: queryParams['size'] || 10,
      page: queryParams['page'] || 0,
      sortDirection: queryParams['sortDirection'] || this.sortDefault.direction,
      sortFields: queryParams['sortFields'] || [this.sortDefault.field],
    };

    return mappedData;
  }

  gotoImportMenuItems(): void {
    this._router.navigate([`${ROUTE.MENU.MAIN}/${ROUTE.MENU.IMPORT.MAIN}`]);
  }

  handleAddNewMenu(): void {
    this._router.navigate([`${ROUTE.MENU.MAIN}/${ROUTE.MENU.ADD}`]);
  }

  handleViewMenuDetail(menu: any): void {
    this._router.navigate([
      `${ROUTE.MENU.MAIN}/${ROUTE.MENU.DETAIL}/${menu.id}`,
    ]);
  }

  handleEditMenu(menu: any): void {
    this._router.navigate([`${ROUTE.MENU.MAIN}/${ROUTE.MENU.EDIT}/${menu.id}`]);
  }

  onOpenActivateMenuDialog(element: any): void {
    const dialogRef = this._confirmationService.open({
      title: 'Activate Menu',
      message: 'Please confirm to activate this menu.',
      icon: {
        show: false,
      },
      actions: {
        confirm: {
          label: 'Activate',
        },
        cancel: {
          label: 'Close',
        },
      },
      dismissible: false,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        this.handleActivateMenu(element);
      }
    });
  }

  handleActivateMenu(menu: IMenuInfo): void {
    this._menusService.activateMenu(menu.id).subscribe(() => {
      this._toast.success('Activate menu success!');
      this.handleGetMenus();
    });
  }

  onOpenDeactivateMenuDialog(element: any): void {
    const dialogRef = this._confirmationService.open({
      title: 'Deactivate Menu',
      message: 'Please confirm to deactivate this menu.',
      icon: {
        show: false,
      },
      actions: {
        confirm: {
          label: 'Deactivate',
        },
        cancel: {
          label: 'Close',
        },
      },
      dismissible: false,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        this.handleDeactivateMenu(element);
      }
    });
  }

  handleDeactivateMenu(menu: IMenuInfo): void {
    this._menusService.deactivateMenu(menu.id).subscribe(() => {
      this._toast.success('Deactivate menu success!');
      this.handleGetMenus();
    });
  }

  onOpenDeleteMenuDialog(element: any): void {
    const dialogRef = this._confirmationService.open({
      title: 'Delete Menu',
      message: 'Please confirm to delete this menu.',
      icon: {
        show: false,
      },
      actions: {
        confirm: {
          label: 'Delete',
        },
        cancel: {
          label: 'Close',
        },
      },
      dismissible: false,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        this.handleDeleteMenu(element);
      }
    });
  }

  handleDeleteMenu(menu: IMenuInfo): void {
    this._menusService.deleteMenu(menu.id).subscribe(() => {
      this._toast.success('Delete menu success!');
      this.handleGetMenus();
    });
  }

  onChangeSelectedElement(event: Array<any>): void {
    this.selectedMenus = event.map((item) => item.id);
  }

  onOpenDeleteSelectedMenusDialog() {
    const dialogRef = this._confirmationService.open({
      title: 'Delete Menus',
      message: 'Please confirm to delete the selected menus.',
      icon: {
        show: false,
      },
      actions: {
        confirm: {
          label: 'Delete',
        },
        cancel: {
          label: 'Close',
        },
      },
      dismissible: false,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        this.handleDeleteSelectedMenus();
      }
    });
  }

  handleDeleteSelectedMenus(): void {
    forkJoin(
      this.selectedMenus.map((id: string) => this._menusService.deleteMenu(id)),
    ).subscribe({
      next: () => {
        this._toast.success('Selected menu(s) have been deleted successfully!');
        this.handleGetMenus();
      },
      error: () => {
        this.handleGetMenus();
      },
    });
  }

  private initMenusSubscription(): void {
    this._menusService.menus$
      .pipe(
        takeUntil(this._unsubscribeAll),
        tap((menus: IMenuList) => {
          this.dataSource = menus.content;
          this.total = menus.totalElements;
        }),
        switchMap((menus: IMenuList) => {
          const storeIds = [
            ...new Set(
              menus.content
                .filter((value: any) => value.storeId != null)
                .map((value) => value.storeId),
            ),
          ];
          return this.handleGetDisplayedStores(storeIds);
        }),
      )
      .subscribe();
  }

  /**
   * Initializes the table column configuration.
   */
  private initTableColumn(): void {
    this.displayedColumns = [
      {
        key: 'name',
        name: 'Menu Name',
        sort: true,
        selected: true,
        renderHtml: (value: string) => {
          return `<p class="font-semibold">${value}</p>`;
        },
      },
      {
        key: 'description',
        name: 'Description',
        selected: true,
      },
      {
        key: 'storeId',
        name: 'Store',
        selected: true,
        render: (value: string) => {
          return `Unknown Store (${value})`;
        },
      },
      {
        key: 'status',
        name: 'Status',
        selected: true,
        renderHtml: (value: string) => {
          const chipColor = Utils.getStatusColor(value);
          return `<mat-chip class="px-2 py-1 rounded-3 capitalize ${chipColor}">${value}</mat-chip>`;
        },
      },
      {
        key: 'type',
        name: 'Menu Type',
        selected: true,
      },
      {
        key: 'createdAt',
        name: 'Creation Date',
        selected: false,
        render: (date: number) => {
          return FuseUltils.tsToLocalTime(date, DATE_TIME_FORMAT.DATE_DEFAULT);
        },
      },
      {
        key: 'lastModifiedAt',
        name: 'Last Modified Date',
        selected: false,
        render: (date: number) => {
          return FuseUltils.tsToLocalTime(date, DATE_TIME_FORMAT.DATE_DEFAULT);
        },
      },
    ];
  }

  /**
   * Initializes the actions array for the table.
   *
   * @private
   * @returns {void}
   */
  private initTableAction(): void {
    this.actions = [
      {
        name: 'View',
        type: 'event',
        callback: (element) => this.handleViewMenuDetail(element),
      },
      {
        name: 'Edit',
        type: 'event',
        hidden: () => !this.permissionList.update,
        callback: (element) => this.handleEditMenu(element),
      },
      {
        name: 'Activate',
        type: 'event',
        hidden: (element) =>
          element.status != MENU_STATUS.INACTIVE || !this.permissionList.update,
        callback: (element) => this.onOpenActivateMenuDialog(element),
      },
      {
        name: 'Deactivate',
        type: 'event',
        hidden: (element) =>
          element.status != MENU_STATUS.ACTIVE || !this.permissionList.update,
        callback: (element) => this.onOpenDeactivateMenuDialog(element),
      },
      {
        name: 'Delete',
        type: 'event',
        hidden: () => !this.permissionList.delete,
        callback: (element) => this.onOpenDeleteMenuDialog(element),
      },
    ];

    this.bulkActions = [
      {
        name: 'Delete',
        type: 'event',
        hidden: () => !this.permissionList.delete,
        callback: () => this.onOpenDeleteSelectedMenusDialog(),
      },
    ];
  }

  /**
   * Initializes the search form configuration object.
   *
   * @returns {any} The search form configuration object.
   */
  private initSearchForm(): any {
    return {
      basic: [
        {
          label: 'Menu Name',
          name: 'name',
          placeholder: 'Enter name',
          type: 'text',
          defaultValue: '',
        },
        {
          label: 'Store',
          name: 'storeId',
          type: 'lazy-load-select',
          showSearch: true,
          defaultValue: '',
          options: [],
        },
        {
          label: 'Menu Type',
          name: 'type',
          type: 'select',
          defaultValue: '',
          options: [{ label: 'All', value: '' }, ...MenuTypeList],
        },
        {
          label: 'Status',
          name: 'statuses',
          type: 'select',
          defaultValue: [MENU_STATUS.ACTIVE, MENU_STATUS.INACTIVE],
          isMultiple: true,
          options: [...MenuStatusList],
        },
      ],
    };
  }

  private getPermission(): void {
    this._userPermissionService.permissions$
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe(() => {
        this.permissionList = {
          create: this._userPermissionService.hasPermissions([
            {
              module: ROLE_MODULES.MENU_MGMT,
              scope: ROLE_SCOPES.ADD,
            },
          ]),
          update: this._userPermissionService.hasPermissions([
            {
              module: ROLE_MODULES.MENU_MGMT,
              scope: ROLE_SCOPES.UPDATE,
            },
          ]),
          delete: this._userPermissionService.hasPermissions([
            {
              module: ROLE_MODULES.MENU_MGMT,
              scope: ROLE_SCOPES.DELETE,
            },
          ]),
        };
      });

    this.displayedColumns = [...this.displayedColumns];
  }
}
