import { ComponentFixture, TestBed } from '@angular/core/testing';

import { provideHttpClient } from '@angular/common/http';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { provideIcons } from 'apps/pacific-ii-cms/src/app/core/icons/icons.provider';
import { ToastrModule } from 'ngx-toastr';
import { of } from 'rxjs';
import { FuseUltils } from '../../../../../../../../libs/fuse/src/lib/ultils';
import { provideTranslate } from '../../../../core/transloco/transloco.provider';
import { CustomerManagementService } from '../../customer-management/customer-management.service';
import { ICustomer } from '../../customer-management/customer.types';
import { SourceOfFundService } from '../source-of-fund.service';
import {
  ICustomerBeneficiary,
  IDataResponse,
  ISourceOfFund,
} from '../source-of-fund.types';
import { AddCustomerBeneficiaryComponent } from './add-customer-beneficiary.component';

describe('AddCustomerBeneficiaryComponent', () => {
  let component: AddCustomerBeneficiaryComponent;
  let fixture: ComponentFixture<AddCustomerBeneficiaryComponent>;

  const mockSourceOfFund: ISourceOfFund = {
    fundSourceId: 'test',
    tenantId: '0',
    name: 'Test',
    sourceType: 'TEST',
    totalBalance: 10,
    status: 'ACTIVE',
    createdAt: 1736310924000,
    currency: {
      currencyCode: 'SGD',
      displayName: 'Singapore Dollar',
      fractionDigits: 2,
      numericCode: 702,
      symbol: 'SGD',
    },
    description: 'Test',
    topupFrequency: 'TEST',
    topupAmount: 10,
    topupDate: 1234,
    endTopupDate: 1234,
    fundExpiresOn: **********,
    fundExpiresInMs: **********000,
  };

  const mockCustomer: ICustomer = {
    id: '116052035555620864',
    ssoId: null,
    externalId: null,
    firstName: 'God',
    lastName: 'Father',
    email: '<EMAIL>',
    userType: 'CUSTOMER',
    realmId: '********-45f9-4cc2-9946-bc00abed6203',
    avatar: null,
    userStatus: 'CREATED',
    phoneNumber: null,
    totalSubAccounts: null,
    userGroup: null,
    userNonCompletedActions: ['KEYCLOAK_REGISTRATION'],
    permissions: [
      {
        tenantId: '116024918514279424',
        userId: '116052035555620864',
        userRoleId: '116024931125993472',
        permissionStatus: 'CREATED',
      },
    ],
  };

  const mockCustomerResponse: IDataResponse<ICustomer> = {
    content: [mockCustomer],
    totalElements: 1,
    totalPages: 1,
    page: 0,
    sort: [],
  };

  const mockCustomerBeneficiary: ICustomerBeneficiary = {
    beneficiaryId: '154859957574453248',
    customerId: '116052035555620864',
    fundSource: {
      fundSourceId: '154431511254901760',
      tenantId: '116024918514279424',
      name: 'Test',
      description: '********',
      status: 'ACTIVE',
      currency: {
        displayName: 'Singapore Dollar',
        numericCode: 702,
        currencyCode: 'SGD',
        symbol: 'SGD',
        fractionDigits: 2,
      },
      topupAmount: ********,
      topupDate: **********730,
      endTopupDate: **********730,
      topupFrequency: 'WEEKLY',
      fundExpiresOn: **********730,
      fundExpiresInMs: **********730000,
    },
    createdAt: 1736921491100,
  };

  const mockCustomerBeneficiaryResponse: IDataResponse<ICustomerBeneficiary> = {
    content: [mockCustomerBeneficiary],
    totalElements: 1,
    totalPages: 1,
    page: 0,
    sort: [],
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AddCustomerBeneficiaryComponent, ToastrModule.forRoot()],
      providers: [
        provideIcons(),
        provideTranslate(),
        provideAnimationsAsync(),
        provideHttpClient(),
        {
          provide: MatDialogRef,
          useValue: {
            close: jest.fn(),
          },
        },
        {
          provide: MAT_DIALOG_DATA,
          useValue: {
            fundSourceId: mockSourceOfFund.fundSourceId,
          },
        },
        {
          provide: CustomerManagementService,
          useValue: {
            getCustomers: jest.fn().mockReturnValue(of(mockCustomerResponse)),
          },
        },
        {
          provide: SourceOfFundService,
          useValue: {
            getAllCustomerBeneficiaries: jest
              .fn()
              .mockReturnValue(of(mockCustomerBeneficiaryResponse)),
            addCustomerBeneficiary: jest.fn().mockReturnValue(of(null)),
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(AddCustomerBeneficiaryComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Lifecycle Hook', () => {
    it('should call the getCustomerList when run ngOnInit', () => {
      jest.spyOn(component, 'getCustomerList');

      component.ngOnInit();

      expect(component.getCustomerList).toHaveBeenCalled();
    });

    it('should unsubscribe the all the observable when run the ngOnDestroy ', () => {
      const unsubscribeNextSpy = jest.spyOn(
        component['_unsubscribeAll'],
        'next',
      );
      const unsubscribeCompleteSpy = jest.spyOn(
        component['_unsubscribeAll'],
        'complete',
      );

      component.ngOnDestroy();

      expect(unsubscribeNextSpy).toHaveBeenCalled();
      expect(unsubscribeCompleteSpy).toHaveBeenCalled();
    });
  });

  describe('Data Fetching', () => {
    it('should send request to get the list of customer and replace the option with new data when run getCustomerList with customerAppend is false', () => {
      jest.spyOn(component['_changeDetectorRef'], 'detectChanges');

      const testData = [
        {
          ...mockCustomer,
          label: `${mockCustomer.firstName} ${mockCustomer.lastName}`,
          value: mockCustomer.id,
          hidden: false,
        },
      ];

      jest
        .spyOn(component, 'getCustomerBeneficiaries')
        .mockReturnValue(of(testData));

      component.getCustomerList();

      expect(component.customerOptions).toEqual(testData);
      expect(component.getCustomerBeneficiaries).toHaveBeenCalledWith([
        mockCustomer,
      ]);
      expect(component['_changeDetectorRef'].detectChanges).toHaveBeenCalled();
    });

    it('should send request to get the list of customer and append new data to the option when run getCustomerList with customerAppend is true', () => {
      jest.spyOn(component['_changeDetectorRef'], 'detectChanges');

      component.customerAppend = true;
      component.customerOptions = [
        {
          ...mockCustomer,
          label: `${mockCustomer.firstName} ${mockCustomer.lastName}`,
          value: mockCustomer.id,
          hidden: false,
        },
      ];
      const testData = [
        {
          ...mockCustomer,
          label: `${mockCustomer.firstName} ${mockCustomer.lastName}`,
          value: mockCustomer.id,
          hidden: false,
        },
      ];

      jest
        .spyOn(component, 'getCustomerBeneficiaries')
        .mockReturnValue(of(testData));

      component.getCustomerList();

      expect(component.customerOptions).toEqual([...testData, ...testData]);
      expect(component.getCustomerBeneficiaries).toHaveBeenCalledWith([
        mockCustomer,
      ]);
      expect(component['_changeDetectorRef'].detectChanges).toHaveBeenCalled();
    });

    it('should call getAllCustomerBeneficiaries with correct parameters and update beneficiaryOptions when run getCustomerBeneficiaries', (done) => {
      const params = {
        filter: {
          customerIds: [mockCustomer.id],
          fundSourceId: mockSourceOfFund.fundSourceId,
        },
        size: 1,
      };

      const testData = [
        {
          ...mockCustomer,
          value: mockCustomer.id,
          label: `${mockCustomer.firstName} ${mockCustomer.lastName}`,
          hidden: false,
        },
      ];

      jest
        .spyOn(component, 'hideExistingBeneficiaries')
        .mockReturnValue(testData);

      component.getCustomerBeneficiaries([mockCustomer]).subscribe((result) => {
        expect(
          component['_sourceOfFundService'].getAllCustomerBeneficiaries,
        ).toHaveBeenCalledWith(FuseUltils.objectToQueryString(params));
        expect(component.beneficiaryOptions).toEqual([mockCustomerBeneficiary]);
        expect(component.hideExistingBeneficiaries).toHaveBeenCalledWith(
          [mockCustomerBeneficiary],
          [mockCustomer],
        );
        expect(result).toEqual(testData);
        done();
      });
    });
  });

  describe('Utility Function', () => {
    it('should close the dialog when run onClose method', () => {
      component.onClose('test');

      expect(component['_dialogRef'].close).toHaveBeenCalledWith('test');
    });

    it('should update the customer params and call the getCustomerList method when run handleLazyLoad', () => {
      jest.spyOn(component, 'getCustomerList');
      const mockEvent = {
        search: 'test',
        page: 1,
      };

      component.handleLazyLoad(mockEvent);

      expect(component.customerAppend).toBe(true);
      expect(component.customerParams.filter.byName).toBe(mockEvent.search);
      expect(component.customerParams.page).toBe(mockEvent.page);
      expect(component.getCustomerList).toHaveBeenCalled();
    });

    it('should send request to add customer and show toast after success when run submitForm with valid value', () => {
      component.customerForm.patchValue({
        name: [mockCustomer.id, mockCustomer.id],
      });
      jest.spyOn(component, 'onClose');

      component.submitForm();

      expect(
        component['_sourceOfFundService'].addCustomerBeneficiary,
      ).toHaveBeenCalledTimes(1);
      expect(
        component['_sourceOfFundService'].addCustomerBeneficiary,
      ).toHaveBeenCalledWith({
        customerIds: [mockCustomer.id, mockCustomer.id],
        fundSourceId: mockSourceOfFund.fundSourceId,
      });
      expect(component.onClose).toHaveBeenCalledWith('Success');
    });

    it('should do nothing when run submitForm with invalid value', () => {
      component.customerForm.patchValue({
        name: null,
      });
      jest.spyOn(component, 'onClose');

      component.submitForm();

      expect(
        component['_sourceOfFundService'].addCustomerBeneficiary,
      ).not.toHaveBeenCalled();
      expect(
        component['_sourceOfFundService'].addCustomerBeneficiary,
      ).not.toHaveBeenCalled();
      expect(component.onClose).not.toHaveBeenCalledWith('Success');
    });

    it('should return customers with hidden flag based on existing beneficiaries when run hideExistingBeneficiaries', () => {
      const customers: ICustomer[] = [
        mockCustomer,
        { ...mockCustomer, id: 'Test123' },
      ];
      const beneficiaries: ICustomerBeneficiary[] = [
        { ...mockCustomerBeneficiary, customerId: 'Test123' },
      ];

      const result = component.hideExistingBeneficiaries(
        beneficiaries,
        customers,
      );

      expect(result).toEqual([
        {
          ...customers[0],
          value: mockCustomer.id,
          label: `${mockCustomer.firstName} ${mockCustomer.lastName}`,
          hidden: false,
        },
        {
          ...customers[1],
          value: 'Test123',
          label: `${mockCustomer.firstName} ${mockCustomer.lastName}`,
          hidden: true,
        },
      ]);
    });
  });
});
