import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable, ReplaySubject, tap } from 'rxjs';
import { API } from '../../../core/const';
import {
  ICustomerBeneficiary,
  IDataResponse,
  IScheduler,
  ISourceOfFund,
  ISourceOfFundAdded,
} from './source-of-fund.types';
import { DateTime } from 'luxon';
import { Currency } from '../tenant-management/tenant.types';
import { FuseUltils } from '../../../../../../../libs/fuse/src/lib/ultils';

@Injectable({
  providedIn: 'root',
})
export class SourceOfFundService {
  private _httpClient = inject(HttpClient);
  private _sourceOfFundResponse: ReplaySubject<IDataResponse<ISourceOfFund>> =
    new ReplaySubject(1);
  private _customerBeneficiaryResponse: ReplaySubject<any> = new ReplaySubject(
    1,
  );

  // -----------------------------------------------------------------------------------------------------
  // @ Accessors
  // -----------------------------------------------------------------------------------------------------

  /**
   * Getter for source of fund list
   */
  get sourceOfFund$() {
    return this._sourceOfFundResponse.asObservable();
  }

  /**
   * Getter for customer beneficiary list
   */
  get customerBeneficiary$() {
    return this._customerBeneficiaryResponse.asObservable();
  }

  // -----------------------------------------------------------------------------------------------------
  // @ Public Methods
  // -----------------------------------------------------------------------------------------------------

  /**
   * Get all source of fund data
   */
  getSourceOfFundList(
    queryParams: string,
  ): Observable<IDataResponse<ISourceOfFund>> {
    return this._httpClient
      .get<
        IDataResponse<ISourceOfFund>
      >(`${API.SOURCE_OF_FUND.LIST}?${queryParams}`)
      .pipe(
        tap((value) => {
          this._sourceOfFundResponse.next(value);
        }),
      );
  }

  /**
   * Get the detail of specific source of fund
   */
  getSourceOfFundDetail(id: string): Observable<ISourceOfFund> {
    return this._httpClient.get<ISourceOfFund>(
      API.SOURCE_OF_FUND.DETAIL.replace('{id}', id),
    );
  }

  /**
   * Create a new source of fund
   */
  createSourceOfFund(data: Partial<ISourceOfFundAdded>) {
    return this._httpClient.post(API.SOURCE_OF_FUND.CREATE, data);
  }

  /**
   * Update the detail data of specific source of fund
   */
  updateSourceOfFund(id: string, data: Partial<ISourceOfFund>) {
    return this._httpClient.put<ISourceOfFund>(
      API.SOURCE_OF_FUND.UPDATE.replace('{id}', id),
      data,
    );
  }

  /**
   * Update the status of specific source of fund
   */
  updateStatusSourceOfFund(id: string, action: 'block' | 'activate') {
    const apiUrl =
      action === 'block'
        ? API.SOURCE_OF_FUND.BLOCK
        : API.SOURCE_OF_FUND.ACTIVATE;

    return this._httpClient.post<ISourceOfFund>(apiUrl.replace('{id}', id), {});
  }

  /**
   * Delete the specific source of fund
   */
  deleteSourceOfFund(params: Array<string>) {
    return this._httpClient.delete<ISourceOfFund>(API.SOURCE_OF_FUND.DELETE, {
      body: {
        ids: params,
      },
    });
  }

  /**
   * Get all the customer beneficiary
   */
  getAllCustomerBeneficiaries(
    queryParams: string,
  ): Observable<IDataResponse<ICustomerBeneficiary>> {
    return this._httpClient
      .get<
        IDataResponse<ICustomerBeneficiary>
      >(`${API.SOURCE_OF_FUND.BENEFICIARY_LIST}?${queryParams}`)
      .pipe(
        tap((value) => {
          this._customerBeneficiaryResponse.next(value);
        }),
      );
  }

  /**
   * Add new customer to beneficiary list
   */
  removeCustomerBeneficiary(data: {
    fundSourceId: string;
    customerIds?: Array<string>;
  }) {
    return this._httpClient.delete<ICustomerBeneficiary>(
      API.SOURCE_OF_FUND.BENEFICIARY_DELETE,
      {
        body: data,
      },
    );
  }

  /**
   * Add new customer to beneficiary list
   */
  addCustomerBeneficiary(data: {
    customerIds: Array<string>;
    fundSourceId: string;
  }) {
    return this._httpClient.post(API.SOURCE_OF_FUND.BENEFICIARY_ADD, data);
  }

  /**
   * Get the list of top-up scheduler of specific source of fund
   */
  getTopUpSchedulerList(fundSourceId: string): Observable<Array<IScheduler>> {
    return this._httpClient.get<Array<IScheduler>>(
      `${API.SOURCE_OF_FUND.TOP_UP_LIST}?fundSourceId=${fundSourceId}`,
    );
  }

  /**
   * Add new top-up scheduler to source of fund
   */
  addTopUpScheduler(data: any) {
    return this._httpClient.post(API.SOURCE_OF_FUND.TOP_UP_ADD, data);
  }

  /**
   * Update the scheduler of source of fund
   */
  updateTopUpScheduler(id: string, data: any) {
    return this._httpClient.put(
      API.SOURCE_OF_FUND.TOP_UP_UPDATE.replace('{id}', id),
      data,
    );
  }

  /**
   * Remove the scheduler out of source of fund
   */
  removeTopUpScheduler(ids: Array<string>) {
    return this._httpClient.delete(API.SOURCE_OF_FUND.TOP_UP_DELETE, {
      body: {
        ids,
      },
    });
  }

  /**
   * Format fund data
   */
  formatFundData(
    sourceFormValue: any,
    expireFormValue: any,
    currency: Currency,
  ): any {
    const { expiredMode, ...formValue } = sourceFormValue;
    const expiredValue: any = {};

    if (expiredMode === 'amount') {
      expiredValue['fundExpiresInMs'] =
        Number(expireFormValue.fundExpiresIn) * 3600 * 24 * 1000;
    } else if (expiredMode === 'time') {
      expiredValue['fundExpiresOn'] = DateTime.fromMillis(
        expireFormValue.fundExpiresOn,
      )
        .endOf('day')
        .toMillis();
    }

    return {
      ...formValue,
      ...expiredValue,
      currency: currency.currencyCode,
      ...(formValue?.topupAmount && {
        topupAmount: FuseUltils.truncateDecimalCurrency(
          Number(formValue.topupAmount),
          currency.fractionDigits,
        ),
      }),
    };
  }
}
