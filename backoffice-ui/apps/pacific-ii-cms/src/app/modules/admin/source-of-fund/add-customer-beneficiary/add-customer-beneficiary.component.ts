import { NgClass } from '@angular/common';
import {
  ChangeDetectorRef,
  Component,
  Inject,
  OnDestroy,
  OnInit,
} from '@angular/core';
import {
  FormBuilder,
  ReactiveFormsModule,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatSpinner } from '@angular/material/progress-spinner';
import { FuseLazyLoadSelectComponent } from '@fuse/components/lazy-load-select/lazy-load-select.component';
import { FuseUltils } from '@fuse/ultils';
import { Utils } from 'apps/pacific-ii-cms/src/app/core/utils/utils';
import { ToastrService } from 'ngx-toastr';
import { map, Subject, switchMap, takeUntil, tap } from 'rxjs';
import { CustomerManagementService } from '../../customer-management/customer-management.service';
import { ICustomer } from '../../customer-management/customer.types';
import { SourceOfFundService } from '../source-of-fund.service';
import { ICustomerBeneficiary } from '../source-of-fund.types';

interface ICustomerOption extends ICustomer {
  value: string;
  label: string;
  hidden?: boolean;
}

@Component({
  selector: 'app-add-customer-beneficiary',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    MatIconModule,
    FuseLazyLoadSelectComponent,
    NgClass,
    MatSpinner,
    MatButtonModule,
  ],
  templateUrl: './add-customer-beneficiary.component.html',
})
export class AddCustomerBeneficiaryComponent implements OnInit, OnDestroy {
  customerForm!: UntypedFormGroup;
  loading = false;

  errorMessages = {
    name: {
      required: 'You must choose at least 1 customer!',
    },
  };

  customerParams = {
    filter: {
      byName: '',
    },
    size: 10,
    page: 0,
  };
  customerAppend = false;
  customerOptions: Array<ICustomerOption> = [];

  beneficiaryOptions: Array<ICustomerBeneficiary> = [];

  selectedNames: any = [];

  private _unsubscribeAll: Subject<any> = new Subject();

  constructor(
    private _dialogRef: MatDialogRef<any>,
    private _formBuilder: FormBuilder,
    private _customerManagementService: CustomerManagementService,
    private _changeDetectorRef: ChangeDetectorRef,
    private _sourceOfFundService: SourceOfFundService,
    private _toast: ToastrService,
    @Inject(MAT_DIALOG_DATA) private data: any,
  ) {
    this.initCustomerForm();
  }

  ngOnInit(): void {
    this.getCustomerList();
  }

  ngOnDestroy(): void {
    this._unsubscribeAll.next(null);
    this._unsubscribeAll.complete();
  }

  submitForm() {
    for (const key in this.customerForm.controls) {
      this.customerForm.controls[key].markAsTouched();
      this.customerForm.controls[key].markAsDirty();
      this.customerForm.controls[key].updateValueAndValidity();
    }

    if (this.customerForm.invalid) {
      Utils.scrollToInvalid();
      return;
    }

    const customerList = this.customerForm.value.name;

    this.loading = true;

    this._sourceOfFundService
      .addCustomerBeneficiary({
        customerIds: [...customerList],
        fundSourceId: this.data.fundSourceId,
      })
      .subscribe({
        next: () => {
          this.loading = false;
          this._toast.success(
            'You have successfully added customer beneficiaries to this source of fund!',
          );
          this.onClose('Success');
        },
        error: () => {
          this.loading = false;
        },
      });
  }

  onClose(message?: string) {
    this._dialogRef.close(message || null);
  }

  handleLazyLoad(event: any) {
    this.customerAppend = event.page > this.customerParams.page;
    this.customerParams.filter.byName = event.search;
    this.customerParams.page = event.page;

    this.getCustomerList();
  }

  getCustomerList() {
    this._customerManagementService
      .getCustomers(FuseUltils.objectToQueryString(this.customerParams))
      .pipe(
        takeUntil(this._unsubscribeAll),
        switchMap((value) => {
          return this.getCustomerBeneficiaries(value.content);
        }),
      )
      .subscribe((options) => {
        this.customerOptions = this.customerAppend
          ? [...this.customerOptions, ...options]
          : [...options];

        this._changeDetectorRef.detectChanges();
      });
  }

  getCustomerBeneficiaries(customers: Array<ICustomer>) {
    const customerIds = customers.map((data: ICustomer) => data.id);
    const params = {
      filter: {
        customerIds,
        fundSourceId: this.data.fundSourceId,
      },
      size: customerIds.length,
    };

    return this._sourceOfFundService
      .getAllCustomerBeneficiaries(FuseUltils.objectToQueryString(params))
      .pipe(
        takeUntil(this._unsubscribeAll),
        tap((value) => {
          this.beneficiaryOptions = [...value.content];
        }),
        map((value) => {
          return this.hideExistingBeneficiaries(value.content, customers);
        }),
      );
  }

  hideExistingBeneficiaries(
    beneficiaries: Array<ICustomerBeneficiary>,
    customers: Array<ICustomer>,
  ) {
    return customers.map((value) => {
      return {
        ...value,
        value: value.id,
        label: `${value.firstName} ${value.lastName}`,
        hidden: beneficiaries.some((data) => data.customerId === value.id),
      };
    });
  }

  private initCustomerForm() {
    this.customerForm = this._formBuilder.group({
      name: [null, [Validators.required]],
    });

    this.customerForm.controls['name'].valueChanges.subscribe((value) => {
      this.selectedNames = this.customerOptions.filter((data) => {
        return value.includes(data.id);
      });
    });
  }
}
