import { ComponentFixture, TestBed } from '@angular/core/testing';

import { AddGroupBeneficiaryComponent } from './add-group-beneficiary.component';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { provideIcons } from 'apps/pacific-ii-cms/src/app/core/icons/icons.provider';
import { provideHttpClient } from '@angular/common/http';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { CustomerManagementService } from '../../customer-management/customer-management.service';
import { of } from 'rxjs';
import { SourceOfFundService } from '../source-of-fund.service';
import { GroupManagementService } from '../../group-management/group-management.service';
import { IDataResponse } from '../source-of-fund.types';
import {
  ICustomer,
  IUserGroup,
} from '../../customer-management/customer.types';
import { ToastrModule } from 'ngx-toastr';
import { provideTranslate } from '../../../../core/transloco/transloco.provider';

describe('AddGroupBeneficiaryComponent', () => {
  let component: AddGroupBeneficiaryComponent;
  let fixture: ComponentFixture<AddGroupBeneficiaryComponent>;

  const fundSourceId = 'test';

  const mockCustomer: ICustomer = {
    id: '116052035555620864',
    ssoId: null,
    externalId: null,
    firstName: 'God',
    lastName: 'Father',
    email: '<EMAIL>',
    userType: 'CUSTOMER',
    realmId: '********-45f9-4cc2-9946-bc00abed6203',
    avatar: null,
    userStatus: 'CREATED',
    phoneNumber: null,
    totalSubAccounts: null,
    userGroup: null,
    userNonCompletedActions: ['KEYCLOAK_REGISTRATION'],
    permissions: [
      {
        tenantId: '116024918514279424',
        userId: '116052035555620864',
        userRoleId: '116024931125993472',
        permissionStatus: 'CREATED',
      },
    ],
  };

  const mockCustomerResponse: IDataResponse<ICustomer> = {
    content: [mockCustomer],
    totalElements: 1,
    totalPages: 1,
    page: 0,
    sort: [],
  };

  const mockGroup: IUserGroup = {
    id: '116041875361691648',
    tenantId: '116024918514279424',
    path: '116041875361691648',
    groupName: 'Primary',
    groupKey: undefined,
    parent: null,
    isDefaultGroup: false,
    description: null,
    createdAt: '*************',
    updatedAt: '*************',
  };

  const mockGroupResponse: IDataResponse<IUserGroup> = {
    content: [mockGroup],
    totalElements: 1,
    totalPages: 1,
    page: 0,
    sort: [],
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AddGroupBeneficiaryComponent, ToastrModule.forRoot()],
      providers: [
        provideAnimationsAsync(),
        provideIcons(),
        provideTranslate(),
        provideHttpClient(),
        {
          provide: MatDialogRef,
          useValue: {
            close: jest.fn(),
          },
        },
        {
          provide: MAT_DIALOG_DATA,
          useValue: {
            fundSourceId: fundSourceId,
          },
        },
        {
          provide: CustomerManagementService,
          useValue: {
            getCustomers: jest.fn().mockImplementation((value: any) => {
              if (value.includes('page=2')) {
                return of({ content: [] });
              }

              return of(mockCustomerResponse);
            }),
          },
        },
        {
          provide: GroupManagementService,
          useValue: {
            getGroups: jest.fn().mockReturnValue(of(mockGroupResponse)),
          },
        },
        {
          provide: SourceOfFundService,
          useValue: {
            addCustomerBeneficiary: jest.fn().mockReturnValue(of(null)),
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(AddGroupBeneficiaryComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    jest.spyOn(component['_toast'], 'error');
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Lifecycle Hook', () => {
    it('should call the getGroupList when run ngOnInit', () => {
      jest.spyOn(component, 'getGroupList');

      component.ngOnInit();

      expect(component.getGroupList).toHaveBeenCalled();
    });

    it('should unsubscribe the all the observable when run the ngOnDestroy ', () => {
      const unsubscribeNextSpy = jest.spyOn(
        component['_unsubscribeAll'],
        'next',
      );
      const unsubscribeCompleteSpy = jest.spyOn(
        component['_unsubscribeAll'],
        'complete',
      );

      component.ngOnDestroy();

      expect(unsubscribeNextSpy).toHaveBeenCalled();
      expect(unsubscribeCompleteSpy).toHaveBeenCalled();
    });
  });

  describe('Data Fetching', () => {
    it('should send request to get the list of group and replace the option with new data when run getGroupList with groupAppend is false', () => {
      jest.spyOn(component['_changeDetectorRef'], 'detectChanges');
      const testData = [
        {
          ...mockGroup,
          label: mockGroup.groupName,
          value: mockGroup.path,
        },
      ];

      component.getGroupList();

      expect(component.groupOptions).toEqual(testData);
      expect(component['_changeDetectorRef'].detectChanges).toHaveBeenCalled();
    });

    it('should send request to get the list of group and append new data to the option when run getGroupList with groupAppend is true', () => {
      jest.spyOn(component['_changeDetectorRef'], 'detectChanges');
      component.groupAppend = true;
      component.groupOptions = [
        {
          ...mockGroup,
          label: mockGroup.groupName,
          value: mockGroup.path,
        },
      ];
      const testData = [
        {
          ...mockGroup,
          label: mockGroup.groupName,
          value: mockGroup.path,
        },
        {
          ...mockGroup,
          label: mockGroup.groupName,
          value: mockGroup.path,
        },
      ];

      component.getGroupList();

      expect(component.groupOptions).toEqual(testData);
      expect(component['_changeDetectorRef'].detectChanges).toHaveBeenCalled();
    });
  });

  describe('Utility Function', () => {
    it('should close the dialog when run onClose method', () => {
      component.onClose();

      expect(component['_dialogRef'].close).toHaveBeenCalled();
    });

    it('should update the group params and call the getGroupList method when run handleLazyLoad', () => {
      jest.spyOn(component, 'getGroupList');
      const mockEvent = {
        search: 'test',
        page: 1,
      };

      component.handleLazyLoad(mockEvent);

      expect(component.groupAppend).toBe(true);
      expect(component.groupParams.filter.byGroupName).toBe(mockEvent.search);
      expect(component.groupParams.page).toBe(mockEvent.page);
      expect(component.getGroupList).toHaveBeenCalled();
    });

    it('should subscribe to the method addCustomerToSourceOfFund when run submitForm with valid data', () => {
      jest.spyOn(component, 'addCustomerToSourceOfFund');
      component.groupForm.patchValue({ name: mockGroup.path });

      component.submitForm();

      expect(component.addCustomerToSourceOfFund).toHaveBeenCalled();
    });

    it('should not subscribe to the method addCustomerToSourceOfFund when run submitForm with invalid data', () => {
      jest.spyOn(component, 'addCustomerToSourceOfFund');

      component.submitForm();

      expect(component.addCustomerToSourceOfFund).not.toHaveBeenCalled();
    });

    it('should send request to server to get the customer from group and add it to group', (done) => {
      jest.spyOn(component['_sourceOfFundService'], 'addCustomerBeneficiary');
      jest.spyOn(component, 'addCustomerToSourceOfFund');
      component.groupOptions = [
        { ...mockGroup, value: mockGroup.id, label: mockGroup.groupName },
      ];

      component.addCustomerToSourceOfFund(mockGroup.id).subscribe(() => {
        done();
      });

      expect(
        component['_sourceOfFundService'].addCustomerBeneficiary,
      ).toHaveBeenCalledWith({
        customerIds: [mockCustomer.id],
        fundSourceId: fundSourceId,
      });
      expect(component.addCustomerToSourceOfFund).toHaveBeenCalledWith(
        mockGroup.id,
        1,
      );
      expect(component.addSuccess).toBe(true);
      expect(component.progressView).toBe(true);
    });

    it('should send request to server to show toast error when run addCustomerToSourceOfFund but there is no customer in group', (done) => {
      jest.spyOn(component['_sourceOfFundService'], 'addCustomerBeneficiary');
      jest.spyOn(component, 'addCustomerToSourceOfFund');
      jest
        .spyOn(component['_customerManagementService'], 'getCustomers')
        .mockImplementation(() => of({ content: [] }));

      component.addCustomerToSourceOfFund(mockGroup.id).subscribe(() => {
        done();
      });

      expect(
        component['_sourceOfFundService'].addCustomerBeneficiary,
      ).not.toHaveBeenCalled();
      expect(component.addCustomerToSourceOfFund).not.toHaveBeenCalledWith(
        mockGroup.id,
        1,
      );
      expect(component.progressView).toBe(false);
      expect(component['_toast'].error).toHaveBeenCalledWith(
        'There is no customer in this group. Please choose another group!',
      );
    });
  });
});
