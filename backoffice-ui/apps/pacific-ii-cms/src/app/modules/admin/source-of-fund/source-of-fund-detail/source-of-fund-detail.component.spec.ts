import { ComponentFixture, TestBed } from '@angular/core/testing';

import { SourceOfFundDetailComponent } from './source-of-fund-detail.component';
import { provideIcons } from 'apps/pacific-ii-cms/src/app/core/icons/icons.provider';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { provideHttpClient } from '@angular/common/http';
import { ActivatedRoute } from '@angular/router';
import { of } from 'rxjs';
import { ISourceOfFund } from '../source-of-fund.types';
import {
  ROLE_MODULES,
  ROLE_SCOPES,
  ROUTE,
} from 'apps/pacific-ii-cms/src/app/core/const';
import { SourceOfFundService } from '../source-of-fund.service';
import { Utils } from 'apps/pacific-ii-cms/src/app/core/utils/utils';
import { ToastrModule } from 'ngx-toastr';
import { StorageService } from 'apps/pacific-ii-cms/src/app/core/services/storage.service';
import { Currency } from '../../tenant-management/tenant.types';
import { provideTranslate } from '../../../../core/transloco/transloco.provider';

describe('SourceOfFundDetailComponent', () => {
  let component: SourceOfFundDetailComponent;
  let fixture: ComponentFixture<SourceOfFundDetailComponent>;

  const mockPermissionList = [
    {
      externalId: `${ROLE_MODULES.SOURCE_OF_FUND_MGMT}_${ROLE_SCOPES.UPDATE}`,
    },
  ];

  const mockSourceOfFund: ISourceOfFund = {
    fundSourceId: 'test',
    tenantId: '0',
    name: 'Test',
    sourceType: 'TEST',
    totalBalance: 10,
    status: 'ACTIVE',
    createdAt: 1736310924000,
    currency: {
      currencyCode: 'SGD',
      displayName: 'Singapore Dollar',
      fractionDigits: 2,
      numericCode: 702,
      symbol: 'SGD',
    },
    description: 'Test',
    topupFrequency: 'DAILY',
    topupAmount: 10,
    topupDate: 1234,
    endTopupDate: 1234,
    fundExpiresOn: **********,
    fundExpiresInMs: **********000,
  };

  const mockCurrency: Currency = {
    displayName: 'Singapore Dollar',
    numericCode: 702,
    currencyCode: 'SGD',
    symbol: 'SGD',
    fractionDigits: 2,
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SourceOfFundDetailComponent, ToastrModule.forRoot()],
      providers: [
        provideIcons(),
        provideTranslate(),
        provideAnimationsAsync(),
        provideHttpClient(),
        {
          provide: ActivatedRoute,
          useValue: {
            paramMap: of({
              params: {
                id: mockSourceOfFund.fundSourceId,
              },
            }),
            queryParams: of(null),
          },
        },
        {
          provide: SourceOfFundService,
          useValue: {
            getSourceOfFundDetail: jest
              .fn()
              .mockReturnValue(of(mockSourceOfFund)),
          },
        },
        {
          provide: StorageService,
          useValue: {
            getTenantCurrency: () => mockCurrency,
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(SourceOfFundDetailComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    component['_userPermissionService']['_permissions'].next(
      mockPermissionList,
    );
    component['_userPermissionService'].permissionList = [
      ...mockPermissionList,
    ];

    jest.spyOn(component['_router'], 'navigate');
  });

  it('should create', () => {
    expect(component).toBeTruthy();
    expect(component.id).toBe(mockSourceOfFund.fundSourceId);
  });

  describe('Lifecycle Hook', () => {
    it('should call the handleGetSourceOfFundDetail when run ngOnInit', () => {
      jest.spyOn(component, 'handleGetSourceOfFundDetail');

      component.ngOnInit();

      expect(component.handleGetSourceOfFundDetail).toHaveBeenCalled();
    });

    it('should unsubscribe the all the observable when run the ngOnDestroy ', () => {
      const unsubscribeNextSpy = jest.spyOn(
        component['_unsubscribeAll'],
        'next',
      );
      const unsubscribeCompleteSpy = jest.spyOn(
        component['_unsubscribeAll'],
        'complete',
      );

      component.ngOnDestroy();

      expect(unsubscribeNextSpy).toHaveBeenCalled();
      expect(unsubscribeCompleteSpy).toHaveBeenCalled();
    });
  });

  describe('Data Fetching', () => {
    it('should get source of fund detail, create the chip element based on it status and detect change when run handleGetSourceOfFundDetail', () => {
      jest.spyOn(component['_changeDetectorRef'], 'detectChanges');
      const checkData = { ...mockSourceOfFund, topupFrequency: 'Daily' };

      component.handleGetSourceOfFundDetail();

      expect(
        component['_sourceOfFundService'].getSourceOfFundDetail,
      ).toHaveBeenCalledWith(mockSourceOfFund.fundSourceId);
      expect(component.sourceOfFundDetail).toEqual(checkData);
      expect(component['_changeDetectorRef'].detectChanges).toHaveBeenCalled();
      expect(component.chipElement).toBe(
        `<mat-chip class="px-2 py-1 rounded-3 capitalize ${Utils.getStatusColor(mockSourceOfFund.status)}">${mockSourceOfFund.status}</mat-chip>`,
      );
    });

    it('should get the permission to authorize the action when run the getPermissions method', () => {
      expect(component.permissionList).toEqual({
        update: false,
      });

      component['getPermissions']();

      expect(component.permissionList).toEqual({
        update: true,
      });
    });
  });

  describe('Utility Function', () => {
    it('should navigate to edit page when run navigateToEditPage', () => {
      component.navigateToEditPage();

      expect(component['_router'].navigate).toHaveBeenCalledWith([
        `${ROUTE.SOURCE_OF_FUND.MAIN}/${ROUTE.SOURCE_OF_FUND.EDIT}/${component.id}`,
      ]);
    });

    it('should navigate to management page when run navigateToMain', () => {
      component.navigateToMain();

      expect(component['_router'].navigate).toHaveBeenCalledWith([
        ROUTE.SOURCE_OF_FUND.MAIN,
      ]);
    });
  });
});
