<div class="parent-page">
  <div class="parent-page__header">
    <div>Data Sync Monitor</div>
  </div>

  <div class="parent-page__body flex flex-col gap-4">
    <!-- <div>
      <button
        class="font-bold text-primary"
        (click)="handleDownloadSchoolSyncSample()"
      >
        Download Sample File
      </button>
    </div> -->

    <div class="parent-page__body__table">
      <fuse-table-component
        [dataSource]="dataSource"
        [displayedColumns]="displayedColumns"
        [actions]="actions"
        [total]="total"
        [hideColBtn]="true"
        [sortDefault]="sortDefault"
        [heightClass]="'h-[calc(100vh-425px)]'"
        [rowClick]="navigateToDetail"
        [template]="template"
      >
        <ng-template #template let-column="header" let-element="data">
          @if (column.key === 'validChecksum') {
            <div class="flex justify-center">
              <mat-icon
                matPrefix
                [svgIcon]="
                  element[column.key]
                    ? 'heroicons_outline:check-circle'
                    : 'heroicons_outline:x-circle'
                "
                class="w-8 h-8 cursor-pointer"
                [class.text-error-main]="!element[column.key]"
                [class.text-success-main]="element[column.key]"
              ></mat-icon>
            </div>
          }
        </ng-template>
      </fuse-table-component>
    </div>
  </div>
</div>
