import { Routes } from '@angular/router';
import { ROUTE } from '../../../core/const';
import { SchoolSyncMonitorComponent } from './school-sync-monitor.component';
import { SchoolSyncDetailComponent } from './school-sync-detail/school-sync-detail.component';

export default [
  {
    path: ROUTE.DATA_SYNC.LIST,
    component: SchoolSyncMonitorComponent,
  },
  {
    path: ROUTE.DATA_SYNC.DETAIL + '/:id',
    component: SchoolSyncDetailComponent,
  },
  { path: '', pathMatch: 'full', redirectTo: ROUTE.DATA_SYNC.LIST },
] as Routes;
