import { AuthorizeMenuBoardComponent } from './authorize-menu-board.component';
import { ComponentFixture, TestBed } from '@angular/core/testing';

import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { provideHttpClient } from '@angular/common/http';
import { ToastrModule } from 'ngx-toastr';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { IMenuBoard } from '../menu-board.types';
import {
  API,
  MENU_BOARD_DISPLAY_MODE,
} from 'apps/pacific-ii-cms/src/app/core/const';
import { provideIcons } from 'apps/pacific-ii-cms/src/app/core/icons/icons.provider';
import { provideTranslate } from '../../../../core/transloco/transloco.provider';

describe('AuthorizeMenuBoardComponent', () => {
  let component: AuthorizeMenuBoardComponent;
  let fixture: ComponentFixture<AuthorizeMenuBoardComponent>;
  let httpTestingController: HttpTestingController;

  const mockMenuBoard: IMenuBoard = {
    id: 'test1',
    name: 'Menu Board 1',
    displayMode: MENU_BOARD_DISPLAY_MODE.SINGLE,
    status: 'CREATED',
    createdAt: 1729340726348,
    activatedAt: 1729340726348,
    updatedAt: 1729340726348,
    onboard: 'test1',
    tenantId: '1234',
    images: [],
    delayBetweenImages: 60,
  };

  const mockCode = '123456';

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AuthorizeMenuBoardComponent, ToastrModule.forRoot()],
      providers: [
        provideIcons(),
        provideTranslate(),
        provideHttpClient(),
        provideHttpClientTesting(),
        provideAnimationsAsync(),
        {
          provide: MatDialogRef,
          useValue: {
            close: jest.fn(),
          },
        },
        {
          provide: MAT_DIALOG_DATA,
          useValue: {
            id: mockMenuBoard.id,
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(AuthorizeMenuBoardComponent);
    component = fixture.componentInstance;

    httpTestingController = TestBed.inject(HttpTestingController);

    jest.spyOn(component['_toast'], 'success');
    jest.spyOn(component, 'onClose');

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should send request to authorize the Menu Board, then show toast and close the dialog after success when run the submitForm method with valid form value ', () => {
    component.authorizeForm.patchValue({
      code: mockCode,
    });

    component.submitForm();

    expect(component.authorizeForm.invalid).toBe(false);
    expect(component.loading).toBe(true);

    const req = httpTestingController.expectOne(API.MENU_BOARD.AUTHORIZE);
    expect(req.request.body).toEqual({
      menuBoardId: mockMenuBoard.id,
      code: mockCode,
    });
    req.flush(null);

    expect(component.loading).toBe(false);
    expect(component['_toast'].success).toHaveBeenCalledWith(
      'You have authorized the menu board successfully!',
    );
    expect(component.onClose).toHaveBeenCalledWith(mockMenuBoard.id);
  });

  it('should send request to authorize the Menu Board, then close the dialog after error when run the submitForm method with valid form value ', () => {
    component.authorizeForm.patchValue({
      code: mockCode,
    });

    component.submitForm();

    expect(component.authorizeForm.invalid).toBe(false);
    expect(component.loading).toBe(true);

    const req = httpTestingController.expectOne(API.MENU_BOARD.AUTHORIZE);
    expect(req.request.body).toEqual({
      menuBoardId: mockMenuBoard.id,
      code: mockCode,
    });
    req.error(new ProgressEvent(''));

    expect(component.loading).toBe(false);
    expect(component['_toast'].success).not.toHaveBeenCalledWith(
      'You have authorized the menu board successfully!',
    );
    expect(component.onClose).toHaveBeenCalledWith(mockMenuBoard.id);
  });

  it('should not send request to authorize the Menu Board when run the submitForm method with invalid form value ', () => {
    component.authorizeForm.patchValue({
      code: null,
    });

    component.submitForm();

    expect(component.authorizeForm.invalid).toBe(true);
    expect(component.loading).toBe(false);

    httpTestingController.expectNone(API.MENU_BOARD.AUTHORIZE);
    expect(component['_toast'].success).not.toHaveBeenCalledWith(
      'You have authorized the menu board successfully!',
    );
    expect(component.onClose).not.toHaveBeenCalledWith(mockMenuBoard.id);
  });

  it('should close the dialog when run the onClose method', () => {
    component.onClose(mockMenuBoard.id);

    expect(component['_dialogRef'].close).toHaveBeenCalledWith(
      mockMenuBoard.id,
    );
  });
});
