import { provideHttpClient } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { ActivatedRoute } from '@angular/router';
import { provideIcons } from 'apps/pacific-ii-cms/src/app/core/icons/icons.provider';
import { ToastrModule } from 'ngx-toastr';
import { of, ReplaySubject } from 'rxjs';
import { UserManagementService } from '../../user-management/user-management.service';
import { ICustomer } from '../customer.types';
import { EditCustomerComponent } from './edit-customer.component';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog } from '@angular/material/dialog';
import { UploadService } from 'apps/pacific-ii-cms/src/app/services/upload.service';
import { UtilityService } from 'apps/pacific-ii-cms/src/app/services/utility.service';
import { CustomerManagementService } from '../customer-management.service';
import { GroupManagementService } from '../../group-management/group-management.service';
import { provideTranslate } from '../../../../core/transloco/transloco.provider';

describe('EditCustomerComponent', () => {
  let component: EditCustomerComponent;
  let fixture: ComponentFixture<EditCustomerComponent>;

  const mockTenantId = '135261622300581888';

  const mockCustomer: ICustomer = {
    id: '125157569389419520',
    ssoId: null,
    externalId: null,
    uniqueExternalId: null,
    firstName: 'aa',
    lastName: 'aaa',
    email: '',
    userType: 'CUSTOMER',
    realmId: '********-45f9-4cc2-9946-bc00abed6203',
    avatar: null,
    userStatus: 'ACTIVE',
    phoneNumber: null,
    totalSubAccounts: null,
    totalSponsors: null,
    userGroup: {
      id: '121520479628366848',
      tenantId: '116024918514279424',
      path: '121520479628366848',
      groupName: 'Default',
      groupKey: 'DEFAULT_GROUP',
      parent: null,
      isDefaultGroup: true,
      description:
        'By default, all customer of tenant would be added into this group. ',
      createdAt: null,
      updatedAt: null,
    },
    userNonCompletedActions: ['KEYCLOAK_REGISTRATION'],
    permissions: [
      {
        tenantId: '116024918514279424',
        userId: '125157569389419520',
        userRoleId: '125157222424000514',
        permissionStatus: 'ACTIVE',
      },
    ],
    createdAt: *************,
    updatedAt: *************,
  };

  const mockQueryParams = {
    tab: 'card',
  };

  const mockPresignedUrl = '';

  const mockGroups = {
    content: [
      {
        id: '116041875361691648',
        tenantId: '116024918514279424',
        path: '116041875361691648',
        groupName: 'Primary',
        groupKey: null,
        parent: null,
        isDefaultGroup: false,
        description: null,
        createdAt: 1727666539058,
        updatedAt: 1727666539058,
      },
      {
        id: '116041925341018112',
        tenantId: '116024918514279424',
        path: '116041925341018112',
        groupName: 'Secondary',
        groupKey: null,
        parent: null,
        isDefaultGroup: false,
        description: null,
        createdAt: 1727666550959,
        updatedAt: 1727666550959,
      },
    ],
  };

  const mockGroupsOptions: Array<any> = mockGroups.content.map((value) => ({
    ...value,
    value: value.id,
    label: value.groupName,
  }));

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [EditCustomerComponent, ToastrModule.forRoot()],
      providers: [
        provideIcons(),
        provideTranslate(),
        provideHttpClient(),
        provideAnimationsAsync(),
        {
          provide: ActivatedRoute,
          useValue: {
            queryParams: of({
              ...mockQueryParams,
            }),
            paramMap: of({
              params: {
                id: mockCustomer.id,
              },
            }),
          },
        },
        {
          provide: UserManagementService,
          useValue: {
            userDetail$: new ReplaySubject(1),
            getUserDetail: function () {
              this.userDetail$.next(mockCustomer);

              return of(null);
            },
          },
        },
        {
          provide: FuseConfirmationService,
          useValue: {
            open: jest.fn().mockReturnValue({
              afterClosed: () => of('confirmed'),
            }),
          },
        },
        {
          provide: MatDialog,
          useValue: {
            open: jest.fn().mockReturnValue({
              afterClosed: () => of('confirmed'),
            }),
          },
        },
        {
          provide: UploadService,
          useValue: {
            uploadFile: jest.fn().mockReturnValue(of(null)),
          },
        },
        {
          provide: UtilityService,
          useValue: {
            getUploadUrl: jest.fn().mockReturnValue(of(mockPresignedUrl)),
          },
        },
        {
          provide: CustomerManagementService,
          useValue: {
            updateCustomer: jest.fn().mockReturnValue(of(mockCustomer)),
          },
        },
        {
          provide: GroupManagementService,
          useValue: {
            getGroups: jest.fn().mockReturnValue(of(mockGroups)),
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(EditCustomerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    jest.spyOn(component['_toast'], 'success');
    jest.spyOn(component['_toast'], 'error');
    jest.spyOn(component['_router'], 'navigate');
    jest.spyOn(component['_changeDetectorRef'], 'detectChanges');
  });

  it('should create', () => {
    expect(component).toBeTruthy();
    expect(component.id).toBe(mockCustomer.id);
  });

  describe('Lifecycle Hook', () => {
    it('should get the selected tab and call the handleGetGroup and handleGetCustomer when run ngOnInit', () => {
      jest.spyOn(component, 'handleGetCustomerDetail');
      jest.spyOn(component, 'handleGetGroups');

      component.ngOnInit();

      expect(component.selectedTab).toBe('card');
      expect(component.handleGetGroups).toHaveBeenCalled();
      expect(component.handleGetCustomerDetail).toHaveBeenCalled();
    });
  });
});
