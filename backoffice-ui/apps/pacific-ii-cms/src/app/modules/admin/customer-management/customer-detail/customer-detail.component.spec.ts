import { provideHttpClient } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { ActivatedRoute } from '@angular/router';
import {
  ROLE_MODULES,
  ROLE_SCOPES,
  ROUTE,
  USER_STATUS,
} from 'apps/pacific-ii-cms/src/app/core/const';
import { provideIcons } from 'apps/pacific-ii-cms/src/app/core/icons/icons.provider';
import { ToastrModule } from 'ngx-toastr';
import { of, ReplaySubject } from 'rxjs';
import { UserManagementService } from '../../user-management/user-management.service';
import { ICustomer } from '../customer.types';
import { CustomerDetailComponent } from './customer-detail.component';
import { Utils } from 'apps/pacific-ii-cms/src/app/core/utils/utils';
import { provideTranslate } from '../../../../core/transloco/transloco.provider';

describe('CustomerDetailComponent', () => {
  let component: CustomerDetailComponent;
  let fixture: ComponentFixture<CustomerDetailComponent>;

  const mockTenantId = '135261622300581888';

  const mockCustomer: ICustomer = {
    id: '125157569389419520',
    ssoId: null,
    externalId: null,
    uniqueExternalId: null,
    firstName: 'aa',
    lastName: 'aaa',
    email: '',
    userType: 'CUSTOMER',
    realmId: '********-45f9-4cc2-9946-bc00abed6203',
    avatar: null,
    userStatus: 'ACTIVE',
    phoneNumber: null,
    totalSubAccounts: null,
    totalSponsors: null,
    userGroup: {
      id: '121520479628366848',
      tenantId: '116024918514279424',
      path: '121520479628366848',
      groupName: 'Default',
      groupKey: 'DEFAULT_GROUP',
      parent: null,
      isDefaultGroup: true,
      description:
        'By default, all customer of tenant would be added into this group. ',
      createdAt: null,
      updatedAt: null,
    },
    userNonCompletedActions: ['KEYCLOAK_REGISTRATION'],
    permissions: [
      {
        tenantId: '116024918514279424',
        userId: '125157569389419520',
        userRoleId: '125157222424000514',
        permissionStatus: 'ACTIVE',
      },
    ],
    createdAt: *************,
    updatedAt: *************,
  };

  const mockPermissionList = [
    {
      externalId: `${ROLE_MODULES.CUSTOMER_MGMT}_${ROLE_SCOPES.UPDATE}`,
    },
  ];

  const mockQueryParams = {
    tab: 'card',
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CustomerDetailComponent, ToastrModule.forRoot()],
      providers: [
        provideIcons(),
        provideTranslate(),
        provideHttpClient(),
        provideAnimationsAsync(),
        {
          provide: ActivatedRoute,
          useValue: {
            queryParams: of({
              ...mockQueryParams,
            }),
            paramMap: of({
              params: {
                id: mockCustomer.id,
              },
            }),
          },
        },
        {
          provide: UserManagementService,
          useValue: {
            userDetail$: new ReplaySubject(1),
            getUserDetail: function () {
              this.userDetail$.next(mockCustomer);

              return of(null);
            },
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(CustomerDetailComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    component['_userPermissionService']['_permissions'].next(
      mockPermissionList,
    );
    component['_userPermissionService'].permissionList = mockPermissionList;

    jest.spyOn(component['_router'], 'navigate');
  });

  it('should create', () => {
    expect(component).toBeTruthy();
    expect(component.id).toBe(mockCustomer.id);
  });

  describe('Lifecycle Hook', () => {
    it('should get the tab name and call the handleGetCustomerDetail when run ngOnInit', () => {
      jest.spyOn(component, 'handleGetCustomerDetail');

      component.ngOnInit();

      expect(component.selectedTab).toBe('card');
      expect(component.handleGetCustomerDetail).toHaveBeenCalled();
    });

    it('should unsubscribe the all the observable when run the ngOnDestroy ', () => {
      const unsubscribeNextSpy = jest.spyOn(
        component['_unsubscribeAll'],
        'next',
      );
      const unsubscribeCompleteSpy = jest.spyOn(
        component['_unsubscribeAll'],
        'complete',
      );

      component.ngOnDestroy();

      expect(unsubscribeNextSpy).toHaveBeenCalled();
      expect(unsubscribeCompleteSpy).toHaveBeenCalled();
    });
  });

  describe('Data Fetching', () => {
    it('should send request to get customer detail when run handleGetCustomerDetail', () => {
      jest.spyOn(component['_usersService'], 'getUserDetail');

      component.handleGetCustomerDetail();

      expect(component['_usersService'].getUserDetail).toHaveBeenCalledWith(
        mockCustomer.id,
      );
      expect(component.customerDetail).toEqual(mockCustomer);
    });

    it('should get the permission to authorize the action when run the getPermission method', () => {
      expect(component.permissionList).toEqual({
        update: false,
      });

      component['getPermission']();

      expect(component.permissionList).toEqual({
        update: true,
      });
    });
  });

  describe('Utility Function', () => {
    it('should navigate to Edit Customer page when run gotoEditCustomer', () => {
      component.gotoEditCustomer();

      expect(component['_router'].navigate).toHaveBeenCalledWith([
        `${ROUTE.CUSTOMERS.MAIN}/${ROUTE.CUSTOMERS.EDIT}/${mockCustomer.id}`,
      ]);
    });

    it('should navigate to Customer Management page when run gotoCustomerManagement', () => {
      component.gotoCustomerManagement();

      expect(component['_router'].navigate).toHaveBeenCalledWith([
        ROUTE.CUSTOMERS.MAIN,
      ]);
    });

    it('should change the value of selectedTab when run onChangeSelectedTab with index we want', () => {
      component.onChangeSelectedTab({ index: 'test' });

      expect(component.selectedTab).toBe('test');
    });

    it('should return the correct status color when run getStatusColor', () => {
      const color = component.getStatusColor(USER_STATUS.ACTIVE);

      expect(color).toBe(Utils.getStatusColor(USER_STATUS.ACTIVE));
    });
  });
});
