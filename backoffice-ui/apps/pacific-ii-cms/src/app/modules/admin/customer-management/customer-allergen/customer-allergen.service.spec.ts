import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { provideHttpClient } from '@angular/common/http';
import { CustomerAllergenService } from './customer-allergen.service';
import { IUserAllergen, IUserAllergenInfo } from '../customer.types';
import { API } from 'apps/pacific-ii-cms/src/app/core/const';

describe.only('CustomerAllergenService', () => {
  let service: CustomerAllergenService;
  let httpClientTesting: HttpTestingController;

  const mockCustomerId = '125157569389419520';

  const mockAllergens: Array<IUserAllergenInfo> = [
    {
      id: '150473244348115968',
      tenantId: '116024918514279424',
      allergenId: '117147696369341440',
      createdAt: 1735875617111,
      restricted: false,
    },
    {
      id: '150473250601823232',
      tenantId: '116024918514279424',
      allergenId: '117147901269480448',
      createdAt: 1735875618601,
      restricted: false,
    },
    {
      id: '150473960760404992',
      tenantId: '116024918514279424',
      allergenId: '117147605453607936',
      createdAt: 1735875787916,
      restricted: false,
    },
  ];

  const mockUpdateAllergen: IUserAllergen = {
    items: [
      ...mockAllergens.map((value) => ({
        id: value.id,
        allergenId: value.allergenId,
      })),
    ],
    requestUserId: mockCustomerId,
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [provideHttpClient(), provideHttpClientTesting()],
    });
    service = TestBed.inject(CustomerAllergenService);
    httpClientTesting = TestBed.inject(HttpTestingController);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it("should send request to server to get the customer's allergen when run getCustomerAllergen", (done) => {
    service.getCustomerAllergen(mockCustomerId).subscribe((value) => {
      expect(value).toEqual(mockAllergens);
      done();
    });

    const req = httpClientTesting.expectOne(
      `${API.USER.ALLERGEN.LIST.replace('{userId}', mockCustomerId)}?`,
    );
    expect(req.request.method).toBe('GET');
    req.flush(mockAllergens);
  });

  it("should send request to server to update the customer's allergen when run updateCustomerAllergen", () => {
    service
      .updateCustomerAllergen(mockCustomerId, mockUpdateAllergen)
      .subscribe();

    const req = httpClientTesting.expectOne(
      `${API.USER.ALLERGEN.UPDATE.replace('{userId}', mockCustomerId)}`,
    );
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual(mockUpdateAllergen);
  });
});
