import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CustomerAllergenComponent } from './customer-allergen.component';
import { ToastrModule, ToastrService } from 'ngx-toastr';
import { provideIcons } from '../../../../core/icons/icons.provider';
import { provideHttpClient } from '@angular/common/http';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { ActivatedRoute } from '@angular/router';
import { of, throwError } from 'rxjs';
import { ChangeDetectorRef } from '@angular/core';
import { ProductAllergenManagementService } from '../../product-allergen-management/product-allergen-management.service';
import { CustomerAllergenService } from './customer-allergen.service';
import { provideTranslate } from '../../../../core/transloco/transloco.provider';
import { ALLERGEN_PAYMENT_RESTRICTION } from 'apps/pacific-ii-cms/src/app/core/const';

describe('CustomerAllergenComponent', () => {
  let component: CustomerAllergenComponent;
  let fixture: ComponentFixture<CustomerAllergenComponent>;

  let allergensService: ProductAllergenManagementService;
  let customerAllergenService: CustomerAllergenService;
  let toast: ToastrService;
  let changeDetectorRef: ChangeDetectorRef;

  const mockProductAllergens: any = {
    content: [
      {
        id: 1,
        name: 'Peanuts',
        description: 'Peanut allergy',
        restricted: false,
      },
      { id: 2, name: 'Milk', description: 'Dairy allergy', restricted: false },
      { id: 3, name: 'Tea', description: 'Oolong tea', restricted: false },
    ],
  };

  const mockCustomerAllergens: any = [
    { allergenId: 1, id: 1, restricted: true },
    { allergenId: 3, id: 3, restricted: false },
  ];

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CustomerAllergenComponent, ToastrModule.forRoot()],
      providers: [
        provideIcons(),
        provideTranslate(),
        provideHttpClient(),
        provideAnimationsAsync(),
        {
          provide: ActivatedRoute,
          useValue: {
            queryParams: of({}),
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(CustomerAllergenComponent);
    component = fixture.componentInstance;
    component.id = 'test-user-id';
    fixture.detectChanges();

    allergensService = TestBed.inject(ProductAllergenManagementService);
    customerAllergenService = TestBed.inject(CustomerAllergenService);
    toast = TestBed.inject(ToastrService);
    changeDetectorRef = fixture.debugElement.injector.get(ChangeDetectorRef);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Lifecycle hooks', () => {
    it('should call handleGetAllergens on init', () => {
      const spy = jest.spyOn(component, 'handleGetAllergens');
      component.ngOnInit();
      expect(spy).toHaveBeenCalled();
    });

    it('should initialize table columns in ngAfterViewInit', () => {
      const detectChangesSpy = jest.spyOn(
        changeDetectorRef.constructor.prototype,
        'detectChanges',
      );
      component.ngAfterViewInit();

      expect(component.displayedColumns).toEqual([
        // {
        //   key: 'active',
        //   name: 'Active',
        //   selected: true,
        //   custom: true,
        // },
        {
          key: 'name',
          name: 'Name',
          selected: true,
          renderHtml: expect.any(Function),
        },
        {
          key: 'description',
          name: 'Description',
          selected: true,
        },
        {
          key: 'paymentRestriction',
          name: 'Payment Restriction',
          selected: true,
          custom: true,
          // renderHtml: (value: string) => {
          //   const chipColor = Utils.getStatusColor(value);
          //   return `<div class="w-fit px-2 py-1 rounded-3 capitalize ${chipColor}">${value}</div>`;
          // },
        },
      ]);
      expect(detectChangesSpy).toHaveBeenCalled();
    });

    it('should unsubscribe from all subscriptions on destroy', () => {
      fixture.detectChanges();
      const nextSpy = jest.spyOn(component['_unsubscribeAll'], 'next');
      const completeSpy = jest.spyOn(component['_unsubscribeAll'], 'complete');

      component.ngOnDestroy();

      expect(nextSpy).toHaveBeenCalled();
      expect(completeSpy).toHaveBeenCalled();
    });
  });

  describe('Data Fetching', () => {
    let handleGetCustomerAllergenSpy: any;
    let getProductAllergensSpy: any;
    let getCustomerAllergenSpy: any;

    beforeEach(() => {
      handleGetCustomerAllergenSpy = jest.spyOn(
        component,
        'handleGetCustomerAllergen',
      );
      getProductAllergensSpy = jest.spyOn(
        allergensService,
        'getProductAllergens',
      );
      getCustomerAllergenSpy = jest.spyOn(
        customerAllergenService,
        'getCustomerAllergen',
      );
    });

    it('should fetch allergens on init', () => {
      const getAllergensSpy = jest.spyOn(component, 'handleGetAllergens');
      component.ngOnInit();

      expect(getAllergensSpy).toHaveBeenCalled();
      expect(getProductAllergensSpy).toHaveBeenCalled();
    });

    it('should update dataSource after fetching allergens', () => {
      getProductAllergensSpy.mockReturnValue(of(mockProductAllergens));

      component.handleGetAllergens();

      expect(getProductAllergensSpy).toHaveBeenCalled();
      expect(handleGetCustomerAllergenSpy).toHaveBeenCalled();
      expect(component.dataSource).toBeTruthy();
      expect(component.dataSource).toEqual(mockProductAllergens.content);
    });

    it('should update dataSource and customerAllergens after fetching customer allergens', () => {
      getCustomerAllergenSpy.mockReturnValue(of(mockCustomerAllergens));

      component.dataSource = mockProductAllergens.content;
      fixture.detectChanges();
      component.handleGetCustomerAllergen().subscribe();

      expect(getCustomerAllergenSpy).toHaveBeenCalled();
      expect(component.customerAllergens).toEqual(mockCustomerAllergens);
      expect(component.dataSource).toEqual([
        {
          ...mockProductAllergens.content[0],
          paymentRestriction: ALLERGEN_PAYMENT_RESTRICTION.RESTRICTED,
        },
        {
          ...mockProductAllergens.content[1],
          paymentRestriction: ALLERGEN_PAYMENT_RESTRICTION.NONE,
        },
        {
          ...mockProductAllergens.content[2],
          paymentRestriction: ALLERGEN_PAYMENT_RESTRICTION.WARNING,
        },
      ]);
    });
  });

  describe('Customer Allergen Updates', () => {
    let handleGetCustomerAllergenSpy: any;
    let updateCustomerAllergenSpy: any;
    let successToastSpy: any;

    beforeEach(() => {
      handleGetCustomerAllergenSpy = jest.spyOn(
        component,
        'handleGetCustomerAllergen',
      );
      updateCustomerAllergenSpy = jest.spyOn(
        customerAllergenService,
        'updateCustomerAllergen',
      );
      successToastSpy = jest.spyOn(toast, 'success');

      component.dataSource = [
        { ...mockProductAllergens.content[0], checked: true },
        { ...mockProductAllergens.content[1], checked: false },
        { ...mockProductAllergens.content[2], checked: false },
      ];
      component.customerAllergens = mockCustomerAllergens;
    });

    it('should handle removing an allergen correctly', () => {
      updateCustomerAllergenSpy.mockReturnValue(of([{ id: 3, allergenId: 3 }]));

      component.handleUpdateCustomerAllergen(mockProductAllergens.content[0], {
        value: 'NONE',
      });

      expect(updateCustomerAllergenSpy).toHaveBeenCalledWith(
        'test-user-id',
        expect.objectContaining({
          items: [{ allergenId: 3, id: 3, restricted: false }],
          requestUserId: 'test-user-id',
        }),
      );
      expect(successToastSpy).toHaveBeenCalledWith(
        'Update customer allergen success!',
      );
    });

    it('should handle adding a new allergen correctly', () => {
      updateCustomerAllergenSpy.mockReturnValue(
        of([
          { id: 1, allergenId: 1 },
          { id: 2, allergenId: 2 },
          { id: 3, allergenId: 3 },
        ]),
      );

      component.handleUpdateCustomerAllergen(mockProductAllergens.content[1], {
        value: 'WARNING',
      });

      expect(updateCustomerAllergenSpy).toHaveBeenCalledWith(
        'test-user-id',
        expect.objectContaining({
          items: expect.arrayContaining([
            { id: 1, allergenId: 1, restricted: true },
            { id: null, allergenId: 2, restricted: false },
            { id: 3, allergenId: 3, restricted: false },
          ]),
          requestUserId: 'test-user-id',
        }),
      );
      expect(successToastSpy).toHaveBeenCalledWith(
        'Update customer allergen success!',
      );
      expect(handleGetCustomerAllergenSpy).toHaveBeenCalled();
    });

    it('should handle update failure by refreshing customer allergens', () => {
      updateCustomerAllergenSpy.mockReturnValue(
        throwError(() => new Error('Update failed')),
      );

      component.handleUpdateCustomerAllergen(mockProductAllergens.content[1], {
        value: 'RESTRICTED',
      });

      expect(successToastSpy).not.toHaveBeenCalled();
      expect(handleGetCustomerAllergenSpy).toHaveBeenCalled();
    });
  });

  describe('Table Column Initialization', () => {
    it('should render HTML correctly for name column', () => {
      fixture.detectChanges();
      component.ngAfterViewInit();
      const nameColumn = component.displayedColumns.find(
        (col: any) => col.key === 'name',
      );
      const renderedHtml = nameColumn?.renderHtml('Test Name');

      expect(renderedHtml).toBe(
        '<div class="w-[20vw] font-semibold"> Test Name </div>',
      );
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });
});
