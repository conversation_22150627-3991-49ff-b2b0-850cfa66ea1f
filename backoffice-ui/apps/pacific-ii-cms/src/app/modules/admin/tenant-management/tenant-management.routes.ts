import { Routes } from '@angular/router';
import { TenantManagementComponent } from './tenant-management.component';
import { TenantDetailComponent } from './tenant-detail/tenant-detail.component';
import { ROUTE } from '../../../core/const';
import { TenantOnboardingComponent } from './tenant-onboarding/tenant-onboarding.component';
import { EditTenantComponent } from './edit-tenant/edit-tenant.component';
import { TenantReviewComponent } from './tenant-review/tenant-review.component';
import { TenantOnboardSuccessComponent } from './tenant-onboard-success/tenant-onboard-success.component';
import { TenantOverviewComponent } from './tenant-overview/tenant-overview.component';

export default [
  {
    path: ROUTE.TENANT.LIST,
    component: TenantManagementComponent,
  },
  {
    path: ROUTE.TENANT.ONBOARDING,
    component: TenantOnboardingComponent,
  },
  {
    path: ROUTE.TENANT.INFORMATION,
    component: TenantOverviewComponent,
  },
  {
    path: ROUTE.TENANT.ONBOARDING + '/:id/' + ROUTE.TENANT.REVIEW,
    component: TenantReviewComponent,
  },
  {
    path: ROUTE.TENANT.ONBOARDING + '/:id/' + ROUTE.TENANT.SUCCESS,
    component: TenantOnboardSuccessComponent,
  },
  {
    path: ROUTE.TENANT.EDIT + '/:id',
    component: EditTenantComponent,
  },
  {
    path: ROUTE.TENANT.DETAIL + '/:id',
    component: TenantDetailComponent,
  },
  { path: '', pathMatch: 'full', redirectTo: ROUTE.TENANT.LIST },
] as Routes;
