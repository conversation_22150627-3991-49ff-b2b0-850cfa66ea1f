import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  Inject,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core';
import {
  ReactiveFormsModule,
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { DeviceManagementService } from '../device-management.service';
import { TenantManagementService } from '../../tenant-management/tenant-management.service';
import { StoreManagementService } from '../../store-management/store-management.service';
import { FuseUltils } from '@fuse/ultils';
import { STORE_STATUS, TENANT_STATUS } from '../../../../core/const';
import { FuseLazyLoadSelectComponent } from '@fuse/components/lazy-load-select/lazy-load-select.component';
import { Subject, takeUntil } from 'rxjs';
import { MatIconModule } from '@angular/material/icon';
import { Utils } from 'apps/pacific-ii-cms/src/app/core/utils/utils';
import { FuseHelpLinkComponent } from '@fuse/components/help-link';

@Component({
  selector: 'app-assign-device',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    MatProgressSpinnerModule,
    FuseLazyLoadSelectComponent,
    MatIconModule,
    FuseHelpLinkComponent,
  ],
  templateUrl: './device-assign.component.html',
})
export class DeviceAssignComponent implements OnInit, AfterViewInit, OnDestroy {
  assignForm!: UntypedFormGroup;
  loading = false;

  optionListTenant: Array<any> = [];
  optionListStore: Array<any> = [];

  tenantAppend = false;
  tenantSearchConfig: {
    page: number;
    size: number;
    filter: {
      name: string;
      statuses: any;
    };
    sortDirection: string;
    sortFields: string;
  } = {
    sortDirection: 'asc',
    sortFields: 'name',
    page: 0,
    size: 10,
    filter: {
      name: '',
      statuses: [TENANT_STATUS[1].value],
    },
  };

  storeDetail!: any;
  storeAppend = false;
  storeSearchConfig: {
    page: number;
    size: number;
    filter: {
      name: string;
      statuses: any;
    };
    sortDirection: string;
    sortFields: string;
  } = {
    sortDirection: 'asc',
    sortFields: 'name',
    page: 0,
    size: 10,
    filter: {
      name: '',
      statuses: [
        STORE_STATUS.PENDING,
        STORE_STATUS.ACTIVE,
        STORE_STATUS.SUSPENDED,
      ],
    },
  };

  selectedTenantId!: string;

  errorMessages = {
    tenantId: {
      required: 'Please select tenant!',
    },
    storeId: {
      required: 'Please select store!',
    },
  };

  private _unsubscribeAll: Subject<any> = new Subject<any>();

  constructor(
    private _toast: ToastrService,
    private _dialogRef: MatDialogRef<any>,
    private _formBuilder: UntypedFormBuilder,
    private _deviceService: DeviceManagementService,
    private _storeService: StoreManagementService,
    private _tenantService: TenantManagementService,
    private _changeDetectorRef: ChangeDetectorRef,
    @Inject(MAT_DIALOG_DATA) public data: any,
  ) {
    this.initForm();
    this.tenantListSubscription();
  }

  ngOnInit() {
    if (this.data?.tenantId) {
      this.assignForm.patchValue({
        ...this.data,
      });

      if (this.data?.storeId) {
        this.handleGetStoreDetail(this.data.storeId);
      }
    } else {
      this.getTenantList();
    }
  }

  ngAfterViewInit(): void {
    this._changeDetectorRef.detectChanges();
  }

  ngOnDestroy(): void {
    this._unsubscribeAll.next(null);
    this._unsubscribeAll.complete();
  }

  tenantListSubscription() {
    this._tenantService.tenants$
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((data: any) => {
        const serverDataMap = data.content;
        const updatedSearchForm = this.tenantAppend
          ? [...this.optionListTenant, ...serverDataMap]
          : [...serverDataMap];

        this.optionListTenant = [...updatedSearchForm];
        this._changeDetectorRef.markForCheck();
      });
  }

  getTenantList() {
    this._tenantService
      .getTenants(FuseUltils.objectToQueryString(this.tenantSearchConfig))
      .subscribe();
  }

  handleLazyLoadSelectTenant(event: any) {
    this.tenantAppend = event.page > this.tenantSearchConfig.page;

    this.tenantSearchConfig = {
      ...this.tenantSearchConfig,
      page: event.page,
      size: 10,
      filter: {
        name: event.search,
        statuses: [TENANT_STATUS[1].value],
      },
    };

    this.getTenantList();
  }

  getStoreList(tenantId: any) {
    this._storeService
      .getStores(
        FuseUltils.objectToQueryString(this.storeSearchConfig),
        tenantId,
      )
      .subscribe((data) => {
        const serverDataMap = data.content;
        const updatedStore = this.storeAppend
          ? [...this.optionListStore, ...serverDataMap]
          : [...serverDataMap];

        this.optionListStore = this.prepareListStore(
          updatedStore,
          this.storeDetail,
        );
        this._changeDetectorRef.markForCheck();
      });
  }

  handleGetStoreDetail(storeId: string): void {
    this._storeService.getStoreDetail(storeId).subscribe((detail: any) => {
      this.storeDetail = detail;
      this.optionListStore = this.prepareListStore(
        this.optionListStore,
        this.storeDetail,
      );
    });
  }

  prepareListStore(stores: Array<any> = [], storeDetail: any): Array<any> {
    let optionListStore: Array<any> = JSON.parse(JSON.stringify(stores));

    if (!this.storeSearchConfig.filter.name && this.storeDetail) {
      optionListStore = [storeDetail, ...optionListStore];
    }

    optionListStore = optionListStore.filter(
      (item, index, self) =>
        index ===
        self.findIndex(
          (t) =>
            t.storeId === item.storeId &&
            t.tenantId == this.assignForm.controls['tenantId'].value,
        ),
    );

    return optionListStore;
  }

  handleLazyLoadSelectStore(event: any) {
    this.storeAppend = event.page > this.storeSearchConfig.page;

    this.storeSearchConfig = {
      ...this.storeSearchConfig,
      page: event.page,
      size: 10,
      filter: {
        name: event.search,
        statuses: [
          STORE_STATUS.PENDING,
          STORE_STATUS.ACTIVE,
          STORE_STATUS.SUSPENDED,
        ],
      },
    };

    this.getStoreList(this.assignForm.controls['tenantId'].value);
  }

  submitForm(): void {
    for (const i in this.assignForm.controls) {
      this.assignForm.controls[i].markAsTouched();
      this.assignForm.controls[i].updateValueAndValidity();
    }

    if (this.assignForm.invalid) {
      Utils.scrollToInvalid();
      return;
    }

    this.loading = true;
    const deviceData = {
      ...this.assignForm.value,
    };
    this.handleAssignDevice(deviceData);
  }

  handleAssignDevice(deviceData: any): void {
    this._deviceService.assign(this.data.deviceId, deviceData).subscribe({
      next: () => {
        this.loading = false;
        this._toast.success(
          this.data.storeId ? 'Change store success!' : 'Assign success!',
        );
        this.onClose('save');
      },
      error: () => {
        this.loading = false;
      },
    });
  }

  onClose(message?: string): void {
    this._dialogRef.close(message);
  }

  private initForm(): void {
    this.assignForm = this._formBuilder.group({
      tenantId: [null, [Validators.required]],
      storeId: [null, [Validators.required]],
    });

    this.assignForm.get('tenantId')?.valueChanges.subscribe((tenantId: any) => {
      if (tenantId != this.selectedTenantId) {
        this.selectedTenantId = tenantId;
        this.optionListStore = [];

        if (!this.data?.tenantId) {
          this.assignForm.patchValue({
            storeId: null,
          });
        }
        this.storeSearchConfig = {
          ...this.storeSearchConfig,
          page: 0,
          size: 10,
          filter: {
            name: '',
            statuses: [
              STORE_STATUS.PENDING,
              STORE_STATUS.ACTIVE,
              STORE_STATUS.SUSPENDED,
            ],
          },
        };

        this.getStoreList(tenantId);
      }
    });
  }
}
