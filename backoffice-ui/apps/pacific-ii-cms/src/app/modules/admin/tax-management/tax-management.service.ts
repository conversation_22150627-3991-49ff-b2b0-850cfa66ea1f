import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable, ReplaySubject, Subject, tap } from 'rxjs';
import { TaxData, TaxResponse } from './tax-management.types';
import { API } from '../../../core/const';

@Injectable({
  providedIn: 'root',
})
export class TaxManagementService {
  private _httpClient = inject(HttpClient);

  private _taxesResponse: ReplaySubject<TaxResponse> =
    new ReplaySubject<TaxResponse>(1);
  private _taxDetail: Subject<TaxData> = new Subject<TaxData>();

  // -----------------------------------------------------------------------------------------------------
  // @ Accessors
  // -----------------------------------------------------------------------------------------------------

  /**
   * Getter for all taxes data
   */
  get taxes$(): Observable<TaxResponse> {
    return this._taxesResponse.asObservable();
  }

  /**
   * Getter for tax detail
   */
  get taxDetail$(): Observable<TaxData> {
    return this._taxDetail.asObservable();
  }

  // -----------------------------------------------------------------------------------------------------
  // @ Public methods
  // -----------------------------------------------------------------------------------------------------

  /**
   * Get all taxes
   */
  getAllTaxes(): Observable<TaxResponse> {
    return this._httpClient.get<TaxResponse>(API.TAX_MANAGEMENT.TAX).pipe(
      tap((value: TaxResponse) => {
        this._taxesResponse.next(value);
      }),
    );
  }

  /**
   * Get tax detail
   */
  getTaxDetail(id: string): Observable<TaxData> {
    return this._httpClient
      .get<TaxData>(API.TAX_MANAGEMENT.TAX + `/${id}`)
      .pipe(tap((value) => this._taxDetail.next(value)));
  }

  /**
   * Add new tax
   */
  addTax(data: TaxData): Observable<any> {
    return this._httpClient.post(API.TAX_MANAGEMENT.TAX, data);
  }

  /**
   * Delete the existing tax
   */
  deleteTax(id: string): Observable<any> {
    return this._httpClient.delete(`${API.TAX_MANAGEMENT.TAX}/${id}`);
  }

  /**
   * Edit the existing tax
   */
  editTax(id: string, data: TaxData) {
    return this._httpClient.put(`${API.TAX_MANAGEMENT.TAX}/${id}`, data);
  }

  /**
   * Get the current active tax
   */
  getCurrentActiveTax(): Observable<TaxData> {
    return this._httpClient.get<TaxData>(`${API.TAX_MANAGEMENT.CURRENT_TAX}`);
  }
}
