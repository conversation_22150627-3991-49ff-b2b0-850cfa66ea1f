import { ComponentFixture, TestBed } from '@angular/core/testing';
import { LinkPaymentConnectedAccountComponent } from './link-payment-connected-account.component';
import { ToastrModule, ToastrService } from 'ngx-toastr';
import { provideIcons } from '../../../../../core/icons/icons.provider';
import { provideHttpClient } from '@angular/common/http';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { ReactiveFormsModule } from '@angular/forms';
import { PaymentMethodService } from '../../payment-method.service';
import { of, throwError } from 'rxjs';
import { provideTranslate } from '../../../../../core/transloco/transloco.provider';

describe('LinkPaymentConnectedAccountComponent', () => {
  let component: LinkPaymentConnectedAccountComponent;
  let fixture: ComponentFixture<LinkPaymentConnectedAccountComponent>;

  let toast: ToastrService;
  let dialogRef: MatDialogRef<any>;
  let paymentMethodService: PaymentMethodService;

  const mockStripeAccounts: any = [
    {
      id: '1234568',
      isActive: false,
      tenantId: '**********',
      processorId: 'STRIPE_CONNECT_STANDARD_WEB_PAYMENT',
      ownerEmail: null,
      clientExternalId: 'account_02',
    },
    {
      id: '1234567',
      isActive: true,
      tenantId: '**********',
      processorId: 'STRIPE_CONNECT_STANDARD_WEB_PAYMENT',
      ownerEmail: '<EMAIL>',
      clientExternalId: 'account_01',
    },
    {
      id: '1234569',
      isActive: false,
      tenantId: '**********',
      processorId: 'STRIPE_CONNECT_STANDARD_WEB_PAYMENT',
      ownerEmail: null,
      clientExternalId: 'account_03',
    },
  ];

  const mockPaymentMethodDetail = {
    id: '********',
    processorId: 'STRIPE_CONNECT_STANDARD_WEB_PAYMENT',
    tenantId: '*********',
    displayName: 'D Stripe Connect 4',
    icon: {
      path: null,
    },
    description: null,
    isActive: false,
    paymentInstruction: null,
    surchargeRate: '0',
    fixedSurcharge: 0,
    currency: {
      displayName: 'US Dollar',
      numericCode: 840,
      currencyCode: 'USD',
      symbol: '$',
      fractionDigits: 2,
    },
    surchargeTitle: null,
    transactionReversible: true,
    connectedAccount: null,
    processorConfig: null,
    acceptedApplications: ['TOP_UP_KIOSK'],
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        LinkPaymentConnectedAccountComponent,
        MatDialogModule,
        ReactiveFormsModule,
        ToastrModule.forRoot(),
      ],
      providers: [
        provideIcons(),
        provideTranslate(),
        provideHttpClient(),
        provideAnimationsAsync(),
        {
          provide: MatDialogRef,
          useValue: {
            close: jest.fn(),
          },
        },
        { provide: MAT_DIALOG_DATA, useValue: { data: {} } },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(LinkPaymentConnectedAccountComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    toast = TestBed.inject(ToastrService);
    dialogRef = TestBed.inject(MatDialogRef);
    paymentMethodService = TestBed.inject(PaymentMethodService);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Lifecycle hooks', () => {
    it('should initialize the form correctly on constructor', () => {
      expect(component.linkAccountForm).toBeDefined();
      expect(component.linkAccountForm.get('connectedAccountId')).toBeDefined();
      expect(
        component.linkAccountForm.get('connectedAccountId')?.value,
      ).toBeNull();
    });

    it('should call handleGetConnectedAccounts on init', () => {
      const spy = jest.spyOn(component, 'handleGetConnectedAccounts');
      component.ngOnInit();
      expect(spy).toHaveBeenCalled();
    });

    it('should patch form value on init if data.connectedAccount exist', () => {
      const spy = jest.spyOn(component.linkAccountForm, 'patchValue');
      component.data = {
        connectedAccount: {
          id: '1234',
        },
      };

      component.ngOnInit();
      expect(spy).toHaveBeenCalledWith({
        connectedAccountId: '1234',
      });
      expect(component.linkAccountForm.value.connectedAccountId).toEqual(
        '1234',
      );
    });

    it('should not patch form value on init if data.connectedAccount does not exist', () => {
      const spy = jest.spyOn(component.linkAccountForm, 'patchValue');
      component.data = {
        connectedAccount: null,
      };

      component.ngOnInit();
      expect(spy).not.toHaveBeenCalled();
      expect(component.linkAccountForm.value.connectedAccountId).toEqual(null);
    });
  });

  describe('Data Fetching', () => {
    let handleGetConnectedAccountsSpy: any;
    let getPaymentConnectedAccountsSpy: any;

    beforeEach(() => {
      handleGetConnectedAccountsSpy = jest.spyOn(
        component,
        'handleGetConnectedAccounts',
      );
      getPaymentConnectedAccountsSpy = jest.spyOn(
        paymentMethodService,
        'getPaymentConnectedAccounts',
      );
    });

    it('should fetch stripe connected accounts on init', () => {
      component.ngOnInit();

      expect(handleGetConnectedAccountsSpy).toHaveBeenCalled();
      expect(getPaymentConnectedAccountsSpy).toHaveBeenCalled();
    });

    it('should update connectedAccountAppend/connectedAccountParams and fetch stripe connected accounts when handleLazyLoadSelect called', () => {
      const event = {
        page: 1,
      };
      component.handleLazyLoadSelect(event);

      expect(handleGetConnectedAccountsSpy).toHaveBeenCalled();
      expect(getPaymentConnectedAccountsSpy).toHaveBeenCalled();
      expect(component.connectedAccountAppend).toBeTruthy();
      expect(component.connectedAccountParams).toEqual({
        page: event.page,
        size: 10,
      });
    });

    it('should update optionListConnectedAccount after fetching stripe connected accounts with data.connectedAccount exist', () => {
      getPaymentConnectedAccountsSpy.mockReturnValue(
        of({ content: mockStripeAccounts }),
      );

      component.data = {
        connectedAccount: mockStripeAccounts[1],
      };
      component.handleGetConnectedAccounts();

      expect(getPaymentConnectedAccountsSpy).toHaveBeenCalled();
      expect(component.optionListConnectedAccount).toEqual([
        {
          id: '1234567',
          isActive: true,
          label: '<EMAIL> (account_01)',
          tenantId: '**********',
          processorId: 'STRIPE_CONNECT_STANDARD_WEB_PAYMENT',
          ownerEmail: '<EMAIL>',
          clientExternalId: 'account_01',
        },
        {
          id: '1234568',
          isActive: false,
          label: 'account_02',
          tenantId: '**********',
          processorId: 'STRIPE_CONNECT_STANDARD_WEB_PAYMENT',
          ownerEmail: null,
          clientExternalId: 'account_02',
        },
        {
          id: '1234569',
          isActive: false,
          label: 'account_03',
          tenantId: '**********',
          processorId: 'STRIPE_CONNECT_STANDARD_WEB_PAYMENT',
          ownerEmail: null,
          clientExternalId: 'account_03',
        },
      ]);
    });

    it('should update optionListConnectedAccount after fetching stripe connected accounts with data.connectedAccount empty', () => {
      getPaymentConnectedAccountsSpy.mockReturnValue(
        of({ content: mockStripeAccounts }),
      );

      component.data = {
        connectedAccount: null,
      };
      component.handleGetConnectedAccounts();

      expect(getPaymentConnectedAccountsSpy).toHaveBeenCalled();
      expect(component.optionListConnectedAccount).toEqual([
        {
          id: '1234568',
          isActive: false,
          label: 'account_02',
          tenantId: '**********',
          processorId: 'STRIPE_CONNECT_STANDARD_WEB_PAYMENT',
          ownerEmail: null,
          clientExternalId: 'account_02',
        },
        {
          id: '1234567',
          isActive: true,
          label: '<EMAIL> (account_01)',
          tenantId: '**********',
          processorId: 'STRIPE_CONNECT_STANDARD_WEB_PAYMENT',
          ownerEmail: '<EMAIL>',
          clientExternalId: 'account_01',
        },
        {
          id: '1234569',
          isActive: false,
          label: 'account_03',
          tenantId: '**********',
          processorId: 'STRIPE_CONNECT_STANDARD_WEB_PAYMENT',
          ownerEmail: null,
          clientExternalId: 'account_03',
        },
      ]);
    });
  });

  describe('Form Validation', () => {
    it('should be invalid when form and data.connectedAccount are empty ', () => {
      component.data = {
        connectedAccount: null,
      };
      component.ngOnInit();
      component.submitForm();
      expect(component.linkAccountForm.valid).toBeFalsy();
    });

    it('should be valid when connectedAccountId is selected', () => {
      component.linkAccountForm.patchValue({
        connectedAccountId: '1',
      });
      component.ngOnInit();
      component.submitForm();
      expect(component.linkAccountForm.valid).toBeTruthy();
    });

    it('should be valid when data.connectedAccount exists', () => {
      component.data = {
        connectedAccount: { id: '1' },
      };
      component.ngOnInit();
      component.submitForm();
      expect(component.linkAccountForm.valid).toBeTruthy();
    });

    it('should mark fields as touched on submit', () => {
      const formControls = component.linkAccountForm.controls;
      component.submitForm();

      Object.keys(formControls).forEach((key) => {
        expect(formControls[key].touched).toBeTruthy();
      });
    });
  });

  describe('Form Submission', () => {
    let dialogRefCloseSpy: any;
    let handleUpdatePaymentMethodSpy: any;
    let editPaymentMethodSpy: any;
    let successToastSpy: any;

    const paymentMethodDetailData = {
      displayName: mockPaymentMethodDetail.displayName,
      iconPath: mockPaymentMethodDetail.icon?.path ?? null,
      isActive: mockPaymentMethodDetail.isActive,
      description: mockPaymentMethodDetail.description,
      paymentInstruction: mockPaymentMethodDetail.paymentInstruction,
      surchargeRate: mockPaymentMethodDetail.surchargeRate,
      fixedSurcharge: mockPaymentMethodDetail.fixedSurcharge,
      currencyCode: mockPaymentMethodDetail.currency?.currencyCode ?? null,
      surchargeTitle: mockPaymentMethodDetail.surchargeTitle,
      connectedAccountId: mockStripeAccounts[1].id,
      acceptedApplications: mockPaymentMethodDetail.acceptedApplications,
      processorConfig: mockPaymentMethodDetail.processorConfig,
    };

    beforeEach(() => {
      dialogRefCloseSpy = jest.spyOn(dialogRef, 'close');
      handleUpdatePaymentMethodSpy = jest.spyOn(
        component,
        'handleUpdatePaymentMethod',
      );
      editPaymentMethodSpy = jest.spyOn(
        paymentMethodService,
        'editPaymentMethod',
      );
      successToastSpy = jest.spyOn(toast, 'success');
    });

    it('should not proceed with submission if form is invalid', () => {
      component.submitForm();
      expect(component.loading).toBeFalsy();
      expect(handleUpdatePaymentMethodSpy).not.toHaveBeenCalled();
      expect(dialogRefCloseSpy).not.toHaveBeenCalled();
    });

    it('should call handleUpdatePaymentMethod on valid submission', () => {
      component.linkAccountForm.patchValue({
        connectedAccountId: mockStripeAccounts[1].id,
      });
      component.submitForm();

      expect(handleUpdatePaymentMethodSpy).toHaveBeenCalled();
      expect(editPaymentMethodSpy).toHaveBeenCalled();
    });

    it('should close dialog and show toast message when stripe account linked successfully', () => {
      component.data = mockPaymentMethodDetail;
      component.linkAccountForm.patchValue({
        connectedAccountId: mockStripeAccounts[1].id,
      });

      editPaymentMethodSpy.mockReturnValue(
        of({
          ...mockPaymentMethodDetail,
          connectedAccount: mockStripeAccounts[1],
        }),
      );
      component.handleUpdatePaymentMethod(paymentMethodDetailData);

      expect(editPaymentMethodSpy).toHaveBeenCalledWith(
        mockPaymentMethodDetail.id,
        paymentMethodDetailData,
      );

      expect(component.loading).toBeFalsy();
      expect(successToastSpy).toHaveBeenCalledWith(
        'Stripe connected account linked successfully!',
      );
      expect(dialogRefCloseSpy).toHaveBeenCalledWith({
        ...mockPaymentMethodDetail,
        connectedAccount: mockStripeAccounts[1],
      });
    });

    it('should not close dialog and show toast message when stripe account linked unsuccessfully', () => {
      component.data = mockPaymentMethodDetail;
      component.linkAccountForm.patchValue({
        connectedAccountId: mockStripeAccounts[1].id,
      });

      editPaymentMethodSpy.mockReturnValue(
        throwError(() => new Error('Update failed')),
      );
      component.handleUpdatePaymentMethod(paymentMethodDetailData);

      expect(editPaymentMethodSpy).toHaveBeenCalledWith(
        mockPaymentMethodDetail.id,
        paymentMethodDetailData,
      );

      expect(component.loading).toBeFalsy();
      expect(successToastSpy).not.toHaveBeenCalled();
      expect(dialogRefCloseSpy).not.toHaveBeenCalled();
    });
  });

  describe('Dialog Closing', () => {
    let dialogRefCloseSpy: any;

    beforeEach(() => {
      dialogRefCloseSpy = jest.spyOn(dialogRef, 'close');
    });

    it('should close dialog with null when no result provided', () => {
      component.onClose();
      expect(dialogRefCloseSpy).toHaveBeenCalledWith(undefined);
    });

    it('should close dialog with roleId when result provided', () => {
      component.onClose('1');
      expect(dialogRefCloseSpy).toHaveBeenCalledWith('1');
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });
});
