<div class="flex flex-col gap-6">
  <div class="flex flex-col gap-8 px-3 pt-2 pb-4">
    <div class="web__h6 text-grey-900">
      Link Existing Stripe Connected Account
    </div>

    <form class="flex flex-col gap-4" [formGroup]="linkAccountForm">
      <fuse-lazy-load-select
        class="w-full"
        [form]="linkAccountForm"
        [label]="'Stripe Connected Account'"
        [name]="'connectedAccountId'"
        [options]="optionListConnectedAccount"
        [optionValue]="'id'"
        [placeholder]="'Select account'"
        [errorMessages]="errorMessages.connectedAccountId"
        (searchValueChanged)="handleLazyLoadSelect($event)"
      />
    </form>
  </div>

  <div class="flex items-start justify-end gap-2">
    <button class="btn-outlined__primary__medium" (click)="onClose()">
      Cancel
    </button>

    <button
      class="btn-contained__primary__medium"
      [disabled]="loading || linkAccountForm.invalid || !linkAccountForm.dirty"
      (click)="submitForm()"
    >
      @if (loading) {
        <mat-spinner diameter="18" class="mr-0"></mat-spinner>
      }
      Link
    </button>
  </div>
</div>
