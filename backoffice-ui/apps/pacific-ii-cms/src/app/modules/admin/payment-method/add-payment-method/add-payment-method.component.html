<div class="flex flex-col w-full">
  <div class="page-header">
    <div class="page-header__wrapper">
      <div class="page-header__breadcrumb">
        <div class="page-header__breadcrumb__parent">Payment Method</div>
        <div class="page-header__breadcrumb__dot"></div>
        <div class="page-header__breadcrumb__child">
          {{ id ? 'Edit' : 'Add' }} Payment Method
        </div>
      </div>

      <div class="page-header__title">
        <button type="button" (click)="navigateToPage(id ? 'detail' : 'main')">
          <mat-icon [svgIcon]="'heroicons_outline:chevron-left'"></mat-icon>
        </button>
        <div>{{ id ? 'Edit' : 'Add' }} Payment Method</div>
        <fuse-help-link
          [url]="'/configuration/payment-wallet-tax/#manage-payment-methods'"
        ></fuse-help-link>
      </div>
    </div>

    <div class="flex gap-4">
      <button
        class="btn-outlined__primary__medium"
        (click)="navigateToPage(id ? 'detail' : 'main')"
      >
        <mat-icon
          class="w-5 h-5"
          [svgIcon]="'heroicons_outline:x-mark'"
        ></mat-icon>
        <span>Cancel</span>
      </button>
      <button
        [class]="
          id
            ? 'btn-contained__success__medium'
            : 'btn-contained__primary__medium'
        "
        [disabled]="
          loading ||
          paymentForm.invalid ||
          (apiConfigForm && apiConfigForm.invalid) ||
          (!paymentForm.dirty && !apiConfigForm) ||
          (!paymentForm.dirty && apiConfigForm && !apiConfigForm.dirty)
        "
        (click)="submitForm()"
      >
        @if (loading) {
          <mat-spinner diameter="18" class="mr-0"></mat-spinner>
        } @else if (id) {
          <mat-icon
            class="w-5 h-5"
            [svgIcon]="'heroicons_outline:check'"
          ></mat-icon>
        }
        <span>{{ id ? 'Save' : 'Create' }}</span>
      </button>
    </div>
  </div>

  <div class="flex flex-col w-full gap-4 p-4">
    <!-- GENERAL INFORMATION -->
    <form class="flex flex-col gap-4" [formGroup]="paymentForm">
      <div class="box">
        <div class="flex items-center justify-between gap-4">
          <div class="box__header__title">General Information</div>
        </div>

        <div class="grid grid-cols-2 gap-4">
          <fuse-input
            class="w-full"
            [form]="paymentForm"
            [label]="'Display Name'"
            [name]="'displayName'"
            [placeholder]="'Enter payment method name'"
            [errorMessages]="errorMessages.displayName"
          />

          <fuse-select
            class="w-full"
            [form]="paymentForm"
            [label]="'Currency'"
            [name]="'currencyCode'"
            [placeholder]="'Select currency'"
            [errorMessages]="errorMessages.currencyCode"
            [options]="currencyOptions"
            [optionValue]="'currencyCode'"
            [optionLabel]="'currencyCode'"
          />

          <fuse-select
            class="w-full"
            [form]="paymentForm"
            [label]="'Payment Processor'"
            [name]="'processorId'"
            [placeholder]="'Select payment processor'"
            [errorMessages]="errorMessages.processorId"
            [options]="paymentProcessors"
            [optionValue]="'processorId'"
            [optionLabel]="'processorName'"
          />

          <fuse-select
            class="w-full"
            [form]="paymentForm"
            [label]="'Accepted Applications'"
            [name]="'acceptedApplications'"
            [placeholder]="'Select applications'"
            [multiple]="true"
            [errorMessages]="errorMessages.acceptedApplications"
            [options]="acceptedApps"
            [description]="'Options show after selecting processor'"
            [optionLabel]="'label'"
            [optionValue]="'value'"
          />

          <fuse-input
            class="w-full"
            [form]="paymentForm"
            [label]="'Surcharge Title'"
            [name]="'surchargeTitle'"
            [placeholder]="'Enter surcharge title'"
          />
          <div class="grid grid-cols-2 gap-4">
            <div class="flex flex-col">
              <fuse-input
                class="w-full"
                [form]="surchargeForm"
                [name]="'surchargeRate'"
                [placeholder]="'Enter surchage rate'"
                [prefixIcon]="'feather:percent'"
                [label]="'Surcharge Rate'"
                [errorMessages]="errorMessages.surchargeRate"
              />
            </div>

            <fuse-input
              class="w-full"
              [form]="surchargeForm"
              [name]="'fixedSurcharge'"
              [placeholder]="'Enter fixed surchage'"
              [prefixText]="tenantCurrency.currencyCode"
              [prefixIconColor]="'#343330'"
              [label]="'Fixed Surcharge'"
              [errorMessages]="errorMessages.fixedSurcharge"
            />
          </div>
        </div>

        <!-- IMAGE PICKER COMPONENT -->
        <div class="upload-image-box">
          <label class="upload-image-box__label">
            Image (Maximum size 5MB)
          </label>

          <div class="upload-image-box__content">
            @if (paymentForm.controls['image'].value) {
              <div class="upload-image-box__image">
                <img [src]="paymentForm.controls['image'].value" alt="" />

                <div class="upload-image-box__image__action">
                  <mat-icon
                    [svgIcon]="'heroicons_outline:eye'"
                    (click)="viewLogo()"
                  ></mat-icon>
                  <mat-icon
                    [svgIcon]="'styl:swap'"
                    (click)="fileInput.click()"
                  ></mat-icon>
                  <mat-icon
                    [svgIcon]="'heroicons_outline:trash'"
                    (click)="resetUploadedLogoField()"
                  ></mat-icon>
                </div>
              </div>
            } @else {
              <div class="upload-image-box__upload" (click)="fileInput.click()">
                <mat-icon [svgIcon]="'styl:fileImage'"></mat-icon>
                <div>Supported up to 5MB for JPG, PNG</div>
              </div>
            }

            <input
              #fileInput
              type="file"
              accept="image/*"
              hidden
              (change)="onUploadLogoFile($event)"
            />
          </div>
        </div>

        <fuse-textarea
          class="w-full"
          [form]="paymentForm"
          [label]="'Description'"
          [name]="'description'"
          [placeholder]="'Enter payment description'"
          [errorMessages]="errorMessages.description"
        />

        <fuse-textarea
          class="w-full"
          [form]="paymentForm"
          [label]="'Payment Instruction'"
          [name]="'paymentInstruction'"
          [placeholder]="'Enter payment instruction'"
        />

        <div>
          <fuse-switch
            class="w-full h-1"
            [form]="paymentForm"
            [label]="'Status'"
            [name]="'isActive'"
          />
        </div>
      </div>
    </form>
  </div>

  @if (configState.isShow) {
    <div class="flex flex-col w-full gap-4 px-4 pb-4">
      <!-- API CREDENTIALS -->
      <form class="flex flex-col gap-4" [formGroup]="apiConfigForm">
        <div class="box">
          <div class="flex items-center justify-between gap-4">
            <div class="box__header__title">API Credentials</div>
          </div>

          @for (renderObject of apiConfigRender; track trackByFn($index)) {
            @switch (renderObject.type) {
              @case ('input') {
                <fuse-input
                  class="w-full"
                  [form]="apiConfigForm"
                  [label]="renderObject.label"
                  [name]="renderObject.name"
                  [placeholder]="renderObject.placeholder"
                  [errorMessages]="renderObject.errorMsg"
                />
              }
              @case ('password') {
                <fuse-input
                  class="w-full"
                  [form]="apiConfigForm"
                  [type]="'password'"
                  [label]="renderObject.label"
                  [name]="renderObject.name"
                  [placeholder]="renderObject.placeholder"
                  [errorMessages]="renderObject.errorMsg"
                  [description]="renderObject?.description || ''"
                />
              }
              @case ('select') {
                <fuse-select
                  class="w-full"
                  [form]="apiConfigForm"
                  [label]="renderObject.label"
                  [name]="renderObject.name"
                  [placeholder]="renderObject.placeholder"
                  [errorMessages]="renderObject.errorMsg"
                  [options]="renderObject.options || []"
                  [optionLabel]="renderObject.optionLabel || ''"
                  [optionValue]="renderObject.optionValue || ''"
                />
              }
            }
          }
        </div>
      </form>
    </div>
  }
</div>

<ng-template #viewLogoTemplate>
  <div class="w-full h-auto">
    <img
      class="object-contain w-full h-full"
      [src]="paymentForm.controls['image'].value"
      alt=""
    />
  </div>
</ng-template>
