export interface IDomainList {
  content: Array<IDomainInfo>;
  totalElements: number;
  totalPages: number;
  page: number;
  sort: Array<string>;
}

export interface IDomainInfo {
  id: string;
  tenantId: string;
  tenantName: string;
  domainName: string;
  status: string;
}

export interface IDomainFilter {
  filter?: {
    domainName?: string;
    tenantName?: string;
    tenantId?: string;
    statuses?: Array<string>;
  };
  size?: number;
  page?: number;
  sortDirection?: string;
  sortFields?: string;
}
