import { Currency } from '../tenant-management/tenant.types';

export interface IServiceChargesResponse {
  content: IServiceChargeDetail[];
}

export interface IServiceChargeDetail {
  id: string;
  tenantId: string;
  isActive: true;
  name: string;
  description: string;
  chargeFixedAmount: number;
  chargeRate: number;
  currency: Currency;
  platformOrderTypes: string[];
  createdAt: number;
  updatedAt: number;
  createdBy: string;
  updatedBy: string;
}

export interface IServiceCharge {
  isActive: boolean;
  name: string;
  description: string;
  chargeFixedAmount: number;
  chargeRate: number;
  platformOrderTypes: string[];
}
