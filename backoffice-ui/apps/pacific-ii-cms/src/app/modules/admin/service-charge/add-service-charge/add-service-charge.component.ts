import { ChangeDetector<PERSON><PERSON>, Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import {
  FormBuilder,
  ReactiveFormsModule,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatIcon } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { ActivatedRoute, Router } from '@angular/router';
import { FuseInputComponent } from '@fuse/components/input';
import { FuseTextareaComponent } from '@fuse/components/textarea';
import {
  PLATFORM_ORDER_TYPE_OPTIONS,
  REGEX,
  ROUTE,
} from 'apps/pacific-ii-cms/src/app/core/const';
import { ToastrService } from 'ngx-toastr';
import { Subject, takeUntil } from 'rxjs';
import {
  generateNumberInputErrorMsg,
  generateRequiredErrorMsg,
} from '../../payment-method/add-payment-method/add-payment-method.component';
import { ServiceChargeService } from '../service-charge.service';
import { IServiceChargeDetail } from '../service-charge.types';
import { Utils } from 'apps/pacific-ii-cms/src/app/core/utils/utils';
import { FuseHelpLinkComponent } from '@fuse/components/help-link';
import { FuseSwitchComponent } from '../../../../../../../../libs/fuse/src/lib/components/switch';
import { TranslocoPipe, TranslocoService } from '@jsverse/transloco';
import { FuseLazyLoadSelectComponent } from '../../../../../../../../libs/fuse/src/lib/components/lazy-load-select/lazy-load-select.component';
import { Currency } from '../../tenant-management/tenant.types';
import { StorageService } from '../../../../core/services/storage.service';
import { FuseUltils } from '@fuse/ultils';

@Component({
  selector: 'app-add-service-charge',
  standalone: true,
  imports: [
    FuseInputComponent,
    ReactiveFormsModule,
    MatProgressSpinnerModule,
    FuseTextareaComponent,
    MatCheckboxModule,
    MatIcon,
    FuseLazyLoadSelectComponent,
    FuseHelpLinkComponent,
    FuseSwitchComponent,
    TranslocoPipe,
  ],
  templateUrl: './add-service-charge.component.html',
})
export class AddServiceChargeComponent implements OnInit, OnDestroy {
  id!: string;
  serviceChargeDetail!: IServiceChargeDetail;
  serviceChargeForm!: UntypedFormGroup;
  loading = false;

  tenantCurrency!: Currency;

  optionListServiceChargeType: Array<any> = PLATFORM_ORDER_TYPE_OPTIONS;

  errorMessages = {
    name: {
      required: generateRequiredErrorMsg('input', 'Name'),
      maxlength: 'Name cannot exceed 80 characters!',
    },
    platformOrderTypes: {
      required: generateRequiredErrorMsg('select', 'platform order type'),
    },
    chargeFixedAmount: {
      min: generateNumberInputErrorMsg('min', 'charge amount'),
      pattern:
        'Invalid charge amount value. Please ensure the charge amount is a numerical value and has no more than {n} digits in decimal!',
    },
    chargeRate: {
      min: generateNumberInputErrorMsg('min', 'charge rate'),
      pattern:
        'Invalid charge rate value. Please ensure the charge rate is a numeric value and has no more than 2 digits in decimal!',
    },
    description: {
      maxlength: 'Description cannot exceed 255 characters!',
    },
  };

  private _destroy$: Subject<any> = new Subject();

  constructor(
    private _router: Router,
    private _toast: ToastrService,
    private _formBuilder: FormBuilder,
    private _activatedRoute: ActivatedRoute,
    private _storageService: StorageService,
    private _translocoService: TranslocoService,
    private _changeDetectorRef: ChangeDetectorRef,
    private _serviceChargeService: ServiceChargeService,
  ) {
    this.tenantCurrency = this._storageService.getTenantCurrency();
    this.initForm();
    this.getServiceChargeId();
    this.updateErrorMessages();
  }

  ngOnInit(): void {
    if (this.id) {
      this.handleGetServiceChargeDetail();
    }
  }

  ngOnDestroy(): void {
    this._destroy$.next(null);
    this._destroy$.complete();
  }

  /**
   * Get service charge detail if this is edit page
   */
  handleGetServiceChargeDetail() {
    this._serviceChargeService
      .getServiceChargeDetail(this.id)
      .pipe(takeUntil(this._destroy$))
      .subscribe((detail) => {
        this.serviceChargeDetail = detail;
        this.serviceChargeForm.patchValue({
          ...detail,
          chargeFixedAmount: FuseUltils.formatCurrency(
            detail.chargeFixedAmount,
            this.tenantCurrency.fractionDigits,
          ),
          chargeRate: (detail.chargeRate * 100).toFixed(2),
        });
      });
  }

  /**
   * Form actions
   */
  submitForm(): void {
    this.serviceChargeForm.patchValue({
      ...FuseUltils.trimObjectValues({ ...this.serviceChargeForm.value }),
    });

    for (const i in this.serviceChargeForm.controls) {
      this.serviceChargeForm.controls[i].markAsTouched();
      this.serviceChargeForm.controls[i].updateValueAndValidity();
    }

    this._changeDetectorRef.detectChanges();
    if (this.serviceChargeForm.invalid) {
      Utils.scrollToInvalid();
      return;
    }

    this.loading = true;
    const formData = { ...this.serviceChargeForm.value };
    const serviceChargeData = {
      ...formData,
      chargeFixedAmount: !FuseUltils.isNullOrEmpty(formData.chargeFixedAmount)
        ? FuseUltils.truncateDecimalCurrency(
            formData.chargeFixedAmount,
            this.tenantCurrency.fractionDigits,
          )
        : 0,
      chargeRate: !FuseUltils.isNullOrEmpty(formData.chargeRate)
        ? (formData.chargeRate / 100).toFixed(4)
        : '0',
    };

    if (this.id) {
      this.handleUpdateServiceCharge(serviceChargeData);
    } else {
      this.handleAddServiceCharge(serviceChargeData);
    }
  }

  handleAddServiceCharge(data: any) {
    this._serviceChargeService.addServiceCharge(data).subscribe({
      next: () => {
        this.loading = false;
        this._toast.success(
          this._translocoService.translate(
            'service-charge.message.add-success',
          ),
        );
        this.navigateServiceChargeRoute('main');
      },
      error: () => {
        this.loading = false;
      },
    });
  }

  handleUpdateServiceCharge(data: any) {
    this._serviceChargeService.updateServiceCharge(this.id, data).subscribe({
      next: () => {
        this.loading = false;
        this._toast.success(
          this._translocoService.translate(
            'service-charge.message.update-success',
          ),
        );
        this.navigateServiceChargeRoute('detail');
      },
      error: () => {
        this.loading = false;
      },
    });
  }

  navigateServiceChargeRoute(pageName: string): void {
    const url =
      pageName === 'detail' ? `/${ROUTE.SERVICE_CHARGE.DETAIL}/${this.id}` : '';

    this._router.navigate([
      `${ROUTE.TENANT_CONFIGURATION.MAIN}/${ROUTE.SERVICE_CHARGE.MAIN}${url}`,
    ]);
  }

  updateErrorMessages(): void {
    this.errorMessages = {
      ...this.errorMessages,
      chargeFixedAmount: {
        ...this.errorMessages.chargeFixedAmount,
        pattern: this.tenantCurrency.fractionDigits
          ? this.errorMessages.chargeFixedAmount.pattern.replace(
              '{n}',
              `${this.tenantCurrency.fractionDigits}`,
            )
          : this.errorMessages.chargeFixedAmount.pattern
              .replace('numerical', 'integer')
              .replace(' and has no more than {n} digits in decimal', ''),
      },
    };
  }

  /**
   * Init the form group
   */
  private initForm() {
    this.serviceChargeForm = this._formBuilder.group({
      name: [null, [Validators.required, Validators.maxLength(80)]],
      chargeFixedAmount: [
        0,
        [
          Validators.min(0),
          Validators.pattern(
            this.tenantCurrency.fractionDigits
              ? REGEX.PRICE.replace(
                  'n',
                  `${this.tenantCurrency.fractionDigits}`,
                )
              : REGEX.INTEGER,
          ),
        ],
      ],
      chargeRate: [
        0,
        [Validators.min(0), Validators.pattern(REGEX.PRICE.replace('n', '2'))],
      ],
      platformOrderTypes: [null, [Validators.required]],
      isActive: [true],
      description: [null, [Validators.maxLength(255)]],
    });
  }

  /**
   * Get the service charge id to show on edit page
   */
  private getServiceChargeId() {
    this._activatedRoute.params
      .pipe(takeUntil(this._destroy$))
      .subscribe((params) => {
        this.id = params['id'];
      });
  }
}
