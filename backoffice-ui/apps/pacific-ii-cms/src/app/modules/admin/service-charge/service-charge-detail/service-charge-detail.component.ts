import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Currency } from '../../tenant-management/tenant.types';
import { Subject, takeUntil } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { ServiceChargeService } from '../service-charge.service';
import {
  PLATFORM_ORDER_TYPE_OPTIONS,
  ROLE_MODULES,
  ROLE_SCOPES,
  ROUTE,
} from 'apps/pacific-ii-cms/src/app/core/const';
import { MatIcon } from '@angular/material/icon';
import { DatePipe, NgClass } from '@angular/common';
import { UserPermissionService } from '../../../../core/user/user-permission.service';
import { MatTooltipModule } from '@angular/material/tooltip';
import { FuseHelpLinkComponent } from '@fuse/components/help-link';
import { IServiceChargeDetail } from '../service-charge.types';
import { FuseUltils } from '../../../../../../../../libs/fuse/src/lib/ultils';
import { StorageService } from '../../../../core/services/storage.service';
import { TranslocoPipe } from '@jsverse/transloco';

@Component({
  selector: 'app-service-charge-detail',
  standalone: true,
  imports: [
    NgClass,
    MatIcon,
    DatePipe,
    TranslocoPipe,
    MatTooltipModule,
    FuseHelpLinkComponent,
  ],
  templateUrl: './service-charge-detail.component.html',
})
export class ServiceChargeDetailComponent implements OnInit, OnDestroy {
  id!: string;
  serviceChargeDetail!: IServiceChargeDetail;
  tenantCurrency!: Currency;

  permissionList = {
    update: false,
  };

  utils = FuseUltils;

  private _destroy$: Subject<void> = new Subject<void>();

  constructor(
    private _router: Router,
    private _activatedRoute: ActivatedRoute,
    private _storageService: StorageService,
    private _serviceChargeService: ServiceChargeService,
    private _userPermissionService: UserPermissionService,
  ) {
    this.tenantCurrency = this._storageService.getTenantCurrency();
    this.getServiceChargeId();
    this.getPermission();
  }

  /**
   * On Init
   */
  ngOnInit(): void {
    this.handleGetServiceChargeDetail();
  }

  /**
   * On destroy
   */
  ngOnDestroy(): void {
    this._destroy$.next();
    this._destroy$.complete();
  }

  /**
   * Get ServiceCharge detail
   */
  handleGetServiceChargeDetail() {
    this._serviceChargeService
      .getServiceChargeDetail(this.id)
      .pipe(takeUntil(this._destroy$))
      .subscribe((detail) => {
        detail.platformOrderTypes = detail.platformOrderTypes.map((type) => {
          const matchedOption = PLATFORM_ORDER_TYPE_OPTIONS.find(
            (option) => option.value === type,
          );

          return matchedOption?.label ?? type;
        });
        this.serviceChargeDetail = detail;
      });
  }

  /**
   * Go to the service charge update page
   */
  gotoUpdateServiceCharge(): void {
    this._router.navigate([
      `${ROUTE.TENANT_CONFIGURATION.MAIN}/${ROUTE.SERVICE_CHARGE.MAIN}/${ROUTE.SERVICE_CHARGE.EDIT}/${this.id}`,
    ]);
  }

  /**
   * Go to the service charge management page
   */
  gotoServiceChargeManagement(): void {
    this._router.navigate([
      `${ROUTE.TENANT_CONFIGURATION.MAIN}/${ROUTE.SERVICE_CHARGE.MAIN}`,
    ]);
  }

  /**
   * Get service charge id
   */
  private getServiceChargeId(): void {
    this._activatedRoute.paramMap
      .pipe(takeUntil(this._destroy$))
      .subscribe((params: any) => {
        if (params.params.id) {
          this.id = params.params.id;
        }
      });
  }

  private getPermission(): void {
    this._userPermissionService.permissions$
      .pipe(takeUntil(this._destroy$))
      .subscribe(() => {
        this.permissionList = {
          update: this._userPermissionService.hasPermissions([
            {
              module: ROLE_MODULES.SERVICE_CHARGE_MGMT,
              scope: ROLE_SCOPES.UPDATE,
            },
          ]),
        };
      });
  }
}
