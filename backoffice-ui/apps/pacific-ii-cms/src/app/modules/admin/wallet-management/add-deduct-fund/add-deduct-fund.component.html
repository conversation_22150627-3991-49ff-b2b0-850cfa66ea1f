<div class="relative flex flex-col gap-6 max-h-[568px]">
  <div class="web__h6 text-grey-900">
    {{
      (data.action === 'add' ? 'wallet.add-fund' : 'wallet.deduct-fund')
        | transloco
    }}
  </div>
  <div class="flex flex-col gap-8 px-3 pt-2 pb-20 overflow-scroll">
    <form class="flex flex-col gap-4" [formGroup]="fundForm">
      <fuse-input
        class="w-full"
        [form]="fundForm"
        [label]="'wallet.amount-field'"
        [name]="'amount'"
        [placeholder]="''"
        [prefixText]="tenantCurrency.currencyCode"
        [prefixIconColor]="'#343330'"
        [errorMessages]="errorMessages.amount"
      />
      <fuse-textarea
        class="w-full"
        [form]="fundForm"
        [label]="'common.note'"
        [name]="'description'"
        [placeholder]="''"
        [errorMessages]="errorMessages.description"
      />
    </form>
  </div>

  <div
    class="absolute bottom-0 flex items-start justify-end w-full gap-2 p-4 mt-auto bg-white"
  >
    <button class="btn-outlined__primary__medium" (click)="onClose()">
      {{ 'common.action.cancel' | transloco }}
    </button>
    <button
      class="btn-contained__primary__medium"
      [disabled]="loading || fundForm.invalid"
      (click)="submitForm()"
    >
      @if (loading) {
        <mat-spinner diameter="18" class="mr-0"></mat-spinner>
      }
      {{ 'common.action.confirm' | transloco }}
    </button>
  </div>
</div>
