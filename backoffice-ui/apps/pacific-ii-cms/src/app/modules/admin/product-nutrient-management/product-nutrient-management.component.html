<div class="parent-page">
  <!--  Breadcrumb -->
  <div class="parent-page__header">
    <div class="flex items-center gap-2">
      Nutrient Management
      <fuse-help-link
        [url]="
          '/product-management/healthier-choice/#nutrient-management-procedure'
        "
      ></fuse-help-link>
    </div>
    <div class="parent-page__header__actions">
      @if (permissionList.action) {
        <button
          class="btn-contained__primary__medium"
          (click)="onOpenAddNutrientDialog()"
        >
          Add Nutrient
        </button>
      }
    </div>
  </div>
  <!-- Main -->
  <div class="parent-page__body">
    <!-- CONTENT GOES HERE -->
    <div class="parent-page__body__table">
      <fuse-table-component
        [dataSource]="dataSource"
        [displayedColumns]="displayedColumns"
        [total]="total"
        [pagination]="false"
        [sortDefault]="sortDefault"
        [actions]="permissionList.action ? actions : []"
        [bulkActions]="permissionList.action ? bulkActions : []"
        [rowClick]="permissionList.action ? onRowClick : undefined"
        [selectElement]="permissionList.action"
        (selectedElementChanged)="onChangeSelectedElement($event)"
        [searchForm]="searchForm"
      ></fuse-table-component>
    </div>
  </div>
</div>
