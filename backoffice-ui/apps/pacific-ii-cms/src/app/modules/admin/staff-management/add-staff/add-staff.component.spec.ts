import { provideHttpClient } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatDialogRef } from '@angular/material/dialog';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { ToastrModule } from 'ngx-toastr';
import { of, throwError } from 'rxjs';
import { FuseUltils } from '../../../../../../../../libs/fuse/src/lib/ultils';
import { provideIcons } from '../../../../core/icons/icons.provider';
import { provideTranslate } from '../../../../core/transloco/transloco.provider';
import { IDataTable } from '../../../../types/utility.types';
import { StaffService } from '../staff-management.service';
import { StaffInfo } from '../staff-management.types';
import { AddStaffComponent } from './add-staff.component';

describe('AddStaffComponent', () => {
  let component: AddStaffComponent;
  let fixture: ComponentFixture<AddStaffComponent>;

  const mockStaff: StaffInfo = {
    staffId: 118572059130307584,
    tenantId: 116024918514279424,
    cardId: '',
    staffCode: '1234567',
    name: 'Trang',
    email: '',
    type: 'CASHIER',
    status: 'ACTIVE',
    createdAt: 1728269781859,
  };

  const mockStoresResponse: IDataTable<any> = {
    content: [
      {
        storeId: '116037144468648960',
        tenantId: '116024918514279424',
        name: 'The Little Bean Rooftop',
        email: null,
        phoneNumber: '09123456789',
        addressLine1: null,
        addressLine2: null,
        city: null,
        country: null,
        status: 'ACTIVE',
        postalCode: null,
        workingHour: null,
        createdAt: 1727665411111,
      },
      {
        storeId: '116741353926476800',
        tenantId: '116024918514279424',
        name: 'Red Bean',
        email: '<EMAIL>',
        phoneNumber: null,
        addressLine1: null,
        addressLine2: null,
        city: null,
        country: null,
        status: 'TERMINATED',
        postalCode: null,
        workingHour: null,
        createdAt: 1727833307728,
      },
      {
        storeId: '117510486964629504',
        tenantId: '116024918514279424',
        name: 'White Bean',
        email: null,
        phoneNumber: null,
        addressLine1: null,
        addressLine2: null,
        city: null,
        country: null,
        status: 'ACTIVE',
        postalCode: null,
        workingHour: null,
        createdAt: 1728016683354,
      },
    ],
    totalElements: 1,
    totalPages: 1,
    page: 0,
    sort: ['id,ASC'],
  };

  const mockFormValue = {
    name: mockStaff.name,
    email: mockStaff.email,
    phoneNumber: mockStaff.phoneNumber,
    type: mockStaff.type,
    cardId: mockStaff.cardId,
    staffCode: mockStaff.staffCode,
    storeIds: mockStoresResponse.content.map((value) => value.storeId),
    pinCode: '12341234',
    retypePinCode: '12341234',
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AddStaffComponent, ToastrModule.forRoot()],
      providers: [
        provideIcons(),
        provideTranslate(),
        provideHttpClient(),
        provideAnimationsAsync(),
        {
          provide: MatDialogRef,
          useValue: {
            close: jest.fn(),
          },
        },
        {
          provide: StaffService,
          useValue: {
            getStoreList: jest.fn().mockReturnValue(of(mockStoresResponse)),
            addStaff: jest.fn().mockReturnValue(of(null)),
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(AddStaffComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    jest.spyOn(component['_ref'], 'markForCheck');

    jest.spyOn(component['_toast'], 'success');
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Lifecycle Hook', () => {
    it('should call handleGetStore and get the store detail when run ngOnInit', () => {
      jest.spyOn(component, 'handleGetStore');

      component.ngOnInit();

      expect(component.handleGetStore).toHaveBeenCalled();
    });
  });

  describe('Data Fetching', () => {
    it("should get the store list and update the store's options when run handleGetStore with storeAppend is false", () => {
      component.optionListStore = [];

      component.handleGetStore();

      expect(component['_staffService'].getStoreList).toHaveBeenCalledWith(
        FuseUltils.objectToQueryString(component.storeParams),
      );
      expect(component.optionListStore).toEqual([
        ...mockStoresResponse.content,
      ]);
      expect(component['_ref'].markForCheck).toHaveBeenCalled();
    });

    it("should get the store list and append the store's options when run handleGetStore with storeAppend is true", () => {
      component.optionListStore = [...mockStoresResponse.content];
      component.storeAppend = true;

      component.handleGetStore();

      expect(component['_staffService'].getStoreList).toHaveBeenCalledWith(
        FuseUltils.objectToQueryString(component.storeParams),
      );
      expect(component.optionListStore).toEqual([
        ...mockStoresResponse.content,
        ...mockStoresResponse.content,
      ]);
      expect(component['_ref'].markForCheck).toHaveBeenCalled();
    });
  });

  describe('Utility Function', () => {
    beforeEach(() => {
      jest.spyOn(component, 'handleGetStore');
      jest.spyOn(component, 'onClose');
    });

    it('should return not call the addStaff function if form is invalid when run submitForm', () => {
      component.staffForm.patchValue({
        name: null,
        storeIds: [],
      });
      component.submitForm();
      expect(component['_staffService'].addStaff).not.toHaveBeenCalled();
    });

    it('should call addStaff and handle success when run submitForm with valid data', () => {
      component.staffForm.patchValue(mockFormValue);
      const { retypePinCode, ...data } = mockFormValue;

      component.submitForm();

      expect(component['_staffService'].addStaff).toHaveBeenCalledWith({
        ...data,
      });
      expect(component['_toast'].success).toHaveBeenCalledWith(
        'Add staff successfully!',
      );
      expect(component.loading).toBe(false);
      expect(component.onClose).toHaveBeenCalledWith('save');
    });

    it('should handle error in submitForm', () => {
      jest
        .spyOn(component['_staffService'], 'addStaff')
        .mockReturnValueOnce(throwError(() => null));
      component.submitForm();
      expect(component.loading).toBe(false);
    });

    it('should update storeParams and call handleGetStore when run handleLazyLoadSelect', () => {
      const event = { page: 2, search: 'test' };
      component.handleLazyLoadSelect(event);
      expect(component.storeAppend).toBe(true);
      expect(component.storeParams.filter.name).toBe('test');
      expect(component.storeParams.page).toBe(2);
      expect(component.handleGetStore).toHaveBeenCalled();
    });

    it('should close the dialog when run onClose', () => {
      component.onClose('save');
      expect(component['_dialogRef'].close).toHaveBeenCalledWith('save');
    });
  });
});
