import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { API } from '../../../core/const';
import { Observable, ReplaySubject, tap } from 'rxjs';
import { IStaffAssignment } from './staff-management.types';
import { IDataResponse } from '../source-of-fund/source-of-fund.types';
import { IStore } from '../store-management/store.types';

@Injectable({ providedIn: 'root' })
export class StaffService {
  private _httpClient = inject(HttpClient);
  private _staffDetail: ReplaySubject<any> = new ReplaySubject<any>(1);

  /**
   * Getter for staff detail
   */
  get staffDetail$(): Observable<any> {
    return this._staffDetail.asObservable();
  }

  /**
   * Get staff list
   */
  getStaffList(params?: any): Observable<any> {
    return this._httpClient.get<any>(`${API.STAFF.LIST}?${params}`);
  }

  /**
   * Add new staff
   */
  addStaff(data: any): Observable<any> {
    return this._httpClient.post<any>(API.STAFF.ADD, data);
  }

  /**
   * Get staff detail
   */
  getStaffDetail(id: string): Observable<any> {
    return this._httpClient.get<any>(API.STAFF.DETAIL.replace('{id}', id)).pipe(
      tap((detail) => {
        this._staffDetail.next(detail);
      }),
    );
  }

  /**
   * Edit staff
   */
  editStaff(id: any, data: any): Observable<any> {
    return this._httpClient.put<any>(API.STAFF.EDIT.replace('{id}', id), data);
  }

  /**
   * Reset pin code
   */
  resetPinCode(id: any, data: any): Observable<any> {
    return this._httpClient.put<any>(
      API.STAFF.RESET_PINCODE.replace('{staffId}', id),
      data,
    );
  }

  /**
   * Deactivate staff
   */
  deactivateStaff(id: any): Observable<any> {
    return this._httpClient.put<any>(
      API.STAFF.DEACTIVATE.replace('{id}', id),
      {},
    );
  }

  /**
   * Activate staff
   */
  activateStaff(id: any): Observable<any> {
    return this._httpClient.put<any>(
      API.STAFF.ACTIVATE.replace('{id}', id),
      {},
    );
  }

  /**
   * Archive staff
   */
  archiveStaff(id: any): Observable<any> {
    return this._httpClient.put<any>(API.STAFF.ARCHIVE.replace('{id}', id), {});
  }

  getStoreList(params?: any): Observable<IDataResponse<IStore>> {
    return this._httpClient.get<IDataResponse<IStore>>(
      `${API.STORE.LIST}?${params}`,
    );
  }

  /**
   * Get the stores that staffs are assigned to
   */
  getAssignStores(staffIds: Array<string>) {
    return this._httpClient.get<Array<IStaffAssignment>>(
      API.STAFF.ASSIGN_STORE,
      {
        params: {
          staffIds,
        },
      },
    );
  }
}
