import {
  AfterViewInit,
  ChangeDetector<PERSON><PERSON>,
  Component,
  OnDestroy,
  OnInit,
} from '@angular/core';
import {
  FormBuilder,
  ReactiveFormsModule,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { ActivatedRoute, Router } from '@angular/router';
import { FuseHelpLinkComponent } from '@fuse/components/help-link';
import { FuseInputComponent } from '@fuse/components/input';
import { FuseSelectComponent } from '@fuse/components/select';
import { FuseUltils } from '@fuse/ultils';
import { Utils } from 'apps/pacific-ii-cms/src/app/core/utils/utils';
import { ToastrService } from 'ngx-toastr';
import { Subject, switchMap, takeUntil, tap } from 'rxjs';
import { FuseLazyLoadSelectComponent } from '../../../../../../../../libs/fuse/src/lib/components/lazy-load-select/lazy-load-select.component';
import { REGEX, ROUTE, STORE_STATUS } from '../../../../core/const';
import { IStore } from '../../store-management/store.types';
import { StaffService } from '../staff-management.service';
import { StaffInfo } from '../staff-management.types';

@Component({
  selector: 'app-edit-staff',
  standalone: true,
  imports: [
    MatIconModule,
    ReactiveFormsModule,
    MatProgressSpinnerModule,
    FuseInputComponent,
    FuseSelectComponent,
    FuseHelpLinkComponent,
    FuseLazyLoadSelectComponent,
  ],
  templateUrl: './edit-staff.component.html',
})
export class EditStaffComponent implements OnInit, OnDestroy {
  loading = false;

  id!: string;
  staffDetail!: StaffInfo;
  staffForm!: UntypedFormGroup;

  optionListRole: Array<any> = [
    { id: 'CASHIER', label: 'Cashier' },
    { id: 'SUPERVISOR', label: 'Supervisor' },
  ];

  tenantId!: string;
  optionListStore: Array<any> = [];
  storeAppend = false;
  storeParams = {
    filter: {
      name: '',
      statuses: [
        STORE_STATUS.ACTIVE,
        STORE_STATUS.PENDING,
        STORE_STATUS.SUSPENDED,
      ],
    },
    size: 10,
    page: 0,
  };
  staffStoreList: Array<IStore> = [];

  errorMessages = {
    name: {
      required: 'Name must not be empty!',
    },
    storeId: {
      required: 'Please select store!',
    },
  };

  private _unsubscribeAll: Subject<void> = new Subject<void>();

  constructor(
    private _router: Router,
    private _formBuilder: FormBuilder,
    private _activatedRoute: ActivatedRoute,
    private _staffService: StaffService,
    private _toast: ToastrService,
    private _ref: ChangeDetectorRef,
  ) {
    this.initForm();
    this.getStaffId();
    this.initStaffDetail();
  }

  ngOnInit(): void {
    this.handleGetStores();
    this._staffService
      .getStaffDetail(this.id)
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe();
  }

  /**
   * On destroy
   */
  ngOnDestroy(): void {
    this._unsubscribeAll.next();
    this._unsubscribeAll.complete();
  }

  submitForm(): void {
    for (const i in this.staffForm.controls) {
      this.staffForm.controls[i].markAsTouched();
      this.staffForm.controls[i].updateValueAndValidity();
    }

    if (this.staffForm.invalid) {
      Utils.scrollToInvalid();
      return;
    }

    this.loading = true;
    const data = this.staffForm.value;
    this._staffService.editStaff(this.id, data).subscribe({
      next: () => {
        this.loading = false;
        this._toast.success('Edit staff successfully!');
        this.gotoStaffDetail();
      },
      error: () => {
        this.loading = false;
      },
    });
  }

  gotoStaffDetail(): void {
    this._router.navigate([
      `${ROUTE.STAFF.MAIN}/${ROUTE.STAFF.DETAIL}/${this.id}`,
    ]);
  }

  handleLazyLoadSelect(event: any) {
    this.storeAppend = event.page > this.storeParams.page;
    this.storeParams.filter.name = event.search;
    this.storeParams.page = event.page;

    this.handleGetStores();
  }

  handleGetStores() {
    this._staffService
      .getStoreList(FuseUltils.objectToQueryString(this.storeParams))
      .pipe(
        tap((data) => {
          const serverDataMap = data.content;
          const updatedStore = this.storeAppend
            ? [...this.optionListStore, ...serverDataMap]
            : [...serverDataMap];

          this.optionListStore = this.combineListStore(
            updatedStore,
            this.staffStoreList,
          );
          this._ref.markForCheck();
        }),
      )
      .subscribe();
  }

  combineListStore(stores: Array<any> = [], storeList: Array<any>): Array<any> {
    let optionListStore: Array<any> = JSON.parse(JSON.stringify(stores));

    if (!this.storeParams.filter.name && this.staffStoreList) {
      optionListStore = [...storeList, ...optionListStore];
    }

    optionListStore = optionListStore.filter(
      (item, index, self) =>
        index === self.findIndex((t) => t.storeId === item.storeId),
    );

    return optionListStore;
  }

  private initStaffDetail(): void {
    this._staffService.staffDetail$
      .pipe(
        takeUntil(this._unsubscribeAll),
        tap((staffDetail: StaffInfo) => {
          this.staffForm.patchValue({
            ...staffDetail,
          });

          this.staffDetail = { ...staffDetail };
        }),
        switchMap((staffDetail: StaffInfo) => {
          return this._staffService
            .getAssignStores([staffDetail.staffId.toString()])
            .pipe(
              switchMap((value) => {
                const storeIds = value.map((data) => data.storeId);
                const params = {
                  filter: {
                    storeIds,
                  },
                  size: storeIds.length,
                };
                return this._staffService.getStoreList(
                  FuseUltils.objectToQueryString(params),
                );
              }),
            );
        }),
      )
      .subscribe((data) => {
        this.staffStoreList = data.content;
        this.staffForm.patchValue({
          storeIds: this.staffStoreList.map((value) => value.storeId),
        });

        this.optionListStore = this.combineListStore(
          this.optionListStore,
          this.staffStoreList,
        );
      });
  }

  private getStaffId(): void {
    this._activatedRoute.paramMap.subscribe((params: any) => {
      if (params.params.id) {
        this.id = params.params.id;
      }
    });
  }

  private initForm() {
    this.staffForm = this._formBuilder.group({
      name: [null, Validators.required],
      email: [
        { value: null, disabled: true },
        [Validators.pattern(REGEX.EMAIL)],
      ],
      phoneNumber: [
        { value: null, disabled: true },
        [Validators.pattern(REGEX.PHONE_NUMBER)],
      ],
      type: [null, Validators.required],
      cardId: [null, [Validators.maxLength(16)]],
      staffCode: [
        { value: null, disabled: true },
        [Validators.required, Validators.maxLength(10)],
      ],
      storeIds: [null, [Validators.required]],
    });
  }
}
