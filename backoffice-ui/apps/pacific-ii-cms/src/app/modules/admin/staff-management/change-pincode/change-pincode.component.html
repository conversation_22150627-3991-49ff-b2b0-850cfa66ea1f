<div class="relative flex flex-col gap-6 max-h-[700px]">
  <div class="web__h6 text-grey-900 flex gap-2 items-center">
    Change Pincode
    <fuse-help-link
      [url]="'/store-operation/staffmanage/#view-staff-and-edit-details'"
    ></fuse-help-link>
  </div>
  <div class="flex flex-col gap-8 px-3 pt-2 pb-20 overflow-scroll">
    <form class="flex flex-col gap-4" [formGroup]="pinCodeForm">
      <fuse-input
        class="w-full"
        [form]="pinCodeForm"
        [label]="'Pincode'"
        [name]="'pinCode'"
        [placeholder]="''"
        [type]="'password'"
        [errorMessages]="errorMessages.pinCode"
        [autocomplete]="'off'"
        ngDefaultControl
      />
      <fuse-input
        class="w-full"
        [form]="pinCodeForm"
        [label]="'Confirm Pincode'"
        [name]="'retypePinCode'"
        [placeholder]="''"
        [type]="'password'"
        [errorMessages]="errorMessages.retypePinCode"
      />
    </form>
  </div>

  <div
    class="absolute flex items-start justify-end gap-2 mt-auto bg-white w-full bottom-0 p-4"
  >
    <button class="btn-outlined__primary__medium" (click)="onClose()">
      Cancel
    </button>
    <button
      class="btn-contained__primary__medium"
      [disabled]="loading || pinCodeForm.invalid"
      (click)="submitForm()"
    >
      @if (loading) {
        <mat-spinner diameter="18" class="mr-0"></mat-spinner>
      }
      Create
    </button>
  </div>
</div>
