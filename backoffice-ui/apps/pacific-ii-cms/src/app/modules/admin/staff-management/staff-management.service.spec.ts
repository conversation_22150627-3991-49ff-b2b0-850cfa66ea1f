import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { StaffService } from './staff-management.service';
import { TestBed } from '@angular/core/testing';
import { provideHttpClient } from '@angular/common/http';
import { IStaffAssignment, StaffInfo } from './staff-management.types';
import { IDataTable } from '../../../types/utility.types';
import { API } from '../../../core/const';

describe.only('StaffService', () => {
  let service: StaffService;
  let httpClientTesting: HttpTestingController;

  const mockStaff: StaffInfo = {
    staffId: 118572059130307584,
    tenantId: 116024918514279424,
    cardId: '',
    staffCode: '1234567',
    name: 'Trang',
    email: '',
    type: 'CASHIER',
    status: 'ACTIVE',
    createdAt: 1728269781859,
  };

  const mockStaffResponse: IDataTable<StaffInfo> = {
    content: [mockStaff],
    page: 0,
    totalPages: 1,
    totalElements: 1,
    sort: [],
  };

  const mockStaffAssignments: Array<IStaffAssignment> = [
    {
      staffId: mockStaff.staffId.toString(),
      storeId: '116741353926476800',
    },
    {
      staffId: mockStaff.staffId.toString(),
      storeId: '116037144468648960',
    },
    {
      staffId: mockStaff.staffId.toString(),
      storeId: '117510486964629504',
    },
  ];

  const mockStoresResponse: IDataTable<any> = {
    content: [
      {
        storeId: '116037144468648960',
        tenantId: '116024918514279424',
        name: 'The Little Bean Rooftop',
        email: null,
        phoneNumber: '09123456789',
        addressLine1: null,
        addressLine2: null,
        city: null,
        country: null,
        status: 'ACTIVE',
        postalCode: null,
        workingHour: null,
        createdAt: 1727665411111,
      },
      {
        storeId: '116741353926476800',
        tenantId: '116024918514279424',
        name: 'Red Bean',
        email: '<EMAIL>',
        phoneNumber: null,
        addressLine1: null,
        addressLine2: null,
        city: null,
        country: null,
        status: 'TERMINATED',
        postalCode: null,
        workingHour: null,
        createdAt: 1727833307728,
      },
      {
        storeId: '117510486964629504',
        tenantId: '116024918514279424',
        name: 'White Bean',
        email: null,
        phoneNumber: null,
        addressLine1: null,
        addressLine2: null,
        city: null,
        country: null,
        status: 'ACTIVE',
        postalCode: null,
        workingHour: null,
        createdAt: 1728016683354,
      },
    ],
    totalElements: 1,
    totalPages: 1,
    page: 0,
    sort: ['id,ASC'],
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [provideHttpClient(), provideHttpClientTesting()],
    });
    service = TestBed.inject(StaffService);
    httpClientTesting = TestBed.inject(HttpTestingController);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should send request to server to get staff list when run getStaffList', (done) => {
    service.getStaffList('').subscribe((value) => {
      expect(value).toEqual(mockStaffResponse);
      done();
    });

    const req = httpClientTesting.expectOne(`${API.STAFF.LIST}?`);
    expect(req.request.method).toBe('GET');
    req.flush(mockStaffResponse);
  });

  it('should send request to server to get staff detail when run getStaffDetail', (done) => {
    service.staffDetail$.subscribe((value) => {
      expect(value).toEqual(mockStaff);
      done();
    });

    service.getStaffDetail(mockStaff.staffId.toString()).subscribe();

    const req = httpClientTesting.expectOne(
      `${API.STAFF.DETAIL.replace('{id}', mockStaff.staffId.toString())}`,
    );
    expect(req.request.method).toBe('GET');
    req.flush(mockStaff);
  });

  it('should send request to server to add new staff when run addStaff method', () => {
    service.addStaff(mockStaff).subscribe();

    const req = httpClientTesting.expectOne(API.STAFF.ADD);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual(mockStaff);
  });

  it('should send request to server to update existing Staff when run editStaff method', () => {
    service.editStaff(mockStaff.staffId, mockStaff).subscribe();

    const req = httpClientTesting.expectOne(
      API.STAFF.EDIT.replace('{id}', mockStaff.staffId.toString()),
    );
    expect(req.request.method).toBe('PUT');
    expect(req.request.body).toEqual(mockStaff);
  });

  it('should send request to server to deactivate Staff when run deactivateStaff method', () => {
    service.deactivateStaff(mockStaff.staffId).subscribe();

    const req = httpClientTesting.expectOne(
      API.STAFF.DEACTIVATE.replace('{id}', mockStaff.staffId.toString()),
    );
    expect(req.request.method).toBe('PUT');
  });

  it('should send request to server to activate Staff when run activateStaff method', () => {
    service.activateStaff(mockStaff.staffId).subscribe();

    const req = httpClientTesting.expectOne(
      API.STAFF.ACTIVATE.replace('{id}', mockStaff.staffId.toString()),
    );
    expect(req.request.method).toBe('PUT');
  });

  it('should send request to server to archive Staff when run archiveStaff method', () => {
    service.archiveStaff(mockStaff.staffId).subscribe();

    const req = httpClientTesting.expectOne(
      API.STAFF.ARCHIVE.replace('{id}', mockStaff.staffId.toString()),
    );
    expect(req.request.method).toBe('PUT');
  });

  it('should send request to server to reset pin code of Staff when run resetPinCode method', () => {
    service
      .resetPinCode(mockStaff.staffId, { pinCode: '12341234' })
      .subscribe();

    const req = httpClientTesting.expectOne(
      API.STAFF.RESET_PINCODE.replace(
        '{staffId}',
        mockStaff.staffId.toString(),
      ),
    );
    expect(req.request.method).toBe('PUT');
    expect(req.request.body).toEqual({ pinCode: '12341234' });
  });

  it('should send request to server to get assigned stores of staff when run getAssignedStores method', () => {
    service.getAssignStores([mockStaff.staffId.toString()]).subscribe();

    const req = httpClientTesting.expectOne(
      `${API.STAFF.ASSIGN_STORE}?staffIds=${mockStaff.staffId}`,
    );
    expect(req.request.method).toBe('GET');
  });

  it('should send request to server to get stores list when run getStoresList method', (done) => {
    service.getStoreList('test').subscribe((value) => {
      expect(value).toEqual(mockStoresResponse);
      done();
    });

    const req = httpClientTesting.expectOne(`${API.STORE.LIST}?test`);
    expect(req.request.method).toBe('GET');

    req.flush(mockStoresResponse);
  });
});
