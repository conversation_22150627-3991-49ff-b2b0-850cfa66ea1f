import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable, ReplaySubject, tap } from 'rxjs';
import {
  IConfigAddParams,
  ILabelConfig,
  ILabelConfigResponse,
} from './print-label.types';
import { API } from '../../../core/const';

@Injectable({
  providedIn: 'root',
})
export class PrintLabelService {
  private _httpClient = inject(HttpClient);
  private _labelConfigList = new ReplaySubject<ILabelConfigResponse>(1);
  private _configDetail = new ReplaySubject<ILabelConfig>(1);

  // -----------------------------------------------------------------------------------------------------
  // @ Accessors
  // -----------------------------------------------------------------------------------------------------

  /**
   * Getter for the label config list observable
   */
  get labelConfigList$(): Observable<ILabelConfigResponse> {
    return this._labelConfigList.asObservable();
  }

  /**
   * Getter for the label config detail observable
   */
  get configDetail$(): Observable<ILabelConfig> {
    return this._configDetail.asObservable();
  }

  // -----------------------------------------------------------------------------------------------------
  // @ Public methods
  // -----------------------------------------------------------------------------------------------------

  /**
   * Get the list of label configs
   * @param params the params to filter label config
   * @returns observable to subscribe to
   */
  getLabelConfigList(params: string): Observable<ILabelConfigResponse> {
    return this._httpClient
      .get<ILabelConfigResponse>(`${API.PRINT_LABEL.LIST}?${params}`)
      .pipe(
        tap((value: ILabelConfigResponse) => {
          this._labelConfigList.next(value);
        }),
      );
  }

  /**
   * Get label config detail
   * @param id Id of the label config you want to get
   * @returns the observable to subscribe to
   */
  getLabelConfigDetail(id: string): Observable<ILabelConfig> {
    return this._httpClient
      .get<ILabelConfig>(API.PRINT_LABEL.DETAIL.replace('{id}', id))
      .pipe(
        tap((value) => {
          this._configDetail.next(value);
        }),
      );
  }

  /**
   * Add new label config
   * @param params the data needed to create the new label config
   * @returns observable to subscribe to
   */
  addNewLabelConfig(params: IConfigAddParams): Observable<ILabelConfig> {
    return this._httpClient.post<ILabelConfig>(API.PRINT_LABEL.ADD, params);
  }

  /**
   * Edit the existing label config
   * @param id the id of label config that is edited
   * @param params the edited data of the label config
   * @returns observable to subscribe to
   */
  editLabelConfig(
    id: string,
    params: IConfigAddParams,
  ): Observable<ILabelConfig> {
    return this._httpClient.put<ILabelConfig>(
      API.PRINT_LABEL.EDIT.replace('{id}', id),
      params,
    );
  }

  /**
   * Delete label config with specific id
   * @param id Id of the label config you want to delete
   * @returns Observable to subscribe to
   */
  deleteLabelConfig(id: string): Observable<any> {
    return this._httpClient.delete(API.PRINT_LABEL.DELETE.replace('{id}', id));
  }
}
