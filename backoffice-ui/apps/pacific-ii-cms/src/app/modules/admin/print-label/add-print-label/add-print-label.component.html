<div class="flex flex-col w-full">
  <div class="page-header">
    <div class="page-header__wrapper">
      <div class="page-header__breadcrumb">
        <div class="page-header__breadcrumb__parent">
          Order Delivery Label Configuration
        </div>
        <div class="page-header__breadcrumb__dot"></div>
        <div class="page-header__breadcrumb__child">
          {{ id ? 'Edit Label' : 'Create New Label' }}
        </div>
      </div>

      <div class="page-header__title">
        <button type="button" (click)="navigateToList()">
          <mat-icon [svgIcon]="'heroicons_outline:chevron-left'"></mat-icon>
        </button>
        <div>
          {{ id ? 'Edit Label' : 'Create New Label' }}
        </div>
        <fuse-help-link
          [url]="
            '/configuration/order-delivery/#configure-order-delivery-label'
          "
        >
        </fuse-help-link>
      </div>
    </div>

    <div class="flex gap-4">
      <button class="btn-outlined__primary__medium" (click)="previewLabel()">
        <mat-icon
          class="w-5 h-5"
          [svgIcon]="'heroicons_outline:eye'"
        ></mat-icon>
        <span>Preview Label</span>
      </button>

      <button
        [ngClass]="
          id
            ? 'btn-contained__success__medium'
            : 'btn-contained__primary__medium'
        "
        (click)="submitForm()"
        [disabled]="
          loading ||
          pageSetupForm.invalid ||
          labelLayoutForm.invalid ||
          textAreaSetupForm.invalid ||
          (!pageSetupForm.dirty &&
            !labelLayoutForm.dirty &&
            !textAreaSetupForm.dirty)
        "
      >
        @if (loading) {
          <mat-spinner diameter="18" class="mr-0"></mat-spinner>
        } @else if (id) {
          <mat-icon
            class="w-5 h-5"
            [svgIcon]="'heroicons_outline:check'"
          ></mat-icon>
        }
        <span>{{ id ? 'Save' : 'Create' }}</span>
      </button>
    </div>
  </div>

  <div class="flex flex-col w-full gap-4 p-4">
    <!-- PAGE SETUP -->
    <form
      class="flex flex-col gap-4"
      [formGroup]="pageSetupForm"
      autocomplete="off"
    >
      <div class="box">
        <div class="flex items-center justify-between gap-4">
          <div class="box__header__title">Page Setup</div>
        </div>

        <div class="grid grid-cols-[25%_75%]">
          <mat-label>
            Page Name
            <span class="text-xl font-bold text-error-main"> * </span>
          </mat-label>
          <div>
            <fuse-input
              class="w-full"
              [form]="pageSetupForm"
              [name]="'pageName'"
              [placeholder]="'Enter page name'"
              [errorMessages]="errorMessages.pageName"
            />
          </div>
        </div>

        <div class="grid grid-cols-[25%_75%]">
          <mat-label>
            Page Size
            <span class="text-xl font-bold text-error-main"> * </span>
          </mat-label>
          <div class="grid grid-cols-2 gap-3">
            <fuse-input
              class="w-full"
              [form]="pageSetupForm"
              [label]="'Width (mm)'"
              [name]="'pageWidth'"
              [placeholder]="'Enter width of page'"
              [errorMessages]="errorMessages.pageSize.width"
            />
            <fuse-input
              class="w-full"
              [form]="pageSetupForm"
              [label]="'Height (mm)'"
              [name]="'pageHeight'"
              [placeholder]="'Enter height of page'"
              [errorMessages]="errorMessages.pageSize.height"
            />
          </div>
        </div>

        <div class="grid grid-cols-[25%_75%]">
          <div class="relative">
            <mat-label>
              Page Margins
              <span class="text-xl font-bold text-error-main"> * </span>
            </mat-label>

            <div class="absolute top-6 right-2">
              <button
                class="btn-outlined__primary__medium h-[42px]"
                [matTooltip]="
                  'Let the changes be applied simultaneously to the top, bottom, left, and right margins.'
                "
                (click)="linkFormFields('page')"
              >
                <mat-icon
                  class="w-5 h-5"
                  [svgIcon]="
                    !linkPageMargins.isLink
                      ? 'mat_outline:link'
                      : 'mat_outline:link_off'
                  "
                ></mat-icon>
              </button>
            </div>
          </div>

          <div class="grid grid-cols-4 gap-3">
            <fuse-input
              class="w-full"
              [form]="pageMarginsForm"
              [label]="'Top (mm)'"
              [name]="'top'"
              [placeholder]="'Enter top margin of page'"
              [errorMessages]="errorMessages.pageMargins.top"
            />
            <fuse-input
              class="w-full"
              [form]="pageMarginsForm"
              [label]="'Bottom (mm)'"
              [name]="'bottom'"
              [placeholder]="'Enter bottom margin of page'"
              [errorMessages]="errorMessages.pageMargins.bottom"
            />

            <fuse-input
              class="w-full"
              [form]="pageMarginsForm"
              [label]="'Right (mm)'"
              [name]="'right'"
              [placeholder]="'Enter right margin of page'"
              [errorMessages]="errorMessages.pageMargins.right"
            />
            <fuse-input
              class="w-full"
              [form]="pageMarginsForm"
              [label]="'Left (mm)'"
              [name]="'left'"
              [placeholder]="'Enter left margin of page'"
              [errorMessages]="errorMessages.pageMargins.left"
            />
          </div>
        </div>
      </div>
    </form>
  </div>

  <div class="flex flex-col w-full gap-4 px-4 pb-4">
    <!-- LABEL LAYOUT -->
    <form
      class="flex flex-col gap-4"
      [formGroup]="labelLayoutForm"
      autocomplete="off"
    >
      <div class="box">
        <div class="flex items-center justify-between gap-4">
          <div class="box__header__title">Label Layout</div>
        </div>

        <div class="grid grid-cols-[25%_75%]">
          <mat-label>
            Label Dimensions
            <span class="text-xl font-bold text-error-main"> * </span>
          </mat-label>
          <div class="grid grid-cols-2 gap-3">
            <fuse-input
              class="w-full"
              [form]="labelLayoutForm"
              [label]="'Height (mm)'"
              [name]="'dimensionHeight'"
              [placeholder]="'Enter height of label'"
              [errorMessages]="errorMessages.labelDimensions.height"
            />
            <fuse-input
              class="w-full"
              [form]="labelLayoutForm"
              [label]="'Number of Columns'"
              [name]="'dimensionNumberOfColumns'"
              [placeholder]="'Enter number of columns'"
              [errorMessages]="errorMessages.labelDimensions.numberOfColumns"
            />
          </div>
        </div>

        <div class="grid grid-cols-[25%_75%]">
          <div class="relative">
            <mat-label>
              Cell Margins
              <span class="text-xl font-bold text-error-main"> * </span>
            </mat-label>

            <div class="absolute top-6 right-2">
              <button
                class="btn-outlined__primary__medium h-[42px]"
                [matTooltip]="
                  'Let the changes be applied simultaneously to the top, bottom, left, and right margins.'
                "
                (click)="linkFormFields('label')"
              >
                <mat-icon
                  class="w-5 h-5"
                  [svgIcon]="
                    !linkCellMargins.isLink
                      ? 'mat_outline:link'
                      : 'mat_outline:link_off'
                  "
                ></mat-icon>
              </button>
            </div>
          </div>

          <div class="grid grid-cols-4 gap-3">
            <fuse-input
              class="w-full"
              [form]="cellMarginsForm"
              [label]="'Top (mm)'"
              [name]="'top'"
              [placeholder]="'Enter top margin of cell'"
              [errorMessages]="errorMessages.cellMargins.top"
            />
            <fuse-input
              class="w-full"
              [form]="cellMarginsForm"
              [label]="'Bottom (mm)'"
              [name]="'bottom'"
              [placeholder]="'Enter bottom margin of cell'"
              [errorMessages]="errorMessages.cellMargins.bottom"
            />

            <fuse-input
              class="w-full"
              [form]="cellMarginsForm"
              [label]="'Right (mm)'"
              [name]="'right'"
              [placeholder]="'Enter right margin of cell'"
              [errorMessages]="errorMessages.cellMargins.right"
            />
            <fuse-input
              class="w-full"
              [form]="cellMarginsForm"
              [label]="'Left (mm)'"
              [name]="'left'"
              [placeholder]="'Enter left margin of left'"
              [errorMessages]="errorMessages.cellMargins.left"
            />
          </div>
        </div>
      </div>
    </form>
  </div>

  <div class="flex flex-col w-full gap-4 px-4 pb-4">
    <!-- TEXT AREA SETUP -->
    <form
      class="flex flex-col gap-4"
      [formGroup]="textAreaSetupForm"
      autocomplete="off"
    >
      <div class="box">
        <div class="flex items-center justify-between gap-4">
          <div class="box__header__title">Text Area Setup</div>
        </div>

        <div class="grid grid-cols-[25%_75%]">
          <mat-label>
            Text Setup
            <span class="text-xl font-bold text-error-main"> * </span>
          </mat-label>

          <div class="grid grid-cols-4 gap-3">
            <fuse-input
              class="w-full"
              [form]="textAreaSetupForm"
              [label]="'Font Size (pt)'"
              [name]="'fontSize'"
              [placeholder]="'Enter font size of text'"
              [errorMessages]="errorMessages.textSetup.fontSize"
            />
            <fuse-select
              class="w-full"
              [form]="textAreaSetupForm"
              [label]="'Vertical Alignment'"
              [name]="'verticalAlignment'"
              [options]="verticalAlignmentOptions"
              [placeholder]="'Enter vertical alignment'"
              [errorMessages]="errorMessages.textSetup.verticalAlignment"
            />

            <fuse-select
              class="w-full"
              [form]="textAreaSetupForm"
              [label]="'Horizontal Alignment'"
              [name]="'horizontalAlignment'"
              [options]="horizontalAlignmentOptions"
              [placeholder]="'Enter horizontal alignment'"
              [errorMessages]="errorMessages.textSetup.horizontalAlignment"
            />
            <fuse-select
              class="w-full"
              [form]="textAreaSetupForm"
              [label]="'Text Area Border'"
              [name]="'borderType'"
              [options]="textAreaBorderOptions"
              [placeholder]="'Enter text area border'"
              [errorMessages]="errorMessages.textSetup.textAreaBorder"
            />
          </div>
        </div>

        <div class="grid grid-cols-[25%_75%]">
          <div class="relative">
            <mat-label>
              Text Area Padding
              <span class="text-xl font-bold text-error-main"> * </span>
            </mat-label>

            <div class="absolute top-6 right-2">
              <button
                class="btn-outlined__primary__medium h-[42px]"
                [matTooltip]="
                  'Let the changes be applied simultaneously to the top, bottom, left, and right paddings.'
                "
                (click)="linkFormFields('textarea')"
              >
                <mat-icon
                  class="w-5 h-5"
                  [svgIcon]="
                    !linkTextAreaPaddings.isLink
                      ? 'mat_outline:link'
                      : 'mat_outline:link_off'
                  "
                ></mat-icon>
              </button>
            </div>
          </div>

          <div class="grid grid-cols-4 gap-3">
            <fuse-input
              class="w-full"
              [form]="textAreaPaddingsForm"
              [label]="'Top (mm)'"
              [name]="'top'"
              [placeholder]="'Enter top padding of textarea'"
              [errorMessages]="errorMessages.textAreaPadding.top"
            />
            <fuse-input
              class="w-full"
              [form]="textAreaPaddingsForm"
              [label]="'Bottom (mm)'"
              [name]="'bottom'"
              [placeholder]="'Enter bottom padding of textarea'"
              [errorMessages]="errorMessages.textAreaPadding.bottom"
            />

            <fuse-input
              class="w-full"
              [form]="textAreaPaddingsForm"
              [label]="'Right (mm)'"
              [name]="'right'"
              [placeholder]="'Enter right padding of textarea'"
              [errorMessages]="errorMessages.textAreaPadding.right"
            />
            <fuse-input
              class="w-full"
              [form]="textAreaPaddingsForm"
              [label]="'Left (mm)'"
              [name]="'left'"
              [placeholder]="'Enter left padding of textarea'"
              [errorMessages]="errorMessages.textAreaPadding.left"
            />
          </div>
        </div>
      </div>
    </form>
  </div>
</div>
