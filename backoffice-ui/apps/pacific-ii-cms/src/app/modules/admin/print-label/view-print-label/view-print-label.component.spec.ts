import { ComponentFixture, TestBed } from '@angular/core/testing';

import { provideHttpClient } from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { API } from 'apps/pacific-ii-cms/src/app/core/const';
import { provideIcons } from 'apps/pacific-ii-cms/src/app/core/icons/icons.provider';
import { ILabelConfig } from '../print-label.types';
import { ViewPrintLabelComponent } from './view-print-label.component';
import { provideTranslate } from '../../../../core/transloco/transloco.provider';

describe('ViewPrintLabelComponent', () => {
  let component: ViewPrintLabelComponent;
  let fixture: ComponentFixture<ViewPrintLabelComponent>;
  let httpTestingController: HttpTestingController;

  const mockLabelConfig: ILabelConfig = {
    id: '123',
    tenantId: '',
    pageSetup: {
      pageName: 'Test',
      pageWidth: 100,
      pageHeight: 200,
      pageMarginTop: 1,
      pageMarginBottom: 1,
      pageMarginLeft: 1,
      pageMarginRight: 1,
    },
    labelLayout: {
      dimensionHeight: 16,
      dimensionNumberOfColumns: 2,
      cellMarginTop: 2,
      cellMarginBottom: 2,
      cellMarginLeft: 2,
      cellMarginRight: 2,
    },
    cellFormat: {
      fontSize: 10,
      verticalAlignment: 'ALIGN_CENTER',
      horizontalAlignment: 'ALIGN_MIDDLE',
      areaPaddingTop: 1,
      areaPaddingBottom: 1,
      areaPaddingLeft: 1,
      areaPaddingRight: 1,
      borderType: 'NONE',
    },
    createdAt: 0,
    updatedAt: 0,
  };

  const verticalAlignmentOptions = [
    {
      value: 'ALIGN_TOP',
      style: 'start',
    },
    {
      value: 'ALIGN_BOTTOM',
      style: 'end',
    },
    {
      value: 'ALIGN_MIDDLE',
      style: 'center',
    },
    {
      value: 'JUSTIFY',
      style: 'space-evenly',
    },
  ];

  const horizontalAlignmentOptions = [
    {
      value: 'ALIGN_LEFT',
      style: 'left',
    },
    {
      value: 'ALIGN_RIGHT',
      style: 'right',
    },
    {
      value: 'ALIGN_CENTER',
      style: 'center',
    },
    {
      value: 'JUSTIFY',
      style: 'justify',
    },
  ];

  const textAreaBorderOptions = [
    {
      value: 'NONE',
      style: 'none',
    },
    {
      value: 'SOLID',
      style: 'solid',
    },
    {
      value: 'DOTTED',
      style: 'dotted',
    },
    {
      value: 'DASHED',
      style: 'dashed',
    },
  ];

  const convertMillimeterToPixelFactor = 3.78;

  const mockFormatData = {
    pageSetup: mockLabelConfig.pageSetup,
    labelLayout: mockLabelConfig.labelLayout,
    cellFormat: mockLabelConfig.cellFormat,
  };

  const borderSize =
    Math.min(
      mockLabelConfig.pageSetup.pageWidth,
      mockLabelConfig.pageSetup.pageHeight,
    ) *
    0.005 *
    convertMillimeterToPixelFactor;

  const labelWidth =
    (mockLabelConfig.pageSetup.pageWidth -
      mockLabelConfig.pageSetup.pageMarginRight -
      mockLabelConfig.pageSetup.pageMarginLeft) /
      mockLabelConfig.labelLayout.dimensionNumberOfColumns -
    mockLabelConfig.labelLayout.cellMarginLeft -
    mockLabelConfig.labelLayout.cellMarginRight;

  const mockPageFormat = [
    `width: ${mockLabelConfig.pageSetup.pageWidth * convertMillimeterToPixelFactor}px`,
    `height: ${mockLabelConfig.pageSetup.pageHeight * convertMillimeterToPixelFactor}px`,
    `padding-top: ${mockLabelConfig.pageSetup.pageMarginTop * convertMillimeterToPixelFactor}px`,
    `padding-bottom: ${mockLabelConfig.pageSetup.pageMarginBottom * convertMillimeterToPixelFactor}px`,
    `padding-right: ${mockLabelConfig.pageSetup.pageMarginRight * convertMillimeterToPixelFactor}px`,
    `padding-left: ${mockLabelConfig.pageSetup.pageMarginLeft * convertMillimeterToPixelFactor}px`,
  ];

  const mockLabelFormat = [
    `height: ${mockLabelConfig.labelLayout.dimensionHeight * convertMillimeterToPixelFactor}px`,
    `width: ${labelWidth * convertMillimeterToPixelFactor}px`,
    `margin-top: ${mockLabelConfig.labelLayout.cellMarginTop * convertMillimeterToPixelFactor}px`,
    `margin-bottom: ${mockLabelConfig.labelLayout.cellMarginBottom * convertMillimeterToPixelFactor}px`,
    `margin-right: ${mockLabelConfig.labelLayout.cellMarginRight * convertMillimeterToPixelFactor}px`,
    `margin-left: ${mockLabelConfig.labelLayout.cellMarginLeft * convertMillimeterToPixelFactor}px`,
  ];

  const mockTextAreaFormat = [
    `padding-top: ${mockLabelConfig.cellFormat.areaPaddingRight * convertMillimeterToPixelFactor}px`,
    `padding-bottom: ${mockLabelConfig.cellFormat.areaPaddingRight * convertMillimeterToPixelFactor}px`,
    `padding-right: ${mockLabelConfig.cellFormat.areaPaddingRight * convertMillimeterToPixelFactor}px`,
    `padding-left: ${mockLabelConfig.cellFormat.areaPaddingLeft * convertMillimeterToPixelFactor}px`,
    `font-size: ${(mockLabelConfig.cellFormat.fontSize * convertMillimeterToPixelFactor) / 2.835}px`,
    `border-style: ${textAreaBorderOptions.find((value) => value.value === mockLabelConfig.cellFormat.borderType)?.style ?? 'none'}`,
    `border-width: ${Math.min(borderSize, 3)}px`,
    `border-radius: ${Math.min(borderSize, 3)}px`,
    `text-align: ${horizontalAlignmentOptions.find((value) => value.value === mockLabelConfig.cellFormat.horizontalAlignment)?.style ?? 'left'}`,
    `align-content: ${verticalAlignmentOptions.find((value) => value.value === mockLabelConfig.cellFormat.verticalAlignment)?.style ?? 'start'}`,
    `height: ${mockLabelConfig.labelLayout.dimensionHeight * convertMillimeterToPixelFactor}px`,
    `border-color: black`,
  ];

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ViewPrintLabelComponent],
      providers: [
        provideIcons(),
        provideTranslate(),
        provideHttpClient(),
        provideHttpClientTesting(),
        provideAnimationsAsync(),
        {
          provide: MatDialogRef,
          useValue: {
            close: jest.fn(),
          },
        },
        {
          provide: MAT_DIALOG_DATA,
          useValue: {
            id: mockLabelConfig.id,
            configData: mockFormatData,
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ViewPrintLabelComponent);
    component = fixture.componentInstance;

    httpTestingController = TestBed.inject(HttpTestingController);

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Lifecycle Hooks', () => {
    it('should call to the handleGetLabelConfig when run the ngOnInit with id', () => {
      jest.spyOn(component, 'handleGetLabelConfig');

      component.ngOnInit();

      expect(component.handleGetLabelConfig).toHaveBeenCalled();
    });

    it('should get the configData from the MAT_DIALOG_DATA constant when run the ngOnInit without id', () => {
      component.data.id = '';
      component.ngOnInit();

      expect(component.configData).toEqual(mockFormatData);
    });

    it('should unsubscribe all service when run ngOnDestroy', () => {
      const unsubscribeNextSpy = jest.spyOn(
        component['_unsubscribeAll'],
        'next',
      );
      const unsubscribeCompleteSpy = jest.spyOn(
        component['_unsubscribeAll'],
        'complete',
      );

      component.ngOnDestroy();

      expect(unsubscribeNextSpy).toHaveBeenCalled();
      expect(unsubscribeCompleteSpy).toHaveBeenCalled();
    });
  });

  describe('Data Fetching', () => {
    it('should get the label config detail when run the handleGetLabelConfig', () => {
      component.data.id = mockLabelConfig.id;
      component.handleGetLabelConfig();

      const req = httpTestingController.match(
        API.PRINT_LABEL.DETAIL.replace('{id}', mockLabelConfig.id),
      );
      req[0].flush(mockLabelConfig);

      expect(component.configData).toEqual(mockFormatData);
    });
  });

  describe('Utility Function', () => {
    it('should close the dialog when run the onClose method', () => {
      jest.spyOn(component['_dialogRef'], 'close');

      component.onClose();

      expect(component['_dialogRef'].close).toHaveBeenCalled();
    });

    it('should setup the page style when run pageSetupClass', () => {
      component.configData = mockFormatData;
      const pageStyles = component.pageSetupClass();

      expect(pageStyles).toBe(mockPageFormat.join('; '));
    });

    it('should setup the label style when run labelInitialize method', () => {
      component.configData = mockFormatData;
      component.labelsInitialize();

      expect(component.labelStyles).toBe(mockLabelFormat.join('; '));
      expect(component.textAreaStyles).toBe(mockTextAreaFormat.join('; '));
    });
  });
});
