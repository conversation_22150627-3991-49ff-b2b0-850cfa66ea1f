import { NgClass } from '@angular/common';
import { ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import {
  FormBuilder,
  ReactiveFormsModule,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatLabel } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatSpinner } from '@angular/material/progress-spinner';
import { ActivatedRoute, Router } from '@angular/router';
import { FuseInputComponent } from '@fuse/components/input';
import { FuseSelectComponent } from '@fuse/components/select';
import { REGEX, ROUTE } from 'apps/pacific-ii-cms/src/app/core/const';
import { pairwise, startWith, Subject, takeUntil } from 'rxjs';
import { ViewPrintLabelComponent } from '../view-print-label/view-print-label.component';
import { PrintLabelService } from '../print-label.service';
import { ToastrService } from 'ngx-toastr';
import { IConfigAddParams, ILabelConfig } from '../print-label.types';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Utils } from 'apps/pacific-ii-cms/src/app/core/utils/utils';
import { FuseHelpLinkComponent } from '@fuse/components/help-link';

@Component({
  selector: 'app-add-print-label',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    MatIconModule,
    FuseSelectComponent,
    FuseInputComponent,
    NgClass,
    MatSpinner,
    MatLabel,
    MatDialogModule,
    MatTooltipModule,
    FuseHelpLinkComponent,
  ],
  templateUrl: './add-print-label.component.html',
})
export class AddPrintLabelComponent implements OnDestroy, OnInit {
  pageSetupForm!: UntypedFormGroup;
  labelLayoutForm!: UntypedFormGroup;
  textAreaSetupForm!: UntypedFormGroup;

  id = '';
  loading = false;

  labelConfig: ILabelConfig | null = null;

  pageSizeDescription =
    "Ensure that the page's width and height exceed the minimum required dimensions:\n - Minimum Height: Calculated as the sum of the label's height, vertical margins (label's vertical paddings are included in label's height).\n - Minimum Width: Determined by multiplying the number of columns by the combined total of the label's horizontal margins and paddings.";
  labelSizeDescription =
    "The label height should not be less than the label's margin";

  // Form default options
  errorMessages = {
    pageName: {
      required: 'Please enter the page name!',
    },
    pageSize: {
      width: {
        required: 'Please enter the width of page!',
        pattern:
          'The width of page only accept the non-negative integer value!',
      },
      height: {
        required: 'Please enter the height of page!',
        pattern:
          'The height of page only accept the non-negative integer value!',
      },
    },
    pageMargins: {
      top: {
        required: "Please enter the page's top margin!",
        pattern: "The page's top margin only accept the non-negative integer!",
      },
      left: {
        required: "Please enter the page's left margin!",
        pattern: "The page's left margin only accept the non-negative integer!",
      },
      bottom: {
        required: "Please enter the page's bottom margin!",
        pattern:
          "The page's bottom margin only accept the non-negative integer!",
      },
      right: {
        required: "Please enter the page's right margin!",
        pattern:
          "The page's right margin only accept the non-negative integer!",
      },
    },
    labelDimensions: {
      height: {
        required: 'Please enter the height of label!',
        pattern:
          'The height of label only accept the non-negative integer value!',
      },
      numberOfColumns: {
        required: "Please enter the number of label's columns!",
        pattern: 'The number of column only accept the non-negative integer!',
      },
    },
    cellMargins: {
      top: {
        required: "Please enter the the cell's top margin!",
        pattern: "The cell's top margin only accept the non-negative integer!",
      },
      left: {
        required: "Please enter the cell's left margin!",
        pattern: "The cell's left margin only accept the non-negative integer!",
      },
      bottom: {
        required: "Please enter the cell's bottom margin!",
        pattern:
          "The cell's bottom margin only accept the non-negative integer!",
      },
      right: {
        required: "Please enter the cell's right margin!",
        pattern:
          "The cell's right margin only accept the non-negative integer!",
      },
    },
    textSetup: {
      fontSize: {
        required: 'Please enter the font size!',
        pattern: 'The font size only accept the non-negative integer value!',
      },
      verticalAlignment: {
        required: 'Please choose the vertical alignment!',
      },
      horizontalAlignment: {
        required: 'Please choose the horizontal alignment!',
      },
      textAreaBorder: {
        required: 'Please choose the text area border!',
      },
    },
    textAreaPadding: {
      top: {
        required: "Please enter the textarea's top padding!",
        pattern:
          "The textarea's top padding only accept the non-negative integer!",
      },
      left: {
        required: "Please enter the textarea's left padding!",
        pattern:
          "The textarea's left padding only accept the non-negative integer!",
      },
      bottom: {
        required: "Please enter the textarea's bottom padding!",
        pattern:
          "The textarea's bottom padding only accept the non-negative integer!",
      },
      right: {
        required: "Please enter the textarea's right padding!",
        pattern:
          "The textarea's right padding only accept the non-negative integer!",
      },
    },
  };

  verticalAlignmentOptions = [
    {
      value: 'ALIGN_TOP',
      label: 'Vertical Align Top',
    },
    {
      value: 'ALIGN_BOTTOM',
      label: 'Vertical Align Bottom',
    },
    {
      value: 'ALIGN_MIDDLE',
      label: 'Vertical Align Middle',
    },
    {
      value: 'JUSTIFY',
      label: 'Vertical Justified',
    },
  ];

  horizontalAlignmentOptions = [
    {
      value: 'ALIGN_LEFT',
      label: 'Horizontal Align Left',
    },
    {
      value: 'ALIGN_RIGHT',
      label: 'Horizontal Align Right',
    },
    {
      value: 'ALIGN_CENTER',
      label: 'Horizontal Align Center',
    },
    {
      value: 'JUSTIFY',
      label: 'Horizontal Justified',
    },
  ];

  textAreaBorderOptions = [
    {
      value: 'NONE',
      label: 'No Border',
    },
    {
      value: 'SOLID',
      label: 'Solid',
    },
    {
      value: 'DOTTED',
      label: 'Dotted',
    },
    {
      value: 'DASHED',
      label: 'Dashed',
    },
  ];

  linkPageMargins = {
    isLink: false,
    unlinkSubject: new Subject(),
    defaultValue: {
      top: '3',
      bottom: '3',
      left: '6',
      right: '6',
    },
  };

  linkCellMargins = {
    isLink: false,
    unlinkSubject: new Subject(),
    defaultValue: {
      top: '3',
      bottom: '3',
      left: '3',
      right: '3',
    },
  };

  linkTextAreaPaddings = {
    isLink: false,
    unlinkSubject: new Subject(),
    defaultValue: {
      top: '2',
      bottom: '2',
      left: '2',
      right: '2',
    },
  };

  private _unsubscribeAll: Subject<any> = new Subject();

  constructor(
    private _formBuilder: FormBuilder,
    private _activatedRoute: ActivatedRoute,
    private _router: Router,
    private _changeDetectorRef: ChangeDetectorRef,
    private _dialog: MatDialog,
    private _printLabelService: PrintLabelService,
    private _toast: ToastrService,
  ) {
    this.initForm();
    this.getLabelId();
  }

  ngOnInit(): void {
    if (this.id) {
      this.handleGetLabelConfig();
    }
  }

  ngOnDestroy(): void {
    this._unsubscribeAll.next(null);
    this._unsubscribeAll.complete();
  }

  handleGetLabelConfig() {
    this._printLabelService
      .getLabelConfigDetail(this.id)
      .subscribe((value: ILabelConfig) => {
        this.pageSetupForm.patchValue(value.pageSetup);
        this.labelLayoutForm.patchValue(value.labelLayout);
        this.textAreaSetupForm.patchValue(value.cellFormat);

        this.linkPageMargins.defaultValue = {
          top: value.pageSetup.pageMarginTop.toString(),
          bottom: value.pageSetup.pageMarginBottom.toString(),
          left: value.pageSetup.pageMarginLeft.toString(),
          right: value.pageSetup.pageMarginRight.toString(),
        };

        this.linkTextAreaPaddings.defaultValue = {
          top: value.cellFormat.areaPaddingTop.toString(),
          bottom: value.cellFormat.areaPaddingBottom.toString(),
          left: value.cellFormat.areaPaddingLeft.toString(),
          right: value.cellFormat.areaPaddingRight.toString(),
        };

        this.linkCellMargins.defaultValue = {
          top: value.labelLayout.cellMarginTop.toString(),
          bottom: value.labelLayout.cellMarginBottom.toString(),
          left: value.labelLayout.cellMarginLeft.toString(),
          right: value.labelLayout.cellMarginRight.toString(),
        };

        this.pageMarginsForm.patchValue(this.linkPageMargins.defaultValue);
        this.cellMarginsForm.patchValue(this.linkCellMargins.defaultValue);
        this.textAreaPaddingsForm.patchValue(
          this.linkTextAreaPaddings.defaultValue,
        );
      });
  }

  navigateToList() {
    this._router.navigate([
      `${ROUTE.TENANT_CONFIGURATION.MAIN}/${ROUTE.PRINT_LABEL.MAIN}`,
    ]);
  }

  previewLabel() {
    this.updateFormsValidity();

    if (
      this.pageSetupForm.invalid ||
      this.labelLayoutForm.invalid ||
      this.textAreaSetupForm.invalid
    ) {
      return;
    }

    if (this.checkLabelSize()) {
      return;
    }

    if (this.checkMinSize()) {
      return;
    }

    const configData = this.formatFormData();

    this._dialog.open(ViewPrintLabelComponent, {
      width: '60%',
      data: {
        configData: { ...configData },
      },
    });
  }

  formatFormData() {
    // Format the page setup
    const pageSetup = {
      ...this.pageSetupForm.value,
      pageMarginTop: this.pageMarginsForm.value.top,
      pageMarginBottom: this.pageMarginsForm.value.bottom,
      pageMarginLeft: this.pageMarginsForm.value.left,
      pageMarginRight: this.pageMarginsForm.value.right,
    };
    delete pageSetup.pageMargins;

    for (const key in pageSetup) {
      if (key !== 'pageName') {
        pageSetup[key] = Number(pageSetup[key]);
      }
    }

    // Format the label layout
    const labelLayout = {
      ...this.labelLayoutForm.value,
      cellMarginTop: this.cellMarginsForm.value.top,
      cellMarginBottom: this.cellMarginsForm.value.bottom,
      cellMarginLeft: this.cellMarginsForm.value.left,
      cellMarginRight: this.cellMarginsForm.value.right,
    };
    delete labelLayout.cellMargins;

    for (const key in labelLayout) {
      labelLayout[key] = Number(labelLayout[key]);
    }

    // Format the cell format
    const cellFormat = {
      ...this.textAreaSetupForm.value,
      areaPaddingTop: this.textAreaPaddingsForm.value.top,
      areaPaddingBottom: this.textAreaPaddingsForm.value.bottom,
      areaPaddingLeft: this.textAreaPaddingsForm.value.left,
      areaPaddingRight: this.textAreaPaddingsForm.value.right,
    };
    delete cellFormat.textAreaPaddings;

    for (const key in cellFormat) {
      if (key === 'fontSize' || key.includes('Padding')) {
        cellFormat[key] = Number(cellFormat[key]);
      }
    }

    return {
      pageSetup,
      labelLayout,
      cellFormat,
    };
  }

  updateFormsValidity() {
    for (const key in this.pageSetupForm.controls) {
      this.pageSetupForm.controls[key].markAsTouched();
      this.pageSetupForm.controls[key].markAsDirty();
      this.pageSetupForm.controls[key].updateValueAndValidity();
    }

    for (const key in this.labelLayoutForm.controls) {
      this.labelLayoutForm.controls[key].markAsTouched();
      this.labelLayoutForm.controls[key].markAsDirty();
      this.labelLayoutForm.controls[key].updateValueAndValidity();
    }

    for (const key in this.textAreaSetupForm.controls) {
      this.textAreaSetupForm.controls[key].markAsTouched();
      this.textAreaSetupForm.controls[key].markAsDirty();
      this.textAreaSetupForm.controls[key].updateValueAndValidity();
    }

    for (const key in this.pageMarginsForm.controls) {
      this.pageMarginsForm.controls[key].markAsTouched();
      this.pageMarginsForm.controls[key].markAsDirty();
      this.pageMarginsForm.controls[key].updateValueAndValidity();
    }

    for (const key in this.cellMarginsForm.controls) {
      this.cellMarginsForm.controls[key].markAsTouched();
      this.cellMarginsForm.controls[key].markAsDirty();
      this.cellMarginsForm.controls[key].updateValueAndValidity();
    }

    for (const key in this.textAreaPaddingsForm.controls) {
      this.textAreaPaddingsForm.controls[key].markAsTouched();
      this.textAreaPaddingsForm.controls[key].markAsDirty();
      this.textAreaPaddingsForm.controls[key].updateValueAndValidity();
    }
  }

  checkMinSize() {
    const [cellMargin, cellPadding] = [
      this.cellMarginsForm.value,
      this.textAreaPaddingsForm.value,
    ].map((value) => {
      return {
        top: Number(value.top),
        bottom: Number(value.bottom),
        right: Number(value.right),
        left: Number(value.left),
      };
    });

    const pageSize = {
      width: Number(this.pageSetupForm.value.pageWidth),
      height: Number(this.pageSetupForm.value.pageHeight),
    };

    const labelSize = {
      numberOfColumns: Number(
        this.labelLayoutForm.value.dimensionNumberOfColumns,
      ),
      dimensionHeight: Number(this.labelLayoutForm.value.dimensionHeight),
    };

    const minWidth =
      (cellMargin.left +
        cellMargin.right +
        cellPadding.left +
        cellPadding.right) *
      labelSize.numberOfColumns;
    const minHeight =
      cellMargin.top + cellMargin.bottom + labelSize.dimensionHeight;

    if (pageSize.width < minWidth) {
      this._toast.error(
        "Page width is smaller than the allowable size. Please adjust the page's dimensions, label's margin and paddings or label's number of columns!",
      );
      return true;
    }

    if (pageSize.height < minHeight) {
      this._toast.error(
        "Page height is smaller than the allowable size. Please adjust the page's dimensions or label's margin and paddings!",
      );
      return true;
    }

    return false;
  }

  checkLabelSize() {
    const labelHeight = this.labelLayoutForm.value.dimensionHeight;
    const { top, bottom } = this.textAreaPaddingsForm.value;

    if (Number(labelHeight) < Number(top) + Number(bottom)) {
      this._toast.error(
        "Label height is smaller than the text area's vertical padding. Please adjust the label's dimensions or text area's vertical paddings!",
      );
      return true;
    }

    return false;
  }

  submitForm() {
    this.updateFormsValidity();

    if (
      this.pageSetupForm.invalid ||
      this.labelLayoutForm.invalid ||
      this.textAreaSetupForm.invalid
    ) {
      {
        Utils.scrollToInvalid();
        return;
      }
    }

    if (this.checkLabelSize()) {
      return;
    }

    if (this.checkMinSize()) {
      return;
    }

    const formatValue = this.formatFormData();
    this.loading = true;

    if (this.id) {
      this.handleEditLabelConfig(formatValue);
    } else {
      this.handleAddLabelConfig(formatValue);
    }
  }

  handleEditLabelConfig(formatValue: IConfigAddParams) {
    this._printLabelService.editLabelConfig(this.id, formatValue).subscribe({
      next: () => {
        this.loading = false;
        this._toast.success('You have edited the label successfully!');
        this.navigateToList();
      },
      error: () => {
        this.loading = false;
      },
    });
  }

  handleAddLabelConfig(formatValue: IConfigAddParams) {
    this._printLabelService.addNewLabelConfig(formatValue).subscribe({
      next: () => {
        this.loading = false;
        this._toast.success('You have added the label successfully!');
        this.navigateToList();
      },
      error: () => {
        this.loading = false;
      },
    });
  }

  linkFormFields(name: string) {
    let targetForm: UntypedFormGroup;
    let targetLinkObject: {
      isLink: boolean;
      unlinkSubject: Subject<any>;
      defaultValue: any;
    };

    switch (name) {
      case 'page':
        targetForm = this.pageMarginsForm;
        targetLinkObject = this.linkPageMargins;
        break;
      case 'label':
        targetForm = this.cellMarginsForm;
        targetLinkObject = this.linkCellMargins;
        break;
      case 'textarea':
        targetForm = this.textAreaPaddingsForm;
        targetLinkObject = this.linkTextAreaPaddings;
        break;
      default: {
        return;
      }
    }

    if (targetLinkObject.isLink) {
      targetLinkObject.unlinkSubject.next(null);
      targetLinkObject.isLink = false;
    } else {
      targetForm.valueChanges
        .pipe(
          takeUntil(targetLinkObject.unlinkSubject),
          startWith(targetLinkObject.defaultValue),
          pairwise(),
        )
        .subscribe(([previousValue, currentValue]) => {
          let checkField: any;

          for (const key in previousValue) {
            if (currentValue[key] !== previousValue[key]) {
              checkField = key;
              break;
            }
          }

          Promise.resolve().then(() => {
            if (!checkField) {
              return;
            }

            for (const key in targetForm.controls) {
              if (key === checkField) {
                continue;
              }

              if (currentValue[checkField] !== targetForm.value[key]) {
                targetForm.patchValue({
                  top: currentValue[checkField],
                  bottom: currentValue[checkField],
                  right: currentValue[checkField],
                  left: currentValue[checkField],
                });

                targetLinkObject.defaultValue = {
                  top: currentValue[checkField],
                  bottom: currentValue[checkField],
                  right: currentValue[checkField],
                  left: currentValue[checkField],
                };
                break;
              }
            }
          });
        });
      targetLinkObject.isLink = true;
    }

    this.linkPageMargins = { ...this.linkPageMargins };
    this.linkCellMargins = { ...this.linkCellMargins };
    this.linkTextAreaPaddings = { ...this.linkTextAreaPaddings };

    this._changeDetectorRef.detectChanges();
  }

  get pageMarginsForm() {
    return this.pageSetupForm.controls['pageMargins'] as UntypedFormGroup;
  }

  get cellMarginsForm() {
    return this.labelLayoutForm.controls['cellMargins'] as UntypedFormGroup;
  }

  get textAreaPaddingsForm() {
    return this.textAreaSetupForm.controls[
      'textAreaPaddings'
    ] as UntypedFormGroup;
  }

  private getLabelId() {
    this._activatedRoute.paramMap.subscribe((params: any) => {
      if (params.params.id) {
        this.id = params.params.id;
      }
    });
  }

  private initForm() {
    this.pageSetupForm = this._formBuilder.group({
      pageName: [null, [Validators.required]],
      pageWidth: [
        null,
        [Validators.required, Validators.pattern(REGEX.NONNEGATIVE_INTEGER)],
      ],
      pageHeight: [
        null,
        [Validators.required, Validators.pattern(REGEX.NONNEGATIVE_INTEGER)],
      ],
      pageMargins: this._formBuilder.group({
        top: [
          null,
          [Validators.required, Validators.pattern(REGEX.NONNEGATIVE_INTEGER)],
        ],
        left: [
          null,
          [Validators.required, Validators.pattern(REGEX.NONNEGATIVE_INTEGER)],
        ],
        bottom: [
          null,
          [Validators.required, Validators.pattern(REGEX.NONNEGATIVE_INTEGER)],
        ],
        right: [
          null,
          [Validators.required, Validators.pattern(REGEX.NONNEGATIVE_INTEGER)],
        ],
      }),
    });

    this.labelLayoutForm = this._formBuilder.group({
      dimensionHeight: [
        null,
        [Validators.required, Validators.pattern(REGEX.NONNEGATIVE_INTEGER)],
      ],
      dimensionNumberOfColumns: [
        null,
        [Validators.required, Validators.pattern(REGEX.NONNEGATIVE_INTEGER)],
      ],
      cellMargins: this._formBuilder.group({
        top: [
          null,
          [Validators.required, Validators.pattern(REGEX.NONNEGATIVE_INTEGER)],
        ],
        left: [
          null,
          [Validators.required, Validators.pattern(REGEX.NONNEGATIVE_INTEGER)],
        ],
        bottom: [
          null,
          [Validators.required, Validators.pattern(REGEX.NONNEGATIVE_INTEGER)],
        ],
        right: [
          null,
          [Validators.required, Validators.pattern(REGEX.NONNEGATIVE_INTEGER)],
        ],
      }),
    });

    this.textAreaSetupForm = this._formBuilder.group({
      fontSize: [
        null,
        [Validators.required, Validators.pattern(REGEX.NONNEGATIVE_INTEGER)],
      ],
      verticalAlignment: ['ALIGN_MIDDLE', [Validators.required]],
      horizontalAlignment: ['ALIGN_CENTER', [Validators.required]],
      borderType: ['NONE', [Validators.required]],
      textAreaPaddings: this._formBuilder.group({
        top: [
          null,
          [Validators.required, Validators.pattern(REGEX.NONNEGATIVE_INTEGER)],
        ],
        left: [
          null,
          [Validators.required, Validators.pattern(REGEX.NONNEGATIVE_INTEGER)],
        ],
        bottom: [
          null,
          [Validators.required, Validators.pattern(REGEX.NONNEGATIVE_INTEGER)],
        ],
        right: [
          null,
          [Validators.required, Validators.pattern(REGEX.NONNEGATIVE_INTEGER)],
        ],
      }),
    });

    this.pageMarginsForm.patchValue(this.linkPageMargins.defaultValue);
    this.cellMarginsForm.patchValue(this.linkCellMargins.defaultValue);
    this.textAreaPaddingsForm.patchValue(
      this.linkTextAreaPaddings.defaultValue,
    );
  }
}
