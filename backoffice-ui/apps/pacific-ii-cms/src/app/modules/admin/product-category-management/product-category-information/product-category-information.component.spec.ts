import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ProductCategoryInformationComponent } from './product-category-information.component';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { ToastrModule, ToastrService } from 'ngx-toastr';
import { ActivatedRoute, Router } from '@angular/router';
import { provideHttpClient } from '@angular/common/http';
import { provideIcons } from '../../../../core/icons/icons.provider';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { of, throwError } from 'rxjs';
import { ReactiveFormsModule } from '@angular/forms';
import { provideTranslate } from '../../../../core/transloco/transloco.provider';
import { ProductCategoryManagementService } from '../product-category-management.service';
import { FuseUltils } from '../../../../../../../../libs/fuse/src/lib/ultils';
import { FuseConfirmationService } from '../../../../../../../../libs/fuse/src/lib/services/confirmation';
import { AddProductCategoryComponent } from '../add-product-category/add-product-category.component';
import { ProductCategoryDetailComponent } from '../product-category-detail/product-category-detail.component';

describe('ProductCategoryInformationComponent', () => {
  let component: ProductCategoryInformationComponent;
  let fixture: ComponentFixture<ProductCategoryInformationComponent>;

  let router: Router;
  let dialog: MatDialog;
  let toast: ToastrService;
  let activatedRoute: ActivatedRoute;
  let confirmationService: FuseConfirmationService;
  let categoriesService: ProductCategoryManagementService;

  const mockQueryParams = {
    categoryId: '1',
  };
  const mockCategoryDetail = {
    id: '1',
    name: 'Category 1',
    description: 'Description 1',
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
  };
  const mockCategoriesResponse = {
    content: [
      {
        id: '1',
        tenantId: '1',
        name: 'Category 1',
        icon: {
          path: 'path/to/icon',
          url: 'url/to/icon',
        },
        parentId: '2',
        subCategories: [],
        description: 'Description 1',
        createdAt: **********,
        updatedAt: **********,
      },
      {
        id: '2',
        tenantId: '1',
        name: 'Category 2',
        icon: {
          path: 'path/to/icon',
          url: 'url/to/icon',
        },
        parentId: null,
        subCategories: [],
        description: 'Description 2',
        createdAt: **********,
        updatedAt: **********,
      },
    ],
    totalElements: 2,
    totalPages: 1,
  };
  const mockCategoryParams = {
    filter: {},
    size: 10,
    page: 0,
    sortDirection: 'asc',
    sortFields: 'name',
  };
  const mockPermissionList = {
    update: false,
    delete: false,
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        ProductCategoryInformationComponent,
        MatDialogModule,
        ReactiveFormsModule,
        ToastrModule.forRoot(),
      ],
      providers: [
        provideIcons(),
        provideTranslate(),
        provideHttpClient(),
        provideAnimationsAsync(),
        {
          provide: ActivatedRoute,
          useValue: {
            queryParams: of({
              params: { tab: 0 },
            }),
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ProductCategoryInformationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    router = TestBed.inject(Router);
    dialog = TestBed.inject(MatDialog);
    toast = TestBed.inject(ToastrService);
    activatedRoute = TestBed.inject(ActivatedRoute);
    confirmationService = TestBed.inject(FuseConfirmationService);
    categoriesService = TestBed.inject(ProductCategoryManagementService);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Lifecycle hooks', () => {
    it('should initialize the search form correctly on constructor', () => {
      expect(component.searchForm).toEqual({
        basic: [
          {
            label: 'Search by Name',
            name: 'name',
            placeholder: 'Search...',
            type: 'text',
            prefixIcon: 'styl:MagnifyingGlassOutlineBold',
            defaultValue: '',
          },
        ],
      });
    });

    it('should call handleGetCategoriesFromParams on init', () => {
      const spy = jest.spyOn(component, 'handleGetCategoriesFromParams');
      component.ngOnInit();

      expect(spy).toHaveBeenCalled();
    });

    it('should initialize the table columns and table actions when run the ngAfterViewInit', () => {
      const initTableColumnSpy = jest.spyOn(
        Object.getPrototypeOf(component),
        'initTableColumn',
      );
      const initTableActionSpy = jest.spyOn(
        Object.getPrototypeOf(component),
        'initTableAction',
      );
      const detectChangesSpy = jest.spyOn(
        component['_changeDetectorRef'],
        'detectChanges',
      );

      component.ngAfterViewInit();

      expect(initTableColumnSpy).toHaveBeenCalled();
      expect(initTableActionSpy).toHaveBeenCalled();
      expect(detectChangesSpy).toHaveBeenCalled();
    });

    it('should unsubscribe from all subscriptions on destroy', () => {
      fixture.detectChanges();
      const nextSpy = jest.spyOn(component['_unsubscribeAll'], 'next');
      const completeSpy = jest.spyOn(component['_unsubscribeAll'], 'complete');

      component.ngOnDestroy();

      expect(nextSpy).toHaveBeenCalled();
      expect(completeSpy).toHaveBeenCalled();
    });
  });

  describe('Data Fetching', () => {
    let getCategoryDetailSpy: any;
    let handleGetCategoriesSpy: any;
    let getProductCategoriesSpy: any;
    let getCategoriesTreeSpy: any;

    beforeEach(() => {
      getCategoryDetailSpy = jest.spyOn(categoriesService, 'getCategoryDetail');
      handleGetCategoriesSpy = jest.spyOn(component, 'handleGetCategories');
      getProductCategoriesSpy = jest.spyOn(
        categoriesService,
        'getProductCategories',
      );
      getCategoriesTreeSpy = jest.spyOn(categoriesService, 'getCategoriesTree');
    });

    it('should not fetch category details when no categoryId in query params', () => {
      activatedRoute.queryParams = of({});
      component.handleGetCategoriesFromParams();
      expect(getCategoryDetailSpy).not.toHaveBeenCalled();
    });

    it('should fetch category details when categoryId in query params', () => {
      activatedRoute.queryParams = of(mockQueryParams);
      component.handleGetCategoriesFromParams();
      expect(getCategoryDetailSpy).toHaveBeenCalledWith('1');
    });

    it('should fetch sub-category list after fetching category detail successfully', () => {
      activatedRoute.queryParams = of(mockQueryParams);
      getCategoryDetailSpy.mockReturnValue(of(mockCategoryDetail));

      component.handleGetCategoriesFromParams();

      expect(getCategoryDetailSpy).toHaveBeenCalledWith('1');
      expect(component.queryParams).toEqual(mockQueryParams);
      expect(component.categoryDetail).toEqual(mockCategoryDetail);
      expect(handleGetCategoriesSpy).toHaveBeenCalled();
    });

    it('should not fetch sub-category list after fetching category detail successfully', () => {
      activatedRoute.queryParams = of(mockQueryParams);
      getCategoryDetailSpy.mockReturnValue(of(mockCategoryDetail));

      component.handleGetCategoriesFromParams();

      expect(getCategoryDetailSpy).toHaveBeenCalledWith('1');
      expect(component.queryParams).toEqual(mockQueryParams);
      expect(component.categoryDetail).toEqual(mockCategoryDetail);
      expect(handleGetCategoriesSpy).toHaveBeenCalled();
    });

    it('should update table data and total after fetching product category list successfully', () => {
      getProductCategoriesSpy.mockReturnValue(of(mockCategoriesResponse));
      component.handleGetCategories();

      expect(getProductCategoriesSpy).toHaveBeenCalledWith(
        FuseUltils.objectToQueryString(mockCategoryParams),
      );
      expect(component.dataSource).toEqual(mockCategoriesResponse.content);
      expect(component.total).toEqual(mockCategoriesResponse.totalElements);
    });

    it('should not update table data and total after fetching product category list unsuccessfully', () => {
      getProductCategoriesSpy.mockReturnValue(
        throwError(() => new Error(undefined)),
      );
      component.handleGetCategories();

      expect(getProductCategoriesSpy).toHaveBeenCalledWith(
        FuseUltils.objectToQueryString(mockCategoryParams),
      );
      expect(component.dataSource).toEqual([]);
      expect(component.total).toEqual(0);
    });

    it('should fetch product category tree', () => {
      component.handleGetCategoriesTree();
      expect(getCategoriesTreeSpy).toHaveBeenCalled();
    });
  });

  describe('Get Product Categories Params', () => {
    beforeEach(() => {
      component.categoryDetail = mockCategoryDetail;
    });

    it('should return default values when no query params provided', () => {
      const result = component.getCategoriesParams({});
      expect(result).toEqual({
        filter: {
          name: '',
          parentId: mockCategoryDetail.id,
        },
        size: 10,
        page: 0,
        sortDirection: 'asc',
        sortFields: 'name',
      });
    });

    it('should return provided values from query params', () => {
      const result = component.getCategoriesParams({
        name: 'test',
        size: 20,
        page: 2,
        sortDirection: 'desc',
        sortFields: 'createdAt',
      });
      expect(result).toEqual({
        filter: {
          name: 'test',
          parentId: mockCategoryDetail.id,
        },
        size: 20,
        page: 2,
        sortDirection: 'desc',
        sortFields: 'createdAt',
      });
    });
  });

  it('should update selectedCategories when onChangeSelectedElement is called', () => {
    expect(component.selectedCategories).toEqual([]);

    component.onChangeSelectedElement(mockCategoriesResponse.content);
    expect(component.selectedCategories).toEqual([
      mockCategoriesResponse.content[0].id,
      mockCategoriesResponse.content[1].id,
    ]);
  });

  describe('Open Dialog', () => {
    let dialogOpenSpy: any;
    let confirmationOpenSpy: any;
    let handleGetCategoriesSpy: any;
    let handleGetCategoriesTreeSpy: any;
    let getCategoryDetailSpy: any;
    let onOpenEditCategoryDialogSpy: any;
    let handleDeleteCategorySpy: any;
    let handleDeleteSelectedCategoriesSpy: any;

    const expectedAddDialogConfig = {
      width: '60%',
      data: {
        parentId: mockCategoryDetail.id,
      },
      autoFocus: false,
      disableClose: true,
    };
    const expectedEditDialogConfig = {
      width: '60%',
      data: mockCategoryDetail,
      autoFocus: false,
      disableClose: true,
    };
    const expectedViewDialogConfig = {
      width: '60%',
      data: {
        category: mockCategoriesResponse.content[0],
        permissionList: mockPermissionList,
        mainCategory: mockCategoryDetail,
      },
      autoFocus: false,
    };
    const expectedConfirmationConfig = {
      title: 'Delete',
      message: 'Please confirm to delete this category.',
      icon: {
        show: false,
      },
      actions: {
        confirm: {
          label: 'Delete',
        },
        cancel: {
          label: 'Close',
        },
      },
      dismissible: false,
    };

    beforeEach(() => {
      dialogOpenSpy = jest.spyOn(dialog, 'open');
      confirmationOpenSpy = jest.spyOn(confirmationService, 'open');
      handleGetCategoriesSpy = jest.spyOn(component, 'handleGetCategories');
      handleGetCategoriesTreeSpy = jest.spyOn(
        component,
        'handleGetCategoriesTree',
      );
      getCategoryDetailSpy = jest.spyOn(categoriesService, 'getCategoryDetail');
      onOpenEditCategoryDialogSpy = jest.spyOn(
        component,
        'onOpenEditCategoryDialog',
      );
      handleDeleteCategorySpy = jest.spyOn(component, 'handleDeleteCategory');
      handleDeleteSelectedCategoriesSpy = jest.spyOn(
        component,
        'handleDeleteSelectedCategories',
      );

      component.categoryId = mockCategoryDetail.id;
      component.categoryDetail = mockCategoryDetail;
      component.permissionList = mockPermissionList;
    });

    it('should open add sub-category dialog with correct configuration', () => {
      component.onOpenAddSubCategoryDialog();

      expect(dialogOpenSpy).toHaveBeenCalledWith(
        AddProductCategoryComponent,
        expectedAddDialogConfig,
      );
    });

    it('should get category list and category tree when add dialog closes with non-empty result', () => {
      dialogOpenSpy.mockReturnValue({
        afterClosed: () => of('save'),
      });
      component.onOpenAddSubCategoryDialog();

      expect(dialogOpenSpy).toHaveBeenCalledWith(
        AddProductCategoryComponent,
        expectedAddDialogConfig,
      );
      expect(handleGetCategoriesSpy).toHaveBeenCalled();
      expect(handleGetCategoriesTreeSpy).toHaveBeenCalled();
    });

    it('should not get category list and category tree when add dialog closes with empty result', () => {
      dialogOpenSpy.mockReturnValue({
        afterClosed: () => of(null),
      });
      component.onOpenAddSubCategoryDialog();

      expect(dialogOpenSpy).toHaveBeenCalledWith(
        AddProductCategoryComponent,
        expectedAddDialogConfig,
      );
      expect(handleGetCategoriesSpy).not.toHaveBeenCalled();
      expect(handleGetCategoriesTreeSpy).not.toHaveBeenCalled();
    });

    it('should open edit category dialog with correct configuration', () => {
      component.onOpenEditCategoryDialog();

      expect(dialogOpenSpy).toHaveBeenCalledWith(
        AddProductCategoryComponent,
        expectedEditDialogConfig,
      );
    });

    it('should get category list and category tree when edit dialog closes with non-empty result and type is sub-category', () => {
      dialogOpenSpy.mockReturnValue({
        afterClosed: () => of('save'),
      });
      component.onOpenEditCategoryDialog(
        mockCategoriesResponse.content[0],
        'sub-category',
      );

      expect(dialogOpenSpy).toHaveBeenCalledWith(AddProductCategoryComponent, {
        ...expectedEditDialogConfig,
        data: mockCategoriesResponse.content[0],
      });
      expect(handleGetCategoriesTreeSpy).toHaveBeenCalled();
      expect(handleGetCategoriesSpy).toHaveBeenCalled();
      expect(getCategoryDetailSpy).not.toHaveBeenCalled();
    });

    it('should get category detail and category tree when edit dialog closes with non-empty result and type is not sub-category', () => {
      dialogOpenSpy.mockReturnValue({
        afterClosed: () => of('save'),
      });
      getCategoryDetailSpy.mockReturnValue(of(mockCategoryDetail));

      component.onOpenEditCategoryDialog();

      expect(dialogOpenSpy).toHaveBeenCalledWith(
        AddProductCategoryComponent,
        expectedEditDialogConfig,
      );
      expect(handleGetCategoriesTreeSpy).toHaveBeenCalled();
      expect(handleGetCategoriesSpy).not.toHaveBeenCalled();
      expect(getCategoryDetailSpy).toHaveBeenCalledWith(mockCategoryDetail.id);
      expect(component.categoryDetail).toEqual(mockCategoryDetail);
    });

    it('should not category tree and get category list or category detail when edit dialog closes with empty result', () => {
      dialogOpenSpy.mockReturnValue({
        afterClosed: () => of(null),
      });
      component.onOpenEditCategoryDialog();

      expect(dialogOpenSpy).toHaveBeenCalledWith(
        AddProductCategoryComponent,
        expectedEditDialogConfig,
      );
      expect(handleGetCategoriesSpy).not.toHaveBeenCalled();
      expect(handleGetCategoriesTreeSpy).not.toHaveBeenCalled();
      expect(getCategoryDetailSpy).not.toHaveBeenCalled();
    });

    it('should open view sub-category dialog with correct configuration', () => {
      component.onOpenViewCategoryDetailDialog(
        mockCategoriesResponse.content[0],
      );

      expect(dialogOpenSpy).toHaveBeenCalledWith(
        ProductCategoryDetailComponent,
        expectedViewDialogConfig,
      );
    });

    it('should open edit category dialog when view dialog closes with non-empty result', () => {
      dialogOpenSpy.mockReturnValue({
        afterClosed: () => of('save'),
      });
      component.onOpenViewCategoryDetailDialog(
        mockCategoriesResponse.content[0],
      );

      expect(dialogOpenSpy).toHaveBeenCalledWith(
        ProductCategoryDetailComponent,
        expectedViewDialogConfig,
      );
      expect(onOpenEditCategoryDialogSpy).toHaveBeenCalledWith(
        mockCategoriesResponse.content[0],
        'sub-category',
      );
    });

    it('should not open edit category dialog when view dialog closes with empty result', () => {
      dialogOpenSpy.mockReturnValue({
        afterClosed: () => of(null),
      });
      component.onOpenViewCategoryDetailDialog(
        mockCategoriesResponse.content[0],
      );

      expect(dialogOpenSpy).toHaveBeenCalledWith(
        ProductCategoryDetailComponent,
        expectedViewDialogConfig,
      );
      expect(onOpenEditCategoryDialogSpy).not.toHaveBeenCalled();
    });

    it('should open delete category confirmation dialog with correct configuration', () => {
      confirmationOpenSpy.mockReturnValue({
        afterClosed: () => of('confirmed'),
      });
      component.onOpenDeleteCategoryDialog(mockCategoriesResponse.content[0]);

      expect(confirmationOpenSpy).toHaveBeenCalledWith(
        expectedConfirmationConfig,
      );
    });

    it('should delete category when confirmation dialog closes with result is confirmed', () => {
      confirmationOpenSpy.mockReturnValue({
        afterClosed: () => of('confirmed'),
      });
      component.onOpenDeleteCategoryDialog(mockCategoriesResponse.content[0]);

      expect(handleDeleteCategorySpy).toHaveBeenCalledWith(
        mockCategoriesResponse.content[0],
      );
    });

    it('should not delete category when confirmation dialog closes with result is cancelled', () => {
      confirmationOpenSpy.mockReturnValue({
        afterClosed: () => of('cancelled'),
      });
      component.onOpenDeleteCategoryDialog(mockCategoriesResponse.content[0]);

      expect(handleDeleteCategorySpy).not.toHaveBeenCalled();
    });

    it('should open delete multi category confirmation dialog with correct configuration', () => {
      confirmationOpenSpy.mockReturnValue({
        afterClosed: () => of('confirmed'),
      });
      component.onOpenDeleteSelectedCategoriesDialog();

      expect(confirmationOpenSpy).toHaveBeenCalledWith({
        ...expectedConfirmationConfig,
        message: 'Please confirm to delete the selected categories.',
      });
    });

    it('should delete multi category when confirmation dialog closes with result is confirmed', () => {
      confirmationOpenSpy.mockReturnValue({
        afterClosed: () => of('confirmed'),
      });
      component.onOpenDeleteSelectedCategoriesDialog();

      expect(handleDeleteSelectedCategoriesSpy).toHaveBeenCalled();
    });

    it('should not delete multi category when confirmation dialog closes with result is cancelled', () => {
      confirmationOpenSpy.mockReturnValue({
        afterClosed: () => of('cancelled'),
      });
      component.onOpenDeleteSelectedCategoriesDialog();

      expect(handleDeleteSelectedCategoriesSpy).not.toHaveBeenCalled();
    });
  });

  describe('Delete Product Category', () => {
    let deleteCategorySpy: any;
    let toastSuccessSpy: any;
    let handleGetCategoriesTreeSpy: any;
    let handleGetCategoriesSpy: any;
    let navigateSpy: any;

    beforeEach(() => {
      deleteCategorySpy = jest.spyOn(categoriesService, 'deleteCategory');
      toastSuccessSpy = jest.spyOn(toast, 'success');
      handleGetCategoriesTreeSpy = jest.spyOn(
        component,
        'handleGetCategoriesTree',
      );
      handleGetCategoriesSpy = jest.spyOn(component, 'handleGetCategories');
      navigateSpy = jest.spyOn(router, 'navigate');

      component.categoryId = mockCategoryDetail.id;
      component.categoryDetail = mockCategoryDetail;
      component.selectedCategories = [
        mockCategoriesResponse.content[0].id,
        mockCategoriesResponse.content[1].id,
      ];
    });

    it('should show success toast and get category tree and get category list after deleting category successfully and category is sub-category', () => {
      deleteCategorySpy.mockReturnValue(of({}));

      component.handleDeleteCategory(mockCategoriesResponse.content[0]);

      expect(deleteCategorySpy).toHaveBeenCalledWith(
        mockCategoriesResponse.content[0].id,
      );
      expect(toastSuccessSpy).toHaveBeenCalledWith(
        'Delete the category success!',
      );
      expect(handleGetCategoriesTreeSpy).toHaveBeenCalled();
      expect(handleGetCategoriesSpy).toHaveBeenCalled();
      expect(navigateSpy).not.toHaveBeenCalledWith();
    });

    it('should show success toast and get category tree and update router after deleting category successfully and category is main-category', () => {
      deleteCategorySpy.mockReturnValue(of({}));

      component.handleDeleteCategory();

      expect(deleteCategorySpy).toHaveBeenCalledWith(mockCategoryDetail.id);
      expect(toastSuccessSpy).toHaveBeenCalledWith(
        'Delete the category success!',
      );
      expect(handleGetCategoriesTreeSpy).toHaveBeenCalled();
      expect(handleGetCategoriesSpy).not.toHaveBeenCalled();
      expect(navigateSpy).toHaveBeenCalledWith([], {
        queryParams: {
          categoryId: '',
        },
        queryParamsHandling: 'merge',
        preserveFragment: true,
      });
    });

    it('should not show success toast after deleting category unsuccessfully', () => {
      deleteCategorySpy.mockReturnValue(throwError(() => new Error('Error')));

      component.handleDeleteCategory();

      expect(deleteCategorySpy).toHaveBeenCalledWith(mockCategoryDetail.id);
      expect(toastSuccessSpy).not.toHaveBeenCalled();
      expect(handleGetCategoriesSpy).not.toHaveBeenCalled();
      expect(handleGetCategoriesTreeSpy).not.toHaveBeenCalled();
      expect(navigateSpy).not.toHaveBeenCalled();
    });

    it('should show success toast and get category tree and get category list after deleting multi category successfully', () => {
      deleteCategorySpy.mockReturnValue(of({}));

      component.handleDeleteSelectedCategories();

      expect(deleteCategorySpy).toHaveBeenCalledTimes(2);
      expect(deleteCategorySpy).toHaveBeenCalledWith(
        mockCategoriesResponse.content[0].id,
      );
      expect(deleteCategorySpy).toHaveBeenCalledWith(
        mockCategoriesResponse.content[1].id,
      );
      expect(handleGetCategoriesSpy).toHaveBeenCalled();
      expect(handleGetCategoriesTreeSpy).toHaveBeenCalled();
      expect(toastSuccessSpy).toHaveBeenCalledWith(
        'Selected categories have been deleted successfully!',
      );
    });

    it('should get category tree and get category list and not show success toast after deleting multi category unsuccessfully', () => {
      deleteCategorySpy.mockReturnValue(throwError(() => new Error('Error')));

      component.handleDeleteSelectedCategories();

      expect(deleteCategorySpy).toHaveBeenCalledTimes(2);
      expect(deleteCategorySpy).toHaveBeenCalledWith(
        mockCategoriesResponse.content[0].id,
      );
      expect(deleteCategorySpy).toHaveBeenCalledWith(
        mockCategoriesResponse.content[1].id,
      );
      expect(handleGetCategoriesSpy).toHaveBeenCalled();
      expect(handleGetCategoriesTreeSpy).toHaveBeenCalled();
      expect(toastSuccessSpy).not.toHaveBeenCalled();
    });
  });

  it('should open view category dialog when clicking on row', () => {
    const spy = jest.spyOn(component, 'onOpenViewCategoryDetailDialog');
    component.onRowClick(mockCategoriesResponse.content[0]);
    expect(spy).toHaveBeenCalledWith(mockCategoriesResponse.content[0]);
  });

  describe('Table Column Initialization', () => {
    it('should initialize table columns', () => {
      component['initTableColumn']();

      expect(component.displayedColumns).toEqual([
        {
          key: 'name',
          name: 'Category Name',
          selected: true,
        },
        {
          key: 'description',
          name: 'Description',
          selected: true,
          renderHtml: expect.any(Function),
        },
        {
          key: 'createdAt',
          name: 'Creation Date',
          selected: true,
          render: expect.any(Function),
        },
      ]);
    });

    it('should render HTML correctly for description column', () => {
      component['initTableColumn']();
      const descriptionColumn = component.displayedColumns.find(
        (col: any) => col.key === 'description',
      );
      const renderedHtml = descriptionColumn?.renderHtml('Description 1');

      expect(renderedHtml).toEqual(
        '<div class="break-words"> Description 1 </div>',
      );
    });

    it('should render HTML correctly for creation date column', () => {
      component['initTableColumn']();
      const createdAtColumn = component.displayedColumns.find(
        (col: any) => col.key === 'createdAt',
      );
      const renderedHtml = createdAtColumn?.render(1734416501942);

      expect(renderedHtml).toEqual('17/12/2024');
    });
  });

  describe('Table Action Initialization', () => {
    const mockRowDetail = mockCategoriesResponse.content[0];

    beforeEach(() => {
      component.permissionList = mockPermissionList;
    });

    it('should initialize table actions', () => {
      component['initTableAction']();

      expect(component.actions).toEqual([
        {
          name: 'View',
          type: 'event',
          callback: expect.any(Function),
        },
        {
          name: 'Edit',
          type: 'event',
          hidden: expect.any(Function),
          callback: expect.any(Function),
        },
        {
          name: 'Delete',
          type: 'event',
          hidden: expect.any(Function),
          callback: expect.any(Function),
        },
      ]);

      expect(component.bulkActions).toEqual([
        {
          name: 'Delete',
          type: 'event',
          hidden: expect.any(Function),
          callback: expect.any(Function),
        },
      ]);
    });

    it('should call function onOpenViewCategoryDetailDialog after clicking view action', () => {
      const spy = jest.spyOn(component, 'onOpenViewCategoryDetailDialog');

      component['initTableAction']();
      const previewAction = component.actions.find(
        (col: any) => col.name === 'View',
      );

      if (previewAction?.callback) {
        previewAction.callback(mockRowDetail);
        expect(spy).toHaveBeenCalledWith(mockRowDetail);
      }
    });

    it('should call function onOpenEditCategoryDialog after clicking edit action', () => {
      const spy = jest.spyOn(component, 'onOpenEditCategoryDialog');

      component['initTableAction']();
      const editAction = component.actions.find(
        (col: any) => col.name === 'Edit',
      );

      if (editAction?.hidden) {
        const result = editAction.hidden(mockRowDetail);
        expect(result).toBeTruthy();
      }

      if (editAction?.callback) {
        editAction.callback(mockRowDetail);
        expect(spy).toHaveBeenCalledWith(mockRowDetail, 'sub-category');
      }
    });

    it('should call function onOpenDeleteCategoryDialog after clicking delete action', () => {
      const spy = jest.spyOn(component, 'onOpenDeleteCategoryDialog');

      component['initTableAction']();
      const deleteAction = component.actions.find(
        (col: any) => col.name === 'Delete',
      );

      if (deleteAction?.hidden) {
        const result = deleteAction.hidden(mockRowDetail);
        expect(result).toBeTruthy();
      }

      if (deleteAction?.callback) {
        deleteAction.callback(mockRowDetail);
        expect(spy).toHaveBeenCalledWith(mockRowDetail);
      }
    });

    it('should call function onOpenDeleteSelectedCategoriesDialog after clicking delete bulk action', () => {
      const spy = jest.spyOn(component, 'onOpenDeleteSelectedCategoriesDialog');

      component['initTableAction']();
      const deleteAction = component.bulkActions.find(
        (col: any) => col.name === 'Delete',
      );

      if (deleteAction?.hidden) {
        const result = deleteAction.hidden(mockRowDetail);
        expect(result).toBeTruthy();
      }

      if (deleteAction?.callback) {
        deleteAction.callback();
        expect(spy).toHaveBeenCalled();
      }
    });
  });
});
