import { TestBed } from '@angular/core/testing';

import { provideHttpClient } from '@angular/common/http';
import {
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TransactionHistoryService } from './transaction-history.service';

import { FuseUltils } from '@fuse/ultils';
import { API } from '../../../core/const';
import {
  ITransactionHistory,
  ITransactionHistoryResponse,
} from './transaction-history.types';

describe.only('TransactionHistoryService', () => {
  let service: TransactionHistoryService;
  let httpClientTesting: HttpTestingController;

  /**
   * Mock value return from http
   */
  const mockTransactionHistoryList: ITransactionHistoryResponse = {
    content: [
      {
        transactionId: '122577099144884224',
        tenantId: '115697404415799296',
        customerId: '116039188868036608',
        walletId: '119777210019456000',
        sourceWalletId: null,
        destinationWalletId: 119777210019456000,
        transactionType: 'CREDIT',
        transactionCategory: 'PURCHASE',
        currency: {
          displayName: 'US Dollar',
          numericCode: 840,
          currencyCode: 'USD',
          symbol: '$',
          fractionDigits: 2,
        },
        amount: 1781,
        oldBalance: null,
        balance: null,
        createdAt: 1729224657810,
        description: null,
      },
      {
        transactionId: '122575934952894464',
        tenantId: '115697404415799296',
        customerId: '116039188868036608',
        walletId: '119777210019456000',
        sourceWalletId: null,
        destinationWalletId: 119777210019456000,
        transactionType: 'CREDIT',
        transactionCategory: 'PURCHASE',
        currency: {
          displayName: 'US Dollar',
          numericCode: 840,
          currencyCode: 'USD',
          symbol: '$',
          fractionDigits: 2,
        },
        amount: 1110,
        oldBalance: null,
        balance: null,
        createdAt: 1729224380255,
        description: null,
      },
      {
        transactionId: '121543294095952896',
        tenantId: '115697404415799296',
        customerId: '116039188868036608',
        walletId: '119777210019456000',
        sourceWalletId: null,
        destinationWalletId: 119777210019456000,
        transactionType: 'CREDIT',
        transactionCategory: 'PURCHASE',
        currency: {
          displayName: 'US Dollar',
          numericCode: 840,
          currencyCode: 'USD',
          symbol: '$',
          fractionDigits: 2,
        },
        amount: 500,
        oldBalance: null,
        balance: null,
        createdAt: 1728978179480,
        description: null,
      },
    ],
    totalElements: 3,
    totalPages: 1,
    page: 0,
    sort: ['createdAt'],
  };

  const mockTransactionHistoryDetail: ITransactionHistory = {
    transactionId: '122575934952894464',
    tenantId: '115697404415799296',
    customerId: '116039188868036608',
    walletId: '119777210019456000',
    sourceWalletId: null,
    destinationWalletId: 119777210019456000,
    transactionType: 'CREDIT',
    transactionCategory: 'PURCHASE',
    currency: {
      displayName: 'US Dollar',
      numericCode: 840,
      currencyCode: 'USD',
      symbol: '$',
      fractionDigits: 2,
    },
    amount: 1110,
    oldBalance: null,
    balance: null,
    createdAt: 1729224380255,
    description: null,
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [provideHttpClient(), provideHttpClientTesting()],
    });
    service = TestBed.inject(TransactionHistoryService);
    httpClientTesting = TestBed.inject(HttpTestingController);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should return the transaction list when we call getAllTransactionHistory function', (done) => {
    const params = FuseUltils.objectToQueryString({});
    service.getAllTransactionHistories(params).subscribe();
    const req = httpClientTesting.expectOne(
      `${API.TRANSACTION_HISTORY.MAIN}?${params}`,
    );
    expect(req.request.method).toBe('GET');

    service.transactionHistories$.subscribe((value) => {
      expect(value).toEqual(mockTransactionHistoryList);
      done();
    });

    req.flush(mockTransactionHistoryList);
  });

  it('should return the transaction detail when we call getTransactionDetail function', (done) => {
    const id = '123';
    service.getTransactionDetail(id).subscribe();
    const req = httpClientTesting.expectOne(
      `${API.TRANSACTION_HISTORY.MAIN}/${id}`,
    );
    expect(req.request.method).toBe('GET');

    service.transactionDetail$.subscribe((value) => {
      expect(value).toEqual(mockTransactionHistoryDetail);
      done();
    });

    req.flush(mockTransactionHistoryDetail);
  });
});
