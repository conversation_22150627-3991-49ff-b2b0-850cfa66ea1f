import { Component, inject, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import {
  IDetailDisplayConfig,
  ITransactionHistory,
} from '../transaction-history.types';
import { Subject } from 'rxjs';
import { FuseSafePipe } from '@fuse/pipes/safe';
import { FuseUltils } from '@fuse/ultils';

@Component({
  selector: 'app-transaction-history-detail',
  standalone: true,
  imports: [FuseSafePipe],
  templateUrl: './transaction-history-detail.component.html',
  styleUrl: './transaction-history-detail.scss',
})
export class TransactionHistoryDetailComponent implements OnInit, OnDestroy {
  data = inject(MAT_DIALOG_DATA);
  transactionDetail: ITransactionHistory | null = null;

  /**
   * Map to display general information of payment transaction detail
   */
  dataDisplay: Array<IDetailDisplayConfig> = [
    {
      label: 'Transaction Information',
      key: 'line',
    },
    {
      label: 'Transaction ID',
      key: 'transactionId',
    },
    {
      label: 'Wallet ID',
      key: 'walletId',
    },
    {
      label: 'Amount',
      key: 'amount',
      renderHtml: (value: number, data: ITransactionHistory) => {
        const textColorByType: any = {
          DEBIT: 'text-success-main',
          CREDIT: 'text-error-main',
          EXPIRED: 'text-error-main',
        };

        return `<span class="${textColorByType[data.transactionType] || ''} font-semibold">${FuseUltils.formatPrice(value, data.currency) ?? '_'}</span>`;
      },
    },
    {
      label: 'Transaction Category',
      key: 'transactionCategory',
    },
    {
      label: 'Wallet Type',
      key: 'walletType',
    },
    {
      label: 'Customer Name',
      key: 'customerName',
    },
    {
      label: 'Description',
      key: 'description',
    },
    {
      label: 'Payment Session ID',
      key: 'paymentSessionId',
    },
    {
      label: 'Payment Transaction ID',
      key: 'paymentTransactionId',
    },
    {
      label: 'Device ID',
      key: 'deviceId',
    },
    {
      label: 'Source Owner ID',
      key: 'sourceWalletId',
    },
    {
      label: 'Destination Owner ID',
      key: 'destinationWalletId',
    },
    {
      label: 'Creation Time',
      key: 'createdAt',
      renderHtml: (value: number) => {
        return FuseUltils.tsToLocalTime(value);
      },
    },
  ];

  private _unsubscribeAll: Subject<any> = new Subject<any>();

  constructor(private _dialogRef: MatDialogRef<any>) {}

  ngOnInit(): void {
    this.updateTransactionDetail();
  }

  ngOnDestroy(): void {
    this._unsubscribeAll.next(null);
    this._unsubscribeAll.complete();
  }

  updateTransactionDetail(): void {
    this.transactionDetail = this.data;

    // if (this.data.transactionType === 'CREDIT') {
    //   this.dataDisplay[2].label = 'Credit Amount';
    // } else {
    //   this.dataDisplay[2].label = 'Debit Amount';
    // }
  }

  /**
   * Close dialog
   */
  onClose() {
    this._dialogRef.close();
  }

  /**
   * Track function
   */
  trackBy(index: number, id: string) {
    return id || index;
  }

  /**
   * Format displayed data
   */
  formatData(field: IDetailDisplayConfig) {
    const value = this.transactionDetail;
    if (!value) {
      return 'N/A';
    }

    if (field.key === 'line') {
      return;
    }

    if (value[field.key] == null) {
      return 'N/A';
    }

    if (!field.renderHtml) {
      return value[field.key];
    }

    return field.renderHtml(value[field.key], value);
  }
}
