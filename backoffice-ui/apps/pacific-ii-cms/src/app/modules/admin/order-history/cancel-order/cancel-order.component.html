<div class="relative flex flex-col gap-6 max-h-[700px]">
  <div class="web__h6 text-grey-900 flex gap-2 items-center">
    Cancel {{ data?.type === 'pre-order' ? 'Pre-order' : 'Order' }}
    <fuse-help-link
      [url]="
        data?.type === 'pre-order'
          ? '/sales-and-order/order-and-pre-order-history/#view-pre-order-details'
          : '/sales-and-order/order-and-pre-order-history/#view-order-details'
      "
    ></fuse-help-link>
  </div>
  <div class="flex flex-col gap-8 px-3 pt-2 pb-20 overflow-scroll">
    <form class="flex flex-col gap-4" [formGroup]="cancelForm">
      <fuse-input
        class="w-full"
        [form]="cancelForm"
        [label]="'Reason'"
        [name]="'reason'"
        [placeholder]="''"
        [errorMessages]="errorMessages.reason"
        [autocomplete]="'Off'"
      />
      @if (checkCanRefund()) {
        <mat-checkbox
          formControlName="refundable"
          color="primary"
          class="checkbox-round"
        >
          <div>
            <span> Refund {{ formatCurrency() }} to Customer's Wallet </span>
          </div>
        </mat-checkbox>
      }
    </form>
  </div>

  <div
    class="absolute bottom-0 flex items-start justify-end w-full gap-2 p-4 mt-auto bg-white"
  >
    <button class="btn-outlined__primary__medium" (click)="onClose()">
      Cancel
    </button>
    <button
      class="btn-contained__primary__medium"
      [disabled]="loading || cancelForm.invalid"
      (click)="submitForm()"
    >
      @if (loading) {
        <mat-spinner diameter="18" class="mr-0"></mat-spinner>
      }
      Confirm
    </button>
  </div>
</div>
