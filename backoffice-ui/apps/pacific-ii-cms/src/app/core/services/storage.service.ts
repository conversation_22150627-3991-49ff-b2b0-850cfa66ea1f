import { Injectable } from '@angular/core';
import { Currency } from '../../modules/admin/tenant-management/tenant.types';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class StorageService {
  private tenantIdSubject = new BehaviorSubject<string | null>(this.getTenantId());
  tenantId$ = this.tenantIdSubject.asObservable();

  clearAll(): void {
    sessionStorage.clear();
    this.tenantIdSubject.next(null);
  }

  setUserMode(userMode: string): void {
    sessionStorage.removeItem('userMode');
    sessionStorage.setItem('userMode', userMode);
  }

  getUserMode(): any {
    if (sessionStorage.getItem('userMode')) {
      return sessionStorage.getItem('userMode');
    }
    return null;
  }

  setTenantId(id: string): void {
    sessionStorage.removeItem('tenantId');
    sessionStorage.setItem('tenantId', id);
    this.tenantIdSubject.next(id);
  }

  getTenantId(): any {
    if (sessionStorage.getItem('tenantId')) {
      return sessionStorage.getItem('tenantId');
    }
    return null;
  }

  setTenantCurrency(currency: Currency): void {
    sessionStorage.removeItem('currency');
    sessionStorage.setItem('currency', JSON.stringify(currency));
  }

  getTenantCurrency(): any {
    if (sessionStorage.getItem('currency')) {
      const currency = sessionStorage.getItem('currency');
      return currency ? JSON.parse(currency) : null;
    }
    return null;
  }
}
