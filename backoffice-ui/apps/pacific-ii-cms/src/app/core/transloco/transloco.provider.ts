import { provideTransloco } from '@jsverse/transloco';
import { TranslocoHttpLoader } from './transloco.http-loader';

export const provideTranslate = () => {
  return provideTransloco({
    config: {
      availableLangs: [
        {
          id: 'en',
          label: 'English',
        },
        {
          id: 'vn',
          label: 'Vietnamese',
        },
      ],
      defaultLang: 'en',
      fallbackLang: 'en',
      reRenderOnLangChange: true,
      prodMode: true,
    },
    loader: TranslocoHttpLoader,
  });
};
