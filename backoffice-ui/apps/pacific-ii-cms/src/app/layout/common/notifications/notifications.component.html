<!-- Notifications toggle -->
<button mat-icon-button (click)="openPanel()" #notificationsOrigin>
  @if (hasNewNotification) {
    <span
      class="absolute top-0 left-0 right-0 flex items-center justify-center h-3"
    >
      <span
        class="ml-4 mt-3.5 flex h-3 w-3 shrink-0 items-center justify-center rounded-full bg-error-main px-1"
      >
      </span>
    </span>
  }
  <mat-icon class="text-grey-500" [svgIcon]="'styl:notification'"></mat-icon>
</button>

<!-- Notifications panel -->
<ng-template #notificationsPanel>
  <div
    class="fixed inset-0 flex flex-col overflow-hidden shadow-lg sm:static sm:inset-auto sm:w-90 sm:min-w-90 sm:rounded-2xl"
  >
    <!-- Header -->
    <div
      class="flex items-center py-1 pl-6 pr-4 shrink-0 bg-white shadow-sm relative z-[2]"
    >
      <div class="mr-3 -ml-1 sm:hidden">
        <button mat-icon-button (click)="closePanel()">
          <mat-icon
            class="text-current icon-size-5"
            [svgIcon]="'heroicons_solid:x-mark'"
          ></mat-icon>
        </button>
      </div>
      <div class="web__h6">Notifications</div>
      <div class="ml-auto">
        <button
          class="text-primary"
          mat-icon-button
          [matTooltip]="'Mark all as read'"
          [disabled]="calculateUnreadCount()"
          (click)="markAllAsRead()"
        >
          <mat-icon
            class="text-current icon-size-5"
            [svgIcon]="'heroicons_solid:envelope-open'"
          ></mat-icon>
        </button>
      </div>
    </div>

    <!-- Content -->
    <div
      class="relative flex flex-col flex-auto overflow-y-auto divide-y bg-card sm:max-h-120"
    >
      @if (notifications && notifications.length) {
        <!-- Notifications -->
        <cdk-virtual-scroll-viewport
          [orientation]="'vertical'"
          [itemSize]="10"
          [style.height]="'412px'"
          [appendOnly]="true"
          #viewport
        >
          <div *cdkVirtualFor="let notification of notifications">
            <div
              class="flex border-l-4 cursor-pointer group hover:bg-gray-100 dark:hover:bg-black dark:hover:bg-opacity-5"
              [ngClass]="{
                'bg-gray-100': notification.read,
                'border-primary': !notification.read,
                'border-transparent': notification.read,
              }"
              (click)="markAsRead(notification)"
            >
              <!-- Notification with a link -->
              @if (notification.link) {
                <!-- Normal links -->
                @if (!notification.useRouter) {
                  <a
                    class="flex flex-auto py-4 pl-5 cursor-pointer"
                    [href]="notification.link"
                  >
                    <ng-container
                      *ngTemplateOutlet="notificationContent"
                    ></ng-container>
                  </a>
                }
                <!-- Router links -->
                @if (notification.useRouter) {
                  <a
                    class="flex flex-auto py-4 pl-5 cursor-pointer"
                    [routerLink]="notification.link"
                  >
                    <ng-container
                      *ngTemplateOutlet="notificationContent"
                    ></ng-container>
                  </a>
                }
              }

              <!-- Notification without a link -->
              @if (!notification.link) {
                <div class="flex flex-auto py-4 pl-5">
                  <ng-container
                    *ngTemplateOutlet="notificationContent"
                  ></ng-container>
                </div>
              }

              <!-- Actions -->
              <div class="relative flex flex-col justify-center my-5 ml-2 mr-6">
                <!-- Remove -->
                <button
                  class="w-6 h-6 min-h-6 sm:opacity-0 sm:group-hover:opacity-100"
                  mat-icon-button
                  (click)="delete(notification, $event)"
                  [matTooltip]="'Remove'"
                >
                  <mat-icon
                    class="icon-size-4"
                    [svgIcon]="'heroicons_solid:x-mark'"
                  ></mat-icon>
                </button>
              </div>
            </div>

            <!-- Notification content template -->
            <ng-template #notificationContent>
              <!-- Icon -->
              @if (notification.icon) {
                <div
                  class="flex items-center justify-center w-8 h-8 mr-4 bg-gray-100 rounded-full shrink-0 dark:bg-gray-700"
                >
                  <mat-icon class="icon-size-5" [svgIcon]="notification.icon">
                  </mat-icon>
                </div>
              }
              <!-- Title, description & time -->
              <div class="flex flex-col flex-auto gap-1">
                @if (notification.title) {
                  <div
                    [id]="'title-' + notification.id"
                    class="web__subtitle2"
                    [class.line-clamp-1]="
                      notification.expand === true ||
                      notification.expand === null ||
                      notification.expand === undefined
                    "
                    [innerHTML]="notification.title"
                  ></div>
                }
                @if (notification.content) {
                  <div
                    [id]="'content-' + notification.id"
                    class="font-normal web__subtitle2 text-grey-700"
                    [class.line-clamp-2]="
                      notification.expand === true ||
                      notification.expand === null ||
                      notification.expand === undefined
                    "
                    [innerHTML]="notification.content"
                  ></div>
                }
                <div class="flex flex-row justify-between h-6">
                  <div
                    class="flex items-center text-sm leading-none text-secondary font-onest"
                  >
                    {{ notification.createdAt | date: 'MMM dd, h:mm a' }}
                  </div>
                  @if (
                    notification &&
                    notification.expand !== null &&
                    notification.expand !== undefined
                  ) {
                    <button
                      class="flex items-center justify-center px-2 py-1 text-primary hover:font-medium font-onest"
                      (click)="
                        $event.stopPropagation();
                        notification.expand = !notification.expand
                      "
                    >
                      {{ notification.expand ? 'See More' : 'See Less' }}
                    </button>
                  }
                </div>
              </div>
            </ng-template>

            <!-- Divider -->
            <mat-divider></mat-divider>
          </div>
        </cdk-virtual-scroll-viewport>
      }

      <!-- No notifications -->
      @if (!notifications || !notifications.length) {
        <div
          class="flex flex-col items-center justify-center flex-auto px-8 py-12 sm:justify-start"
        >
          <div
            class="flex items-center justify-center rounded-full h-14 w-14 flex-0 bg-primary-100 dark:bg-primary-600"
          >
            <mat-icon
              class="text-primary-700 dark:text-primary-50"
              [svgIcon]="'heroicons_outline:bell'"
            ></mat-icon>
          </div>
          <div class="mt-5 text-2xl font-semibold tracking-tight font-onest">
            No Notification
          </div>
          <div
            class="w-full mt-1 text-center text-secondary max-w-60 text-md font-onest"
          >
            When you have notifications, they will appear here.
          </div>
        </div>
      }
    </div>
  </div>
</ng-template>
