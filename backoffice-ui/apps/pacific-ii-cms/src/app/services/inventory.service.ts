import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { API } from '../core/const';

@Injectable({ providedIn: 'root' })
export class InventoryService {
  private _httpClient = inject(HttpClient);

  // -----------------------------------------------------------------------------------------------------
  // @ Public methods
  // -----------------------------------------------------------------------------------------------------

  /**
   * Get all inventory
   */
  getInventories(queryParams: any = ''): Observable<any> {
    return this._httpClient.get<any>(`${API.INVENTORY.LIST}?${queryParams}`);
  }

  /**
   * Get inventory detail
   */
  getInventoryDetail(id: string): Observable<any> {
    return this._httpClient.get<any>(API.INVENTORY.DETAIL.replace('{id}', id));
  }

  /**
   * Update inventory
   */
  updateInventory(id: string, data: any): Observable<any> {
    return this._httpClient.post<any>(
      API.INVENTORY.UPDATE.replace('{id}', id),
      data,
    );
  }
}
