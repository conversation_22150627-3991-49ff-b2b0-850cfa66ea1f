import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';
import { AuthService } from './core/auth/auth.service';
import { UserIdleService } from 'angular-user-idle';
import { Subject, takeUntil } from 'rxjs';
import { KeycloakAngularModule } from 'keycloak-angular';

@Component({
  standalone: true,
  imports: [
    RouterOutlet,
    CommonModule,
    KeycloakAngularModule
  ],
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss'
})
export class AppComponent implements OnInit, OnDestroy {
  title = 'pacific-ii-cms';
  private destroyRef: Subject<void> = new Subject();

  constructor(
    private _authService: AuthService,
    private _userIdleService: UserIdleService
  ) {
  }

  ngOnInit(): void {
    if (this._authService.isLoggedIn()) {
      this._userIdleService
        .onTimerStart()
        .pipe(takeUntil(this.destroyRef))
        .subscribe();
      this._userIdleService
        .onTimeout()
        .pipe(takeUntil(this.destroyRef))
        .subscribe(() => {
          alert('Your session has timed out. Please log in again.');
          this._authService.logout();
          this._userIdleService.resetTimer();
        });
    }
  }


  ngOnDestroy(): void {
    this.destroyRef.next();
    this.destroyRef.complete();
  }
}
