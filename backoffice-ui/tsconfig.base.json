{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@fe-pacific-ii-monorepo/fuse": ["libs/fuse/src/index.ts"], "@fuse/*": ["libs/fuse/src/lib/*"]}}, "exclude": ["node_modules", "tmp"]}