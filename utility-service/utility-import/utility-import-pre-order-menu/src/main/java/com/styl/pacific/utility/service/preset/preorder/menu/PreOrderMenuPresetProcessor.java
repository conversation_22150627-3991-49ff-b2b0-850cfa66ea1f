/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.service.preset.preorder.menu;

import com.styl.pacific.catalog.service.shared.http.product.response.ProductResponse;
import com.styl.pacific.order.service.shared.http.mealtime.response.MealTimeResponse;
import com.styl.pacific.order.service.shared.http.preorder.menu.enums.PreOrderMenuItemStatus;
import com.styl.pacific.order.service.shared.http.preorder.menu.request.CreatePreOrderMenuItemRequest;
import com.styl.pacific.order.service.shared.http.preorder.menu.request.UpdatePreOrderMenuItemRequest;
import com.styl.pacific.order.service.shared.http.preorder.menu.response.PreOrderMenuItemLightResponse;
import com.styl.pacific.order.service.shared.http.preorder.menu.response.PreOrderMenuItemResponse;
import com.styl.pacific.order.service.shared.http.preorder.menu.response.PreOrderMenuResponse;
import com.styl.pacific.store.shared.http.responses.store.StoreResponse;
import com.styl.pacific.tenant.service.shared.http.api.dto.response.TenantResponse;
import com.styl.pacific.utility.service.PresetProcessor;
import com.styl.pacific.utility.service.ThreadLocalMap;
import com.styl.pacific.utility.service.api.dto.PresetType;
import com.styl.pacific.utility.service.domain.exception.UtilityImportException;
import com.styl.pacific.utility.service.domain.preset.PreOrderMenuPresetMetadataDto;
import com.styl.pacific.utility.service.domain.preset.Preset;
import com.styl.pacific.utility.service.domain.preset.PresetRecord;
import com.styl.pacific.utility.service.preset.preorder.menu.entity.PreOrderMenuItemDataDTO;
import com.styl.pacific.utility.service.preset.preorder.menu.helper.PreOrderMenuDataPresetHelper;
import com.styl.pacific.utility.service.preset.preorder.menu.service.PreOrderMenuDataPresetService;
import io.jsonwebtoken.lang.Strings;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class PreOrderMenuPresetProcessor extends PresetProcessor {

	private static final Logger log = LoggerFactory.getLogger(PreOrderMenuPresetProcessor.class);
	private final PreOrderMenuDataPresetHelper preOrderMenuDataPresetHelper;
	private final PreOrderMenuDataPresetService preOrderMenuDataPresetService;
	private final ThreadLocal<TenantResponse> tenantDto = new ThreadLocal<>();
	private final ThreadLocal<PreOrderMenuResponse> menuDto = new ThreadLocal<>();
	private final ThreadLocal<StoreResponse> storeDto = new ThreadLocal<>();
	private final ThreadLocalMap<String, MealTimeResponse> mealTimeMap = new ThreadLocalMap<>();
	private final ThreadLocalMap<String, ProductResponse> productMap = new ThreadLocalMap<>();
	private final ThreadLocalMap<String, ProductResponse> productNameMap = new ThreadLocalMap<>();
	private final ThreadLocal<Set<Triple<LocalDate, String, String>>> menuItemByDateAndMealTimeForProduct = ThreadLocal
			.withInitial(HashSet::new);
	private final ThreadLocalMap<Triple<LocalDate, String, String>, String> menuItemByDateAndMealTimeForPreOrderMenuItem = new ThreadLocalMap<>();

	@Override
	public PresetType supportedPresetType() {
		return PresetType.PRE_ORDER_MENU;
	}

	@Override
	protected void preProcess() {
		fetchTenant();
		fetchMenu();
		fetchStore();
		fetchMealTime();
		fetchProduct();
	}

	@Override
	protected void processSingleRecord(PresetRecord presetRecord) {
		Preset preset = super.presetContext.get();
		PreOrderMenuItemDataDTO data = preOrderMenuDataPresetHelper.parseData(headersContext, presetRecord);
		switch (preset.getStrategy()) {
		case UPSERT -> upsertRecord(data);
		case INSERT_ONLY -> insertRecord(data);
		case UPDATE_ONLY -> updateRecord(data);
		}
	}

	@Override
	protected void postComplete() {
		tenantDto.remove();
		menuDto.remove();
		storeDto.remove();
		productMap.remove();
		mealTimeMap.remove();
		productNameMap.remove();
		menuItemByDateAndMealTimeForProduct.remove();
		menuItemByDateAndMealTimeForPreOrderMenuItem.remove();
	}

	private void fetchTenant() {
		try {
			TenantResponse tenant = preOrderMenuDataPresetHelper.fetchTenant(super.presetContext.get()
					.getTenantId());
			tenantDto.set(tenant);
		} catch (Exception e) {
			log.info("Error fetching tenant", e);
			throw new UtilityImportException("Error fetching tenant");
		}
	}

	private void fetchMenu() {
		try {
			Preset preset = this.presetContext.get();
			if (Objects.isNull(preset.getMetadata())) {
				throw new UtilityImportException("Preset metadata is not set");
			}
			if (preset.getMetadata() instanceof PreOrderMenuPresetMetadataDto metadata) {
				PreOrderMenuResponse menuStubResponse = preOrderMenuDataPresetHelper.fetchMenu(metadata
						.getPreOrderMenuId()
						.getValue()
						.toString());
				menuDto.set(menuStubResponse);
			} else {
				throw new UtilityImportException("Invalid preset metadata");
			}

		} catch (Exception e) {
			log.info("Error fetching pre-order menu", e);
			throw new UtilityImportException("Error fetching pre-order menu");
		}
	}

	private void fetchStore() {
		try {
			PreOrderMenuResponse menu = menuDto.get();
			StoreResponse response = preOrderMenuDataPresetHelper.fetchStore(menu.storeId());
			storeDto.set(response);
		} catch (Exception e) {
			log.info("Error fetching store", e);
			throw new UtilityImportException("Error fetching store");
		}
	}

	private void fetchProduct() {
		try {
			List<ProductResponse> products = preOrderMenuDataPresetHelper.fetchProduct();
			for (ProductResponse product : products) {
				if (!productMap.containsKey(product.sku())) {
					productMap.put(product.sku(), product);
				}
				if (!productNameMap.containsKey(product.name()
						.toLowerCase())) {
					productNameMap.put(product.name()
							.toLowerCase(), product);
				}

			}
		} catch (Exception e) {
			log.info("Error fetching products", e);
			throw new UtilityImportException("Error fetching products");
		}
	}

	private void fetchMealTime() {
		try {
			List<MealTimeResponse> mealTimes = preOrderMenuDataPresetHelper.fetchMealTime();
			for (MealTimeResponse mealTime : mealTimes) {
				if (!mealTimeMap.containsKey(mealTime.name()
						.toLowerCase())) {
					mealTimeMap.put(mealTime.name()
							.toLowerCase(), mealTime);
				}
			}
		} catch (Exception e) {
			log.info("Error fetching meal times", e);
			throw new UtilityImportException("Error fetching meal times");
		}
	}

	private void insertRecord(PreOrderMenuItemDataDTO data) {
		Pair<ProductResponse, List<PreOrderMenuItemLightResponse>> result = createPreOrderMenuItem(data);
		importData(result);
	}

	private void updateRecord(PreOrderMenuItemDataDTO data) {
		Pair<ProductResponse, List<PreOrderMenuItemLightResponse>> result = updatePreOrderMenuItem(data);
		importData(result);
	}

	private void upsertRecord(PreOrderMenuItemDataDTO data) {
		Pair<ProductResponse, List<PreOrderMenuItemLightResponse>> result = upsertPreOrderMenuItem(data);
		importData(result);
	}

	private void importData(Pair<ProductResponse, List<PreOrderMenuItemLightResponse>> result) {
		if (result.getRight()
				.isEmpty()) {
			return;
		}
		ProductResponse product = result.getLeft();
		List<PreOrderMenuItemLightResponse> menuItems = result.getRight();
		for (PreOrderMenuItemLightResponse menuItem : menuItems) {
			var key = Triple.of(LocalDate.parse(menuItem.date()), menuItem.mealTimeId(), product.id());
			menuItemByDateAndMealTimeForProduct.get()
					.add(key);
			var keyForPreOrderMenuItem = Triple.of(LocalDate.parse(menuItem.date()), menuItem.mealTimeId(), product
					.id());
			menuItemByDateAndMealTimeForPreOrderMenuItem.put(keyForPreOrderMenuItem, menuItem.id());
		}
	}

	private Pair<ProductResponse, List<PreOrderMenuItemLightResponse>> upsertPreOrderMenuItem(
			PreOrderMenuItemDataDTO presetRecord) {
		Optional<ProductResponse> productOpt = findProduct(presetRecord);
		if (productOpt.isEmpty()) {
			throw new UtilityImportException("Product not found in Store " + Optional.ofNullable(storeDto.get())
					.map(StoreResponse::getName)
					.orElse(Strings.EMPTY));
		}
		ProductResponse product = productOpt.get();
		LocalDate date = presetRecord.date();
		String mealTimeName = presetRecord.mealTime();
		Optional<MealTimeResponse> mealTimeOpt = findMealTime(mealTimeName);
		if (mealTimeOpt.isEmpty()) {
			throw new UtilityImportException("Meal time not found");
		}
		MealTimeResponse mealTime = mealTimeOpt.get();
		Optional<String> menuItemIdOpt = getPreOrderMenuItemId(date, mealTime.id(), product.id());
		if (menuItemIdOpt.isEmpty()) {
			return createPreOrderMenuItem(presetRecord);
		} else {
			return updatePreOrderMenuItem(presetRecord);
		}
	}

	private Pair<ProductResponse, List<PreOrderMenuItemLightResponse>> updatePreOrderMenuItem(
			PreOrderMenuItemDataDTO data) {
		Optional<ProductResponse> productOpt = findProduct(data);
		if (productOpt.isEmpty()) {
			throw new UtilityImportException("Product not found in Store " + Optional.ofNullable(storeDto.get())
					.map(StoreResponse::getName)
					.orElse(Strings.EMPTY));
		}
		ProductResponse product = productOpt.get();
		LocalDate date = data.date();
		String mealTimeName = data.mealTime();
		Optional<MealTimeResponse> mealTimeOpt = findMealTime(mealTimeName);
		if (mealTimeOpt.isEmpty()) {
			throw new UtilityImportException("Meal time not found");
		}
		MealTimeResponse mealTime = mealTimeOpt.get();
		Integer capacity = data.capacity();
		PreOrderMenuResponse menu = menuDto.get();
		List<PreOrderMenuItemLightResponse> result = new ArrayList<>();
		Optional<String> menuItemIdOpt = getPreOrderMenuItemId(date, mealTime.id(), product.id());
		if (menuItemIdOpt.isEmpty()) {
			List<PreOrderMenuItemResponse> existingMenuItems = findExistPreOrderMenuItemByProduct(menu.id(), date,
					mealTime.id(), product.id());
			if (existingMenuItems.isEmpty()) {
				throw new UtilityImportException("Menu item not found");
			}
			for (PreOrderMenuItemResponse existingMenuItem : existingMenuItems) {
				var request = UpdatePreOrderMenuItemRequest.builder()
						.capacity(capacity)
						.status(existingMenuItem.status())
						.following(false)
						.build();
				result.addAll(preOrderMenuDataPresetService.updateItems(menu.id(), existingMenuItem.id(), request));
			}
		} else {
			var request = UpdatePreOrderMenuItemRequest.builder()
					.capacity(capacity)
					.status(PreOrderMenuItemStatus.ACTIVE)
					.following(false)
					.build();
			result.addAll(preOrderMenuDataPresetService.updateItems(menu.id(), menuItemIdOpt.get(), request));
		}

		return Pair.of(product, result);
	}

	private Pair<ProductResponse, List<PreOrderMenuItemLightResponse>> createPreOrderMenuItem(
			PreOrderMenuItemDataDTO data) {
		Optional<ProductResponse> productOpt = findProduct(data);
		if (productOpt.isEmpty()) {
			throw new UtilityImportException("Product not found in Store " + Optional.ofNullable(storeDto.get())
					.map(StoreResponse::getName)
					.orElse(Strings.EMPTY));
		}

		ProductResponse product = productOpt.get();
		LocalDate date = data.date();
		Integer capacity = data.capacity();
		String mealTimeName = data.mealTime();
		Optional<MealTimeResponse> mealTimeOpt = findMealTime(mealTimeName);
		if (mealTimeOpt.isEmpty()) {
			throw new UtilityImportException("Meal time not found");
		}
		MealTimeResponse mealTime = mealTimeOpt.get();
		PreOrderMenuResponse menu = menuDto.get();

		boolean exist = isMenuItemExist(date, mealTime.id(), product.id());
		if (exist) {
			throw new UtilityImportException("Menu item already exists");
		} else {
			List<PreOrderMenuItemResponse> existingMenuItems = findExistPreOrderMenuItemByProduct(menu.id(), date,
					mealTime.id(), product.id());
			if (!existingMenuItems.isEmpty()) {
				throw new UtilityImportException("Menu item already exists");
			}
		}

		CreatePreOrderMenuItemRequest request = CreatePreOrderMenuItemRequest.builder()
				.productId(product.id())
				.startDate(DateTimeFormatter.ISO_LOCAL_DATE.format(date))
				.capacity(capacity)
				.mealTimeIds(List.of(mealTime.id()))
				.repeated(false)
				.build();

		return Pair.of(product, preOrderMenuDataPresetService.createItems(menuDto.get()
				.id(), request));
	}

	private boolean isMenuItemExist(LocalDate date, String mealTimeId, String productId) {
		var key = Triple.of(date, mealTimeId, productId);
		return menuItemByDateAndMealTimeForProduct.get()
				.contains(key);
	}

	private Optional<String> getPreOrderMenuItemId(LocalDate date, String mealTimeId, String productId) {
		var key = Triple.of(date, mealTimeId, productId);
		if (menuItemByDateAndMealTimeForPreOrderMenuItem.containsKey(key)) {
			return Optional.of(menuItemByDateAndMealTimeForPreOrderMenuItem.get(key));
		}
		return Optional.empty();
	}

	private List<PreOrderMenuItemResponse> findExistPreOrderMenuItemByProduct(String preOrderMenuId, LocalDate date,
			String mealTimeId, String productId) {
		List<PreOrderMenuItemResponse> existingMenuItems = preOrderMenuDataPresetHelper
				.findExistPreOrderMenuItemByProduct(preOrderMenuId, date, mealTimeId, productId);
		for (PreOrderMenuItemResponse menuItem : existingMenuItems) {
			var key = Triple.of(LocalDate.parse(menuItem.date()), menuItem.mealTimeId(), productId);
			menuItemByDateAndMealTimeForProduct.get()
					.add(key);
			var keyForPreOrderMenuItem = Triple.of(LocalDate.parse(menuItem.date()), menuItem.mealTimeId(), productId);
			menuItemByDateAndMealTimeForPreOrderMenuItem.put(keyForPreOrderMenuItem, menuItem.id());
		}
		return existingMenuItems;
	}

	private Optional<ProductResponse> findProduct(PreOrderMenuItemDataDTO data) {
		Optional<ProductResponse> productOpt = Optional.empty();
		if (StringUtils.isNotBlank(data.sku())) {
			productOpt = Optional.ofNullable(productMap.get(data.sku()))
					.filter(productResponse -> productResponse.name()
							.equalsIgnoreCase(data.product()));
		} else if (StringUtils.isNotBlank(data.product())) {
			String name = data.product()
					.trim()
					.toLowerCase();
			productOpt = Optional.ofNullable(productNameMap.get(name));
		}
		return productOpt;
	}

	private Optional<MealTimeResponse> findMealTime(String mealTime) {
		String mealTimeName = mealTime.trim()
				.toLowerCase();
		return Optional.ofNullable(mealTimeMap.get(mealTimeName));
	}

}
