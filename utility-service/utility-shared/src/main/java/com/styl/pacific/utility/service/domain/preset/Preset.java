/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.service.domain.preset;

import com.styl.pacific.utility.service.api.dto.ImportStatus;
import com.styl.pacific.utility.service.api.dto.ImportStrategy;
import com.styl.pacific.utility.service.api.dto.PresetType;
import java.time.Instant;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */

@Data
@Builder
public class Preset {

	private String id;
	private Long tenantId;
	private String name;
	private String fileName;
	private String description;
	private String filePath;
	private ImportStrategy strategy;
	private ImportStatus status;
	private PresetType presetType;
	private long totalRecords;
	private long successRecords;
	private long failedRecords;
	private PresetMetadataDto metadata;
	private Instant createdAt;
	private Instant updatedAt;

	public void init() {
		this.status = ImportStatus.UPLOADING;
		this.totalRecords = 0;
		this.successRecords = 0;
		this.failedRecords = 0;
	}
}
