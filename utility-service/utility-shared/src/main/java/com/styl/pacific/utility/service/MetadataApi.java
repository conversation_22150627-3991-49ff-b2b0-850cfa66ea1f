/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.service;

import com.styl.pacific.domain.dto.CountryResponse;
import com.styl.pacific.domain.dto.CurrencyResponse;
import com.styl.pacific.domain.dto.TimezoneResponse;
import com.styl.pacific.domain.permissions.PacificApiAuthorized;
import java.util.List;
import java.util.Set;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * <AUTHOR>
 */
public interface MetadataApi {

	@GetMapping(path = "/api/utility/metadata/timezones")
	@PacificApiAuthorized
	public ResponseEntity<List<TimezoneResponse>> getTimezonesMetadata();

	@GetMapping(path = "/api/utility/metadata/currencies")
	@PacificApiAuthorized
	public ResponseEntity<List<CurrencyResponse>> getCurrenciesMetadata();

	@GetMapping(path = "/api/utility/metadata/countries")
	@PacificApiAuthorized
	public ResponseEntity<List<CountryResponse>> getCountriesMetadata();

	@GetMapping(path = "/api/utility/metadata/date-formats")
	@PacificApiAuthorized
	public ResponseEntity<Set<String>> getDateFormatMetadata();

	@GetMapping(path = "/api/utility/metadata/time-formats")
	@PacificApiAuthorized
	public ResponseEntity<Set<String>> getTimeFormatMetadata();

	@GetMapping(path = "/api/utility/metadata/business-types")
	@PacificApiAuthorized
	public ResponseEntity<List<String>> getBusinessTypes();

}
