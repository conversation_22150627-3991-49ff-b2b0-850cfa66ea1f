/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.service.data.access.jpa.entity;

import com.styl.pacific.data.access.jpa.specification.BaseSpecification;
import com.styl.pacific.utility.service.api.dto.ImportStatus;
import com.styl.pacific.utility.service.api.dto.PresetType;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import lombok.Builder;

/**
 * <AUTHOR>
 */

@Builder
public class PresetJpaSpecification extends BaseSpecification<PresetEntity> {

	private final Long tenantId;

	private final String id;

	private final String name;

	private final List<ImportStatus> statuses;

	private final PresetType type;

	@Override
	public Predicate toPredicate(Root<PresetEntity> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
		List<Predicate> predicates = new ArrayList<>();

		if (isNotNull(id)) {
			predicates.add(equals(criteriaBuilder, root.get("id"), id));
		}

		if (isNotNull(tenantId)) {
			predicates.add(equals(criteriaBuilder, root.get("tenantId"), tenantId));
		}

		if (isNotBlank(name)) {
			predicates.add(criteriaBuilder.like(criteriaBuilder.lower(root.get("name")), name));
		}

		if (isNotEmpty(statuses)) {
			predicates.add(in(root.get("status"), Arrays.asList(statuses.toArray())));
		}

		if (isNotNull(type)) {
			predicates.add(equals(criteriaBuilder, root.get("presetType"), type));
		}

		return and(criteriaBuilder, predicates);
	}

}
