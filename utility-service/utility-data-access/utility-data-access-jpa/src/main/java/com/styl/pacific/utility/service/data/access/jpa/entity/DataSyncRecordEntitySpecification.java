/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.service.data.access.jpa.entity;

import com.styl.pacific.data.access.jpa.specification.BaseSpecification;
import com.styl.pacific.utility.service.api.dto.DataSyncRecordStatus;
import com.styl.pacific.utility.service.api.dto.DataSyncRecordType;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import lombok.Builder;

/**
 * <AUTHOR>
 */

@Builder
public class DataSyncRecordEntitySpecification extends BaseSpecification<DataSyncRecordEntity> {

	private final Long byTenantId;
	private final Long byJobId;
	private final DataSyncRecordType byType;
	private final String byDataKeywords;
	private final Set<DataSyncRecordStatus> byStatuses;

	@Override
	public Predicate toPredicate(Root<DataSyncRecordEntity> root, CriteriaQuery<?> query,
			CriteriaBuilder criteriaBuilder) {
		List<Predicate> predicates = new ArrayList<>();

		if (isNotNull(byTenantId)) {
			predicates.add(equals(criteriaBuilder, root.get("tenantId"), byTenantId));
		}

		if (isNotEmpty(byStatuses)) {
			predicates.add(in(root.get("recordStatus"), Arrays.asList(byStatuses.toArray())));
		}

		if (isNotNull(byJobId)) {
			predicates.add(equals(criteriaBuilder, root.get("jobId"), byJobId));
		}

		if (isNotBlank(byDataKeywords)) {
			predicates.add(like(criteriaBuilder, root.get("rawData"), byDataKeywords));
		}

		if (isNotNull(byType)) {
			predicates.add(equals(criteriaBuilder, root.get("recordType"), byType));
		}

		return and(criteriaBuilder, predicates);
	}

}
