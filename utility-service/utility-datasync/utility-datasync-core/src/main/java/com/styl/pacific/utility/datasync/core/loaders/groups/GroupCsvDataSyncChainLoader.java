/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.datasync.core.loaders.groups;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.utility.datasync.core.commons.models.CsvDataSchema;
import com.styl.pacific.utility.datasync.core.jobs.DataSyncJobCommandService;
import com.styl.pacific.utility.datasync.core.jobs.entities.DataSyncJob;
import com.styl.pacific.utility.datasync.core.jobs.entities.DataSyncRecordTracking;
import com.styl.pacific.utility.datasync.core.loaders.BaseDataSyncCsvLoader;
import com.styl.pacific.utility.datasync.core.loaders.DataSyncChainLoader;
import com.styl.pacific.utility.datasync.core.loaders.chains.DataSyncChainLoaderData;
import com.styl.pacific.utility.datasync.core.records.DataSyncRecordCommandService;
import com.styl.pacific.utility.datasync.shared.exceptions.DataSyncDomainException;
import com.styl.pacific.utility.datasync.shared.schema.GroupDataModelSchema;
import com.styl.pacific.utility.service.api.dto.DataSyncJobStatus;
import com.styl.pacific.utility.service.api.dto.DataSyncRecordType;
import com.styl.pacific.utility.service.domain.file.FileUtilitiesService;
import com.styl.pacific.utils.string.PathUtils;
import jakarta.validation.Validator;
import java.time.Instant;
import java.util.Arrays;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;

public class GroupCsvDataSyncChainLoader extends BaseDataSyncCsvLoader implements DataSyncChainLoader {

	public GroupCsvDataSyncChainLoader(DataSyncJobCommandService jobCommandService,
			DataSyncRecordCommandService recordCommandService, FileUtilitiesService fileUtilitiesService,
			ObjectMapper mapper, Validator validator) {
		super(DataSyncRecordType.GROUP, jobCommandService, recordCommandService, fileUtilitiesService,
				new GroupCsvRowDataValidator(mapper, validator));
	}

	@Override
	public CsvDataSchema getDefaultSchema() {
		return CsvDataSchema.builder()
				.headerRowIndex(1)
				.columDefinitions(Arrays.stream(GroupDataModelSchema.values())
						.map(it -> CsvDataSchema.ColumDefinition.builder()
								.key(it.getHeaderKey())
								.build())
						.toList())
				.build();
	}

	@Override
	protected DataSyncJob updateCsvSchema(DataSyncJob job, CsvDataSchema csvSchema) {
		return job.withGroupCsvSchema(csvSchema);
	}

	@Override
	public DataSyncChainLoaderData apply(DataSyncChainLoaderData data) {
		final var job = data.getJob();
		final var manifest = job.getManifestContent();

		if (StringUtils.isBlank(manifest.getGroupCsvPath())) {
			return data;
		}

		if (!PathUtils.isValidAbsolutePath(manifest.getGroupCsvPath())) {
			throw new DataSyncDomainException("Group CSV Path must be a well absolute path. Example: /data/file.csv");
		}

		final var groupsObjectKey = "%s%s".formatted(job.getArchivedFileDirectory(), manifest.getGroupCsvPath());
		if (!fileUtilitiesService.exists(groupsObjectKey)) {
			throw new DataSyncDomainException("Group CSV file was not found");
		}

		deleteRecordsIfExisted(job);

		var updatedJob = jobCommandService.updateDataSyncJob(job.withRecordTracking(Optional.ofNullable(job
				.getRecordTracking())
				.map(it -> it.withGroupCsvLoadingStartedAt(Instant.now()))
				.orElseGet(() -> DataSyncRecordTracking.builder()
						.groupCsvLoadingStartedAt(Instant.now())
						.build())));

		final var result = processCsv(updatedJob, fileUtilitiesService.getObject(groupsObjectKey));
		final var hasRecordError = result.getTotalErrorRecords() != 0;

		updatedJob = result.getJob()
				.withStatus(hasRecordError
						? DataSyncJobStatus.ERROR
						: result.getJob()
								.getStatus())
				.withErrorMessage(hasRecordError
						? result.getJob()
								.appendErrorMessage("%s: %s Group Records has been error format".formatted(Instant
										.now(), result.getTotalErrorRecords()))
						: result.getJob()
								.getErrorMessage());

		updatedJob = jobCommandService.updateDataSyncJob(updatedJob.withRecordTracking(Optional.ofNullable(updatedJob
				.getRecordTracking())
				.map(it -> it.withGroupCsvLoadingCompletedAt(Instant.now()))
				.orElseGet(() -> DataSyncRecordTracking.builder()
						.groupCsvLoadingCompletedAt(Instant.now())
						.build())));

		return data.withJob(updatedJob);
	}
}
