/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.datasync.core.records.request;

import com.styl.pacific.domain.valueobject.DataSyncJobId;
import com.styl.pacific.domain.valueobject.TenantId;
import com.styl.pacific.utility.service.api.dto.DataSyncRecordStatus;
import com.styl.pacific.utility.service.api.dto.DataSyncRecordType;
import java.util.Set;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.With;

@Getter
@Builder
@RequiredArgsConstructor
@With
public class FilterDataSyncRecordQuery {
	private final TenantId byTenantId;
	private final DataSyncJobId byJobId;
	private final DataSyncRecordType byType;
	private final String byDataKeywords;
	private final Set<DataSyncRecordStatus> byStatuses;
}
