/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.datasync.scheduling.processor;

import com.styl.pacific.utility.datasync.core.jobs.DataSyncJobQueryService;
import com.styl.pacific.utility.datasync.core.jobs.request.DataSyncJobPaginationQuery;
import com.styl.pacific.utility.datasync.core.jobs.request.FilterDataSyncJobQuery;
import com.styl.pacific.utility.datasync.core.processors.DataSyncRecordProcessorService;
import com.styl.pacific.utility.service.api.dto.DataSyncJobStatus;
import java.util.List;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class DataSyncRecordProcessingSchedulingHandler {
	private final DataSyncRecordProcessorService processorService;
	private final DataSyncJobQueryService queryService;

	public void process() {
		final var pageResult = queryService.queryDataSyncJobs(DataSyncJobPaginationQuery.builder()
				.filter(FilterDataSyncJobQuery.builder()
						.byStatuses(Set.of(DataSyncJobStatus.LOADED))
						.build())
				.page(0)
				.size(1)
				.sortDirection("ASC")
				.sortFields(List.of("id"))
				.build());

		if (pageResult.getContent()
				.isEmpty()) {
			return;
		}
		processorService.process(pageResult.getContent()
				.getFirst());
	}
}
