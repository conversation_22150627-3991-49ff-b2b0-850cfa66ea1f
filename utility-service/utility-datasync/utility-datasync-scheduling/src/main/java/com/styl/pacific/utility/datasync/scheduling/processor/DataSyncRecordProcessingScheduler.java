/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.datasync.scheduling.processor;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.core.LockAssert;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.scheduling.annotation.Scheduled;

@Configuration
@Slf4j
@ConditionalOnProperty(name = "pacific.datasync.processor.scheduling.enabled", havingValue = "true")
@RequiredArgsConstructor
@DependsOn("schedulingConfiguration")
public class DataSyncRecordProcessingScheduler {
	private final DataSyncRecordProcessingSchedulingHandler handler;

	@Scheduled(cron = "${pacific.datasync.detector.scheduling.cron-expression}")
	@SchedulerLock(name = "DataSync.JobProcessorAsyncJob", lockAtLeastFor = "${pacific.datasync.processor.scheduling.lock-at-least-for:PT10S}", lockAtMostFor = "${pacific.datasync.processor.scheduling.lock-at-most-for:PT5M}")
	public void trigger() {
		LockAssert.assertLocked();
		handler.process();
	}
}
