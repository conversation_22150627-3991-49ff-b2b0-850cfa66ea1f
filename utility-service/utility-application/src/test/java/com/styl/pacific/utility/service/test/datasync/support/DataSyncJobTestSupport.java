/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.service.test.datasync.support;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.opencsv.CSVReader;
import com.styl.pacific.common.mapstruct.MapstructCommonMapper;
import com.styl.pacific.utility.datasync.core.commons.models.CardDataModel;
import com.styl.pacific.utility.datasync.core.commons.models.CustomerDataModel;
import com.styl.pacific.utility.datasync.core.commons.models.GroupDataModel;
import com.styl.pacific.utility.datasync.core.records.entities.DataSyncRecord;
import com.styl.pacific.utility.datasync.shared.exceptions.DataSyncDomainException;
import com.styl.pacific.utility.datasync.shared.schema.CustomerDataRecordAction;
import com.styl.pacific.utility.service.api.dto.DataSyncRecordType;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiFunction;
import java.util.stream.Stream;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.CreateBucketRequest;
import software.amazon.awssdk.services.s3.model.HeadBucketRequest;
import software.amazon.awssdk.services.s3.model.NoSuchBucketException;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;

@Service
@Slf4j
public class DataSyncJobTestSupport {
	@Autowired
	private S3Client s3Client;

	@Autowired
	private ObjectMapper objectMapper;

	@SneakyThrows
	public void uploadFolderToDirectoryKey(String resourceName, String bucket) {
		final var dataSyncUri = Thread.currentThread()
				.getContextClassLoader()
				.getResource(resourceName);
		final var resourcePath = Paths.get(Objects.requireNonNull(dataSyncUri)
				.toURI());

		try (Stream<Path> files = Files.walk(resourcePath)) {
			files.filter(Files::isRegularFile)
					.forEach(file -> {
						final var s3ObjectKey = resourcePath.relativize(file)
								.toString()
								.replace("\\", "/");
						log.info("Uploaded: {}:{}", bucket, s3ObjectKey);
						s3Client.putObject(PutObjectRequest.builder()
								.bucket(bucket)
								.key(s3ObjectKey)
								.build(), RequestBody.fromFile(file));
					});
		} catch (IOException e) {
			throw new DataSyncDomainException(e.getMessage(), e);
		}
	}

	@SneakyThrows
	public List<DataSyncRecord> loadRawRecordsFromCsv(String filePath,
			BiFunction<Long, String[], DataSyncRecord> recordBuilder) {

		final var filePathUrl = Thread.currentThread()
				.getContextClassLoader()
				.getResource(filePath);
		final var resourcePath = Paths.get(Objects.requireNonNull(filePathUrl)
				.toURI())
				.toFile();

		final var records = new ArrayList<DataSyncRecord>();

		try (CSVReader csvReader = new CSVReader(new InputStreamReader(new FileInputStream(resourcePath)))) {
			String[] rowData = null;
			while ((rowData = csvReader.readNext()) != null) {
				if (1 == csvReader.getRecordsRead()) {
					continue;
				}
				records.add(recordBuilder.apply(csvReader.getRecordsRead(), rowData));
			}
			return records;
		} catch (Exception exception) {
			throw new DataSyncDomainException(exception.getMessage(), exception);
		}
	}

	public List<DataSyncRecord> loadCustomerRecordsFromCsv(String filePath) {
		return loadRawRecordsFromCsv(filePath, (index, rawData) -> DataSyncRecord.builder()
				.recordIndex(index)
				.recordType(DataSyncRecordType.CUSTOMER)
				.rawData(convertRawDataToString(rawData))
				.data(convertMap(CustomerDataModel.builder()
						.customerNo(MapstructCommonMapper.INSTANCE.stringToNullIfEmpty(rawData[0]))
						.firstName(MapstructCommonMapper.INSTANCE.stringToNullIfEmpty(rawData[1]))
						.lastName(MapstructCommonMapper.INSTANCE.stringToNullIfEmpty(rawData[2]))
						.email(MapstructCommonMapper.INSTANCE.stringToNullIfEmpty(rawData[3]))
						.groupName(MapstructCommonMapper.INSTANCE.stringToNullIfEmpty(rawData[4]))
						.allergens(Optional.ofNullable(rawData[5])
								.filter(StringUtils::isNotBlank)
								.map(it -> it.split(","))
								.map(List::of)
								.orElse(null))
						.action(Optional.ofNullable(rawData[6])
								.filter(StringUtils::isNotBlank)
								.map(CustomerDataRecordAction::parse)
								.orElse(CustomerDataRecordAction.UPDATE)
								.name())
						.avatarImagePath(MapstructCommonMapper.INSTANCE.stringToNullIfEmpty(rawData[7]))
						.customerSponsorEmail(MapstructCommonMapper.INSTANCE.stringToNullIfEmpty(rawData[8]))
						.build()))
				.build());
	}

	@SneakyThrows
	private String convertRawDataToString(String[] rawData) {
		return objectMapper.writeValueAsString(rawData);

	}

	@SneakyThrows
	private Map<String, String> convertMap(Object object) {
		return objectMapper.convertValue(object, new TypeReference<>() {
		});

	}

	public List<DataSyncRecord> loadGroupRecordsFromCsv(String filePath) {
		return loadRawRecordsFromCsv(filePath, (index, rawData) -> DataSyncRecord.builder()
				.recordIndex(index)
				.recordType(DataSyncRecordType.GROUP)
				.rawData(convertRawDataToString(rawData))
				.data(convertMap(GroupDataModel.builder()
						.groupPath(MapstructCommonMapper.INSTANCE.stringToNullIfEmpty(rawData[0]))
						.groupDescription(MapstructCommonMapper.INSTANCE.stringToNullIfEmpty(rawData[1]))
						.build()))
				.build());

	}

	public List<DataSyncRecord> loadCardRecordsFromCsv(String filePath) {
		return loadRawRecordsFromCsv(filePath, (index, rawData) -> DataSyncRecord.builder()
				.recordIndex(index)
				.recordType(DataSyncRecordType.CARD)
				.rawData(convertRawDataToString(rawData))
				.data(convertMap(CardDataModel.builder()
						.customerNo(MapstructCommonMapper.INSTANCE.stringToNullIfEmpty(rawData[0]))
						.email(MapstructCommonMapper.INSTANCE.stringToNullIfEmpty(rawData[1]))
						.cardId(MapstructCommonMapper.INSTANCE.stringToNullIfEmpty(rawData[2]))
						.build()))
				.build());

	}

	public void initBucket(String s3Bucket) {
		try {
			s3Client.headBucket(HeadBucketRequest.builder()
					.bucket(s3Bucket)
					.build());
		} catch (NoSuchBucketException exception) {
			s3Client.createBucket(CreateBucketRequest.builder()
					.bucket(s3Bucket)
					.build());
		}
	}
}
