/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.service;

import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.styl.pacific.utility.service.config.UtilityIntegrationTestContainer;
import com.styl.pacific.utility.service.preset.customer.CustomerPresetProcessor;
import com.styl.pacific.utility.service.test.config.IntegrationTestConfiguration;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

/**
 * <AUTHOR>
 */
@ContextConfiguration(classes = IntegrationTestConfiguration.class)
public class ContextTest extends UtilityIntegrationTestContainer {

	@Autowired
	private CustomerPresetProcessor customerPresetProcessor;

	@Test
	public void testDependencyInjection() {
		assertNotNull(customerPresetProcessor.getPresetRepository());
	}

}
