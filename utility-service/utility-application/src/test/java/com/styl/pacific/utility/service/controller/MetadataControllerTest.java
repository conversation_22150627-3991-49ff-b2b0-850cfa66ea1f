/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.service.controller;

import static org.hamcrest.Matchers.hasSize;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.styl.pacific.application.rest.context.RequestContext;
import com.styl.pacific.domain.dto.CountryResponse;
import com.styl.pacific.domain.dto.CurrencyResponse;
import com.styl.pacific.domain.dto.TimezoneResponse;
import com.styl.pacific.utility.service.rest.controller.MetadataController;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

/**
 * <AUTHOR>
 */

@MockBean(classes = { RequestContext.class })
@WebMvcTest(MetadataController.class)
class MetadataControllerTest {

	@Autowired
	private MockMvc mockMvc;

	@Test
	void testGetTimezonesMetadata() throws Exception {

		// Act & Assert
		MvcResult result = mockMvc.perform(get("/api/utility/metadata/timezones").contentType(
				MediaType.APPLICATION_JSON))
				.andExpect(status().isOk())
				.andExpect(jsonPath("$").isArray())
				.andExpect(jsonPath("$").isNotEmpty())
				.andReturn();

		List<TimezoneResponse> responses = new ObjectMapper().readValue(result.getResponse()
				.getContentAsString(), new TypeReference<List<TimezoneResponse>>() {
				});

		TimezoneResponse singapore = responses.stream()
				.filter(timezone -> timezone.getZoneId()
						.equals("Asia/Singapore"))
				.findFirst()
				.orElse(null);

		assertNotNull(singapore);
		assertEquals("Asia/Singapore", singapore.getZoneId());
		assertEquals("(GMT+08:00) Asia/Singapore", singapore.getDisplayName());
		assertEquals("GMT+08:00", singapore.getGtmOffset());
	}

	@Test
	void testGetCurrenciesMetadata() throws Exception {

		// Act & Assert
		MvcResult result = mockMvc.perform(get("/api/utility/metadata/currencies").contentType(
				MediaType.APPLICATION_JSON))
				.andExpect(status().isOk())
				.andExpect(jsonPath("$", hasSize(CurrencyResponse.AVAILABLE_CURRENCIES.size())))
				.andReturn();

		List<CurrencyResponse> responses = new ObjectMapper().readValue(result.getResponse()
				.getContentAsString(), new TypeReference<List<CurrencyResponse>>() {
				});

		CurrencyResponse sgd = responses.stream()
				.filter(currency -> currency.getCurrencyCode()
						.equals("SGD"))
				.findFirst()
				.orElse(null);

		assertNotNull(sgd);
		assertEquals("SGD", sgd.getCurrencyCode());
		assertEquals("Singapore Dollar", sgd.getDisplayName());
		assertEquals(2, sgd.getFractionDigits());
		assertEquals(702, sgd.getNumericCode());
		assertEquals("SGD", sgd.getSymbol());

		CurrencyResponse usd = responses.stream()
				.filter(currency -> currency.getCurrencyCode()
						.equals("USD"))
				.findFirst()
				.orElse(null);

		assertNotNull(usd);
		assertEquals("USD", usd.getCurrencyCode());
		assertEquals("US Dollar", usd.getDisplayName());
		assertEquals(2, usd.getFractionDigits());
		assertEquals(840, usd.getNumericCode());
		assertEquals("$", usd.getSymbol());

	}

	@Test
	void testGetCountriesMetadata() throws Exception {

		// Act & Assert
		MvcResult result = mockMvc.perform(get("/api/utility/metadata/countries").contentType(
				MediaType.APPLICATION_JSON))
				.andExpect(status().isOk())
				.andExpect(jsonPath("$").isArray())
				.andExpect(jsonPath("$").isNotEmpty())
				.andReturn();

		List<CountryResponse> responses = new ObjectMapper().readValue(result.getResponse()
				.getContentAsString(), new TypeReference<List<CountryResponse>>() {
				});

		CountryResponse singapore = responses.stream()
				.filter(country -> country.getCountryCode()
						.equals("SG"))
				.findFirst()
				.orElse(null);

		assertNotNull(singapore);
		assertEquals("SG", singapore.getCountryCode());
		assertEquals("Singapore", singapore.getCountryName());

		CountryResponse vietnam = responses.stream()
				.filter(country -> country.getCountryCode()
						.equals("VN"))
				.findFirst()
				.orElse(null);

		assertNotNull(vietnam);
		assertEquals("VN", vietnam.getCountryCode());
		assertEquals("Vietnam", vietnam.getCountryName());

		CountryResponse malaysia = responses.stream()
				.filter(country -> country.getCountryCode()
						.equals("MY"))
				.findFirst()
				.orElse(null);

		assertNotNull(malaysia);
		assertEquals("MY", malaysia.getCountryCode());
		assertEquals("Malaysia", malaysia.getCountryName());
	}
}