/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.utility.service.openapi;

import com.styl.pacific.common.test.openapi.AbstractOpenApiGenerator;
import com.styl.pacific.utility.service.test.config.IntegrationTestConfiguration;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.context.ContextConfiguration;

/**
 * <AUTHOR>
 */

@SuppressWarnings("java:S2187")
@ContextConfiguration(classes = { IntegrationTestConfiguration.class })
class OpenAPIGeneratorTest extends AbstractOpenApiGenerator {

	@Value("${spring.application.name}")
	private String applicationName;

	protected String getServiceName() {
		return applicationName;
	}

}
