/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.device.aggregator.features.staff;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.styl.pacific.device.aggregator.features.staff.clients.StaffClient;
import com.styl.pacific.store.shared.http.enums.StaffStatus;
import com.styl.pacific.store.shared.http.enums.StaffType;
import com.styl.pacific.store.shared.http.requests.staff.FindStaffsRequest;
import com.styl.pacific.store.shared.http.requests.staff.VerifyStaffRequest;
import com.styl.pacific.store.shared.http.responses.staff.ListStaffResponse;
import com.styl.pacific.store.shared.http.responses.staff.StaffResponse;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

/**
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class StaffServiceImplTest {
	private static final Long STAFF_ID = 1L;
	private static final Long STORE_ID = 2L;
	private static final Long TENANT_ID = 3L;
	private static final String CARD_ID = "cardId";
	private static final String STAFF_CODE = "staffCode";
	private static final String NAME = "name";
	private static final StaffType TYPE = StaffType.CASHIER;
	private static final StaffStatus STATUS = StaffStatus.ACTIVE;

	@Mock
	private StaffClient staffClient;
	@InjectMocks
	private StaffServiceImpl staffService;

	@Test
	void shouldReturnStaffResponse_whenVerifyStaff() {
		// Arrange
		StaffResponse staffResponseMock = getStaffResponse();
		VerifyStaffRequest verifyStaffRequestMock = new VerifyStaffRequest(null, null, null);
		when(staffClient.verifyStaff(any())).thenReturn(ResponseEntity.ok(staffResponseMock));
		// Act
		StaffResponse staffResponse = staffService.verifyStaff(verifyStaffRequestMock);
		// Assert
		verify(staffClient, times(1)).verifyStaff(eq(verifyStaffRequestMock));
		assertEquals(staffResponseMock, staffResponse);
	}

	@Test
	void shouldReturnListStaffResponse_whenFindAll() {
		// Arrange
		StaffResponse staffResponseMock = getStaffResponse();
		ListStaffResponse listStaffResponseMock = new ListStaffResponse(List.of(staffResponseMock), 0, 0, 0, null);
		FindStaffsRequest findStaffQueryMock = new FindStaffsRequest(null, 0, 0, null, null);
		when(staffClient.findStaff(findStaffQueryMock)).thenReturn(ResponseEntity.ok(listStaffResponseMock));
		// Act
		ListStaffResponse listStaffResponse = staffService.findStaff(findStaffQueryMock);
		// Assert
		verify(staffClient, times(1)).findStaff(eq(findStaffQueryMock));
		assertEquals(listStaffResponseMock, listStaffResponse);
	}

	private StaffResponse getStaffResponse() {
		return StaffResponse.builder()
				.status(STATUS)
				.tenantId(TENANT_ID)
				.staffCode(STAFF_CODE)
				.staffId(STAFF_ID)
				.name(NAME)
				.type(TYPE)
				.cardId(CARD_ID)
				.build();
	}
}
