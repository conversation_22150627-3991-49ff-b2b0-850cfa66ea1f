/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.device.aggregator.features.payment.apis;

import com.styl.pacific.domain.valueobject.PaymentProcessorId;
import com.styl.pacific.payment.shared.http.processors.PaymentProcessorLiteResponse;
import com.styl.pacific.payment.shared.http.processors.PaymentProcessorResponse;
import java.util.List;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.ResponseStatus;

public interface PaymentProcessorApi {

	@GetMapping(path = "/api/payment/processors")
	@ResponseStatus(HttpStatus.OK)
	List<PaymentProcessorLiteResponse> getPaymentProcessors();

	@GetMapping(path = "/api/payment/processors/{processorId}")
	@ResponseStatus(HttpStatus.OK)
	PaymentProcessorResponse getPaymentProcessor(@PathVariable("processorId") PaymentProcessorId processorId);

}
