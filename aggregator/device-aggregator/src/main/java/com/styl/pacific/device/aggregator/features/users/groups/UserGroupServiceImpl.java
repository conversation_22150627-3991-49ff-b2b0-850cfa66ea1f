/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.device.aggregator.features.users.groups;

import com.styl.pacific.device.aggregator.features.users.clients.UserGroupClient;
import com.styl.pacific.device.aggregator.features.users.dtos.UserGroupDto;
import com.styl.pacific.device.aggregator.features.users.mapper.UserGroupPaginationMapper;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.user.shared.http.groups.request.QueryUserGroupPaginationRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class UserGroupServiceImpl implements UserGroupService {
	private final UserGroupClient userGroupClient;

	@Override
	public Paging<UserGroupDto> queryUserGroups(QueryUserGroupPaginationRequest request) {
		return UserGroupPaginationMapper.INSTANCE.toPagingResponse(userGroupClient.queryUserGroups(request));
	}
}
