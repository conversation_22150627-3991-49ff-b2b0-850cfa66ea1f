/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.device.aggregator.features.order.services;

import com.styl.pacific.catalog.service.shared.http.product.request.PaginationProductQueryRequest;
import com.styl.pacific.catalog.service.shared.http.product.request.ProductFilterRequest;
import com.styl.pacific.catalog.service.shared.http.product.response.ProductResponse;
import com.styl.pacific.device.aggregator.config.context.DeviceSessionContext;
import com.styl.pacific.device.aggregator.features.catalog.clients.ProductClient;
import com.styl.pacific.device.aggregator.features.order.clients.MenuClient;
import com.styl.pacific.device.aggregator.features.order.clients.MenuItemClient;
import com.styl.pacific.device.aggregator.features.order.dtos.MenuItemDetailResponse;
import com.styl.pacific.device.aggregator.features.order.mapper.OrderRestMapper;
import com.styl.pacific.domain.dto.Content;
import com.styl.pacific.domain.dto.pagination.Paging;
import com.styl.pacific.domain.enums.StoreStatus;
import com.styl.pacific.order.service.shared.http.menu.request.MenuFilterRequest;
import com.styl.pacific.order.service.shared.http.menu.request.MenuQueryRequest;
import com.styl.pacific.order.service.shared.http.menu.request.item.MenuItemFilterRequest;
import com.styl.pacific.order.service.shared.http.menu.request.item.PaginationMenuItemQueryRequest;
import com.styl.pacific.order.service.shared.http.menu.response.MenuItemResponse;
import com.styl.pacific.order.service.shared.http.menu.response.MenuStubResponse;
import com.styl.pacific.order.service.shared.http.product.enums.ProductStatus;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class MenuServiceImpl implements MenuService {
	private static final Logger log = LoggerFactory.getLogger(MenuServiceImpl.class);
	private final MenuClient menuClient;
	private final ProductClient productClient;
	private final MenuItemClient menuItemClient;
	private final DeviceSessionContext deviceSessionContext;

	@Override
	public Content<MenuStubResponse> findAllMenus(MenuQueryRequest query) {
		List<String> storeIds = List.of(deviceSessionContext.getStoreId()
				.toString());
		List<StoreStatus> storeStatuses = List.of(StoreStatus.ACTIVE);
		query = Optional.ofNullable(query)
				.map(q -> {
					MenuFilterRequest filter = Optional.ofNullable(q.filter())
							.orElseGet(() -> MenuFilterRequest.builder()
									.build());
					return q.withFilter(filter.withStoreIds(storeIds)
							.withStoreStatuses(storeStatuses));
				})
				.orElseGet(() -> MenuQueryRequest.builder()
						.filter(MenuFilterRequest.builder()
								.storeIds(storeIds)
								.storeStatuses(storeStatuses)
								.build())
						.build());
		return menuClient.findAll(query);
	}

	@Override
	public Paging<MenuItemDetailResponse> findAllMenuItems(String menuId, PaginationMenuItemQueryRequest query) {

		query = Optional.ofNullable(query)
				.map(q -> {
					MenuItemFilterRequest filter = Optional.ofNullable(q.getFilter())
							.map(f -> {
								List<ProductStatus> productStatuses = Optional.ofNullable(f.productStatuses())
										.map(statuses -> {
											statuses.remove(ProductStatus.ARCHIVED);
											if (statuses.isEmpty()) {
												return List.of(ProductStatus.ACTIVE, ProductStatus.UNAVAILABLE);
											}
											return statuses;
										})
										.orElse(List.of(ProductStatus.ACTIVE, ProductStatus.UNAVAILABLE));
								return f.withProductStatuses(productStatuses);
							})
							.orElseGet(() -> MenuItemFilterRequest.builder()
									.productStatuses(List.of(ProductStatus.ACTIVE, ProductStatus.UNAVAILABLE))
									.build());
					return new PaginationMenuItemQueryRequest(filter, q.getSize(), q.getPage(), q.getSortDirection(), q
							.getSortFields());
				})
				.orElseGet(() -> new PaginationMenuItemQueryRequest(MenuItemFilterRequest.builder()
						.productStatuses(List.of(ProductStatus.ACTIVE, ProductStatus.UNAVAILABLE))
						.build(), null, null, null, null));

		Paging<MenuItemResponse> pagingResponse = Optional.ofNullable(menuItemClient.findAllPaging(Optional.ofNullable(
				menuId)
				.map(String::valueOf)
				.orElse(null), query))
				.orElse(Paging.<MenuItemResponse>builder()
						.content(new ArrayList<>())
						.page(0)
						.totalElements(0)
						.totalPages(0)
						.build());
		List<ProductResponse> productResponseList = new ArrayList<>();
		if (Boolean.FALSE.equals(pagingResponse.getContent()
				.isEmpty())) {
			ProductFilterRequest productFilterRequest = ProductFilterRequest.builder()
					.ids(pagingResponse.getContent()
							.stream()
							.map(response -> Objects.nonNull(response.product())
									? response.product()
											.id()
									: null)
							.toList())
					.statuses(query.getFilter()
							.productStatuses()
							.stream()
							.map(OrderRestMapper.INSTANCE::toProductStatus)
							.toList())
					.storeId(deviceSessionContext.getStoreId()
							.toString())
					.build();
			PaginationProductQueryRequest request = new PaginationProductQueryRequest(productFilterRequest,
					pagingResponse.getContent()
							.size(), null, null, null);
			Paging<ProductResponse> paging = productClient.findAllPaging(request);
			productResponseList.addAll(paging.getContent());
		}
		Map<String, ProductResponse> productResponseMap = productResponseList.stream()
				.collect(Collectors.toMap(ProductResponse::id, Function.identity()));
		return pagingResponse.map(menuItemResponse -> {
			if (productResponseMap.containsKey(menuItemResponse.product()
					.id())) {
				ProductResponse productResponse = productResponseMap.get(menuItemResponse.product()
						.id());
				return OrderRestMapper.INSTANCE.menuItemResponseToDetail(menuItemResponse, OrderRestMapper.INSTANCE
						.toProductDetail(productResponse, menuItemResponse.product()
								.inventory()));
			} else {
				log.warn("Product {} is miss match data with menu item {}", menuItemResponse.product()
						.id(), menuItemResponse.id());
				return OrderRestMapper.INSTANCE.menuItemResponseToDetail(menuItemResponse, OrderRestMapper.INSTANCE
						.toProductDetail(menuItemResponse.product(), menuItemResponse.product()
								.inventory()));
			}
		});
	}

}
