server:
  port: 9100

spring:
  application:
    name: core-gw
  reactor:
    context-propagation: AUTO
  cloud:
    gateway:
      # Config routes for services
      routes:
        - id: tenant-service
          uri: ${pacific.clients.tenant-service.url}
          predicates:
            - Path=/api/tenant/**
          filters:
            - name: CircuitBreaker
              args:
                name: tenant-service

        - id: user-service
          uri: ${pacific.clients.user-service.url}
          predicates:
            - Path=/api/user/**
          filters:
            - name: CircuitBreaker
              args:
                name: user-service

        - id: catalog-service
          uri: ${pacific.clients.catalog-service.url}
          predicates:
            - Path=/api/catalog/**
          filters:
            - name: CircuitBreaker
              args:
                name: catalog-service

        - id: store-service
          uri: ${pacific.clients.store-service.url}
          predicates:
            - Path=/api/store/**
          filters:
            - name: CircuitBreaker
              args:
                name: store-service

        - id: order-service
          uri: ${pacific.clients.order-service.url}
          predicates:
            - Path=/api/order/**
          filters:
            - name: CircuitBreaker
              args:
                name: order-service

        - id: payment-service
          uri: ${pacific.clients.payment-service.url}
          predicates:
            - Path=/api/payment/**,/webhooks/payment/**
          filters:
            - name: CircuitBreaker
              args:
                name: payment-service

        - id: utility-service
          uri: ${pacific.clients.utility-service.url}
          predicates:
            - Path=/api/utility/**
          filters:
            - name: CircuitBreaker
              args:
                name: utility-service

        - id: authorization-service
          uri: ${pacific.clients.authorization-service.url}
          predicates:
            - Path=/api/authz/**
          filters:
            - name: CircuitBreaker
              args:
                name: authorization-service

        - id: wallet-service
          uri: ${pacific.clients.wallet-service.url}
          predicates:
            - Path=/api/wallet/**
          filters:
            - name: CircuitBreaker
              args:
                name: wallet-service

        - id: notification-service
          uri: ${pacific.clients.notification-service.url}
          predicates:
            - Path=/api/notification/**
          filters:
            - name: CircuitBreaker
              args:
                name: notification-service
# For more information, visit: https://docs.spring.io/spring-cloud-gateway/reference/spring-cloud-gateway-server-mvc/filters/circuitbreaker-filter.html
resilience4j:
  timelimiter:
    configs:
      default:
        timeoutDuration: 30000

pacific:
  scanning-apis:
    base-packages: com.styl.pacific.*;

  aws:
    s3:
      endpoint:
      region: ap-southeast-1
      accessKey:
      secretKey:
  configs:
    tenant-service:
      tenant:
        default-tenant-id: 1
  clients:
    tenant-service:
      url: http://tenant-svc.application.svc.cluster.local:9201
    user-service:
      url: http://user-svc.application.svc.cluster.local:9202
    catalog-service:
      url: http://catalog-svc.application.svc.cluster.local:9203
    store-service:
      url: http://store-svc.application.svc.cluster.local:9204
    order-service:
      url: http://order-svc.application.svc.cluster.local:9205
    payment-service:
      url: http://payment-svc.application.svc.cluster.local:9206
    notification-service:
      url: http://notification-svc.application.svc.cluster.local:9207
    utility-service:
      url: http://utility-svc.application.svc.cluster.local:9208
    authorization-service:
      url: http://auth-svc.application.svc.cluster.local:9209
    wallet-service:
      url: http://wallet-svc.application.svc.cluster.local:9210

  security:
    api:
      authorization:
        enabled: true
        expired-after-access: PT60S
        expired-after-write: PT180S
      issuer-uri-pattern: https://pacific-ii-ciam-sit.styl.solutions/realms
      config:
        uri-pattern: /api/**

    webhooks:
      config:
        uri-pattern: /webhooks/**
        open-endpoints:
          - path: /webhooks/payment/stripe/**
          - path: /webhooks/payment/stripe-connect/**

    defaults:
      config:
        open-endpoints:
          - path: /actuator/**
            methods:
              - GET

  tracing:
    otlp:
      endpoint: http://jeager.svc.monitoring.svc.cluster.local:4317

logging:
  level:
    com.styl.pacific: INFO
    org.springframework.security.jwt: INFO


management:
  tracing:
    sampling:
      probability: 1.0
  health:
    livenessstate:
      enabled: true
    readinessstate:
      enabled: true
  endpoint:
    health:
      probes:
        enabled: true
      metrics:
        enabled: true
  endpoints:
    web:
      exposure:
        include: "*"
