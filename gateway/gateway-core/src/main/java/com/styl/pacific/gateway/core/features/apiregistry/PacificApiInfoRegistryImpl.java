/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.gateway.core.features.apiregistry;

import com.styl.pacific.domain.permissions.PlatformApiSecurityLevel;
import com.styl.pacific.gateway.core.features.apiregistry.entities.ScannedApiInfo;
import com.styl.pacific.gateway.core.features.scanners.PacificApiInfoScanner;
import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Service
@RequiredArgsConstructor
@Slf4j
public class PacificApiInfoRegistryImpl implements PacificApiInfoRegistry {
	private final PacificApiInfoScanner scanner;

	@Getter
	private final List<ScannedApiInfo> privateApis = new ArrayList<>();

	@Getter
	private final List<ScannedApiInfo> publicApis = new ArrayList<>();

	@Getter
	private final List<ScannedApiInfo> authenticatedApis = new ArrayList<>();

	@Getter
	private final List<ScannedApiInfo> authorizedRoleApis = new ArrayList<>();

	@Value("${pacific.scanning-apis.base-packages}")
	private String apiScannedBasePackages;

	@PostConstruct
	public void init() {
		log.info("ApiScannedBasePackages: {}", apiScannedBasePackages);
		final var apiInfoList = scanner.scan(Arrays.stream(apiScannedBasePackages.split(";"))
				.filter(StringUtils::hasText)
				.map(String::strip)
				.map(String::trim)
				.toList());

		privateApis.addAll(apiInfoList.stream()
				.filter(apiInfo -> PlatformApiSecurityLevel.PRIVATE.equals(apiInfo.getSecurityLevel()))
				.toList());

		publicApis.addAll(apiInfoList.stream()
				.filter(apiInfo -> PlatformApiSecurityLevel.PUBLIC.equals(apiInfo.getSecurityLevel()))
				.toList());

		authenticatedApis.addAll(apiInfoList.stream()
				.filter(apiInfo -> PlatformApiSecurityLevel.AUTHENTICATED.equals(apiInfo.getSecurityLevel()))
				.toList());

		authorizedRoleApis.addAll(apiInfoList.stream()
				.filter(api -> PlatformApiSecurityLevel.AUTHENTICATED_PERMISSIONS.equals(api.getSecurityLevel())
						&& !CollectionUtils.isEmpty(api.getPermissionKeys()))
				.toList());

		if (log.isDebugEnabled()) {
			debugApiInfoList(privateApis, "Private APIs");
			debugApiInfoList(publicApis, "Public APIs");
			debugApiInfoList(authenticatedApis, "Authenticated APIs");
			debugApiInfoList(authorizedRoleApis, "Authorized Permissions APIs");
		}

	}

	private void debugApiInfoList(List<ScannedApiInfo> apis, String name) {
		apis.stream()
				.collect(Collectors.groupingBy(ScannedApiInfo::getServiceId))
				.forEach((key, value) -> log.debug("{} [{}]: Size: [{}], API List: {}", name, key, value.size(), value
						.stream()
						.sorted(Comparator.comparing(ScannedApiInfo::getPath))
						.map(it -> String.format("[%s: %s, %s]", it.getMethod(), it.getPath(), it.getPermissionKeys()))
						.toList()));

	}
}
