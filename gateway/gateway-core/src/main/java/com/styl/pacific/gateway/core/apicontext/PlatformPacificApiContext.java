/******************************************************************************
 *  (C) Copyright 2008 STYL Solutions Pte. Ltd. , All rights reserved          *
 *                                                                             *
 *  This source code and any compilation or derivative thereof is the sole     *
 *  property of STYL Solutions Pte. Ltd. and is provided pursuant to a         *
 *  Software License Agreement.  This code is the proprietary information of   *
 *  STYL Solutions Pte. Ltd. and is confidential in nature. Its use and        *
 *  dissemination by any party other than STYL Solutions Pte. Ltd. is strictly *
 *  limited by the confidential information provisions of the Agreement        *
 *  referenced above.                                                          *
 ******************************************************************************/
package com.styl.pacific.gateway.core.apicontext;

import com.styl.pacific.common.constant.PacificRestConstants;
import com.styl.pacific.common.mapstruct.MapstructCommonDomainMapper;
import com.styl.pacific.domain.valueobject.TenantId;
import java.util.List;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;

@Builder
@Getter
@RequiredArgsConstructor
public class PlatformPacificApiContext {
	private final HttpHeaders headers;

	public void forwardHeaders(TenantId tenantId, HttpHeaders httpHeaders) {
		httpHeaders.addAll(headers);
		httpHeaders.put(PacificRestConstants.PlatformHeader.HEADER_X_TENANT_ID, List.of(
				MapstructCommonDomainMapper.INSTANCE.tenantIdToString(tenantId)));
	}
}
